{"name": "sakura", "version": "1.0.0", "private": true, "dependencies": {"@amcharts/amcharts5": "^5.2.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/face_detection": "^0.4.1646425229", "@pdf-lib/fontkit": "^1.1.1", "@rematch/core": "^2.2.0", "@rematch/select": "^3.1.2", "@tanstack/query-async-storage-persister": "^5.66.4", "@tanstack/react-query": "4.36.1", "@tanstack/react-query-devtools": "4.36.1", "@tanstack/react-query-persist-client": "^5.66.9", "@tanstack/react-virtual": "^3.13.9", "antd": "4.24.14", "axios": "^0.27.2", "classnames": "^2.3.1", "dompurify": "^2.3.6", "fs-extra": "^8.1.0", "hooks": "./local-libraries/hooks", "i18next": "^21.6.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-chained-backend": "^4.2.0", "i18next-http-backend": "^1.4.0", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lib-utils": "./local-libraries/lib-utils", "lodash": "^4.17.21", "mainam-react-native-date-utils": "^2.1.1", "mainam-react-native-string-utils": "^4.0.7", "moment": "^2.24.0", "moment-timezone": "^0.6.0", "path": "^0.12.7", "path-browserify": "^1.0.1", "pdf-lib": "^1.16.0", "print-js": "^1.6.0", "prop-types": "^15.7.2", "react": "^17.0.2", "react-app-polyfill": "^1.0.5", "react-beautiful-dnd": "^13.1.1", "react-dev-utils": "^11.0.4", "react-dom": "^17.0.2", "react-i18next": "^11.16.8", "react-multi-date-picker": "^4.5.2", "react-pdf": "^5.7.2", "react-redux": "^8.1.1", "react-render-html": "^0.6.0", "react-router-dom": "^5.3.4", "react-scripts": "^4.0.3", "react-scroll": "^1.9.0", "react-use-face-detection": "^1.0.1", "recharts": "^2.1.15", "redux": "^4.2.1", "retry-axios": "2.6.0", "stream-browserify": "^3.0.0", "styled-components": "5.3.11"}, "resolutions": {"@testing-library/dom": "9.3.4", "pdfmake": "0.2.12", "minimatch": "^9.0.1", "react-draggable": "4.4.6", "simple-swizzle": "0.2.2"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@typescript-eslint/eslint-plugin": "^2.8.0", "@typescript-eslint/parser": "^2.8.0", "babel-loader": "^9.1.3", "camelcase": "^5.3.1", "case-sensitive-paths-webpack-plugin": "2.2.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.2", "env-cmd": "^10.1.0", "eslint": "^6.6.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-react-app": "^5.1.0", "eslint-loader": "3.0.2", "eslint-plugin-flowtype": "3.13.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "7.16.0", "eslint-plugin-react-hooks": "^1.6.1", "external-remotes-plugin": "^1.0.0", "file-loader": "4.3.0", "html-webpack-plugin": "^5.3.2", "interpolate-html-plugin": "^4.0.0", "jest": "24.9.0", "jest-environment-jsdom-fourteen": "0.1.0", "jest-resolve": "24.9.0", "jest-watch-typeahead": "0.4.2", "less": "^3.10.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.2", "optimize-css-assets-webpack-plugin": "5.0.3", "simple-swizzle": "0.2.2", "pnp-webpack-plugin": "1.5.0", "process": "^0.11.10", "resolve-url-loader": "^3.1.2", "style-loader": "^3.3.1", "terser-webpack-plugin": "^2.3.8", "url-loader": "2.3.0", "webpack": "5.99.9", "webpack-cli": "^4.9.0", "webpack-dev-server": "^4.3.1", "webpack-manifest-plugin": "^5.0.0", "workbox-webpack-plugin": "4.3.1"}, "optionalDependencies": {"run-pty": "^4.0.3"}, "scripts": {"postinstall": "npx patch-package && node ./local-libraries/index.js", "start": "run-pty % yarn start-main % yarn start-component % yarn start-env % yarn start-app-info % yarn start-html-editor % yarn start-dashboard", "start-main": "env-cmd -f .env webpack serve --open --mode development", "start-component": "cd component && yarn start-silend", "start-env": "cd env && yarn start-silend", "start-app-info": "cd appInfo && yarn start-silend", "start-html-editor": "cd htmlEditor && yarn start-silend", "start-dashboard": "cd dashboard && yarn start-silend", "build-test": "sh ./build-jenkin.sh", "build": "env-cmd -f .env webpack --mode production", "empty": "node empty-build.js", "build:common": "yarn empty && node build.js main build && node build.js component build && node build.js appInfo build && node build.js htmlEditor build && node build.js dashboard build", "build:develop": "yarn build:common && node build.js env build:develop", "build:stable": "yarn build:common && node build.js env build:stable", "build:khanhhoa:stable": "yarn build:common && node build.js env build:khanhhoa:stable", "build:khanhhoa:production": "yarn build:common && node build.js env build:khanhhoa:production", "build:nhidongtp:develop": "yarn build:common && node build.js env build:nhidongtp:develop", "build:nhidongtp:stable": "yarn build:common && node build.js env build:nhidongtp:stable", "build:nhidongtp:production": "yarn build:common && node build.js env build:nhidongtp:production", "build:pstw:production": "yarn build:common && node build.js env build:pstw:production", "build:pstw:stable": "yarn build:common && node build.js env build:pstw:stable", "build:pshn:stable": "yarn build:common && node build.js env build:pshn:stable", "build:pshn:production": "yarn build:common && node build.js env build:pshn:production", "build:choray:stable": "yarn build:common && node build.js env build:choray:stable", "build:choray:production": "yarn build:common && node build.js env build:choray:production", "build:isofhdm:stable": "yarn build:common && node build.js env build:isofhdm:stable", "build:vietnhat:production": "yarn build:common && node build.js env build:vietnhat:production", "build:vnhsc:stable": "yarn build:common && node build.js env build:vnhsc:stable", "build:vnhsc:production": "yarn build:common && node build.js env build:vnhsc:production", "build:myrehab:stable": "yarn build:common && node build.js env build:myrehab:stable", "build:myrehab:production": "yarn build:common && node build.js env build:myrehab:production", "build:noitiet:production": "yarn build:common && node build.js env build:noitiet:production", "build:noitiet:stable": "yarn build:common && node build.js env build:noitiet:stable", "build:dongtam:production": "yarn build:common && node build.js env build:dongtam:production", "build:vietnga:production": "yarn build:common && node build.js env build:vietnga:production", "build:vietnga:stable": "yarn build:common && node build.js env build:vietnga:stable", "build:tttm:production": "yarn build:common && node build.js env build:tttm:production", "build:tttm:stable": "yarn build:common && node build.js env build:tttm:stable", "build:ctm:stable": "yarn build:common && node build.js env build:ctm:stable", "build:ctm:production": "yarn build:common && node build.js env build:ctm:production", "build:thaihoa:stable": "yarn build:common && node build.js env build:thaihoa:stable", "build:thaihoa:production": "yarn build:common && node build.js env build:thaihoa:production", "build:allen:production": "yarn build:common && node build.js env build:allen:production", "build:demo1:develop": "yarn build:common && node build.js env build:demo1:develop", "build:demo:develop": "yarn build:common && node build.js env build:demo:develop", "build:autotest:develop": "yarn build:common && node build.js env build:autotest:develop", "build:dongda:stable": "yarn build:common && node build.js env build:dongda:stable", "build:dongda:production": "yarn build:common && node build.js env build:dongda:production", "build:pk40:production": "yarn build:common && node build.js env build:pk40:production", "build:mediplus:stable": "yarn build:common && node build.js env build:mediplus:stable", "build:mediplus:production": "yarn build:common && node build.js env build:mediplus:production", "build:mediplus-nd:production": "yarn build:common && node build.js env build:mediplus-nd:production", "build:gout:stable": "yarn build:common && node build.js env build:gout:stable", "build:gout:production": "yarn build:common && node build.js env build:gout:production", "build:pnt:stable": "yarn build:common && node build.js env build:pnt:stable", "build:pnt:production": "yarn build:common && node build.js env build:pnt:production", "build:bvp:production": "yarn build:common && node build.js env build:bvp:production", "build:bvp:stable": "yarn build:common && node build.js env build:bvp:stable", "build:rpt:stable": "yarn build:common && node build.js env build:rpt:stable", "build:rpt:production": "yarn build:common && node build.js env build:rpt:production", "build:caophuc:stable": "yarn build:common && node build.js env build:caophuc:stable", "build:caophuc:production": "yarn build:common && node build.js env build:caophuc:production", "build:hplus:stable": "yarn build:common && node build.js env build:hplus:stable", "build:hplus:production": "yarn build:common && node build.js env build:hplus:production", "build:ntdkth:production": "yarn build:common && node build.js env build:ntdkth:production", "build:thuduc:stable": "yarn build:common && node build.js env build:thuduc:stable", "build:dhy:production": "yarn build:common && node build.js env build:dhy:production", "build:dhyd:stable": "yarn build:common && node build.js env build:dhyd:stable", "build:dhyd:production": "yarn build:common && node build.js env build:dhyd:production", "build:spaul:production": "yarn build:common && node build.js env build:spaul:production", "build:aegis1:production": "yarn build:common && node build.js env build:aegis1:production", "build:aegis2:production": "yarn build:common && node build.js env build:aegis2:production", "build:thuduc:production": "yarn build:common && node build.js env build:thuduc:production", "build:kinhbac:production": "yarn build:common && node build.js env build:kinhbac:production", "build:huyethoc:stable": "yarn build:common && node build.js env build:huyethoc:stable", "build:binhphu:stable": "yarn build:common && node build.js env build:binhphu:stable", "build:huyethoc:production": "yarn build:common && node build.js env build:huyethoc:production", "build:binhphu:production": "yarn build:common && node build.js env build:binhphu:production", "build:mattw:stable": "yarn build:common && node build.js env build:mattw:stable", "pick": "git cherry-pick -x", "logall": "git log --all", "log": "git checkout develop -f && git log -- ", "diff": "git diff --name-status ", "i18n": "node webpack/utils/compare-i18n.js"}, "engines": {"node": ">= 14.21.0"}, "workspaces": ["main", "env", "component", "appInfo", "htmlEditor", "dashboard"], "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "babel": {"presets": ["react-app"]}}