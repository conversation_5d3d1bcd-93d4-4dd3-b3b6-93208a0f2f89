import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import VitalSigns from "../VitalSigns";
import { Main } from "./styled";
import { useSelector } from "react-redux";
import { useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";

const ModalVitalSigns = ({ draw, ...props }, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
  });
  const { values = [] } = useSelector((state) => state.vitalSigns);

  const [dataMA_CSS_VONG_CANH_TAY, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_CSS_VONG_CANH_TAY
  );
  const [dataHIEN_THI_TRUONG_SINH_HIEU, loadFinishHienThiTruongSinhHieu] =
    useThietLap(THIET_LAP_CHUNG.HIEN_THI_TRUONG_SINH_HIEU);

  const refModal = useRef(null);
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const refCallback = useRef(null);
  const refCtxBody = useRef();
  const refCtxFooter = useRef();
  useImperativeHandle(ref, () => ({
    show: (
      { refCanvas, refCanvasFooter, tiepDon, khoaLamViec, nbDotDieuTriId },
      callBack
    ) => {
      setState({
        show: true,
        tiepDon,
        khoaLamViec,
        nbDotDieuTriId,
      });
      refCtxBody.current = refCanvas;
      refCtxFooter.current = refCanvasFooter;
      refCallback.current = callBack;
    },
  }));

  const onOK = (isOk) => () => {
    if (isOk) {
    } else {
      setState({
        show: false,
      });
    }
  };
  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      if (refCtxFooter.current) {
        const ctxFooter = refCtxFooter.current.getContext("2d");
        ctxFooter.clearRect(
          0,
          0,
          refCtxFooter.current.width,
          refCtxFooter.current.height
        );
      }
      if (refCtxBody.current) {
        const ctxBody = refCtxBody.current.getContext("2d");
        ctxBody.clearRect(
          0,
          0,
          refCtxBody.current.width,
          refCtxBody.current.height
        );
      }
      if (draw) draw(values, true);
      // refCallback.current && refCallback.current(values, true);
    }
  }, [state.show]);
  const width = useMemo(() => {
    return window.screen.width - 100;
  }, [window.screen.width]);

  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onOK(false)}
      title={
        state?.tiepDon
          ? " "
          : t("quanLyNoiTru.chiSoSong.phieuTheoDoiChucNangSong")
      }
      width={width}
    >
      <Main>
        {loadFinish && loadFinishHienThiTruongSinhHieu && (
          <VitalSigns
            isModal={true}
            show={state.show}
            dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
            dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
            khoaLamViec={state.khoaLamViec}
            nbDotDieuTriId={state.nbDotDieuTriId}
          />
        )}
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalVitalSigns);
