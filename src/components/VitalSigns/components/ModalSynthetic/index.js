import React, {
  forwardRef,
  useImperative<PERSON>andle,
  useState,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { Row, Col, message, Input } from "antd";
import { useTranslation } from "react-i18next";
import { cloneDeep, get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";

import cacheUtils from "lib-utils/cache-utils";
import { checkRole } from "lib-utils/role-utils";
import { useEnum, useStore, useThietLap } from "hooks";

import { Main } from "./styled";
import {
  Button,
  Checkbox,
  DateTimePicker,
  InputNumberField,
  Select,
  TableWrapper,
} from "components";
import {
  DS_TINH_CHAT_KHOA,
  ENUM,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import { SVG } from "assets";
import { isArray, isNumber, isObject } from "utils/index";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";

const { ModalCustomizeColumn } = TableWrapper;

const MIN_NHIET_DO = 0;
const MAX_NHIET_DO = 42;

const defaultLayout = [
  { key: "thoiGianThucHien", i18Name: "common.thoiGian", type: "date" },
  {
    key: "nhipMach",
    i18Name: "sinhHieu.nhapMach",
    type: "number",
    autoFocus: "autoFocusMach",
  },
  {
    key: "nhietDo",
    i18Name: "sinhHieu.nhapNhietDo",
    type: "number",
    autoFocus: "autoFocusNhiet",
    min: MIN_NHIET_DO,
    max: MAX_NHIET_DO,
    decimalScale: 2,
  },
  {
    key: "huyetApTamThu",
    i18Name: "sinhHieu.huyetApTamThu",
    type: "number",
    autoFocus: "autoFocusHATamThu",
  },
  {
    key: "huyetApTamTruong",
    i18Name: "sinhHieu.huyetApTamTruong",
    type: "number",
    autoFocus: "autoFocusHATamTruong",
  },
  {
    key: "nhipTho",
    i18Name: "sinhHieu.nhapNhipTho",
    type: "number",
  },
];

const listLayoutNgoaiTru = [
  ...defaultLayout,
  {
    key: "chieuCao",
    i18Name: "sinhHieu.chieuCao",
    type: "number",
    autoFocus: "autoFocusChieuCao",
    decimalScale: 2,
  },
  {
    key: "canNang",
    i18Name: "sinhHieu.canNang",
    autoFocus: "autoFocusCanNang",
    decimalScale: 2,
  },
  {
    key: "nhomMau",
    i18Name: "sinhHieu.chonNhomMau",
    autoFocus: "autoFocusNhomMau",
  },
  {
    key: "spo2",
    i18Name: "sinhHieu.spo2%",
    type: "number",
    autoFocus: "autoFocusSpo2",
  },
  {
    key: "bopBong",
    i18Name: "khamBenh.chanDoan.bopBong",
    type: "checkbox",
    autoFocus: "autoFocusBopBong",
  },
];

const newLayoutNoiTru = [
  {
    key: "acvpu",
    i18Name: "sinhHieu.acvpu",
    type: "select",
    autoFocus: "autoFocusAcpvu",
    placeholder: "sinhHieu.chonAcvpu",
  },
  {
    key: "glasgowMat",
    i18Name: "sinhHieu.matE",
    type: "select",
    autoFocus: "autoFocusGlasgowMat",
    placeholder: "sinhHieu.chonMatE",
  },
  {
    key: "glasgowLoiNoi",
    i18Name: "sinhHieu.loiNoiV",
    type: "select",
    autoFocus: "autoFocusGlasgowLoiNoi",
    placeholder: "sinhHieu.chonLoiNoiV",
  },
  {
    key: "glasgowVanDong",
    i18Name: "sinhHieu.vanDongM",
    type: "select",
    autoFocus: "autoFocusGlasgowVanDong",
    placeholder: "sinhHieu.chonVanDongM",
  },
  {
    key: "glasgow",
    i18Name: "sinhHieu.glasgow",
    readOnly: true,
  },
];

const listLayoutNoiTru = [
  ...defaultLayout,
  {
    key: "canNang",
    i18Name: "sinhHieu.canNang",
    type: "number",
    autoFocus: "autoFocusCanNang",
    decimalScale: 2,
  },
  {
    key: "troTho",
    i18Name: "sinhHieu.troTho",
    type: "checkbox",
  },
  {
    key: "khoa",
    i18Name: "common.khoa",
    autoFocus: "autoFocusKhoa",
  },
];

const setValueDefault = (currentValue) => {
  let obj = {};
  if (isObject(currentValue, true)) {
    obj = {
      chieuCao: currentValue.chieuCao,
      canNang: currentValue.canNang,
      nhipTho:
        currentValue?.nhipTho === 0
          ? 0
          : currentValue?.nhipTho
          ? (currentValue?.nhipTho + "").split("/")[0]
          : "",
      nhietDo: currentValue.nhietDo,
    };
  }
  return obj;
};

const ModalSynthetic = (
  { onCancelUpdate = () => {}, khoaLamViec, listChiSoSongMacDinh },
  ref
) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [listTroTho] = useEnum(ENUM.TRO_THO, []);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listAcvpu] = useEnum(ENUM.ACVPU);
  const [listGlasgowMat] = useEnum(ENUM.GLASGOW_MAT);
  const [listGlasgowLoiNoi] = useEnum(ENUM.GLASGOW_LOI_NOI);
  const [listGlasgowVanDong] = useEnum(ENUM.GLASGOW_VAN_DONG);
  const isNoiTru = window.location.pathname.includes("quan-ly-noi-tru");
  const listAllKhoa = useStore("khoa.listAllKhoaTongHop", []);

  const cachedKey = `MODAL_SYNTHETIC_${isNoiTru ? "NOI_TRU" : "NGOAI_TRU"}`;

  const {
    vitalSigns: { updateData },
    khoa: { getListAllKhoaTongHop: getListAllKhoa },
  } = dispatch;
  const { isSaveSucces, configData } = useSelector((state) => state.vitalSigns);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const auth = useStore("auth.auth", {});
  const [dataTU_DONG_LAY_DU_LIEU_SINH_HIEU_GAN_NHAT] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAY_DU_LIEU_SINH_HIEU_GAN_NHAT
  );
  const [dataHIEN_THI_TRUONG_SINH_HIEU, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TRUONG_SINH_HIEU
  );
  const manHinhSinhHieu = window.location.pathname.includes("/sinh-hieu/");
  const manHinhKhamBenh = window.location.pathname.includes("/kham-benh/");
  const manHinhNoiTru = window.location.pathname.includes(
    "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/"
  );

  const refCallback = useRef(null);
  const refMach = useRef(null);
  const refNhietDo = useRef(null);
  const refHuyetAp = useRef(null);
  const refNhiptho = useRef(null);
  const refCannang = useRef(null);
  const refChieuCao = useRef(null);
  const refNhomMau = useRef(null);
  const refSpo2 = useRef(null);
  const refModal = useRef(null);
  const refModalCustomizeField = useRef(null);
  const refValueNhietDo = useRef(null);
  const refKhoa = useRef(null);
  const chiSoSongKhacRef = useRef([]);
  const refAcvpu = useRef(null);
  const refGlasgowMat = useRef(null);
  const refGlasgowLoiNoi = useRef(null);
  const refGlasgowVanDong = useRef(null);

  const listRef = {
    nhipMach: refMach,
    nhietDo: refNhietDo,
    huyetApTamThu: refHuyetAp,
    nhipTho: refNhiptho,
    canNang: refCannang,
    chieuCao: refChieuCao,
    spo2: refSpo2,
    acvpu: refAcvpu,
    glasgowMat: refGlasgowMat,
    glasgowLoiNoi: refGlasgowLoiNoi,
    glasgowVanDong: refGlasgowVanDong,
  };

  const listLayoutsDefaults = useMemo(() => {
    let currentIndex = 1;
    if (!loadFinish) return [];
    const layoutsSource = isNoiTru
      ? [
          ...listLayoutNoiTru,
          ...(dataHIEN_THI_TRUONG_SINH_HIEU?.eval() ? newLayoutNoiTru : []),
        ]
      : listLayoutNgoaiTru;
    let dataDefault = cloneDeep(layoutsSource).map((item) => ({
      ...item,
      index: currentIndex++,
      show: true,
    }));

    if (isArray(listChiSoSongMacDinh, true)) {
      const chiSoMacDinh = listChiSoSongMacDinh.map((item) => ({
        key: item.ma,
        ten: item.ten,
        type: "chiSoSongKhac",
        chiSoSongId: item.id,
        autoFocus: `autoFocus${item.id}`,
        index: currentIndex++,
        show: true,
        min: item.giaTriNhoNhat,
        max: item.giaTriLonNhat,
        donVi: item.donVi,
      }));
      dataDefault = [...dataDefault, ...chiSoMacDinh];
    }
    return dataDefault;
  }, [
    listLayoutNoiTru,
    listLayoutNgoaiTru,
    isNoiTru,
    listChiSoSongMacDinh,
    dataHIEN_THI_TRUONG_SINH_HIEU,
    loadFinish,
  ]);

  const [state, _setState] = useState({
    values: {},
  });
  const [columnsShow, setColumnsShow] = useState(listLayoutsDefaults);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const onCancel = () => {
    setState({
      show: false,
    });
    onCancelUpdate();
  };
  // useOutsideClick(refModal, () => onCancel());

  useImperativeHandle(ref, () => ({
    show: async (x, y, data, callback) => {
      setState({
        show: true,
      });
      let currentValue = data?.values[data?.index];
      let newObj = {};
      if (
        dataTU_DONG_LAY_DU_LIEU_SINH_HIEU_GAN_NHAT?.eval() &&
        (manHinhSinhHieu || manHinhKhamBenh || manHinhNoiTru)
      ) {
        let lastValue = data?.values[data?.values?.length - 1];
        let preValue = data?.values[data?.index - 1];
        if (!lastValue.id && !currentValue.id) {
          // case click vào line tạo mới chứa có id
          if (!preValue) {
            // case 1 bản ghi duy nhất ko tồn tại items trước nó sẽ gọi api tìm theo nbDotDieuTriId
            let nbDotDieuTriId = manHinhNoiTru
              ? thongTinCoBan?.id
              : thongTinBenhNhan?.id;
            const res1 = await nbChiSoSongProvider.getDataVitalSigns({
              nbDotDieuTriId,
            });
            let chiSoSongData = res1?.data || [];
            chiSoSongData.sort(
              (a, b) =>
                new Date(a?.thoiGianThucHien).getTime() -
                new Date(b?.thoiGianThucHien).getTime()
            );
            newObj = {
              ...setValueDefault(chiSoSongData[chiSoSongData.length - 1]),
            };
          } else {
            // case có bản ghi trước đó
            newObj = {
              ...setValueDefault(preValue),
            };
          }
        }
      }
      const values = {
        auToAddNhietDo: currentValue?.auToAddNhietDo,
        auToAddMach: currentValue?.auToAddMach,
        nhipMachDefault: currentValue?.mach,
        nhietDefault: currentValue?.nhietDo,
        nhipMach: currentValue?.auToAddMach ? null : currentValue?.mach,
        nhietDo: currentValue?.auToAddNhietDo ? null : currentValue?.nhietDo,
        huyetApTamThu: currentValue?.huyetap
          ? currentValue?.huyetap?.split("/")[0]
          : "",
        huyetApTamTruong: currentValue?.huyetap
          ? currentValue?.huyetap?.split("/")[1]
          : "",
        nhipTho:
          currentValue?.nhipTho === 0
            ? 0
            : currentValue?.nhipTho
            ? (currentValue?.nhipTho + "").split("/")[0]
            : "",
        troTho: currentValue?.troTho,
        canNang: currentValue?.canNang,
        thoiGianThucHien: currentValue?.thoiGianThucHien || new Date(),
        chieuCao: currentValue?.chieuCao,
        spo2: currentValue?.spo2,
        nhomMau: currentValue?.nhomMau,
        chiDinhTuLoaiDichVu: currentValue?.chiDinhTuLoaiDichVu,
        ...(isNoiTru && {
          khoaChiDinhId: currentValue?.khoaChiDinhId || khoaLamViec?.id,
          dsChiSoSongKhac: currentValue.dsChiSoSongKhac,
          ...(dataHIEN_THI_TRUONG_SINH_HIEU?.eval() && {
            acvpu: currentValue?.acvpu,
            glasgowMat: currentValue?.glasgowMat,
            glasgowLoiNoi: currentValue?.glasgowLoiNoi,
            glasgowVanDong: currentValue?.glasgowVanDong,
            glasgow: currentValue?.glasgow,
          }),
        }),
        ...newObj,
      };

      refValueNhietDo.current = values.nhietDo;

      async function fetchData() {
        let layouts = await cacheUtils.read(
          auth.nhanVienId,
          `DATA_CUSTOMIZE_COLUMN_${cachedKey}`,
          null,
          false
        );
        if (layouts) {
          setColumnsShow(layouts);
        } else {
          setColumnsShow(listLayoutsDefaults);
        }
      }
      if (auth?.nhanVienId) {
        fetchData();
      }
      getListAllKhoa({
        page: "",
        size: "",
        active: true,
        dsTinhChatKhoa: [DS_TINH_CHAT_KHOA.NOI_TRU],
      });
      setState({
        top: y + 10,
        left: x,
        show: true,
        values,
        dataType: data.type,
        isLastColumn: data.isLastColumn,
        data: data?.values,
        index: data.index,
      });
      refCallback.current = callback;
    },
    hide: () => {
      setState({
        show: false,
      });
    },
  }));

  useEffect(() => {
    if (isSaveSucces) {
      setState({
        values: {},
      });
      updateData({
        isSaveSucces: false,
      });
    }
  }, [isSaveSucces]);

  useEffect(() => {
    switch (state.dataType) {
      case "nhipMach":
      case "mach":
        if (refMach.current) {
          refMach.current.focus();
        }
        break;
      case "nhietDo":
        if (refNhietDo.current) {
          refNhietDo.current.focus();
        }
        break;
      case "huyetap":
        if (refHuyetAp.current) {
          refHuyetAp.current.focus();
        }
        break;
      case "nhipTho":
        if (refNhiptho.current) {
          refNhiptho.current.focus();
        }
        break;
      case "canNang":
        if (refCannang.current) {
          refCannang.current.focus();
        }
        break;
      case "chieuCao":
        if (refChieuCao.current) {
          refChieuCao.current.focus();
        }
        break;
      case "nhomMau":
        if (refNhomMau.current) {
          refNhomMau.current.focus();
        }
        break;
      case "spo2":
        if (refSpo2.current) {
          refSpo2.current.focus();
        }
        break;
      case "khoaChiDinhId":
        if (
          refKhoa.current &&
          checkRole([ROLES["QUAN_LY_NOI_TRU"].CHINH_SUA_KHOA_NHAP_SINH_HIEU])
        ) {
          refKhoa.current.focus();
        }
        break;
      case "acvpu":
        if (refAcvpu.current) {
          refAcvpu.current.focus();
        }
        break;
      case "glasgowMat":
        if (refGlasgowMat.current) {
          refGlasgowMat.current.focus();
        }
        break;
      case "glasgowLoiNoi":
        if (refGlasgowLoiNoi.current) {
          refGlasgowLoiNoi.current.focus();
        }
        break;
      case "glasgowVanDong":
        if (refGlasgowVanDong.current) {
          refGlasgowVanDong.current.focus();
        }
        break;
      default:
        if (isNumber(state.dataType)) {
          chiSoSongKhacRef.current[state.dataType]?.focus();
        }
        break;
    }
  }, [state.dataType, state.show, state.index]);

  const onFocus = () => {};

  const blockInvalidChar = (e) => {
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();
  };

  const getPrefixByKey = (key) => {
    switch (key) {
      case "glasgowMat":
        return "E";
      case "glasgowLoiNoi":
        return "V";
      case "glasgowVanDong":
        return "M";
      default:
        return "";
    }
  };

  const getLabValueByKey = (key, value) => {
    let label =
      getListDataByKey(key).find((item) => item.id == value)?.ten || "";
    const match = label.match(/\((.*?)\)/);
    let prefix = match ? match[1] : "";
    return prefix;
  };

  const onChange = (key) => (e) => {
    let value = null;
    let values = {
      ...state.values,
    };
    if (key == "huyetApTamTruong") {
      if (e > state.values.huyetApTamThu) {
        message.error(
          t("quanLyNoiTru.chiSoSong.huyetApTamTruongPhaiNhoHonHuyetApTamThu")
        );
      }
    }
    if (key == "thoiGianThucHien") {
      if (e?._d < new Date(chiTietNguoiBenhNoiTru.thoiGianVaoKhoaNhapVien)) {
        message.error(
          t("quanLyNoiTru.chiSoSong.vuiLongChonNgayLonHonNgayVaoVien")
        );
        return;
      } else if (e?._d > new Date()) {
        message.error(
          t("quanLyNoiTru.chiSoSong.vuiLongChonNgayNhoHonNgayHienTai")
        );
        return;
      } else {
        value = e?._d;
      }
    }
    if (key == "date") {
      if (e?._d < new Date(chiTietNguoiBenhNoiTru.ngayVaoVien)) {
        message.error(
          t("quanLyNoiTru.chiSoSong.vuiLongChonNgayLonHonNgayVaoVien")
        );
        return;
      } else if (e?._d > new Date()) {
        message.error(
          t("quanLyNoiTru.chiSoSong.vuiLongChonNgayNhoHonNgayHienTai")
        );
        return;
      } else {
        value = e?._d;
      }
    } else if (e?.target) {
      if (e?.target.type === "radio") {
        value = e.target?.value;
      } else {
        value = e?.target?.checked;
      }
    } else {
      value = e;
    }

    if (key == "nhipMach") {
      values.auToAddMach = false;
    }
    if (key == "nhietDo") {
      values.auToAddNhietDo = false;
      refValueNhietDo.current = value;
    }

    if (["glasgowMat", "glasgowLoiNoi", "glasgowVanDong"].includes(key)) {
      const glasgowComponents = {
        E: values.glasgowMat
          ? getLabValueByKey("glasgowMat", values.glasgowMat)
          : "",
        V: values.glasgowLoiNoi
          ? getLabValueByKey("glasgowLoiNoi", values.glasgowLoiNoi)
          : "",
        M: values.glasgowVanDong
          ? getLabValueByKey("glasgowVanDong", values.glasgowVanDong)
          : "",
      };

      const prefix = getPrefixByKey(key);
      glasgowComponents[prefix] = getLabValueByKey(key, value);

      values["glasgow"] = `${
        glasgowComponents.E ? "E" + glasgowComponents.E : ""
      }${glasgowComponents.V ? "V" + glasgowComponents.V : ""}${
        glasgowComponents.M ? "M" + glasgowComponents.M : ""
      }`;
    }

    values = {
      ...values,
      [key]: value,
    };

    setState({ values });
  };

  const onBlur = (item) => (e) => {
    let valNhietDo = refValueNhietDo.current;
    if (item.key === "nhietDo" && isNumber(valNhietDo)) {
      if (valNhietDo < MIN_NHIET_DO) {
        valNhietDo = MIN_NHIET_DO;
      } else if (valNhietDo > MAX_NHIET_DO) {
        valNhietDo = MAX_NHIET_DO;
      }
      if (
        refValueNhietDo.current < MIN_NHIET_DO ||
        refValueNhietDo.current > MAX_NHIET_DO
      ) {
        setState({
          values: {
            ...state?.values,
            ["nhietDo"]: valNhietDo,
          },
        });
      }
    }

    if (item.type === "chiSoSongKhac") {
      let value = e;
      if (value < item.min) {
        value = item.min;
      } else if (value > item.max) {
        value = item.max;
      }
      onChangeChiSoSongKhac(item)(value);
    }

    if (item.key === "canNang") {
      const isZero = parseFloat(state.values.canNang) === 0;
      if (isZero) {
        message.error(t("tiepDon.canNangPhaiLonHon0"));
        setState({
          values: {
            ...state?.values,
            ["canNang"]: null,
          },
        });
      }
    }
  };

  const onChangeChiSoSongKhac = (item) => (e) => {
    const value = e?.target?.value ?? e;

    let values = {
      ...state?.values,
      dsChiSoSongKhac: [
        ...state?.values?.dsChiSoSongKhac.filter(
          (x) => x.chiSoSongId !== item.chiSoSongId
        ),
        {
          chiSoSongId: item.chiSoSongId,
          giaTri: value,
          tenChiSoSong: item.ten,
        },
      ],
    };

    setState({ values });
  };

  const onSaveModalCustomizeColumn = (data) => {
    setColumnsShow(data);
    cacheUtils.save(
      auth.nhanVienId,
      "DATA_CUSTOMIZE_COLUMN_" + cachedKey,
      data,
      false
    );
  };

  const onOk = async () => {
    const nhipTho = (state?.values?.nhipTho || "")?.toString();
    const hatThu = state?.values?.huyetApTamThu || "";
    const hatTruong = state?.values?.huyetApTamTruong
      ? "/" + state?.values?.huyetApTamTruong
      : "";
    const data = {
      ...state.data[state.index],
      chiDinhTuLoaiDichVu:
        configData?.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TIEP_DON &&
        !!state?.isLastColumn
          ? LOAI_DICH_VU.TIEP_DON
          : state?.values?.chiDinhTuLoaiDichVu,
      auToAddNhietDo: state?.values?.auToAddNhietDo,
      auToAddMach: state?.values?.auToAddMach,
      canNang: state?.values?.canNang,
      chieuCao: state?.values?.chieuCao,
      spo2: state?.values?.spo2,
      nhomMau: state?.values?.nhomMau,
      huyetap: hatThu + hatTruong,
      nhietDo: state?.values?.auToAddNhietDo
        ? state?.values?.nhietDefault
        : state?.values?.nhietDo,
      mach: state?.values?.auToAddMach
        ? state?.values?.nhipMachDefault
        : state?.values?.nhipMach,
      nhipTho: nhipTho,
      troTho: state?.values?.troTho,
      huyetApTamThu: state?.values?.huyetApTamThu,
      huyetApTamTruong: state?.values?.huyetApTamTruong,
      thoiGianThucHien: state?.values?.thoiGianThucHien || new Date(),
      isNewCol: !!state?.isLastColumn,
      isEditCol: !state?.isLastColumn,
      index: state.index,
      ...(isNoiTru && {
        khoaChiDinhId: state.values?.khoaChiDinhId,
        dsChiSoSongKhac: state.values?.dsChiSoSongKhac,
        acvpu: state.values?.acvpu,
        glasgowMat: state.values?.glasgowMat,
        glasgowLoiNoi: state.values?.glasgowLoiNoi,
        glasgowVanDong: state.values?.glasgowVanDong,
        glasgow: state.values?.glasgow,
      }),
    };
    if (+data.huyetApTamTruong > +data.huyetApTamThu) {
      message.error(
        t("quanLyNoiTru.chiSoSong.huyetApTamTruongPhaiNhoHonHuyetApTamThu")
      );
      return;
    }
    const newData = cloneDeep(state.data);
    newData[state?.index] = data;

    updateData({
      preValues: newData,
      values: newData,
      idxColEdit: null,
    });
    refCallback.current(newData, {
      isCanvasBody: true,
      isCanvasFooter: true,
      isLastColumn: state?.isLastColumn,
    });
    setState({ show: false });
  };

  const getValueChiSoSongKhac = (item) => {
    return (state.values?.dsChiSoSongKhac || []).find(
      (i) => i.chiSoSongId === item.chiSoSongId
    )?.giaTri;
  };

  if (!state.show) return null;

  const left = () => {
    if (state.left) {
      if (window.innerWidth - state.left - 282 > 500) {
        return state.left;
      } else {
        return state.left - 550 > 0 ? state.left - 550 : 20;
      }
    } else {
      return "30%";
    }
  };

  const mapAdditionalProps = (item) => {
    if (item.key === "canNang") {
      return {
        isAllowed: (values) => {
          const { floatValue } = values;
          if (floatValue === undefined) return true;
          const parts = String(floatValue).split(".");
          const [integer, decimal] = parts;
          if (integer.length > 6) return false;
          if (decimal?.length > 2) return false;
          return true;
        },
        status: parseFloat(state.values.canNang) === 0 ? "error" : "",
      };
    }
    if (item.key === "nhipTho") {
      return {
        isAllowed: (values) => {
          const { floatValue } = values;
          if (floatValue === undefined) return true;
          const parts = String(floatValue).split(".");
          const [integer, decimal] = parts;
          if (integer.length > 9) return false;
          if (decimal?.length > 2) return false;
          return true;
        },
        status: parseFloat(state.values.nhipTho) === 0 ? "error" : "",
      };
    }
  };

  const getListDataByKey = (key) => {
    switch (key) {
      case "acvpu":
        return listAcvpu;
      case "glasgowMat":
        return listGlasgowMat;
      case "glasgowLoiNoi":
        return listGlasgowLoiNoi;
      case "glasgowVanDong":
        return listGlasgowVanDong;
      default:
        return [];
    }
  };

  return (
    <Main>
      <div
        style={{
          top: state.top || "20%",
          left: left(),
        }}
        ref={refModal}
        className="main"
        id="modal-synthetic"
      >
        <div className="card">
          <Row gutter={20}>
            <SVG.IcSetting
              className="setting"
              onClick={() => refModalCustomizeField.current?.show()}
            />
            {columnsShow
              .filter((i) => !!i.show)
              .map((item) => {
                const {
                  type,
                  key,
                  i18Name,
                  autoFocus,
                  columnName,
                  show,
                  ten,
                  min,
                  max,
                  chiSoSongId,
                  donVi,
                  ...rest
                } = item || {};
                if (type === "chiSoSongKhac") {
                  if (max || min) {
                    return (
                      <Col key={chiSoSongId} span={12} className="item">
                        <label>{`${ten} (${donVi ? donVi : ""})`}</label>
                        <InputNumberField
                          value={getValueChiSoSongKhac(item)}
                          onChange={onChangeChiSoSongKhac(item)}
                          timeDelay={0}
                          style={{ width: "100%" }}
                          onFocus={onFocus}
                          min={0}
                          decimalScale={6}
                          onKeyDown={blockInvalidChar}
                          onBlur={onBlur(item)}
                          getInputRef={(ref) => {
                            return (chiSoSongKhacRef.current[chiSoSongId] =
                              ref);
                          }}
                          {...rest}
                        />
                      </Col>
                    );
                  } else {
                    return (
                      <Col key={chiSoSongId} span={12} className="item">
                        <label>{`${ten} (${donVi ? donVi : ""})`}</label>
                        <Input
                          style={{ width: "100%" }}
                          onChange={onChangeChiSoSongKhac(item)}
                          value={getValueChiSoSongKhac(item)}
                          ref={(ref) => {
                            return (chiSoSongKhacRef.current[chiSoSongId] =
                              ref);
                          }}
                        />
                      </Col>
                    );
                  }
                } else if (type === "select") {
                  return (
                    <Col key={key} span={12} className="item">
                      <label>{ten ? ten : t(i18Name)}</label>
                      <Select
                        style={{ width: "100%" }}
                        value={state?.values?.[key]}
                        data={getListDataByKey(key) ?? []}
                        onChange={onChange(key)}
                        placeholder={t(rest.placeholder)}
                        autoFocus={state[autoFocus]}
                        refSelect={listRef[key]}
                        showAction={["focus"]}
                      />
                    </Col>
                  );
                }
                switch (key) {
                  case "thoiGianThucHien": {
                    return (
                      <Col key={key} span={24} className="item">
                        <label style={{ width: "100%" }}>
                          {t("common.thoiGian")}
                        </label>
                        <DateTimePicker
                          showTime={{ format: "HH:mm" }}
                          showToday={true}
                          value={moment(state?.values?.thoiGianThucHien)}
                          onChange={onChange("thoiGianThucHien")}
                          style={{ minHeight: "32px" }}
                          focus={onFocus}
                        />
                      </Col>
                    );
                  }
                  case "nhomMau": {
                    return (
                      <Col key={key} span={12} className="item">
                        <label>{t("quanLyNoiTru.mau.nhomMau")}</label>
                        <Select
                          style={{ width: "100%" }}
                          value={state?.values?.nhomMau}
                          data={listNhomMau}
                          onChange={onChange("nhomMau")}
                          placeholder={t("sinhHieu.chonNhomMau")}
                          autoFocus={state.autoFocusNhomMau}
                          refSelect={refNhomMau}
                          showAction={["focus"]}
                        />
                      </Col>
                    );
                  }
                  case "troTho": {
                    return (
                      <Col
                        span={12}
                        className="item"
                        style={{
                          minHeight: "60px",
                          display: "flex",
                          alignItems: "center",
                        }}
                        key={key}
                      >
                        {listTroTho.map((item) => (
                          <Checkbox
                            key={item.id}
                            style={{
                              marginTop: 25,
                            }}
                            checked={state?.values?.troTho == item.id}
                            onChange={(e) =>
                              onChange("troTho")(
                                e?.target?.checked ? item.id : null
                              )
                            }
                          >
                            {item.ten}
                          </Checkbox>
                        ))}
                      </Col>
                    );
                  }
                  case "khoa": {
                    return (
                      <Col key={key} span={12} className="item">
                        <label>{t(i18Name)}</label>
                        <Select
                          style={{ width: "100%" }}
                          value={state?.values?.khoaChiDinhId}
                          data={listAllKhoa}
                          onChange={onChange("khoaChiDinhId")}
                          placeholder={t("common.chonKhoa")}
                          autoFocus={state.autoFocusKhoa}
                          refSelect={refKhoa}
                          showAction={["focus"]}
                          disabled={
                            !checkRole([
                              ROLES["QUAN_LY_NOI_TRU"]
                                .CHINH_SUA_KHOA_NHAP_SINH_HIEU,
                            ])
                          }
                        />
                      </Col>
                    );
                  }
                  case "bopBong": {
                    return (
                      <Col
                        span={12}
                        className="item"
                        style={{
                          minHeight: "60px",
                          display: "flex",
                          alignItems: "center",
                        }}
                        key={key}
                      >
                        <Checkbox
                          style={{
                            paddingTop: "25px",
                          }}
                          checked={state?.values?.troTho == 10}
                          onChange={(e) =>
                            onChange("troTho")(e?.target?.checked ? 10 : null)
                          }
                        >
                          {t("khamBenh.chanDoan.bopBong")}
                        </Checkbox>
                      </Col>
                    );
                  }

                  case "glasgow": {
                    return (
                      <Col key={key} span={12} className="item">
                        <label>{ten ? ten : t(i18Name)}</label>
                        <Input
                          value={state?.values?.[key]}
                          style={{ width: "100%" }}
                          disabled
                        />
                      </Col>
                    );
                  }
                  default:
                    return (
                      <Col key={key} span={12} className="item">
                        <label>{ten ? ten : t(i18Name)}</label>
                        <InputNumberField
                          value={state?.values?.[key]}
                          onChange={onChange(key)}
                          autoFocus={state[autoFocus]}
                          timeDelay={0}
                          style={{ width: "100%" }}
                          onFocus={onFocus}
                          min={0}
                          disabled={!!item.readOnly}
                          decimalScale={item.decimalScale || 0}
                          onKeyDown={blockInvalidChar}
                          onBlur={onBlur(item)}
                          {...(listRef.hasOwnProperty(key) && {
                            getInputRef: listRef[key],
                          })}
                          {...mapAdditionalProps(item)}
                          {...rest}
                        />
                      </Col>
                    );
                }
              })}
            <Col span={12}></Col>
          </Row>
          <div className="action-bottom">
            <Button onClick={onCancel}>{t("common.huy")}</Button>
            <Button type="primary" onClick={onOk}>
              {t("common.dongY")}
            </Button>
          </div>
        </div>
      </div>
      <ModalCustomizeColumn
        columnsShow={columnsShow || []}
        columnsDefault={listLayoutsDefaults}
        allValue={true}
        ref={refModalCustomizeField}
        onSaveModalCustomizeColumn={onSaveModalCustomizeColumn}
      />
    </Main>
  );
};

export default forwardRef(ModalSynthetic);
