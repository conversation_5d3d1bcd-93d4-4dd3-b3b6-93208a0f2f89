import React, { useRef, useEffect, useMemo } from "react";
import { cloneDeep } from "lodash";
import { connect, useSelector } from "react-redux";
import {
  ModalChangeDate,
  ModalInput,
  InputValue,
  SurgeryDetail,
  ColumnState,
} from "../";
import { SIZE } from "utils/vital-signs/constants";
import {
  calculateHeightByKey,
  pointToValue,
} from "utils/vital-signs/canvas-utils";
import { Main } from "./styled";
import ModalSynthetic from "../ModalSynthetic";
import { useStore } from "hooks";
import { GIOI_TINH } from "constants/index";
import { isNumber, roundToDigits } from "utils/index";

export const refClickCanvas = React.createRef({});

// idxColEdit: index cột đang được sửa
// isLastColumn: có phải cột cuối cùng ko

const CanvasTouchable = (props, refs) => {
  const values = props.values;
  const refInputValue = useRef(null);
  const refChangeDate = useRef(null);
  const refModalInput = useRef(null);
  const refModalSynthetic = useRef(null);

  const isNoiTru = window.location.pathname.includes("quan-ly-noi-tru");
  let heightTenDvkham = calculateHeightByKey(values, isNoiTru, "tenDvKham");
  let heightTenKhoaChiDinh = calculateHeightByKey(
    values,
    isNoiTru,
    "tenKhoaChiDinh"
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});

  const gioiTinh = thongTinCoBan?.gioiTinh || thongTinBenhNhan?.gioiTinh;

  const { typeValue, isNotFirstClick } = useSelector(
    (state) => state.vitalSigns
  );

  const { listAllChiSoSong } = useSelector((state) => state.chiSoSong);
  const refreshData = useSelector((state) => state.vitalSigns.refreshData);

  const maCssVongCanhTay = props.dataMA_CSS_VONG_CANH_TAY;
  const dataHIEN_THI_TRUONG_SINH_HIEU = props.dataHIEN_THI_TRUONG_SINH_HIEU;

  const bottomHeight = useMemo(() => {
    return (
      (isNoiTru ? SIZE.bottomHeightNoiTru : SIZE.bottomHeightNgoaiTru) +
      (maCssVongCanhTay?.eval() ? 45 : 0) +
      (dataHIEN_THI_TRUONG_SINH_HIEU?.eval() ? 45 * 3 : 0) +
      props.listChiSoSongMacDinh.length * 45
    );
  }, [
    maCssVongCanhTay,
    isNoiTru,
    props.listChiSoSongMacDinh,
    dataHIEN_THI_TRUONG_SINH_HIEU,
  ]);

  const onChangeDate = (date, index) => {
    if (index == -1) index = 0;
    let values = cloneDeep(props.values);
    values[index].thoiGianThucHien = date;

    props.updateData({
      values,
    });
    props.onValueChange(values);
  };
  const onClickHeader = (index, isLastColumn) => {
    index = parseInt(index);
    if (index == props.currentCol || isLastColumn)
      refChangeDate.current.show(
        values[index]?.thoiGianThucHien?.toDateObject
          ? values[index].thoiGianThucHien.toDateObject()
          : values[index].thoiGianThucHien,
        index,
        (date) => {
          onChangeDate(date, index);
        }
      );
  };
  const getColFromEvent = (e) => {
    return Math.floor(
      (e.nativeEvent.layerX - SIZE.leftColumnWidth) / SIZE.columnWidth
    );
  };
  const checkIsClickInBodyCanvas = (e) => {
    const canvasHeight = props.height;

    return (
      e.nativeEvent.layerY > SIZE.headerHeight &&
      e.nativeEvent.layerY <=
        canvasHeight -
          (bottomHeight + heightTenDvkham + heightTenKhoaChiDinh) -
          props.moreValueIds.length * 45
    );
  };

  const checkIsClickInHeaderCanvas = (e) => {
    return e.nativeEvent.layerY < SIZE.headerHeight;
  };

  useEffect(() => {
    if (isNotFirstClick) {
      showModal({
        type:
          typeValue == 1 ? "nhipMach" : typeValue == 2 ? "nhietDo" : "huyetap",
        values: props.values,
        index: props.values.length - 1,
        isLastColumn: true,
      });
    }
  }, [typeValue]);

  useEffect(() => {
    if (props.show && props.values && props.values.length >= 1) {
      const values = props.values,
        length = values.length,
        index = length - 1;
      showModal({
        type: "nhipMach",
        values,
        index,
        isLastColumn: true,
        x: 120 + length * 40,
      });
      props.updateData({
        values,
        modeAdd: true,
        preValues: values,
        newIndex: index,
      });
    } else {
      refChangeDate.current.hide();
    }
  }, [props.show]);

  useEffect(() => {
    if (refreshData) {
      refModalSynthetic.current.hide();
    }
  }, [refreshData]);

  const editBody = (index, e, values, isLastColumn, modeAdd, modeEdit) => {
    if (
      (modeAdd && index === values.length - 1) ||
      (modeEdit && index < values.length - 1)
    ) {
      if (props.typeValue === 1) {
        const value = parseInt(
          pointToValue({
            point: e.nativeEvent.layerY,
            type: props.typeValue,
            rowHeight: SIZE.rowHeight,
            totalRow: 75,
            headerHeight: SIZE.headerHeight,
            bottomHeight,
          })
        );
        if (!values[index]) return;
        if (isLastColumn && !values[index]?.nhietDo && !values[index]?.mach) {
          const dataPrev = {
            ...values[index - 1],
            thoiGianThucHien: values[index].thoiGianThucHien,
          };
          values[index] = dataPrev;
        }

        if (
          values[index - 1]?.auToAddMach ||
          (!values[index - 1]?.mach && values[index - 2]?.mach)
        ) {
          values[index - 1].auToAddMach = true;
          values[index - 1].mach = (values[index - 2]?.mach + value) / 2;
          if (
            values[index + 1]?.auToAddMach ||
            (!values[index + 1]?.mach && values[index + 2]?.mach)
          ) {
            values[index + 1].auToAddMach = true;
            values[index + 1].mach = (values[index + 2]?.mach + value) / 2;
          }
        }
        values[index].mach = value;
        values[index].auToAddMach = false;
        showModal({
          x: e.nativeEvent.layerX,
          y: e.nativeEvent.layerY,
          type: "nhipMach",
          values,
          index,
          isLastColumn,
        });
      } else {
        const value = Number(
          pointToValue({
            point: e.nativeEvent.layerY,
            type: props.typeValue,
            rowHeight: SIZE.rowHeight,
            totalRow: 75,
            headerHeight: SIZE.headerHeight,
            bottomHeight,
          }).toFixed(1)
        );
        if (!values[index]) return;
        if (isLastColumn && !values[index]?.nhietDo && !values[index]?.mach) {
          const dataPrev = {
            ...values[index - 1],
            thoiGianThucHien: values[index].thoiGianThucHien,
          };
          values[index] = dataPrev;
        }
        if (
          values[index - 1]?.auToAddNhietDo ||
          (!values[index - 1]?.nhietDo && values[index - 2]?.nhietDo)
        ) {
          values[index - 1].auToAddNhietDo = true;
          values[index - 1].nhietDo = (values[index - 2]?.nhietDo + value) / 2;
          if (
            values[index + 1]?.auToAddNhietDo ||
            (!values[index + 1]?.nhietDo && values[index + 2]?.nhietDo)
          ) {
            values[index + 1].auToAddNhietDo = true;
            values[index + 1].nhietDo =
              (values[index + 2]?.nhietDo + value) / 2;
          }
        }
        values[index].nhietDo = value;
        values[index].auToAddNhietDo = false;
        showModal({
          x: e.nativeEvent.layerX,
          y: e.nativeEvent.layerY,
          type: "nhietDo",
          values,
          index,
          isLastColumn,
        });
      }
    }
  };

  const calculateY = (
    data,
    maCssVongCanhTay,
    dataHIEN_THI_TRUONG_SINH_HIEU,
    heightTenDvkham,
    isNoiTru,
    listCss
  ) => {
    const STEP = 45;
    const BASE_INDEX = isNoiTru ? 2 + listCss.length : 10;
    const BASE_INDEX_WITH_VCT = isNoiTru ? 3 + listCss.length : 11;
    const BASE_INDEX_WITH_TRUONG_SINH_HIEU = isNoiTru ? 5 + listCss.length : 13; // 🆕 thêm base riêng
    const BASE_INDEX_WITH_VCT_AND_TRUONG_SINH_HIEU = isNoiTru
      ? 8 + listCss.length
      : 14;

    let result = 0;
    const hasVongCanhTay = maCssVongCanhTay?.eval();
    const hasTruongSinhHieu = dataHIEN_THI_TRUONG_SINH_HIEU?.eval();

    const getBaseIndex = () => {
      if (hasVongCanhTay && hasTruongSinhHieu)
        return BASE_INDEX_WITH_VCT_AND_TRUONG_SINH_HIEU;
      if (hasVongCanhTay) return BASE_INDEX_WITH_VCT;
      if (hasTruongSinhHieu) return BASE_INDEX_WITH_TRUONG_SINH_HIEU;
      return BASE_INDEX;
    };

    const baseIndex = getBaseIndex();
    const baseThreshold = baseIndex * STEP;

    if (data <= baseThreshold) {
      result = Math.floor(data / STEP);
    } else if (!isNoiTru && data <= baseThreshold + heightTenDvkham) {
      result = baseIndex;
    } else if (data <= baseThreshold + (isNoiTru ? 0 : heightTenDvkham)) {
      let plus = isNoiTru ? 0 : 1;
      result = baseIndex + plus;
    } else {
      const remainingData =
        data - (baseThreshold + (isNoiTru ? 0 : heightTenDvkham));

      let plus = isNoiTru ? 0 : 1;
      result = Math.floor(remainingData / STEP) + (baseIndex + plus);
    }

    return result;
  };

  const onClickCanvas = (e) => {
    refClickCanvas.current = true;
    const canvasHeight = props.height;
    const values = props.values;
    const newValues = cloneDeep(values);
    if (
      !(
        e.nativeEvent.layerX <= SIZE.leftColumnWidth &&
        e.nativeEvent.layerY >= canvasHeight - 100 &&
        e.nativeEvent.layerY <= canvasHeight - 50
      )
    ) {
      const newIndex = getColFromEvent(e); //lấy vị trí cột từ event

      if (newIndex < values.length) {
        const isLastColumn = newIndex == values.length - 1; //check xem cột đó có phải là cột cuối cùng không (cột cuối cùng là cột tự trắng, tự thêm khi load)

        const isBody = checkIsClickInBodyCanvas(e);
        const isHeader = checkIsClickInHeaderCanvas(e);
        const preIndexCol = props.currentCol;
        const obj = {
          currentCol: newIndex,
          isLastColumn: !!isLastColumn,
        };
        if (isLastColumn) {
          obj.modeAdd = true;
          obj.modeEdit = false;
        }
        if (!isLastColumn) {
          obj.modeEdit = true;
          obj.modeAdd = false;

          if (!props.idxColEdit) obj.idxColEdit = newIndex;
        }
        props.updateData(obj);

        if (isHeader) {
          // header
          onClickHeader(newIndex, isLastColumn);
        } else if (isBody) {
          // body
          editBody(
            newIndex,
            e,
            newValues,
            isLastColumn,
            obj.modeAdd,
            obj.modeEdit
          );
        } else {
          // footer
          let x =
            e.nativeEvent.layerY -
            (canvasHeight -
              (bottomHeight + heightTenDvkham + heightTenKhoaChiDinh) -
              props.moreValueIds.length * 45);

          if (
            x >
            bottomHeight +
              heightTenDvkham +
              props.moreValueIds.length * 45 -
              100
          )
            return;

          const listCss = props.listChiSoSongMacDinh;

          let y = calculateY(
            x,
            maCssVongCanhTay,
            dataHIEN_THI_TRUONG_SINH_HIEU,
            heightTenDvkham,
            isNoiTru,
            listCss
          );
          const listDataFooterNoiTru = [
            "huyetap",
            "nhipTho",
            "canNang",
            ...(maCssVongCanhTay?.eval() ? ["bmiVct"] : []),
            ...listCss.map((i) => i.id),
            ...(dataHIEN_THI_TRUONG_SINH_HIEU?.eval()
              ? ["acvpu", "glasgow", "canhBaoSom"]
              : []),
          ];
          const listDataFooterNgoaiTru = [
            "mach",
            "nhietDo",
            "huyetap",
            "nhipTho",
            "canNang",
            "chieuCao",
            "nhomMau",
            "spo2",
            "bmi",
            "tenPhanLoaiBmi",
            "moTaPhanLoaiBmi",
            "tenDvKham",
            ...(maCssVongCanhTay?.eval() ? ["bmiVct"] : []),
          ];
          const listDataFooter = isNoiTru
            ? listDataFooterNoiTru
            : listDataFooterNgoaiTru;
          // y start with 0
          const hasVCT = maCssVongCanhTay?.eval();
          const hasTSH = dataHIEN_THI_TRUONG_SINH_HIEU?.eval();

          if (
            (!isNoiTru &&
              y > 7 &&
              y <
                (hasVCT && hasTSH
                  ? 14 // cả 2 thì skip đến 14
                  : hasVCT
                  ? 13 // chỉ VCT
                  : hasTSH
                  ? 13 // chỉ TSH (có thể khác, bạn chỉnh tùy UI)
                  : 12)) ||
            (isNoiTru && hasVCT && y === 3)
          ) {
            return;
          } else if (y > listDataFooter.length - 1) {
            let i = y - listDataFooter.length;
            let _index = (newValues[newIndex].dsChiSoSongKhac || []).findIndex(
              (t) => t.chiSoSongId === props.moreValueIds[i]
            );
            let dsChiSoSongKhac = {
              chiSoSongId: props.moreValueIds[i],
              giaTri: "",
              tenChiSo: "",
            };

            if (_index !== -1)
              dsChiSoSongKhac = (newValues[newIndex].dsChiSoSongKhac || [])[
                _index
              ];
            if (refModalInput.current) {
              const chiSoSongId = listAllChiSoSong.find(
                (item) => item.id == dsChiSoSongKhac?.chiSoSongId
              );
              let maChiSoSong;
              if (maCssVongCanhTay?.eval()) {
                maChiSoSong = chiSoSongId?.ma === maCssVongCanhTay;
              }
              refModalInput.current.show(
                {
                  value: dsChiSoSongKhac.giaTri,
                  type: 3,
                  keyboardType: "numeric",
                  chiSoSongId,
                },
                (value) => {
                  dsChiSoSongKhac.giaTri = value;
                  if (!newValues[newIndex].dsChiSoSongKhac) {
                    newValues[newIndex].dsChiSoSongKhac = [];
                  }

                  if (maChiSoSong && isNumber(value)) {
                    const tinhBmiVct = (value, gioiTinh) => {
                      let result = "";
                      if (gioiTinh === GIOI_TINH.NAM) {
                        result = 1.01 * parseFloat(value) - 4.7;
                      } else if (gioiTinh === GIOI_TINH.NU) {
                        result = 1.1 * parseFloat(value) - 6.7;
                      }
                      return roundToDigits(result, 3);
                    };
                    newValues[newIndex].bmiVct = tinhBmiVct(value, gioiTinh);
                  }

                  if (!value === "") {
                    newValues[newIndex].dsChiSoSongKhac = newValues[
                      newIndex
                    ].dsChiSoSongKhac.filter(
                      (t) => t.dmChiSoId !== dsChiSoSongKhac.dmChiSoId
                    );
                  } else {
                    if (_index === -1)
                      newValues[newIndex].dsChiSoSongKhac.push(dsChiSoSongKhac);
                    else
                      newValues[newIndex].dsChiSoSongKhac[_index] =
                        dsChiSoSongKhac;
                    if (isLastColumn) {
                      newValues[newIndex].isNewCol = true;
                    } else {
                      newValues[newIndex].isEditCol = true;
                    }
                  }
                  props.updateData({
                    values: newValues,
                    modeAdd: isLastColumn,
                    preValues: newValues,
                  });
                  props.onValueChange(newValues, { isCanvasFooter: true });
                },
                {}
              );
            }
          } else {
            const currentValueOfFooter = listDataFooter[y];
            if (!isLastColumn || (isLastColumn && refInputValue.current)) {
              showModal({
                type: currentValueOfFooter,
                values: newValues,
                index: newIndex,
                isLastColumn,
              });
            }
          }
        }
        props.updateData({
          values: props.preValues,
        });
        props.onValueChange(newValues, {
          isCanvasBody: isBody,
          isCanvasFooter: !isBody,
          isLastColumn: isLastColumn,
          currentCol: newIndex,
        });
      }
    }
  };

  const showModal = ({
    x,
    y,
    type = "",
    values = {},
    index = 0,
    valuesCurrent = "",
    isLastColumn = true,
  }) => {
    const vitalSignsBodyHeight =
      props?.refVitalSignsBody?.current?.clientHeight || 0;
    const vitalSignsBodyScroll =
      props?.refVitalSignsBody?.current?.scrollTop || 0;

    let customY = y;

    if (customY + 400 > vitalSignsBodyHeight + vitalSignsBodyScroll) {
      customY = vitalSignsBodyHeight + vitalSignsBodyScroll - 400;
    }
    refModalSynthetic.current.show(
      x + 20,
      customY,
      {
        type,
        values,
        index,
        valuesCurrent,
        isLastColumn,
      },
      props.onValueChange
    );
  };

  const onUpdateSurgery = (id) => {
    props.onAddSurgery(id);
  };

  return (
    <>
      <ColumnState />
      <InputValue ref={refInputValue} />
      <ModalChangeDate
        ref={refChangeDate}
        columnWidth={SIZE.columnWidth}
        marginLeft={SIZE.leftColumnWidth}
      />
      <Main width={props.width} height={props.height} onClick={onClickCanvas} />
      <ModalInput ref={refModalInput} />
      <SurgeryDetail
        onValueChange={props.onValueChange}
        onUpdateSurgery={onUpdateSurgery}
      />
      <ModalSynthetic
        ref={refModalSynthetic}
        onCancelUpdate={props.onCancelUpdate}
        tiepDon={props.tiepDon}
        khoaLamViec={props.khoaLamViec}
        listChiSoSongMacDinh={props.listChiSoSongMacDinh}
      />
    </>
  );
};
CanvasTouchable.defaultProps = {
  onValueChange: () => {},
};

export default connect(
  (state) => {
    return {
      values: state.vitalSigns.values || [],
      modeAdd: state.vitalSigns.modeAdd,
      modeEdit: state.vitalSigns.modeEdit,
      idxColEdit: state.vitalSigns.idxColEdit,
      moreValueIds: state.vitalSigns.moreValueIds || [],
      typeValue: state.vitalSigns.typeValue,
      currentCol: state.vitalSigns.currentCol,
      vitalSignsCategories: state.vitalSigns.vitalSignsCategories || [],
      preValues: state.vitalSigns.preValues || [],
    };
  },
  ({ vitalSigns: { updateData, onUpdateSurgery } }) => ({
    updateData,
    onUpdateSurgery,
  })
)(CanvasTouchable);
