import React, { useState, useEffect, useRef, useMemo } from "react";
import { cloneDeep, isEmpty } from "lodash";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { message } from "antd";
import moment, { isMoment } from "moment";
import { useConfirm, usePrevious, useEnum, useStore, useLoading } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import {
  SelectVitalSignCategory,
  ScrollView,
  ToolBar,
  BloodPressureCanvas,
  BackgroundCanvas,
  LeftColumnCanvas,
  ModalSurgeryInformation,
  CanvasTouchable,
} from "../";
import { SIZE } from "utils/vital-signs/constants";
import {
  drawValueFooter,
  drawValueBody,
  calculateHeightByKey,
} from "utils/vital-signs/canvas-utils";
import { Main } from "./styled";
import ColumnCanvas from "../ColumnCanvas";
import { ENUM, ROLES } from "constants/index";
import { isArray } from "utils/index";
import { TableWrapper, HeaderSearch } from "components";
import { OBJ_LIST_HARDCODE_CSS } from "pages/thietLap/thietLapGiaTriCSS/configs";
import groupBy from "lodash/groupBy";
import useListChiSoSong from "components/VitalSigns/hooks/useListChiSoSong";

const initState = (
  noiTru,
  heightTenDvkham,
  heightTenKhoaChiDinh,
  bottomHeight
) => {
  return {
    canvasHeight:
      SIZE.rowHeight * 75 +
      SIZE.headerHeight +
      bottomHeight +
      heightTenDvkham +
      heightTenKhoaChiDinh,
    canvasWidth: SIZE.leftColumnWidth,
    currentValue: {},
  };
};

function VitalSigns(props, refs) {
  const {
    isEdit,
    isModal,
    isLog,
    nbDotDieuTriId,
    show,
    dataMA_CSS_VONG_CANH_TAY,
    dataHIEN_THI_TRUONG_SINH_HIEU,
    khoaLamViec,
    rightToolbar,
  } = props;
  const isNoiTru = window.location.pathname.includes("quan-ly-noi-tru");

  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();

  const refModalSurgeryInformation = useRef(null);
  const refCanvas = useRef(null);
  const refCanvasFooter = useRef(null);
  const refDraw = useRef(null);
  const refVitalSignsBody = useRef(null);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listAcvpu] = useEnum(ENUM.ACVPU);

  const { currentCol, preValues, resState } = useSelector(
    (state) => state.vitalSigns
  );
  const { listChiSoSongMacDinh, listAllChiSoSong, listChiSoSongKhac } =
    useListChiSoSong({
      isNoiTru,
    });

  const moreValueIds = useStore("vitalSigns.moreValueIds", []);
  const values = useStore("vitalSigns.values", []);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const listThietLapGiaTriCSS = useStore(
    "thietLapGiaTriCSS.listThietLapGiaTriCSS",
    []
  );

  const {
    vitalSigns: { updateData, onUpdate, onCancel, onDelete },
    thietLapGiaTriCSS: { onSearch: onSearchThietLapGiaTriCSS },
  } = useDispatch();

  useEffect(() => {
    onSearchThietLapGiaTriCSS();
  }, []);

  const prevValues = usePrevious(values, []);

  let heightTenDvkham = calculateHeightByKey(values, isNoiTru, "tenDvKham");
  let heightTenKhoaChiDinh = calculateHeightByKey(
    values,
    isNoiTru,
    "tenKhoaChiDinh"
  );

  const bottomHeight =
    (isNoiTru ? SIZE.bottomHeightNoiTru : SIZE.bottomHeightNgoaiTru) +
    (dataMA_CSS_VONG_CANH_TAY?.eval() ? 45 : 0) +
    (dataHIEN_THI_TRUONG_SINH_HIEU?.eval() ? 45 * 3 : 0) +
    listChiSoSongMacDinh.length * 45;

  const canvasHeight1 =
    SIZE.rowHeight * 75 +
    SIZE.headerHeight +
    bottomHeight +
    moreValueIds.length * 45 +
    heightTenDvkham +
    heightTenKhoaChiDinh;

  const [state, _setState] = useState({
    ...initState(isNoiTru, heightTenDvkham, heightTenKhoaChiDinh, bottomHeight),
    canvasHeight: canvasHeight1,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const maxWidth = useMemo(() => {
    return isModal
      ? window.screen.width > 1367
        ? 1800
        : 1250
      : window.screen.width < 1367
      ? 1150
      : 1600;
  }, [isModal]);

  useEffect(() => {
    const _values = values || [];
    let canvasWidth = SIZE.leftColumnWidth + _values.length * SIZE.columnWidth;
    setState({
      canvasWidth: Math.max(canvasWidth, maxWidth),
    });
  }, []);

  useEffect(() => {
    updateData({
      preValues: cloneDeep(values),
      currentCol: values.length - 1,
    });
  }, []);

  useEffect(() => {
    if (refCanvas.current) {
      refCanvas.current.width = state.canvasWidth;
      refCanvas.current.height =
        state.canvasHeight -
        (bottomHeight +
          heightTenDvkham +
          heightTenKhoaChiDinh +
          moreValueIds.length * 45);
    }
  }, [refCanvas.current]);

  useEffect(() => {
    if (refCanvasFooter.current) {
      refCanvasFooter.current.width = state.canvasWidth;
      refCanvasFooter.current.height =
        bottomHeight +
        heightTenDvkham +
        heightTenKhoaChiDinh +
        moreValueIds.length * 45;
    }
  }, [refCanvasFooter.current]);

  useEffect(() => {
    if (
      values &&
      values.length &&
      refCanvasFooter.current &&
      refCanvas.current &&
      !refDraw.current
    ) {
      refDraw.current = true;
      draw(values, true);
    }
  }, [values, moreValueIds, refCanvasFooter.current, refCanvas.current]);

  useEffect(() => {
    if (refCanvas.current) {
      refCanvas.current.width = state.canvasWidth;
      refCanvas.current.height =
        state.canvasHeight -
        (bottomHeight +
          heightTenDvkham +
          heightTenKhoaChiDinh +
          moreValueIds.length * 45);
    }
    if (refCanvasFooter.current) {
      refCanvasFooter.current.width = state.canvasWidth;
      refCanvasFooter.current.height =
        bottomHeight +
        heightTenDvkham +
        heightTenKhoaChiDinh +
        moreValueIds.length * 45;
    }
    draw(values, true);
  }, [state.canvasWidth, state.canvasHeight]);

  useEffect(() => {
    if (prevValues.length !== values.length) {
      let canvasWidth =
        SIZE.leftColumnWidth + values.length * SIZE.columnWidth + 225;
      setState({
        canvasWidth: Math.max(canvasWidth, maxWidth),
      });
    }
  }, [values]);

  useEffect(() => {
    setState({
      canvasHeight:
        SIZE.rowHeight * 75 +
        SIZE.headerHeight +
        bottomHeight +
        heightTenDvkham +
        heightTenKhoaChiDinh +
        moreValueIds.length * 45,
    });
  }, [moreValueIds, heightTenDvkham, heightTenKhoaChiDinh]);

  useEffect(() => {
    return () => {
      updateData({
        isNotFirstClick: false,
      });
    };
  }, []);

  const draw = (values, redraw) => {
    if (refCanvas.current && !isEmpty(values)) {
      drawValues(
        {
          ctxBody: refCanvas.current.getContext("2d"),
          ctxFooter: refCanvasFooter.current?.getContext("2d"),
          values,
        },
        redraw
      );
    }
  };

  const redraw = ({
    values,
    isCanvasBody,
    isCanvasFooter,
    isLastColumn,
    isSurgery,
    clearFromIndex,
    currentCol: currentColProps,
  }) => {
    let ctxBody, ctxFooter;
    let newValue = cloneDeep(values);

    const _currentCol = Number.isInteger(currentColProps)
      ? currentColProps
      : currentCol;

    if (isCanvasBody) {
      ctxBody = refCanvas.current.getContext("2d");
      if (isSurgery) {
        ctxBody.clearRect(
          SIZE.columnWidth * _currentCol + SIZE.leftColumnWidth,
          0,
          (SIZE.columnWidth + 1) * _currentCol + SIZE.leftColumnWidth,
          refCanvas.current.height
        );
      } else {
        ctxBody.clearRect(
          0,
          0,
          refCanvas.current.width,
          refCanvas.current.height
        );
      }
    }
    if (isCanvasFooter) {
      ctxFooter = refCanvasFooter.current?.getContext("2d");
      ctxFooter?.clearRect(
        0,
        0,
        refCanvasFooter.current.width,
        refCanvasFooter.current.height
      );
    }
    drawValues({
      ctxBody,
      ctxFooter,
      values: newValue || values,
    });
  };

  const drawValues = ({ ctxBody, ctxFooter, values }) => {
    let heightTenDvkham = calculateHeightByKey(values, isNoiTru, "tenDvKham");
    let heightTenKhoaChiDinh = calculateHeightByKey(
      values,
      isNoiTru,
      "tenKhoaChiDinh"
    );
    let preMach, preNhiet;
    values.forEach((item, index) => {
      try {
        if (ctxBody) {
          drawValueBody({
            ctx: ctxBody,
            item,
            values,
            index,
            columnWidth: SIZE.columnWidth,
            leftColumnWidth: SIZE.leftColumnWidth,
            headerHeight: SIZE.headerHeight,
            bottomHeight,
            totalRow: 75,
            rowHeight: SIZE.rowHeight,
            startValueMach: 30,
            startValueNhiet: 34.5,
            SIZE,
            preData: { mach: preMach, nhietDo: preNhiet },
          });
        }
        // start footer

        if (ctxFooter) {
          drawValueFooter({
            marginTop: 0,
            ctxFooter,
            index,
            item,
            values,
            moreValueIds,
            SIZE,
            isPrint: false,
            listNhomMau,
            listAcvpu,
            isNoiTru,
            heightTenDvkham,
            heightTenKhoaChiDinh,
            thongTinBenhNhan: isNoiTru ? thongTinCoBan : thongTinBenhNhan,
            dataMA_CSS_VONG_CANH_TAY,
            dataHIEN_THI_TRUONG_SINH_HIEU,
            listAllChiSoSong: listAllChiSoSong,
            listChiSoSongMacDinh,
            listChiSoSongKhac,
            listThietLapGiaTriCSS,
          });
        }
      } catch (error) {
        console.error("error", error);
      }
      if (item.mach) {
        preMach = index;
      }
      if (item.nhietDo || item.nhiet) {
        preNhiet = index;
      }
    });
  };

  const contentCanhBao = (data) => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
        }}
      >
        <div className="content content-2" style={{ color: "red" }}>
          {t("sinhHieu.khongDuocPhepSuaCacSinhHieuSau")}:
        </div>
        <TableWrapper
          columns={[
            {
              title: <HeaderSearch title={t("common.stt")} />,
              width: 50,
              dataIndex: "index",
              key: "index",
              align: "center",
              render: (_, __, index) => index + 1,
            },
            {
              title: <HeaderSearch title={t("danhMuc.tenKhoa")} />,
              width: 200,
              dataIndex: "tenKhoaChiDinh",
              key: "tenKhoaChiDinh",
            },
            {
              title: <HeaderSearch title={t("common.thoiGian")} />,
              width: 250,
              dataIndex: "thoiGian",
              key: "thoiGian",
            },
          ]}
          dataSource={data}
        />
      </div>
    );
  };

  const onUpdateVitalSigns = (editVitalSigns = []) => {
    const arr = [...editVitalSigns];

    const days = [];
    [...editVitalSigns].forEach((item) => {
      const day = new Date(item.thoiGianThucHien);

      let thoiGianThucHien =
        day.getDate() +
        "/" +
        (day.getMonth() + 1) +
        " - " +
        day.getHours() +
        ":" +
        day.getMinutes();

      days.push(thoiGianThucHien);
    });

    const listDataInValid = arr.reduce((acc, cur) => {
      if (!checkRole([ROLES["SINH_HIEU"].CHINH_SUA_SINH_HIEU_KHAC_KHOA])) {
        if (cur.khoaChiDinhId && cur.khoaChiDinhId !== khoaLamViec?.id) {
          acc.push({
            ...cur,
            thoiGian: moment(cur.thoiGianThucHien).format(
              "DD/MM/YYYY HH:mm:ss"
            ),
          });
        }
      }
      return acc;
    }, []);

    if (isArray(listDataInValid, true)) {
      showConfirm({
        title: t("common.thongBao"),
        isContentElement: true,
        content: contentCanhBao(listDataInValid),
        showBtnCancel: false,
        showBtnOk: false,
        typeModal: "error",
        width: 768,
        closable: true,
      });
      return;
    }
    const validCSS = isValidCss(arr, true);
    const hasInvalidTime = arr.some((item) => {
      let thoiGianThucHien = item.thoiGianThucHien;
      if (!thoiGianThucHien || !thongTinBenhNhan.thoiGianVaoVien) return false;
      thoiGianThucHien = isMoment(thoiGianThucHien)
        ? thoiGianThucHien
        : moment(thoiGianThucHien);
      return !thoiGianThucHien.isAfter(thongTinBenhNhan.thoiGianVaoVien);
    });
    if (hasInvalidTime) {
      message.error(
        t("quanLyNoiTru.chiSoSong.vuiLongChonNgayLonHonNgayVaoVien")
      );
    } else {
      if (validCSS) {
        showConfirm(
          {
            title: t("common.xacNhan"),
            content: validCSS,
            cancelText: t("common.huy"),
            okText: t("common.xacNhanNhap"),
            showImg: true,
            showBtnOk: true,
            isContentElement: true,
          },
          () => {
            handleUpdateCss(arr);
          }
        );
      } else {
        showConfirm(
          {
            title: t("common.xacNhan"),
            content: t(
              "quanLyNoiTru.chiSoSong.banCoChacMuonSuaThongTinTaiNgayTitleKhong",
              {
                title: days.join(", "),
              }
            ),
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            showImg: true,
            showBtnOk: true,
          },
          () => {
            handleUpdateCss(arr);
          }
        );
      }
    }
  };

  const handleUpdateCss = async (arr) => {
    try {
      showLoading();
      await Promise.all(
        arr.map(
          (item) =>
            new Promise((resolve, reject) => {
              onUpdate({
                currentCol: item.index,
                khoaChiDinhId: khoaLamViec?.id,
              })
                .then((values) => {
                  resolve(values);
                  redraw({
                    values: values,
                    isCanvasBody: true,
                    isCanvasFooter: true,
                    isLastColumn: false,
                  });
                })
                .catch((err) => {
                  reject(err);
                });
            })
        )
      );
      hideLoading();

      updateData({
        isSaveSucces: true,
        modeEdit: false,
        modeAdd: false,
        idxColEdit: null,
      });
    } catch (error) {
      console.error(error);
      hideLoading();
    }
  };

  const isValidCss = (arr, isEdit) => {
    const errors = [];
    arr.forEach((cur) => {
      const { ngaySinh, gioiTinh } = isNoiTru
        ? thongTinCoBan || {}
        : thongTinBenhNhan || {};
      let listData = isArray(listThietLapGiaTriCSS, true)
        ? listThietLapGiaTriCSS.filter((i) => i.active)
        : null;
      Object.keys(cur).forEach((key) => {
        const value = cur[key];
        if (isArray(listData, true)) {
          let thietLap = listData.find((i) => {
            let tuoi = ngaySinh ? moment().diff(ngaySinh, "years") : -1;
            return (
              i.tenCss === key &&
              i.gioiTinh === gioiTinh &&
              tuoi <= i.denTuoi &&
              tuoi >= i.tuTuoi
            );
          });
          if (thietLap) {
            const {
              giaTriToiDa,
              giaTriToiThieu,
              giaTriVuotNguongToiDa,
              giaTriVuotNguongToiThieu,
            } = thietLap || {};
            if (giaTriVuotNguongToiDa && value > giaTriVuotNguongToiDa) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriVuotNguongToiDa,
                giaTri: value,
                thoiGianThucHien: cur.thoiGianThucHien
                  ? moment(cur.thoiGianThucHien).format("HH:mm DD/MM/YYYY")
                  : "",
              });
            } else if (
              giaTriVuotNguongToiThieu &&
              value < giaTriVuotNguongToiThieu
            ) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriVuotNguongToiThieu,
                giaTri: value,
                thoiGianThucHien: cur.thoiGianThucHien
                  ? moment(cur.thoiGianThucHien).format("HH:mm DD/MM/YYYY")
                  : "",
              });
            } else if (value > giaTriToiDa || value < giaTriToiThieu) {
              errors.push({
                tenCss: OBJ_LIST_HARDCODE_CSS[key],
                giaTriToiThieu,
                giaTriToiDa,
                thoiGianThucHien: cur.thoiGianThucHien
                  ? moment(cur.thoiGianThucHien).format("HH:mm DD/MM/YYYY")
                  : "",
                giaTri: value,
              });
            }
          }
        }
      });
    }, []);
    const errorsGroupBy = groupBy(errors, "thoiGianThucHien");
    if (Object.keys(errorsGroupBy)?.length) {
      return (
        <div style={{ textAlign: "left", marginLeft: 50 }}>
          {Object.keys(errorsGroupBy).map((err) => {
            return (
              <div key={err}>
                <div>
                  {isEdit
                    ? t(
                        "quanLyNoiTru.chiSoSong.banCoChacMuonSuaThongTinTaiNgayTitleKhong",
                        {
                          title: err,
                        }
                      )
                    : null}
                </div>
                <div>
                  {errorsGroupBy[err].map((css) => (
                    <div key={err + css.tenCss}>
                      <span
                        className={`${
                          css.giaTriVuotNguongToiThieu ||
                          css.giaTriVuotNguongToiDa
                            ? "text-danger"
                            : ""
                        }`}
                      >
                        <b>
                          {css.tenCss}: {css.giaTri || 0}&nbsp;
                        </b>
                      </span>
                      {css.giaTriVuotNguongToiThieu && (
                        <span>
                          {t(
                            "sinhHieu.nhapNgoaiGiaTriVuotNguongToiThieu"
                          ).toLowerCase()}
                          &nbsp;
                          <b>&lt; {css.giaTriVuotNguongToiThieu}</b>
                        </span>
                      )}
                      {css.giaTriVuotNguongToiDa && (
                        <span>
                          {t(
                            "sinhHieu.nhapNgoaiGiaTriVuotNguongToiDa"
                          ).toLowerCase()}
                          &nbsp;
                          <b>&gt; {css.giaTriVuotNguongToiDa}</b>
                        </span>
                      )}
                      {css.giaTriToiThieu && css.giaTriToiDa && (
                        <>
                          {t("sinhHieu.nhapNgoaiGiaTriThamChieu")}&nbsp;
                          <span>
                            <b>{css.giaTriToiThieu}</b>
                          </span>
                          &nbsp; - <b>{css.giaTriToiDa}</b>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
          <div
            className="text-warning text-center italic"
            style={{ marginTop: 16 }}
          >
            {t("common.vuiLongKiemTraLai")}!
          </div>
        </div>
      );
    } else {
      return null;
    }
  };

  const onDelelteVitalSigns = () => {
    try {
      const day = new Date(values[currentCol].thoiGianThucHien);
      let thoiGianThucHien =
        day.getDate() +
        "/" +
        (day.getMonth() + 1) +
        " - " +
        day.getHours() +
        ":" +
        day.getMinutes();

      let id = resState[currentCol]?.id; //lấy ra id của item hiện tại
      const valueDelete = values.find((i) => i.id === id);
      if (valueDelete) {
        if (!checkRole([ROLES["SINH_HIEU"].CHINH_SUA_SINH_HIEU_KHAC_KHOA])) {
          if (
            valueDelete.khoaChiDinhId &&
            valueDelete.khoaChiDinhId !== khoaLamViec?.id
          ) {
            message.error(
              t("sinhHieu.khongDuocPhepXoaSinhHieuKhacKhoa", {
                thoiGian: moment(valueDelete.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
                tenKhoa: valueDelete.tenKhoaChiDinh,
              })
            );
            return;
          }
        }
      }

      showConfirm(
        {
          title: t("common.xacNhan"),
          content: t(
            "quanLyNoiTru.chiSoSong.banCoChacMuonSuaThongTinTaiNgayTitleKhong",
            {
              title: thoiGianThucHien,
            }
          ),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          showImg: true,
          showBtnOk: true,
        },
        () => {
          showLoading();
          onDelete()
            .then((values) => {
              updateData({
                preValues: values,
                modeEdit: false,
                modeAdd: false,
              });
              redraw({
                values: values,
                isCanvasBody: true,
                isCanvasFooter: true,
                clearFromIndex: currentCol,
              });
            })
            .finally(hideLoading);
        }
      );
    } catch (error) {}
  };

  const onCancelUpdate = () => {
    onCancel();
    redraw({
      values: preValues,
      isCanvasBody: true,
      isCanvasFooter: true,
    });
  };

  const onValueChange = (values, options) => {
    redraw({ values, ...options });
  };

  const onAddSurgery = (id) => {
    if (refModalSurgeryInformation.current)
      refModalSurgeryInformation.current.show(id, (values) => {
        if (values) redraw({ values, isCanvasBody: true });
      });
  };
  const { canvasWidth, canvasHeight } = state;

  return (
    <Main
      height={canvasHeight}
      isModal={isModal}
      className="vital-signs-layout"
    >
      <ToolBar
        onValueChange={onValueChange}
        onAddSurgery={onAddSurgery}
        isEdit={isEdit}
        isLog={isLog}
        isModal={isModal}
        onCancelUpdate={onCancelUpdate}
        onUpdateVitalSigns={onUpdateVitalSigns}
        onDelelteVitalSigns={onDelelteVitalSigns}
        draw={draw}
        redraw={redraw}
        refCanvas={refCanvas.current}
        refCanvasFooter={refCanvasFooter.current}
        nbDotDieuTriId={nbDotDieuTriId}
        khoaLamViec={khoaLamViec}
        isValidCss={isValidCss}
        rightToolbar={rightToolbar}
        isNoiTru={isNoiTru}
      />
      <div ref={refVitalSignsBody} className={"vital-signs-body"}>
        <ScrollView className="scrollview-body" height={canvasHeight}>
          {/* canvas header, collumn */}
          <BackgroundCanvas
            canvasWidth={canvasWidth}
            canvasHeight={canvasHeight}
            dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
            dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
            listChiSoSongMacDinh={listChiSoSongMacDinh}
          />
          {isModal && (
            <ColumnCanvas
              canvasWidth={canvasWidth}
              canvasHeight={canvasHeight}
              isModal={isModal}
            />
          )}

          <BloodPressureCanvas
            canvasWidth={canvasWidth}
            canvasHeight={
              canvasHeight -
              bottomHeight -
              SIZE.headerHeight -
              moreValueIds.length * 45 -
              heightTenDvkham -
              heightTenKhoaChiDinh
            }
            ref={refCanvas.currentBackground}
          />
          <canvas ref={refCanvas} />
          <canvas ref={refCanvasFooter} />
          <ModalSurgeryInformation ref={refModalSurgeryInformation} />
          {isModal ? (
            <CanvasTouchable
              width={canvasWidth}
              height={canvasHeight}
              onValueChange={onValueChange}
              onAddSurgery={onAddSurgery}
              onCancelUpdate={onCancelUpdate}
              refVitalSignsBody={refVitalSignsBody}
              show={show}
              dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
              dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
              khoaLamViec={khoaLamViec}
              listChiSoSongMacDinh={listChiSoSongMacDinh}
            />
          ) : null}
        </ScrollView>
        <LeftColumnCanvas
          canvasWidth={SIZE.leftColumnWidth}
          canvasHeight={canvasHeight}
          dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
          dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
          listChiSoSongMacDinh={listChiSoSongMacDinh}
        />
        <SelectVitalSignCategory
          top={canvasHeight - 90 - heightTenKhoaChiDinh}
          width={SIZE.leftColumnWidth}
          height={45}
          isModal={isModal}
        />
      </div>
    </Main>
  );
}

export default VitalSigns;
