import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Spin } from "antd";
import { useParams } from "react-router-dom";
import { useStore, useThietLap } from "hooks";
import { VitalSigns } from "./components";
import { THIET_LAP_CHUNG } from "constants/index";
import { Main } from "./styled";

const VitalSignsMain = (props) => {
  const {
    isEdit,
    isTiepDon,
    isReadonly,
    khoaLamViec,
    rightToolbar,
    nbDotDieuTriId,
  } = props;
  const [loading, setLoading] = useState(true);

  const {
    vitalSigns: { getDataVitalSigns, updateData },
    chiSoSong: { getListAllChiSoSong },
    pttt: { onSearch: onSearchPttt },
  } = useDispatch();

  const isLoading = useStore("vitalSigns.isLoading", false);
  const configData = useStore("vitalSigns.configData", {});
  const [dataMA_CSS_VONG_CANH_TAY, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_CSS_VONG_CANH_TAY
  );
  const [dataHIEN_THI_TRUONG_SINH_HIEU, loadFinishHienThiTruongSinhHieu] =
    useThietLap(THIET_LAP_CHUNG.HIEN_THI_TRUONG_SINH_HIEU);

  useEffect(() => {
    if (configData?.chiDinhTuLoaiDichVu) {
      // Tiếp đón chiDinhTuDichVuId null
      setLoading(true);
      getListAllChiSoSong({
        page: "",
        size: "",
        active: true,
        saveCache: false,
        isForceCall: true,
      }).then(() => {
        getDataVitalSigns({
          nbDotDieuTriId,
          khoaChiDinhId: khoaLamViec?.id,
        })
          .then(() => {
            setLoading(false);
          })
          .catch((err) => {
            console.error(err);
            setLoading(false);
          });
      });
    }
  }, [configData?.chiDinhTuLoaiDichVu, nbDotDieuTriId, khoaLamViec]);

  const onRefetchData = () => {
    getDataVitalSigns({
      nbDotDieuTriId: nbDotDieuTriId,
      khoaChiDinhId: khoaLamViec?.id,
    });
  };

  useEffect(() => {
    if (!isTiepDon && nbDotDieuTriId) {
      onSearchPttt({ dataSearch: { nbDotDieuTriId }, page: 0, size: 100 });
    }
    return () => {
      updateData({
        values: null,
        moreValueIds: null,
        actionLoading: false,
        isLoading: false,
      });
    };
  }, [nbDotDieuTriId]);

  const isFetching =
    isLoading || !loadFinish || loading || !loadFinishHienThiTruongSinhHieu;

  return (
    <Main>
      <div className="layout-body">
        <div className="layout-middle">
          {isFetching ? (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                width: "100%",
              }}
            >
              <Spin spinning={true} />
            </div>
          ) : (
            <VitalSigns
              isEdit={isEdit && !isReadonly}
              isTiepDon={isTiepDon}
              dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
              dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
              khoaLamViec={khoaLamViec}
              rightToolbar={React.cloneElement(rightToolbar, {
                onRefetchData,
              })}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          )}
        </div>
      </div>
    </Main>
  );
};

export default VitalSignsMain;
