import React, { useEffect, useRef, useState } from "react";
import { Form, Input, message } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { Button, ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useLoading, useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";
import { Main, InputSearch } from "./styled";

const Content = (props) => {
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();

  const {
    showModal,
    options: { submitCallback },
    onCloseModal,
  } = ModalTemplate.useModal();
  const {
    auth: { onChangePasswordKySo, onLogout },
  } = useDispatch();

  const { auth } = useSelector((state) => state.auth);
  const [state, _setState] = useState({});
  const [form] = Form.useForm();
  const _matKhauCu = Form.useWatch("matKhauCu", form);
  const refPassword = useRef(null);
  const refConfirmPassword = useRef(null);
  const refOldPassword = useRef(null);
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [dataCAP_DO_MAT_KHAU] = useThietLap(
    THIET_LAP_CHUNG.CAP_DO_MAT_KHAU,
    "1"
  );

  useEffect(() => {
    setState({
      confirmPassworderror: false,
      matKhauMoierror: false,
      successPassword: false,
      messageNewPasswordError: null,
      oldPassErrors: false,
      messageConfirmNewPasswordError: null,
    });
    form.resetFields();
  }, [showModal]);

  const onBlurConfirm = () => {
    setState({
      focusConfirm: false,
    });
  };
  const onBlurPass = () => {
    setState({
      focusPass: false,
    });
  };
  const onFocusPass = () => {
    setState({
      focusPass: true,
    });
  };
  const onFocusConfirm = () => {
    setState({
      focusConfirm: true,
    });
  };

  const checkStrengthValidate = {
    checkLengthBigger: (password, length) => password.length >= length,
    checkLengthSmaller: (password, length) => password.length < length,
    checkEmptyPassword: (password) => !password && password.length === 0,
    checkDuplicatePasswordOld: (password) => _matKhauCu === password,
    checkDuplicatePasswordNew: (password) => state.newPass === password,
    checkDuplicatePasswordConfirm: (password) => state.passConfirm === password,

    // >=8 ký tự, chứa ít nhất 1 ký tự số, 1 ký tự chữ, 1 ký tự in hoa, 1 ký tự đặc biệt
    strong: (password) =>
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*#?&'(_)+-./:;<=>?^-{|}~])[A-Za-z\d@$!%*#?&'(_)+-./:;<=>?^-{|}~]{8,}$/.test(
        password
      ),

    // >=8 ký tự, chứa ít nhất 1 ký tự số, 1 ký tự chữ
    medium: (password) =>
      /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/.test(password),

    // >=8 ký tự, chứa ít nhất 1 ký tự số, 1 ký tự chữ thường, 1 ký tự đặc biệt
    mediumNoneUppercaseLetter: (password) =>
      /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&'(_)+-./:;<=>?^-{|}~])[A-Za-z\d@$!%*#?&'(_)+-./:;<=>?^-{|}~]{8,}$/.test(
        password
      ),

    // >=8 ký tự, chứa ít nhất 1 ký tự số, 1 ký tự chữ in hoa, 1 ký tự chữ thường
    mediumNoneSpecialCharacter: (password) =>
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(password),
  };

  const handleChangeNewPass = (e) => {
    let value = e?.target?.value;

    // Kiểm tra trường hợp mật khẩu mới có bị trùng với nhập lại mật khẩu mới
    const handleShowMessage = () => {
      if (!checkStrengthValidate.checkDuplicatePasswordConfirm(value))
        return setState({
          confirmPassworderror: true,
          messageConfirmNewPasswordError: 2,
        });
    };

    if (checkStrengthValidate.checkDuplicatePasswordConfirm(value)) {
      setState({
        confirmPassworderror: false,
        messageConfirmNewPasswordError: null,
      });
    }

    // Kiểm tra độ mạnh của mật khẩu mới
    if (checkStrengthValidate.checkEmptyPassword(value)) {
      setState({
        matKhauMoierror: true,
        successPassword: false,
        confirmPassworderror: false,
        newPass: value,
        messageNewPasswordError: 1,
        messageConfirmNewPasswordError: null,
      });
    } else if (
      checkStrengthValidate.checkLengthSmaller(
        value,
        dataCAP_DO_MAT_KHAU == "1" ? 8 : 10
      )
    ) {
      setState({
        matKhauMoierror: true,
        successPassword: false,
        confirmPassworderror: false,
        newPass: value,
        messageNewPasswordError: 2,
        messageConfirmNewPasswordError: null,
      });
    } else if (checkStrengthValidate.checkDuplicatePasswordOld(value)) {
      setState({
        matKhauMoierror: true,
        successPassword: false,
        newPass: value,
        messageNewPasswordError: 3,
      });
    } else if (checkStrengthValidate.mediumNoneSpecialCharacter(value)) {
      state.passConfirm && state.passConfirm.length > 0 && handleShowMessage();
      setState({
        matKhauMoierror: dataCAP_DO_MAT_KHAU == "3" ? true : false,
        successPassword: dataCAP_DO_MAT_KHAU == "3" ? false : true,
        newPass: value,
        messageNewPasswordError: 5,
      });
    } else if (checkStrengthValidate.strong(value)) {
      state.passConfirm && state.passConfirm.length > 0 && handleShowMessage();
      setState({
        matKhauMoierror: false,
        successPassword: true,
        newPass: value,
        messageNewPasswordError: 4,
      });
    } else if (checkStrengthValidate.mediumNoneUppercaseLetter(value)) {
      state.passConfirm && state.passConfirm.length > 0 && handleShowMessage();
      setState({
        matKhauMoierror: dataCAP_DO_MAT_KHAU == "3" ? true : false,
        successPassword: dataCAP_DO_MAT_KHAU == "3" ? false : true,
        newPass: value,
        messageNewPasswordError: 5,
      });
    } else if (checkStrengthValidate.medium(value)) {
      state.passConfirm && state.passConfirm.length > 0 && handleShowMessage();
      setState({
        matKhauMoierror: dataCAP_DO_MAT_KHAU == "3" ? true : false,
        successPassword: dataCAP_DO_MAT_KHAU == "3" ? false : true,
        newPass: value,
        messageNewPasswordError: 5,
      });
    } else if (
      checkStrengthValidate.checkLengthBigger(
        value,
        dataCAP_DO_MAT_KHAU == "1" ? 8 : 10
      )
    ) {
      state.passConfirm && state.passConfirm.length > 0 && handleShowMessage();
      setState({
        matKhauMoierror: true,
        successPassword: false,
        newPass: value,
        messageNewPasswordError: 6,
      });
    }
  };
  const handleChangeOldPass = (e) => {
    let value = e?.target?.value;
    if (!value?.length) {
      setState({ oldPassErrors: true });
    } else if (checkStrengthValidate.checkDuplicatePasswordNew(value)) {
      setState({
        matKhauMoierror: true,
        successPassword: false,
        messageNewPasswordError: 3,
      });
    } else {
      setState({
        matKhauMoierror: false,
        successPassword: true,
        oldPassErrors: false,
        messageNewPasswordError: null,
      });
    }
  };
  const hanldeChangeConfirmPass = (e) => {
    let value = e.target.value;

    if (checkStrengthValidate.checkEmptyPassword(value)) {
      setState({
        confirmPassworderror: true,
        passConfirm: value,
        messageConfirmNewPasswordError: 1,
      });
    } else if (!checkStrengthValidate.checkDuplicatePasswordNew(value)) {
      setState({
        confirmPassworderror: true,
        passConfirm: value,
        messageConfirmNewPasswordError: 2,
      });
    } else if (checkStrengthValidate.checkDuplicatePasswordNew(value)) {
      setState({
        confirmPassworderror: false,
        passConfirm: value,
        messageConfirmNewPasswordError: null,
      });
    }
  };

  const onfocusOldPassword = () => {
    setState({ focusOldPassword: true });
  };
  const onBlurOldPassWord = () => {
    setState({ focusOldPassword: false });
  };
  const onOK = async () => {
    try {
      showLoading();
      if (!state.successPassword || state.confirmPassworderror) {
        message.error(t("common.luuKhongThanhCong"));
        return;
      }
      const values = await form.validateFields();
      const convertData = {
        matKhauKyCu: values.matKhauCu,
        matKhauKyMoi: values.matKhauMoi,
      };
      await onChangePasswordKySo(convertData);
      onCloseModal();
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const renderStrength = (title) => {
    return (
      <div className="strength">
        <p className="title-strength">
          {t("thietLap.doManhCuaMatKhau", {
            title: title,
          })}
        </p>
        <p>
          {dataCAP_DO_MAT_KHAU == "1"
            ? t("thietLap.haySuDungMatKhauItNhat8KyTuChuaItNhat1KyTuSo1KyTuChu")
            : dataCAP_DO_MAT_KHAU == "2"
              ? t("thietLap.haySuDungMatKhauItNhat8KyTu_CapDo2")
              : t("thietLap.haySuDungMatKhauItNhat8KyTu_CapDo3")}
        </p>
      </div>
    );
  };

  const renderNewPasswordError = () => {
    switch (state.messageNewPasswordError) {
      case 1:
        return (
          <div className="message-error-custom">
            <div className="error">
              <span className="title-error">
                {t("thietLap.vuiLongNhapMatKhauMoi")}
              </span>
            </div>
            {renderStrength("")}
          </div>
        );
      case 2:
        return (
          <div className="message-error-custom">
            <div className="error">
              <span className="title-error">
                {t("thietLap.vuiLongChonMatKhauChuaItNhatNumKyTuChuaItNhat1KyTuSo1KyTuChu", {
                  num: dataCAP_DO_MAT_KHAU == "1" ? 8 : 10,
                })}
              </span>
            </div>
            {renderStrength(t("thietLap.quaNgan"))}
          </div>
        );
      case 3:
        return (
          <div className="message-error-custom">
            <div className="error">
              <span className="title-error">
                {t("thietLap.matKhauMoiKhongDuocTrungVoiMauKhauCu")}
              </span>
            </div>
            {renderStrength("")}
          </div>
        );
      case 4:
        return (
          <div className="message-error-custom">
            <div className="error"></div>
            {renderStrength(t("thietLap.manh"))}
          </div>
        );
      case 5:
        return (
          <div className="message-error-custom">
            <div className="error">
              {dataCAP_DO_MAT_KHAU == "3" && (
                <span className="title-error">
                  {t("thietLap.vuiLongChonMatKhauManhHon")}
                </span>
              )}
            </div>
            {renderStrength(t("thietLap.trungBinh"))}
          </div>
        );
      case 6:
        return (
          <div className="message-error-custom">
            <div className="error">
              {(dataCAP_DO_MAT_KHAU == "2" || dataCAP_DO_MAT_KHAU == "3") && (
                <span className="title-error">
                  {t("thietLap.vuiLongChonMatKhauManhHon")}
                </span>
              )}
            </div>
            {renderStrength(t("thietLap.yeu"))}
          </div>
        );
      default:
        return (
          <div className="message-error-custom">
            <div className="error"></div>
            {renderStrength("")}
          </div>
        );
    }
  };

  const renderConfirmNewPasswordError = () => {
    switch (state.messageConfirmNewPasswordError) {
      case 1:
        return (
          <div className="message-error-custom">
            <div className="error">
              <span className="title-error">
                {t("thietLap.vuiLongNhapLaiMatKhau")}
              </span>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="message-error-custom">
            <div className="error">
              <span className="title-error">
                {t("thietLap.matKhauMoiKhongTrungKhop")}
              </span>
            </div>
          </div>
        );
      default:
        return <></>;
    }
  };
  return (
    <ModalTemplate.Container>
      <ModalTemplate.Content>
        <Main>
          <Form
            form={form}
            name="basic"
            initialValues={{ remember: true }}
            layout="vertical"
            autoComplete="off"
          >
            <Form.Item
              label={t("thietLap.matKhauCu")}
              name="matKhauCu"
              rules={[
                {
                  required: true,
                  message: (
                    <div className="message-error-custom no-strength-required">
                      <div className="error">
                        <span className="title-error">
                          {t("thietLap.vuiLongNhapMatKhauCu")}
                        </span>
                      </div>
                    </div>
                  ),
                },
              ]}
            >
              <InputSearch
                focus={state.focusOldPassword}
                error={state.oldPassErrors}
              >
                <SVG.IcPass className="icon-pass" />
                <Input.Password
                  autoFocus={true}
                  ref={refOldPassword}
                  onChange={handleChangeOldPass}
                  onFocus={onfocusOldPassword}
                  onBlur={onBlurOldPassWord}
                  placeholder={t("thietLap.matKhauCu")}
                />
              </InputSearch>
            </Form.Item>
            <Form.Item
              label={
                <div className="label-required-custom">
                  {t("thietLap.matKhauMoi")}
                </div>
              }
              name="matKhauMoi"
            >
              <InputSearch
                focus={state.focusPass}
                error={state.matKhauMoierror}
              >
                <SVG.IcPass className="icon-pass" />
                <Input.Password
                  ref={refPassword}
                  onBlur={onBlurPass}
                  onFocus={onFocusPass}
                  onChange={handleChangeNewPass}
                  placeholder={t("thietLap.matKhauMoi")}
                />
              </InputSearch>
            </Form.Item>
            {renderNewPasswordError()}

            <Form.Item
              label={
                <div className="label-required-custom">
                  {t("thietLap.nhapLaiMatKhauMoi")}
                </div>
              }
              name="confirmPassword"
            >
              <InputSearch
                focus={state.focusConfirm}
                error={state.confirmPassworderror}
              >
                <SVG.IcPass className="icon-pass" />
                <Input.Password
                  ref={refConfirmPassword}
                  onBlur={onBlurConfirm}
                  onFocus={onFocusConfirm}
                  onChange={hanldeChangeConfirmPass}
                  placeholder={t("thietLap.nhapLaiMatKhauMoi")}
                />
              </InputSearch>
            </Form.Item>
            {renderConfirmNewPasswordError()}
          </Form>
        </Main>
      </ModalTemplate.Content>
      <ModalTemplate.Footer>
        <ModalTemplate.FooterLeft>
          <Button.QuayLai onClick={onCloseModal} />
        </ModalTemplate.FooterLeft>
        <ModalTemplate.FooterRight>
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onOK}
          >
            {t("common.luuThayDoi")}
          </Button>
        </ModalTemplate.FooterRight>
      </ModalTemplate.Footer>
    </ModalTemplate.Container>
  );
};

export default Content;
