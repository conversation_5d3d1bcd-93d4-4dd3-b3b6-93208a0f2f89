import React from "react";
import Label from "./Label";
import TextField from "./TextField";
import Barcode from "./Barcode";
import QRcode from "./QRcode";
import Date from "./DatePicker";
import {
  QrcodeOutlined,
  EditOutlined,
  CalendarOutlined,
  FontSizeOutlined,
  BarcodeOutlined,
  LayoutOutlined,
  CarryOutOutlined,
  TableOutlined,
  BuildOutlined,
  PictureOutlined,
  FileDoneOutlined,
  MinusOutlined,
  FileOutlined,
  BarsOutlined,
  FileTextOutlined,
  AppstoreAddOutlined,
  FontColorsOutlined,
} from "@ant-design/icons";
import Layout from "./Layout";
import CheckGroups from "./CheckGroups";
import Table from "./Table";
import BangTheoDoiBenhNhanHSTC from "./BangTheoDoiBenhNhanHSTC";
import BangTheoDoiBenhNhanGMHS from "./BangTheoDoiBenhNhanGMHS";
// import BangTheoDoiHoiTinh from "./BangTheoDoiHoiTinh";
import InputNumber from "./InputNumber";
import CodeInput from "./CodeInput";
import InputGroup from "./InputGroup";
import Image from "./Image";
import ArrayConverter from "./ArrayConverter";
import BreakLine from "./BreakLine";
import Title from "./Title";
import TitleParam from "./TitleParam";
import Input from "./Input";
import NameWrapper from "./FieldsWrapper";
import Page from "./Page";
import DropDownList from "./DropDownList";
import ToDieuTri from "./ToDieuTri";
import BangKeChiPhiTongHop from "./BangKeChiPhi";
import { FORM_WIDTH } from "components/constanst";
import PhieuYLenh from "./PhieuYLenh";
import FooterAndHeader from "./FooterAndHeader";
import PhieuTruyenMau from "./PhieuTruyenMau";
import PhieuChamSocNang from "./PhieuChamSocNang";
import PhieuChamSocNhe from "./PhieuChamSocNhe";
import BaoCaoADR from "./BaoCaoADR";
import PhieuHuyThuoc from "./PhieuHuyThuoc";
import PhieuTheoDoiPhucHoiChucNang from "./PhieuTheoDoiPhucHoiChucNang";
import PhieuThucHienVaCongKhaiThuoc from "./PhieuThucHienVaCongKhaiThuoc";
import ImageSign from "./ImageSign";
import ElectronicSignatureMultiple from "./ElectronicSignatureMultiple";
import ViewPdf from "./ViewPdf";
import BangTheoDoiBenhNhanGMHS2 from "./BangTheoDoiBenhNhanGMHS2";
import PhieuChamSocCap23 from "./PhieuChamSocCap23";
import PhieuChamSocCap23Old from "./PhieuChamSocCap23_old";
import BangTheoDoiChamSoc from "./BangTheoDoiVaChamSoc";
import BangTheoDoiChamSoc2 from "./BangTheoDoiVaChamSoc2";
import BangTheoDoiChamSocCap1 from "./BangTheoDoiChamSocCap1";
import BangChePhamDD from "./BangChePhamDD";
import KetQuaXnBenhAnSao from "./KetQuaXnBenhAnSao";
import NoiDungTuVanTinhTrangNguoiBenh from "./NoiDungTuVanTinhTrangNguoiBenh";
import BangHoSoXaTri from "./BangHoSoXaTri";
import BangCamDoanPHCN from "./BangCamDoanPHCN";
import TheoDoiVaChamSocNheCap3 from "./TheoDoiVaChamSocNheCap3";
import TheoDoiVaChamSocNheCap3Old from "./TheoDoiVaChamSocNheCap3_Old";
import BangChamSocSanPhuSauDe from "./BangChamSocSanPhuSauDe";
import TheoDoiVaChamSocNangCap12 from "./TheoDoiVaChamSocNangCap12";
import BangTheoDoiTaiPhongHoiTinh from "./BangTheoDoiTaiPhongHoiTinh";
import BangDanhGiaHoiTinh from "./BangDanhGiaHoiTinh";
import BangCanThiepDuoc from "./BangCanThiepDuoc";
import BangApgar from "./BangApgar";
import PhieuChamSocKhoiNgoai from "./PhieuChamSocKhoiNgoai";
import PhieuChamSocCap23NT from "./PhieuChamSocCap23NT";
import BangChamSocC1TongHop from "./BangChamSocCap1TongHop";
import BangThucHienThuThuat from "./BangThucHienThuThuat";
import BangTheoDoiECMO from "./BangTheoDoiECMO";
import PhieuChamSocCap23TD from "./PhieuChamSocCap23TD";
import Bmi from "./Bmi";
import BangTheoDoiDichTruyenBvp from "./BangTheoDoiDichTruyenBvp";
import BangKiemNguyCoNgaMorse from "./BangKiemNguyCoNgaMorse";
import PhieuChamSocCap23PSTW from "./PhieuChamSocCap23PSTW";
import PhieuChamSocCap23PhuKhoaPSTW from "./PhieuChamSocCap23PhuKhoaPSTW";
import PhieuChamSocTreSoSinh from "./PhieuChamSocTreSoSinh";
import BangDauHieuSinhTon from "./BangDauHieuSinhTon";
import PhieuChamSocCap23CR from "./PhieuChamSocCap23CR";
import BangTheoDoiChamSocCap1CR from "./BangTheoDoiChamSocCap1CR";
import InputTuoiThai from "./InputTuoiThai";
import TinhTong from "./TinhTong";
import BieuDoChuyenDa from "./BieuDoChuyenDa";
import BangTheoDoiVaChamSocNb from "./PhieuTheoDoiVaChamSocNb";
import PhieuTheoDoiTNT from "./PhieuTheoDoiTNT";
import BangTheoDoiBenhNhanGMHSPSTW from "./BangTheoDoiBenhNhanGMHSPSTW";
import BangDanhGiaNguyCoTiDe from "./BangDanhGiaNguyCoTiDe";
import PhieuChamSocSoSinhNamCungMe from "./PhieuChamSocSoSinhNamCungMe";
import PhieuChamSocCap23PSHN from "./PhieuChamSocCap23PSHN";
import BangPhacDoKhangSinhYeuCau from "./BangPhacDoKhangSinhYeuCau";
import BangDanhGiaNguyCoTiDeDongDa from "./BangDanhGiaNguyCoTiDeDongDa";
import BangKiemNguyCoNgaMorseDD from "./BangKiemNguyCoNgaMorseDD";
import BangTheoDoiGiamDau from "./BangTheoDoiGiamDau";
import PhieuChamSocSanPhu23TD from "./PhieuChamSocSanPhu23TD";
import PhieuChamSocCap23HH from "./PhieuChamSocCap23HH";
import BangTheoDieuTriThanNhanTao from "./BangTheoDieuTriThanNhanTao";
import PhieuCsCap1NoiNgoaiUb from "./PhieuCsCap1NoiNgoaiUb";
import PhieuCsCap23NoiNgoaiUb from "./PhieuCsCap23NoiNgoaiUb";
import PhieuCsCap1NhiBVP from "./PhieuCsCap1NhiBVP";
import PhieuCsCap23NhiBVP from "./PhieuCsCap23NhiBVP";

export default {
  label: {
    name: "Label",
    component: Label,
    icon: <FontSizeOutlined />,
    defaultProps: {},
  },
  title: {
    name: "Title",
    component: Title,
    icon: <FontColorsOutlined />,
    defaultProps: {},
  },
  titleParam: {
    name: "Title Param",
    component: TitleParam,
    icon: <FontColorsOutlined />,
    defaultProps: {},
  },
  date: {
    name: "Date",
    component: Date,
    icon: <CalendarOutlined />,
    defaultProps: { dateTimeFormat: "D/M/Y" },
  },
  barcode: {
    name: "Barcode",
    component: Barcode,
    icon: <BarcodeOutlined />,
    defaultProps: {},
    groupKey: "qrbarcode",
  },
  qrcode: {
    name: "QRcode",
    component: QRcode,
    icon: <QrcodeOutlined />,
    defaultProps: {},
    groupKey: "qrbarcode",
  },
  layout: {
    name: "Layout",
    component: Layout,
    icon: <LayoutOutlined />,
    defaultProps: {},
  },
  groupCheck: {
    name: "GroupCheck",
    component: CheckGroups,
    icon: <CarryOutOutlined />,
    defaultProps: { checkList: [], direction: "ltr" },
  },
  bmi: {
    name: "BMI",
    component: Bmi,
    icon: <CarryOutOutlined />,
    defaultProps: { checkList: [], direction: "ltr" },
  },
  table: {
    name: "Table",
    component: Table,
    icon: <TableOutlined />,
    defaultProps: {
      cols: [
        { key: 1, width: FORM_WIDTH * 0.4 },
        { key: 2, width: FORM_WIDTH * 0.1 },
        { key: 3, width: FORM_WIDTH * 0.25 },
        { key: 4, width: FORM_WIDTH * 0.25 },
        // { key: 6, width: FORM_WIDTH*0.1 },
      ],
      rows: [
        { key: 1 },
        { key: 2 },
        { key: 4 },
        { key: 5 },
        { key: 6 },
        { key: 7 },
        { key: 8 },
        { key: 9 },
      ],
    },
  },
  bangTheoDoiBenhNhanHSTC: {
    name: "Bảng theo dõi bệnh nhân HSTC",
    component: BangTheoDoiBenhNhanHSTC,
    defaultProps: {},
    groupKey: "more",
    icon: <FileTextOutlined />,
  },
  theoDoiVaChamSocNheCap3: {
    name: "Theo dõi và chăm sóc nhẹ cấp 3",
    component: TheoDoiVaChamSocNheCap3,
    defaultProps: {},
    groupKey: "more",
    icon: <FileTextOutlined />,
  },
  theoDoiVaChamSocNheCap3Old: {
    name: "Theo dõi và chăm sóc nhẹ cấp 3 Old",
    component: TheoDoiVaChamSocNheCap3Old,
    defaultProps: {},
    groupKey: "more",
    icon: <FileTextOutlined />,
  },
  // bangTheoDoiBenhNhanGMHS: {
  //   name: "Bảng theo dõi bệnh nhân GMHS",
  //   component: BangTheoDoiBenhNhanGMHS,
  //   icon: "profile",
  //   defaultProps: {
  //     showDongTu: true,
  //     showALMPB: true,
  //     showALTMTT: true,
  //     showServoran: true,
  //     showDesflurane: true,
  //     showHalotan: true,
  //     showMoreInfo: true,
  //   },
  // },
  // bangTheoDoiHoiTinh: {
  //   name: "Bảng theo dõi hồi tỉnh",
  //   component: BangTheoDoiHoiTinh,
  //   icon: "profile",
  //   defaultProps: {},
  // },
  inputCombo: {
    name: "Code input",
    component: CodeInput,
    icon: <BuildOutlined />,
    defaultProps: {
      size: 2,
      disabled: false,
      props: {
        fieldName: "",
      },
    },
  },
  image: {
    name: "Image",
    component: Image,
    icon: <PictureOutlined />,
    defaultProps: { width: 64, height: 64 },
  },
  inputTuoiThai: {
    name: "Input Tuổi thai",
    component: InputTuoiThai,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  tinhTong: {
    name: "Tính tổng",
    component: TinhTong,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  textField: {
    name: "Text field",
    component: TextField,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  inputNumber: {
    name: "InputNumber",
    component: InputNumber,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  inputGroup: {
    name: "Input Group",
    component: InputGroup,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  input: {
    name: "Input",
    component: Input,
    icon: <EditOutlined />,
    defaultProps: {},
    groupKey: "input",
  },
  arrayConverter: {
    name: "Array Converter",
    component: ArrayConverter,
    icon: <EditOutlined />,
    defaultProps: {},
  },
  nameWrapper: {
    name: "Field wrapper",
    component: NameWrapper,
    icon: <BuildOutlined />,
    defaultProps: {},
  },
  electronicSignature: {
    name: "Electronic Signature",
    component: ImageSign,
    icon: <FileDoneOutlined />,
    defaultProps: {},
    groupKey: "sign",
    hide: true,
  },
  electronicSignatureMultiple: {
    name: "Electronic Signature Multiple",
    component: ElectronicSignatureMultiple,
    icon: <FileDoneOutlined />,
    defaultProps: {},
    groupKey: "sign",
  },
  viewPdf: {
    name: "View PDF",
    component: ViewPdf,
    icon: <FileDoneOutlined />,
    defaultProps: {},
    groupKey: "",
  },
  imageSign: {
    name: "ImageSign",
    component: ImageSign,
    icon: <FileDoneOutlined />,
    defaultProps: {},
    groupKey: "sign",
    nameDisplay: "editor.kySoKyDienTu",
  },

  breakLine: {
    name: "Break Line",
    component: BreakLine,
    icon: <MinusOutlined />,
    defaultProps: { marginTop: 5, marginBottom: 5 },
  },
  page: {
    name: "Page",
    component: Page,
    icon: <FileOutlined />,
    defaultProps: {},
  },
  footerAndHeader: {
    name: "footerAndHeader",
    component: FooterAndHeader,
    icon: <FileOutlined />,
    defaultProps: {},
  },
  dropDownList: {
    name: "DropDownList",
    component: DropDownList,
    icon: <BarsOutlined />,
    defaultProps: {},
  },
  toDieuTri: {
    name: "ToDieuTri",
    component: ToDieuTri,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangKeChiPhi: {
    name: "Bảng kê chi phí tổng hợp",
    component: BangKeChiPhiTongHop,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },

  phieuYLenh: {
    name: "Phiếu y lệnh",
    component: PhieuYLenh,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiBenhNhanGMHS: {
    name: "Bảng theo dõi bệnh nhân GMHS",
    component: BangTheoDoiBenhNhanGMHS,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuTruyenMau: {
    name: "Phiếu truyền máu",
    component: PhieuTruyenMau,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocNang: {
    name: "Phiếu chăm sóc nặng",
    component: PhieuChamSocNang,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocNhe: {
    name: "Phiếu chăm sóc nhẹ",
    component: PhieuChamSocNhe,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  baoCaoADR: {
    name: "Báo cáo ADR",
    component: BaoCaoADR,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuHuyThuoc: {
    name: "Phiếu hủy thuốc",
    component: PhieuHuyThuoc,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuTheoDoiPhucHoiChucNang: {
    name: "Phiếu theo dõi phục hồi chức năng",
    component: PhieuTheoDoiPhucHoiChucNang,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuThucHienVaCongKhaiThuoc: {
    name: "Phiếu thực hiện và công khai thuốc",
    component: PhieuThucHienVaCongKhaiThuoc,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiBenhNhanGMHS2: {
    name: "Bảng theo dõi bệnh nhân GMHS TT32",
    component: BangTheoDoiBenhNhanGMHS2,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23: {
    name: "Bảng chăm sóc cấp 2-3",
    component: PhieuChamSocCap23,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23Old: {
    name: "Bảng chăm sóc cấp 2-3 Old",
    component: PhieuChamSocCap23Old,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiVaChamSoc: {
    name: "Bảng theo dõi và chăm sóc",
    component: BangTheoDoiChamSoc,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiVaChamSoc2: {
    name: "Bảng theo dõi và chăm sóc 2",
    component: BangTheoDoiChamSoc2,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiVaChamSocCap1: {
    name: "Bảng theo dõi và chăm sóc cấp 1",
    component: BangTheoDoiChamSocCap1,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangChePhamDD: {
    name: "Bảng chế phầm dinh dưỡng",
    component: BangChePhamDD,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  ketQuaXnBenhAnSao: {
    name: "Kết quả XN bệnh án sao",
    component: KetQuaXnBenhAnSao,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  noiDungTuVanTinhTrangNguoiBenh: {
    name: "Nội dung tư vấn tình trạng người bệnh",
    component: NoiDungTuVanTinhTrangNguoiBenh,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangHoSoXaTri: {
    name: "Bảng hồ sơ xạ trị",
    component: BangHoSoXaTri,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangCamDoanPHCN: {
    name: "Bảng cam đoạn PHCN",
    component: BangCamDoanPHCN,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangChamSocSanPhuSauDe: {
    name: "Bảng chăm sóc sản phụ sau đẻ",
    component: BangChamSocSanPhuSauDe,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  theoDoiVaChamSocNangCap12: {
    name: "Bảng theo dõi và chăm sóc nặng cấp 1,2",
    component: TheoDoiVaChamSocNangCap12,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiTaiPhongHoiTinh: {
    name: "Bảng theo dõi tại phòng hồi tỉnh",
    component: BangTheoDoiTaiPhongHoiTinh,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangDanhGiaHoiTinh: {
    name: "Bảng đáng giá hồi tỉnh",
    component: BangDanhGiaHoiTinh,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangCanThiepDuoc: {
    name: "Bảng can thiệp dược",
    component: BangCanThiepDuoc,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangApgar: {
    name: "Bảng APGAR",
    component: BangApgar,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocKhoiNgoai: {
    name: "Phiếu chăm sóc khối ngoại",
    component: PhieuChamSocKhoiNgoai,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23NT: {
    name: "Phiếu chăm sóc cấp 2-3 (NT)",
    component: PhieuChamSocCap23NT,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangChamSocC1TongHop: {
    name: "Bảng chăm sóc C1 tổng hợp",
    component: BangChamSocC1TongHop,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangThucHienThuThuat: {
    name: "Bảng thực hiện thủ thuật",
    component: BangThucHienThuThuat,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiECMO: {
    name: "Bảng theo dõi ECMO",
    component: BangTheoDoiECMO,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23TD: {
    name: "Bảng theo cấp 2-3 Thủ Đức",
    component: PhieuChamSocCap23TD,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiDichTruyenBvp: {
    name: "Bảng theo dõi dịch chuyển BVP",
    component: BangTheoDoiDichTruyenBvp,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangKiemNguyCoNgaMorse: {
    name: "Bảng kiểm nguy cơ té ngã morse",
    component: BangKiemNguyCoNgaMorse,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangKiemNguyCoNgaMorseDD: {
    name: "Bảng kiểm nguy cơ té ngã morse (Đống Đa)",
    component: BangKiemNguyCoNgaMorseDD,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiGiamDau: {
    name: "Bảng theo dõi giảm đau",
    component: BangTheoDoiGiamDau,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },

  phieuChamSocSanPhu23TD: {
    name: "Phiếu chăm sóc sản phụ",
    component: PhieuChamSocSanPhu23TD,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },

  phieuChamSocCap23PSTW: {
    name: "Bảng theo cấp 2-3 PSTW",
    component: PhieuChamSocCap23PSTW,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23PSHN: {
    name: "Bảng theo cấp 2-3 PSHN",
    component: PhieuChamSocCap23PSHN,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23PhuKhoaPSTW: {
    name: "Bảng theo cấp 2-3 Phụ khoa PSTW",
    component: PhieuChamSocCap23PhuKhoaPSTW,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocTreSoSinh: {
    name: "Phiếu chăm sóc trẻ sơ sinh",
    component: PhieuChamSocTreSoSinh,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangDauHieuSinhTon: {
    name: "Bảng dấu hiệu sinh tồn",
    component: BangDauHieuSinhTon,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23CR: {
    name: "Phiếu chăm sóc cấp 2-3 CR",
    component: PhieuChamSocCap23CR,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocCap23HH: {
    name: "Phiếu chăm sóc cấp 2-3 HH",
    component: PhieuChamSocCap23HH,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDieuTriThanNhanTao: {
    name: "Bảng theo dõi điều trị thận nhân tạo chu kỳ",
    component: BangTheoDieuTriThanNhanTao,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuCsCap1NoiNgoaiUb: {
    name: "Phiếu chăm sóc cấp 1 nội Ngoại UB",
    component: PhieuCsCap1NoiNgoaiUb,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuCsCap23NoiNgoaiUb: {
    name: "Phiếu chăm sóc cấp 23 nội Ngoại UB",
    component: PhieuCsCap23NoiNgoaiUb,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuCsCap1NhiBVP: {
    name: "Phiếu chăm sóc cấp 1 nhi BVP",
    component: PhieuCsCap1NhiBVP,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuCsCap23NhiBVP: {
    name: "Phiếu chăm sóc cấp 23 nhi BVP",
    component: PhieuCsCap23NhiBVP,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiChamSocCap1CR: {
    name: "Phiếu chăm sóc cấp 1 CR",
    component: BangTheoDoiChamSocCap1CR,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bieuDoChuyenDa: {
    name: "Biểu đồ chuyển dạ",
    component: BieuDoChuyenDa,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiVaChamSocNb: {
    name: "Bảng theo dõi và chăm sóc người bệnh",
    component: BangTheoDoiVaChamSocNb,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuTheoDoiTNTProps: {
    name: "Bảng theo dõi TNT",
    component: PhieuTheoDoiTNT,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangTheoDoiBenhNhanGMHSPSTW: {
    name: "Bảng theo dõi bệnh nhân GMHS BV PSTW",
    component: BangTheoDoiBenhNhanGMHSPSTW,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangDanhGiaNguyCoTiDe: {
    name: "Bảng đánh giá nguy cơ loét tỳ đè - thang đo Braden",
    component: BangDanhGiaNguyCoTiDe,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  phieuChamSocSoSinhNamCungMe: {
    name: "Phiếu chăm sóc sơ sinh nằm cùng mẹ",
    component: PhieuChamSocSoSinhNamCungMe,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangPhacDoKhangSinhYeuCau: {
    name: "Bảng phác đồ kháng sinh yêu cầu",
    component: BangPhacDoKhangSinhYeuCau,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },
  bangDanhGiaNguyCoTiDeDongDa: {
    name: "Bảng đánh giá nguy cơ loét tỳ đè - thang đo Braden(Đống đa)",
    component: BangDanhGiaNguyCoTiDeDongDa,
    icon: <FileTextOutlined />,
    defaultProps: {},
    groupKey: "more",
  },

  groups: [
    {
      groupKey: "more",
      name: "More",
      icon: <AppstoreAddOutlined />,
    },
    {
      groupKey: "qrbarcode",
      name: "QR-Barcode",
      icon: <QrcodeOutlined />,
    },
    {
      groupKey: "input",
      name: "Input",
      icon: <EditOutlined />,
    },
    {
      groupKey: "sign",
      name: "Sign",
      icon: <EditOutlined />,
    },
  ],
};
