import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Checkbox, InputNumber, Input, Row, Col, Select } from "antd";
import { Main } from "./styled";

import {
  EditorTool,
  AlignConfig,
  FontSizeConfig,
} from "components/editor/config";
import { FontColorsOutlined } from "@ant-design/icons";
import { DS_LOAI_KY, FIELD_NAME, VI_TRI_CA } from "../../ImageSign/constanst";
import { useTranslation } from "react-i18next";
import { PropertiesSign } from "../../ImageSign/Properties";
const { PickColor, FieldName } = EditorTool;
const ElectricSignatureMultipleProps = forwardRef((props, ref) => {
  const { apiFields = [] } = props;
  const { t } = useTranslation();
  const [state, _setState] = useState({
    newFields: [],
    fieldName: "",
    levelSign: "",
    width: 200,
    height: 200,
    isPatient: false,
    allowReset: true,
    // currentLevelRef: "soCapKy",
    disableIfSigned: true,
    disabled: false,
    signer: "",
    autoSave: true,
    widthCa: 0,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    ...state,
    fieldName: state.fieldName,
    levelSign: state.levelSign,
    // currentLevelRef: state.currentLevelRef,
    width: state.width,
    height: state.height,
    isPatient: false,
    allowReset: state.allowReset,
    disableIfSigned: state.disableIfSigned,
    displayAsUserName: state.displayAsUserName,
    disabled: state.disabled,
    signer: state.signer,
    autoSave: state.autoSave,
    contentAlign: state.contentAlign,
    viTriCa: state.viTriCa,
    showCa: state.showCa,
    colorCa: state.colorCa,
    fontSize: state.fontSize,
    showAnhCa: state.showAnhCa,
    viTriAnhCa: state.viTriAnhCa,
    widthCa: state.widthCa,
    MaximumQuantity: state.MaximumQuantity,
    viewHorizontal: state.viewHorizontal,
    loaiKy: state.loaiKy,
    capKy: state.capKy,
    showPatientSign: state.showPatientSign,
    huyTrinhKy: state.huyTrinhKy,
  }));

  useEffect(() => {
    if (props.state.key) {
      setState({
        ...props.state.props,
        fieldName: props.state.props.fieldName,
        levelSign: props.state.props.levelSign,
        width: props.state.props.width,
        height: props.state.props.height,
        isPatient: props.state.props.isPatient,
        allowReset: props.state.props.allowReset,
        disableIfSigned: props.state.props.disableIfSigned,
        displayAsUserName: props.state.props.displayAsUserName,
        disabled: props.state.props.disabled,
        // currentLevelRef: props.state.props.currentLevelRef || "soCapKy",
        signer: props.state.props.signer,
        autoSave:
          props.state.props.autoSave == undefined
            ? true
            : props.state.props.autoSave,
        contentAlign: props.state.props.contentAlign,
        viTriCa: props.state.props.viTriCa,
        showCa: props.state.props.showCa,
        colorCa: props.state.props.colorCa,
        fontSize: props.state.props.fontSize,
        showAnhCa: props.state.props.showAnhCa,
        viTriAnhCa: props.state.props.viTriAnhCa,
        widthCa: props.state.props.widthCa,
        MaximumQuantity: props.state.props.MaximumQuantity || 1,
        viewHorizontal: props.state.props.viewHorizontal,
        loaiKy: props.state.props.loaiKy,
        capKy: props.state.props.capKy,
        showPatientSign: props.state.props.showPatientSign,
        huyTrinhKy: props.state.props.huyTrinhKy,
      });
    }
  }, [props.state]);

  const changeValue = (target) => (e) => {
    setState({
      [target]: e,
    });
  };

  const changeInput = (target) => (e) => {
    setState({
      [target]: e.target.value,
    });
  };
  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main gutter={[12, 12]}>
      <Col span={8}>
        <span>{t("editor.hienThiHangNgang")}:</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.viewHorizontal}
          onChange={changeCheckbox("viewHorizontal")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.gioiHanNguoiKy")}:</span>
      </Col>
      <Col span={16}>
        <InputNumber
          size={"small"}
          value={state.MaximumQuantity}
          onChange={changeValue("MaximumQuantity")}
        ></InputNumber>
      </Col>

      <PropertiesSign
        state={state}
        changeCheckbox={changeCheckbox}
        onChangeInput={changeInput}
        onChangeValue={changeValue}
      ></PropertiesSign>
    </Main>
  );
});

ElectricSignatureMultipleProps.defaultProps = {
  component: {},
  apiFields: [],
};

ElectricSignatureMultipleProps.propTypes = {
  component: T.shape({}),
  apiFields: T.arrayOf(T.string),
};

export default ElectricSignatureMultipleProps;
