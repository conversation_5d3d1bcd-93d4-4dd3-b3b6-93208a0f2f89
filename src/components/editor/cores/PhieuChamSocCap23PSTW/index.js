import React, { useEffect, useMemo, useRef, useState } from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button, Pagination } from "antd";
import { cloneDeep, get } from "lodash";
import { refConfirm } from "app";
import { Select } from "components";
import ModalSelectSinhHieu from "../Components/ModalSelectSinhHieu";

const NUM_OF_COLS = 4;
const arr = new Array(NUM_OF_COLS).fill(cloneDeep({}));
const SIZE = [
  { ten: "5 khung", id: 5 },
  { ten: "10 khung", id: 10 },
  { ten: "20 khung", id: 20 },
  { ten: "50 khung", id: 50 },
  { ten: "100 khung", id: 100 },
];
const PhieuChamSocCap23PSTW = (props) => {
  const { t } = useTranslation();

  const [state, _setState] = useState({
    dsTheoDoi: [{}],
    size: 5,
    currentPage: 1,
    size: 5,
  });

  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refValueForm = useRef([]);
  const refModalSinhHieu = useRef();

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  function setSTT(arr) {
    const len = arr.length;
    return arr.map((item, index) => ({
      ...item,
      stt: len - index,
    }));
  }

  useEffect(() => {
    if (form) {
      refValueForm.current = cloneDeep(form.dsTheoDoi || [{}]);
      let dsTenTimThai = [];
      refValueForm.current = setSTT(refValueForm.current);
      // Xử lý trường hợp những bản ghi cũ mà chưa sắp xếp thời gian gần nhất hiện tại lên đầu
      if (refValueForm.current.every((el) => !el.stt)) {
        refValueForm.current = refValueForm.current.reverse();
      }

      refValueForm.current.forEach((item, indexDs) => {
        if (!item.stt && item.stt !== 0) item.stt = indexDs;
        if (!indexDs) {
          dsTenTimThai = item.dsTenTimThai || [];
          if (!item.dsTenTimThai) {
            item.dsTenTimThai = [];
          }
        } else {
          item.dsTenTimThai = dsTenTimThai;
        }
        item.dsChiTiet = arr.map((el, index) => {
          const element = get(item, `dsChiTiet[${index}]`, {});
          element.dsTimThai = dsTenTimThai.map((x, x1) =>
            get(element, `dsTimThai[${x1}]`, "")
          );
          element.dat14ThongSo = new Array(14).fill().map((i, idx) => {
            return get(element, `dat14ThongSo[${idx}]`, "");
          });

          if (!element.stt) {
            element.stt = indexDs * NUM_OF_COLS + index + 1;
          }
          if (element?.chiSoSong) {
            element.chiSoSong.huyetAp =
              element.chiSoSong.huyetApTamThu &&
              element.chiSoSong.huyetApTamTruong
                ? `${element.chiSoSong.huyetApTamThu}/${element.chiSoSong.huyetApTamTruong}`
                : "";
          }

          if (element?.chiSoSong?.thoiGianThucHien) {
            element.thoiGianThucHien = element?.chiSoSong?.thoiGianThucHien;
          }

          //set khoa chỉ định cho sinh hiệu mặc định
          if (element?.chiSoSong && !element?.chiSoSong?.khoaChiDinhId) {
            element.chiSoSong.khoaChiDinhId = form?.khoaChiDinhId;
          }

          if (element.ve && element.vh) {
            element.veh = `${element.ve}/${element.vh}`;
          }
          if (!element.dsVanDe) {
            element.dsVanDe = [];
          }
          return element;
        });
        const dsChiTietExitTG = item.dsChiTiet.filter(
          (el) => el.thoiGianThucHien
        );
        const dsChiTietNotExitTG = item.dsChiTiet.filter(
          (el) => !el.thoiGianThucHien
        );
        dsChiTietExitTG.sort(
          (a, b) =>
            (a.thoiGianThucHien ? new Date(a.thoiGianThucHien) : new Date()) -
            (b.thoiGianThucHien ? new Date(b.thoiGianThucHien) : new Date())
        );
        item.dsChiTiet = dsChiTietExitTG.concat(dsChiTietNotExitTG);
      });
      const dsTheoDoi = refValueForm.current.slice(0, state.size);

      setState({
        dsTheoDoi,
      });
    }
  }, [JSON.stringify(form || {})]);

  // Tính toán các khung sẽ hiển thị dựa trên trang hiện tại
  const pagedChamSoc = useMemo(() => {
    const start = (state.currentPage - 1) * state.size;
    const end = start + state.size;
    const data = refValueForm.current.slice(start, end);
    return data;
  }, [state.currentPage, state.size, refValueForm.current, state.dsTheoDoi]);

  const handleAdd = () => {
    const thietLapPrev = refValueForm.current[0];
    const maxStt =
      Math.max(
        ...refValueForm.current
          .map((el) => el)
          .map((el) => el.dsChiTiet.map((e) => e.stt))
          .flat(Infinity)
      ) || 1;
    const maxSttBang =
      Math.max(...refValueForm.current.map((el) => el.stt)) || 0;
    const dsTenTimThai = refValueForm.current[0].dsTenTimThai;
    refValueForm.current = [
      cloneDeep({
        canThiepDieuDuong: [],
        dsChiTiet: new Array(NUM_OF_COLS)
          .fill({})
          .map((_, idx) => ({ dsVanDe: [], stt: maxStt + idx + 1 })),
        vanDe: [],
        dsTenTimThai,
        stt: maxSttBang + 1,
        thietLap: thietLapPrev,
      }),
      ...refValueForm.current,
    ];
    const dsTheoDoi = refValueForm.current;
    setState({
      dsTheoDoi,
      currentPage: 1, // chuyển về trang cuối
    });
    formChange["dsTheoDoi"](refValueForm.current);
  };

  const handleRemove = (index) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refValueForm.current = refValueForm.current.filter(
            (item, idx) => item.tableIndex !== index
          );
          const dsTheoDoi = refValueForm.current.slice(0, state.size);
          setState({
            dsTheoDoi,
          });
          formChange["dsTheoDoi"](refValueForm.current);
        }
      );
  };

  const onAddTenTimThai = (dsTenTimThai) => {
    refValueForm.current.forEach((item, index) => {
      if (!index) {
        item.dsTenTimThai = dsTenTimThai;
      } else {
        item.dsTenTimThai = dsTenTimThai;
      }
    });

    setState({
      dsTheoDoi: cloneDeep(refValueForm.current),
    });
    formChange["dsTheoDoi"](refValueForm.current);
  };

  // Khi thay đổi trang
  const handlePageChange = (page) => {
    setState({ currentPage: page });
  };

  const handleSizeChange = (page, size) => {
    setState({ size });
  };

  console.log("state.currentPage", state.currentPage);

  return (
    <Main
      className="phieu-cham-soc-cap23"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-cham-soc-cap23"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {pagedChamSoc.map((item, index) => {
        return (
          <div
            className="form"
            style={{
              pageBreakAfter:
                index === state.dsTheoDoi.length - 1 ? "avoid" : "always",
            }}
            key={index}
          >
            <RenderTable
              key={index}
              mode={mode}
              tableIndex={item.stt}
              refValueForm={refValueForm.current}
              item={item}
              formChange={formChange}
              onRemove={handleRemove}
              itemProps={itemProps}
              form={form}
              onAddTenTimThai={onAddTenTimThai}
              dataCopy={dataCopy}
              setDataCopy={setDataCopy}
              refModalSinhHieu={refModalSinhHieu}
              showIconAdd={!index}
              handleAdd={handleAdd}
            />
          </div>
        );
      })}
      <div className="controls-container">
        <Pagination
          current={state.currentPage}
          pageSize={state.size}
          total={refValueForm.current.length}
          onChange={handlePageChange}
          onShowSizeChange={handleSizeChange}
          showSizeChanger={true}
          showQuickJumper
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} của ${total} bảng`
          }
          pageSizeOptions={[5, 10, 20, 50, 100]}
        />
      </div>

      <ModalSelectSinhHieu ref={refModalSinhHieu} />
    </Main>
  );
};

PhieuChamSocCap23PSTW.propTypes = {};

export default PhieuChamSocCap23PSTW;
