import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { cloneDeep, get } from "lodash";
import { SVG } from "assets";
import { DeboundInput } from "components/editor/config";
import { refConfirm } from "app";
import { message } from "antd";
import { useTranslation } from "react-i18next";
const RenderTimThai = (
  {
    value,
    refValue,
    formChangeValue,
    refValueForm,
    tableIndex,
    onAddTenTimThai,
    rowSpanSinhTon,
    setRowSpanSinhTon,
    arr,
  },
  ref
) => {
  const { t } = useTranslation();
  const [data, setData] = useState({});
  useImperativeHandle(
    ref,
    () => ({
      setValue: (item) => {
        setData(cloneDeep(item));
      },
    }),
    []
  );

  useEffect(() => {
    setData(value);
  }, [value]);

  const onChangeTenTimThai = (idx) => (value) => {
    refValue.current.dsTenTimThai[idx] = value;
    formChangeValue();
  };
  const onChangeTimThai = (idx) => (value) => {
    refValue.current.dsChiTiet[idx].timThai = value;
    formChangeValue();
  };
  const onChangeDstimThai = (indexValue, indexTimThai) => (value) => {
    refValue.current.dsChiTiet[indexValue].dsTimThai[indexTimThai] = value;
    formChangeValue();
  };
  const handleAddTenTimThai = () => {
    (refValue.current.dsTenTimThai || []).push(
      `Tên tim thai ${refValue.current.dsTenTimThai.length + 2}`
    );
    refValue.current.dsChiTiet.forEach((x) => {
      x.dsTimThai.push("");
    });
    const indexKhung = refValueForm.findIndex((el) => el.stt === tableIndex);
    refValueForm[indexKhung] = refValue.current;
    onAddTenTimThai(refValue.current.dsTenTimThai);
    setRowSpanSinhTon(rowSpanSinhTon + 1);
  };
  const onRemove = (indexTimThai) => () => {
    const isExitTimThai = refValueForm.some((el) => {
      return (el.dsChiTiet || []).some((el1) => el1.dsTimThai[indexTimThai]);
    });
    if (isExitTimThai) {
      message.error(
        `Đã tồn tại giá trị ${refValue.current.dsTenTimThai[indexTimThai]}`
      );
      return;
    }
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refValue.current.dsTenTimThai.splice(indexTimThai, 1);
          const indexKhung = refValueForm.findIndex(
            (el) => el.stt === tableIndex
          );
          refValueForm[indexKhung] = refValue.current;
          onAddTenTimThai(refValue.current.dsTenTimThai);
          setRowSpanSinhTon(rowSpanSinhTon - 1);
        }
      );
  };

  return (
    <>
      <tr>
        <td colSpan={2}>
          <div style={{ display: "flex", alignItems: "center" }}>
            <span>Tim thai</span>{" "}
            <SVG.IcAdd
              onClick={handleAddTenTimThai}
              className="icon-add hide-print-btn"
            />
          </div>
        </td>
        {arr.map((el, idx) => {
          return (
            <td key={idx}>
              <DeboundInput
                readOnly={false}
                value={get(refValue.current, `dsChiTiet[${idx}].timThai`)}
                onChange={onChangeTimThai(idx)}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={9}
                minHeight={9 + 6}
                markSpanRow={false}
                contentAlign="center"
                inputNumber={true}
                typeNumber={"float"}
              ></DeboundInput>
            </td>
          );
        })}
      </tr>
      {(data.dsTenTimThai || []).map((el, indexTimThai) => {
        return (
          <tr key={indexTimThai}>
            <td colSpan={2} style={{ position: "relative" }}>
              <div>
                <SVG.IcDelete
                  className="ic-remove-tim-thai"
                  onClick={onRemove(indexTimThai)}
                ></SVG.IcDelete>
              </div>

              <DeboundInput
                readOnly={false}
                value={get(refValue.current, `[dsTenTimThai][${indexTimThai}]`)}
                onChange={onChangeTenTimThai(indexTimThai)}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={9}
                minHeight={9 + 6}
                markSpanRow={false}
              ></DeboundInput>
            </td>
            {arr.map((el, idx) => {
              return (
                <td key={idx}>
                  <DeboundInput
                    readOnly={false}
                    value={get(
                      refValue.current,
                      `dsChiTiet[${idx}][dsTimThai][${indexTimThai}]`
                    )}
                    onChange={onChangeDstimThai(idx, indexTimThai)}
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={9}
                    minHeight={9 + 6}
                    markSpanRow={false}
                    contentAlign="center"
                    inputNumber={true}
                    typeNumber={"float"}
                  ></DeboundInput>
                </td>
              );
            })}
          </tr>
        );
      })}
    </>
  );
};
export default forwardRef(RenderTimThai);
