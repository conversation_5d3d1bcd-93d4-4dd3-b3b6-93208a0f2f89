import React, { forwardRef, useImperativeHandle, useState } from "react";
import PopoverSelect from "../../Components/PopoverSelect";
import { Button, Popover, Space } from "antd";
import { cloneDeep, set, get } from "lodash";
import { DeboundInput } from "module_html_editor/Components";
import { useEffect } from "react";
import {
  SettingOutlined,
  CheckOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import styled from "styled-components";

// SelectRow Components
const SelectRowPopoverContent = styled.div`
  min-width: 200px;
  max-width: 300px;
`;

const SelectRowTitle = styled.div`
  margin-bottom: 8px;
  font-weight: 500;
  color: #666;
`;

const SelectRowItem = styled.div`
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid ${(props) => (props.isSelected ? "#1890ff" : "#d9d9d9")};
  background-color: ${(props) => (props.isSelected ? "#f0f8ff" : "#fff")};
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.isSelected ? "#e6f7ff" : "#f5f5f5")};
    border-color: #1890ff;
  }
`;

const SelectRowItemText = styled.span`
  color: ${(props) => (props.isSelected ? "#1890ff" : "#333")};
  font-weight: ${(props) => (props.isSelected ? 500 : 400)};
`;

const SelectRowCheckIcon = styled(CheckOutlined)`
  color: #52c41a;
  font-size: 14px;
`;

const SelectRowDeleteIcon = styled(DeleteOutlined)`
  color: #ff4d4f;
  font-size: 12px;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
`;

const SelectRowEmptyContainer = styled.div`
  display: flex;
  text-align: center;
  background-color: #fafafa;
  border-radius: 6px;
`;

const SelectRowEmptyTitle = styled.div`
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
`;

const SelectRowConfigButton = styled(Button)`
  border-radius: 6px;
  height: 32px;
  padding: 0 16px;
  @meida print {
    display: none;
  }
`;

const SelectRow = (
  { itemRow, data, formChangeValue, refValue, arr, onChangeInput },
  ref
) => {
  const [value, setValue] = useState({});

  useEffect(() => {
    setValue(data);
  }, [data]);
  const [listRow, setListRow] = useState([]);

  const handleChangeRow = (row) => () => {
    let data = cloneDeep(listRow);
    const indexRow = data.findIndex((i) => i === row.key);
    const detailRow = itemRow.listRow.find((i) => i.key === row.key);
    if (detailRow.listKey) {
      if (indexRow !== -1) {
        data = data.filter((el) => !detailRow.listKey.includes(el));
      } else {
        data = [...data, ...detailRow.listKey];
      }
    } else {
      if (indexRow !== -1) {
        data.splice(indexRow, 1);
      } else {
        data.push(row.key);
      }
    }

    set(refValue.current, `thietLap.${itemRow.key}`, data);
    formChangeValue();
    setListRow(cloneDeep(data));
  };

  const handleSetListRow = () => {
    const listRow =
      get(value, `thietLap.${itemRow.key}`) ||
      itemRow.listRow.map((el) => el.key);
    setListRow(listRow);
  };
  useImperativeHandle(ref, () => ({
    setValue,
    handleSetListRow,
  }));

  useEffect(() => {
    handleSetListRow();
  }, [value?.thietLap]);

  const content = (
    <SelectRowPopoverContent>
      <SelectRowTitle>Chọn các hàng hiển thị:</SelectRowTitle>
      <Space direction="vertical" style={{ width: "100%" }} size="small">
        {itemRow.listRow
          .filter((i) => i.label2)
          .map((el) => {
            const isSelected = listRow.includes(el.key);
            return (
              <SelectRowItem
                key={el.key}
                onClick={handleChangeRow(el)}
                isSelected={isSelected}
              >
                <SelectRowItemText isSelected={isSelected}>
                  {el.label2 || el.label1}
                </SelectRowItemText>
                <Space>
                  {isSelected && <SelectRowCheckIcon />}
                  <SelectRowDeleteIcon
                    onClick={(e) => {
                      e.stopPropagation();
                      if (isSelected) {
                        handleChangeRow(el)();
                      }
                    }}
                  />
                </Space>
              </SelectRowItem>
            );
          })}
      </Space>
    </SelectRowPopoverContent>
  );
  return (
    <>
      {listRow.length ? (
        itemRow.listRow.map((item, index) => {
          const keySplit = (item?.key || "").split(".");
          let key1, key2;
          key1 = keySplit[0];
          if (keySplit.length > 1) {
            key2 = keySplit[1];
          }
          const firstMatch =
            itemRow.listRow.find((item) => listRow.includes(item.key))?.key ==
            item.key;
          if (listRow.includes(item.key)) {
            return (
              <tr key={`${item.key}_${index}`}>
                {firstMatch && (
                  <td
                    rowSpan={
                      item.key === "chiSoSong.mach"
                        ? rowSpanSinhTon
                        : listRow?.length || 1
                    }
                    colSpan={item.colSpan1 || 1}
                    style={{ position: "relative" }}
                  >
                    <b>{get(itemRow, `listRow[0].label1`)}</b>
                    <Popover
                      content={content}
                      trigger="click"
                      placement="bottomLeft"
                      overlayStyle={{ maxWidth: "none" }}
                    >
                      <SelectRowConfigButton
                        icon={<SettingOutlined />}
                        size="small"
                        type="primary"
                        ghost
                        className="hide-print"
                      ></SelectRowConfigButton>
                    </Popover>
                  </td>
                )}
                {item.label2 && (
                  <td
                    rowSpan={item.rowSpan2 || 1}
                    colSpan={item.colSpan2 || 1}
                    style={{ width: 50, minWidth: 50, maxWidth: 50 }}
                  >
                    {item.label2}
                  </td>
                )}
                {item.hint && (
                  <td
                    className="hint"
                    rowSpan={item.rowSpanHint || 1}
                    colSpan={item.colSpanHint || 1}
                    style={{ width: 100, minWidth: 100 }}
                  >
                    {(item.hint || []).map((item, index) => (
                      <span key={index}>{item}</span>
                    ))}
                  </td>
                )}

                {arr.map((el, idx) => {
                  let dataDroplist = cloneDeep(item.data);

                  return item.disable ? (
                    <td key={`${item.key}_${index}_${idx}`}></td>
                  ) : item.type === "droplist" ? (
                    <td>
                      <PopoverSelect
                        data={dataDroplist}
                        value={get(
                          value,
                          `dsChiTiet[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                        )}
                        onChangeValue={(e) => {
                          onChangeInput(item.key, idx)(e);
                        }}
                        isMultiple={!(item.mode == "onlyOne")}
                        trigger="click"
                        isShowSearch={
                          (Array.isArray(item.data) ? item.data : []).length >
                          15
                        }
                      />
                    </td>
                  ) : (
                    <td key={`${item.key}_${index}_${idx}`}>
                      <DeboundInput
                        readOnly={false}
                        value={get(
                          value,
                          `dsChiTiet[${idx}]${key1}${key2 ? `[${key2}]` : ""}`
                        )}
                        onChange={onChangeInput(item.key, idx)}
                        type="multipleline"
                        lineHeightText={1.5}
                        fontSize={9}
                        minHeight={9 + 6}
                        markSpanRow={false}
                        contentAlign="center"
                        inputNumber={item.type === "number"}
                        typeNumber={"float"}
                      />
                    </td>
                  );
                })}
              </tr>
            );
          } else {
            return null;
          }
        })
      ) : (
        <tr>
          <td colSpan={3}>
            <SelectRowEmptyContainer>
              <SelectRowEmptyTitle>{itemRow.label1}</SelectRowEmptyTitle>
              <Popover
                content={content}
                trigger="click"
                placement="bottomLeft"
                overlayStyle={{ maxWidth: "none" }}
              >
                <SelectRowConfigButton
                  icon={<SettingOutlined />}
                  size="small"
                  type="primary"
                  ghost
                  className="hide-print"
                ></SelectRowConfigButton>
              </Popover>
            </SelectRowEmptyContainer>
          </td>
          {arr.map((el, indexTd) => {
            return <td key={indexTd}></td>;
          })}
        </tr>
      )}
    </>
  );
};

SelectRow.propTypes = {};

export default forwardRef(SelectRow);
