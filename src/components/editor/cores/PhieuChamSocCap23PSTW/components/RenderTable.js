import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { TR, TR_PHIEU_CAP1, TR_PHIEU_CAP1_PSHN } from "../constants";
import { cloneDeep, get, set } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { updateObject } from "utils";
import { Button, Col, message, Row } from "antd";
import CheckGroups from "../../CheckGroups";
import { useQueryString } from "hooks";
import { SVG } from "assets";
import PopoverSelect from "../../Components/PopoverSelect";
import Render<PERSON>hanDoan from "./RenderChanDoanDieuDuong";
import ImageSign from "../../ImageSign";
import { combineFields, MODE } from "utils/editor-utils";
import RenderBmi from "./RenderBmi";
import RenderTimThai from "./RenderTimThai";
import RenderNhapXuat from "./RenderNhapXuat";
import CopyPasteCol from "../../Components/CopyPasteCol";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import { useTranslation } from "react-i18next";
import { refConfirm } from "app";
import moment from "moment";
import RenderGhiChuBanGiao from "./RenderChiChuBanGiao";
import HeaderFormChamSocC2PSHN from "../../Components/HeaderFormChamSocC2PSHN";
import { PlusOutlined } from "@ant-design/icons";
import SelectRow from "./SelectRow";

const arr = new Array(4).fill(cloneDeep({}));

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  itemProps,
  form,
  onAddTenTimThai,
  dataCopy,
  setDataCopy,
  refModalSinhHieu,
  showIconAdd,
  handleAdd,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });
  const [rowSpanSinhTon, setRowSpanSinhTon] = useState(9);
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const [khoaChiDinhId] = useQueryString("khoaChiDinhId");

  const refSelectRow = useRef({});

  useEffect(() => {
    const row = 9 + (form.dsTheoDoi?.[0]?.dsTenTimThai?.length || 0);
    setRowSpanSinhTon(row);
  }, [form?.dsTheoDoi, itemProps.loaiPhieu]);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refChanDoan = useRef();
  const refValue = useRef();
  const refBmi = useRef();
  const refTongXuat = useRef();
  const refTongNhap = useRef();

  useEffect(() => {
    refValue.current = cloneDeep(item || {});
    setState({
      value: refValue.current,
    });
  }, [item]);

  const onChangeHuyetAp = (index) => (value) => {
    if (value.split("/").length - 1 !== 1) {
      message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 120/90 ");
      return;
    }
    const arr = value.split("/");
    const huyetApTamThu = +arr[0];
    const huyetApTamTruong = +arr[1];
    if (!refValue.current["dsChiTiet"][index]?.chiSoSong) {
      refValue.current["dsChiTiet"][index].chiSoSong = {
        chiDinhTuLoaiDichVu: 201,
        khoaChiDinhId: form.khoaChiDinhId,
        nbDotDieuTriId: form.nbDotDieuTriId,
      };
    }
    if (huyetApTamThu < huyetApTamTruong) {
      message.error("Huyết áp tâm thu cần lớn hơn huyết áp tâm trương");
    } else {
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamThu`,
        huyetApTamThu
      );
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamTruong`,
        huyetApTamTruong
      );
    }
  };

  const onChangeInput = useCallback(
    (key, idx) => (value) => {
      const keySplit = (key || "").split(".");
      let key1, key2;
      key1 = keySplit[0];
      if (keySplit.length > 1) {
        key2 = keySplit[1];
      }
      if (
        !key.includes("thoiGianThucHien") &&
        !refValue.current.dsChiTiet[idx]?.thoiGianThucHien
      ) {
        message.error("Vui lòng nhập thời gian thực hiện!");
      }
      if (key.includes("huyetAp")) {
        onChangeHuyetAp(idx)(value);
      } else {
        if (!key2) {
          updateObject(refValue.current.dsChiTiet[idx], key1, value);
        } else {
          if (!refValue.current.dsChiTiet[idx][key1]) {
            if (key1.includes("chiSoSong")) {
              refValue.current.dsChiTiet[idx][key1] = {
                chiDinhTuLoaiDichVu: 201,
                khoaChiDinhId: form.khoaChiDinhId,
                nbDotDieuTriId: form.nbDotDieuTriId,
              };
            } else {
              refValue.current.dsChiTiet[idx][key1] = {};
            }
          }
          if (get(refValue.current.dsChiTiet[idx], `${key1}`)) {
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          } else {
            refValue.current.dsChiTiet[idx][key1] = {};
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          }
        }
      }
      refChanDoan.current.changeKey(key, idx, value);
      if (key === "thoiGianThucHien") {
        const dsChiTietExitTG = refValue.current.dsChiTiet.filter(
          (el) => el.thoiGianThucHien
        );
        const dsChiTietNotExitTG = refValue.current.dsChiTiet.filter(
          (el) => !el.thoiGianThucHien
        );
        dsChiTietExitTG.sort(
          (a, b) =>
            (a.thoiGianThucHien ? new Date(a.thoiGianThucHien) : new Date()) -
            (b.thoiGianThucHien ? new Date(b.thoiGianThucHien) : new Date())
        );
        refValue.current.dsChiTiet = dsChiTietExitTG.concat(dsChiTietNotExitTG);

        setState({
          value: cloneDeep(refValue.current),
        });
      }
      if (["tmNgoaiBien", "tmTrungTam"].includes(key)) {
        setState({
          value: cloneDeep(refValue.current),
        });
      }

      if (["chiSoSong.canNang", "chiSoSong.chieuCao"].includes(key)) {
        const { canNang, chieuCao } = get(
          refValue.current,
          `dsChiTiet[${idx}].chiSoSong`,
          {}
        );
        if (canNang && chieuCao) {
          const bmi = canNang / ((chieuCao / 100) * (chieuCao / 100));
          refValue.current["dsChiTiet"][idx]["chiSoSong"]["bmi"] =
            bmi.toFixed(2);
          refBmi.current.setValue(refValue.current);
        }
      }
      if (key.includes("dichNhap2")) {
        const { thuocDichTruyen, chePhamMau, anUong, khac } = get(
          refValue.current,
          `dsChiTiet[${idx}].dichNhap2`,
          {}
        );
        const tongNhap =
          (thuocDichTruyen ? parseFloat(thuocDichTruyen) : 0) +
          (chePhamMau ? parseFloat(chePhamMau) : 0) +
          (anUong ? parseFloat(anUong) : 0) +
          (khac ? parseFloat(khac) : 0);
        set(refValue.current.dsChiTiet[idx], "dichNhap2.tongNhap", tongNhap);

        refTongNhap.current.setValue(refValue.current);
      }

      if (
        key.includes("dichXuat2") ||
        ["soLuongNuocTieu", "dichDanLuu"].includes(key)
      ) {
        const { soLuongNuocTieu, dichDanLuu } = get(
          refValue.current,
          `dsChiTiet[${idx}]`,
          {}
        );
        const dichXuat2 = get(
          refValue.current,
          `dsChiTiet[${idx}].dichXuat2`,
          {}
        );
        const khac = dichXuat2?.khac || 0;
        if (soLuongNuocTieu || dichDanLuu) {
          set(refValue.current.thietLap, `dichXuat`, [
            "soLuongNuocTieu",
            "dichDanLuu",
          ]);
          refSelectRow.current["dichXuat"].setValue(
            cloneDeep(refValue.current)
          );
        }
        const tongXuat =
          (soLuongNuocTieu ? parseFloat(soLuongNuocTieu) : 0) +
          (dichDanLuu ? parseFloat(dichDanLuu) : 0) +
          (khac ? parseFloat(khac) : 0);
        set(refValue.current.dsChiTiet[idx], "dichXuat2.tongXuat", tongXuat);

        refTongXuat.current.setValue(refValue.current);
      }
      formChangeValue();
    },
    [refValueForm, form, Object.keys(formChange || {}).length, tableIndex]
  );

  const formChangeValue = () => {
    const indexKhung = refValueForm.findIndex((el) => el.stt === tableIndex);
    refValueForm[indexKhung] = refValue.current;
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
  };

  const onChangeValueChung = useCallback(
    (key) => (value) => {
      formChange[key](value);
    },
    [Object.keys(formChange || {}).length]
  );

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    if (dataCopy.chiSoSong) {
      delete dataCopy.chiSoSong.id;
      delete dataCopy.chiSoSong.createdAt;
      delete dataCopy.chiSoSong.createdBy;
      delete dataCopy.chiSoSong.nguoiThucHienId;
      delete dataCopy.chiSoSong.updatedAt;
      delete dataCopy.chiSoSong.updatedBy;
    }
    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dataCurrent = cloneDeep(refValue.current.dsChiTiet[idx]);
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);

    refValue.current.dsChiTiet[idx].stt = dataCurrent.stt;
    setState({
      value: cloneDeep(refValue.current),
    });
    formChangeValue();
  };

  const handleDelete = (idx) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSong?.id) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSong?.id
              );
            }
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            formChangeValue();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const onSelectSinhHieu = (idx) => () => {
    refModalSinhHieu?.current &&
      refModalSinhHieu?.current.show(
        {
          nbDotDieuTriId: form?.nbDotDieuTriId || nbDotDieuTriId,
          khoaChiDinhId: form?.khoaChiDinhId || khoaChiDinhId,
        },
        (sinhHieu) => {
          const ngayThucHien = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
            : "";
          const thoiGian = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
            : "";
          const allChiTiet = refValueForm
            .map((el, index) => {
              return el.dsChiTiet;
            })
            .flat(Infinity);
          let chiTietOld = allChiTiet.find(
            (el) =>
              el.chiSoSong?.thoiGianThucHien &&
              moment(el.chiSoSong?.thoiGianThucHien).format(
                "DD/MM/YYYY HH:mm"
              ) === moment(sinhHieu.thoiGianThucHien).format("DD/MM/YYYY HH:mm")
          );
          if (chiTietOld) {
            message.error(
              t("editor.nbDaTonTaiSinhHieu", {
                time: moment(chiTietOld.chiSoSong.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
              })
            );
          } else {
            refValue.current.dsChiTiet[idx]["chiSoSong"] = sinhHieu;
            refValue.current.dsChiTiet[idx].ngayThucHien = ngayThucHien;
            refValue.current.dsChiTiet[idx].thoiGian = thoiGian;
            refValue.current.dsChiTiet[idx].thoiGianThucHien = moment(
              sinhHieu.thoiGianThucHien
            ).format("YYYY-MM-DD HH:mm:00");
            setState({
              value: cloneDeep(refValue.current),
            });
            formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
          }
        }
      );
  };

  const listTr = useMemo(() => {
    if (itemProps.loaiPhieu === 1) {
      return TR;
    } else {
      if (itemProps.vienPSHN) {
        return TR_PHIEU_CAP1_PSHN;
      } else {
        return TR_PHIEU_CAP1;
      }
    }
  }, [itemProps]);
  const render = useMemo(() => {
    return (
      <>
        {itemProps.vienPSHN ? (
          <HeaderFormChamSocC2PSHN
            form={form}
            mode={mode}
            formChange={formChange}
            tableIndex={tableIndex}
            loaiPhieu={itemProps.loaiPhieu}
          ></HeaderFormChamSocC2PSHN>
        ) : (
          <div className="header-form">
            <Row className="flex">
              <Col span={6} className="content-title-left">
                <div className="center">{form?.tieuDeTrai1}</div>
                <div className="center">
                  <b>{form?.tieuDeTrai2}</b>
                </div>
                <div className="center">{form?.tenKhoaNb}</div>
              </Col>
              <Col span={12} className="title-center">
                <div className="ten-phieu">PHIẾU THEO DÕI VÀ CHĂM SÓC</div>
                <div>
                  {itemProps.loaiPhieu == 2 ? "(Cấp 1)" : "(Cấp 2 - 3)"}{" "}
                </div>
                <DeboundInput
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ width: 100 }}
                  label="Tờ số  : "
                  onChange={onChangeInput("toSo")}
                  value={item.stt}
                  inputNumber={true}
                  readOnly={true}
                />
              </Col>
              <Col span={6} className="title-right">
                <div>Số vào viện: {form?.maBenhAn}</div>
                <div>Mã LK: {form?.maHoSo}</div>
              </Col>
            </Row>
            <Row className="name-patient">
              <Col span={12}>
                Họ tên người bệnh: ​ <b>{form.tenNb}</b>
              </Col>
              <Col span={4}>Tuổi: {form.tuoi}</Col>
              <Col span={8}>
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Giới tính: Nam",
                          value: 1,
                        },
                        {
                          label: "Nữ",
                          value: 2,
                        },
                      ],
                      fieldName: "gioiTinh",
                      readOnly: true,
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10, minWidth: 200 }}>
                  Phòng: {form.tenPhong}
                </span>
                <span>Giường: {form.soHieuGiuong}</span>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10 }}>
                  Chẩn đoán: {form.tenCdChinh}
                </span>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="flex">
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Tiền sử dị ứng: ",
                          value: 1,
                        },
                        {
                          label: "Chưa ghi nhận",
                          value: 2,
                        },
                      ],
                      fieldName: "tienSuDiUng",
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
                <DeboundInput
                  size={"small"}
                  value={get(form, "ghiRo", "")}
                  onChange={onChangeValueChung("ghiRo")}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ flex: 1 }}
                  label="Có, Dị nguyên: "
                />
              </Col>
            </Row>
          </div>
        )}

        <table style={{ marginTop: 20 }}>
          <tbody>
            {listTr.map((item, index) => {
              const keySplit = (item?.key || "").split(".");
              let key1, key2;
              key1 = keySplit[0];
              if (keySplit.length > 1) {
                key2 = keySplit[1];
              }

              if (item.key == "dsVanDe") {
                return (
                  <RenderChanDoan
                    ref={refChanDoan}
                    onChangeInput={onChangeInput}
                    data={state.value}
                    refValue={refValue}
                    formChangeValue={formChangeValue}
                    arr={arr}
                  />
                );
              } else if (item.key === "chiSoSong.bmi") {
                return (
                  <tr>
                    <td colSpan={2}>BMI</td>
                    <RenderBmi ref={refBmi} arr={arr} value={state.value} />
                  </tr>
                );
              } else if (item.key === "timThai") {
                return (
                  <RenderTimThai
                    ref={refBmi}
                    value={state.value}
                    formChangeValue={formChangeValue}
                    refValue={refValue}
                    onAddTenTimThai={onAddTenTimThai}
                    refValueForm={refValueForm}
                    tableIndex={tableIndex}
                    formChange={formChange}
                    setRowSpanSinhTon={setRowSpanSinhTon}
                    rowSpanSinhTon={rowSpanSinhTon}
                    arr={arr}
                  />
                );
              } else if (
                ["dichXuat2.tongXuat", "dichNhap2.tongNhap"].includes(item.key)
              ) {
                return (
                  <tr>
                    <td colSpan={3}>
                      <b>
                        {item.key === "dichXuat2.tongXuat"
                          ? "Tổng xuất (ml)"
                          : "Tổng nhập (ml)"}
                      </b>
                    </td>
                    <RenderNhapXuat
                      ref={
                        item.key === "dichXuat2.tongXuat"
                          ? refTongXuat
                          : refTongNhap
                      }
                      value={state.value}
                      formChangeValue={formChangeValue}
                      refValue={refValue}
                      refValueForm={refValueForm}
                      tableIndex={tableIndex}
                      formChange={formChange}
                      keyItem={item.key}
                    />
                  </tr>
                );
              } else if (item.key === "ghiChuBanGiao") {
                return (
                  <RenderGhiChuBanGiao
                    arr={arr}
                    value={state.value}
                    formChangeValue={formChangeValue}
                  />
                );
              } else if (item.listRow) {
                return (
                  <SelectRow
                    itemRow={item}
                    data={state.value}
                    formChangeValue={formChangeValue}
                    refValue={refValue}
                    onAddTenTimThai={onAddTenTimThai}
                    refValueForm={refValueForm}
                    tableIndex={tableIndex}
                    formChange={formChange}
                    arr={arr}
                    onChangeInput={onChangeInput}
                    ref={(ref) => {
                      refSelectRow.current[item.key] = ref;
                    }}
                  ></SelectRow>
                );
              } else {
                return (
                  <tr key={`${item.key}_${index}`}>
                    {item.label1 && (
                      <td
                        rowSpan={
                          item.key === "chiSoSong.mach"
                            ? rowSpanSinhTon
                            : item.rowSpan1 || 1
                        }
                        colSpan={item.colSpan1 || 1}
                        style={{ position: "relative" }}
                      >
                        {mode !== MODE.config && showIconAdd && !index && (
                          <Button
                            className="btn-add"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size={"small"}
                            type="primary"
                          ></Button>
                        )}
                        <b>{item.label1}</b>
                      </td>
                    )}
                    {item.label2 && (
                      <td
                        rowSpan={item.rowSpan2 || 1}
                        colSpan={item.colSpan2 || 1}
                        style={{ width: 50, minWidth: 50, maxWidth: 50 }}
                      >
                        {item.label2}
                      </td>
                    )}
                    {item.hint && (
                      <td
                        className="hint"
                        rowSpan={item.rowSpanHint || 1}
                        colSpan={item.colSpanHint || 1}
                        style={{ width: 100, minWidth: 100 }}
                      >
                        {(item.hint || []).map((item, index) => (
                          <span key={index}>{item}</span>
                        ))}
                      </td>
                    )}

                    {arr.map((el, idx) => {
                      let dataDroplist = cloneDeep(item.data);
                      const tmNgoaiBien = get(
                        refValue.current,
                        `dsChiTiet[${idx}].tmNgoaiBien`,
                        []
                      );
                      const tmTrungTam = get(
                        refValue.current,
                        `dsChiTiet[${idx}].tmTrungTam`,
                        []
                      );
                      if (
                        ["dsChamSoc"].includes(item.key) &&
                        ((tmTrungTam || []).some((x) => [2, 3].includes(x)) ||
                          (tmNgoaiBien || []).some((x) => [2, 3].includes(x)))
                      ) {
                        dataDroplist = dataDroplist.concat([
                          {
                            label: "Thông tráng catherter bằng DD NaCL0,9%",
                            value: 13,
                          },
                          {
                            label: "Đặt lại catheter",
                            value: 14,
                          },
                        ]);
                      }
                      return item.disable ? (
                        <td key={`${item.key}_${index}_${idx}`}></td>
                      ) : item.key == "ngayThucHien" ? (
                        <td
                          key={`${item.key}_${index}_${idx}`}
                          style={{
                            width: 150,
                            minWidth: 150,
                            maxWidth: 150,
                            position: "relative",
                          }}
                          className={`col-element`}
                        >
                          <CopyPasteCol
                            colIndex={idx}
                            handlePaste={hanldePaste(idx)}
                            handleCopy={handleCopy(idx)}
                            dataCopy={dataCopy}
                            handleDelete={handleDelete(idx)}
                            onSelectSinhHieu={onSelectSinhHieu(idx)}
                          />
                          <AppDatePicker
                            component={{
                              props: {
                                contentAlign: "center",
                                dateTimeFormat: "HH:mm D/M/Y",
                                fieldName: "value",
                                disableOnblur: true,
                                fontSize: 8,
                              },
                            }}
                            form={{
                              value: get(
                                state.value,
                                `dsChiTiet[${idx}].thoiGianThucHien`
                              ),
                            }}
                            mode={mode}
                            formChange={{
                              value: (e) => {
                                try {
                                  onChangeInput(`thoiGianThucHien`, idx)(e);
                                } catch (error) {}
                              },
                            }}
                          />
                        </td>
                      ) : item.type === "droplist" ? (
                        <td>
                          <PopoverSelect
                            data={dataDroplist}
                            value={get(
                              state.value,
                              `dsChiTiet[${idx}][${key1}]${
                                key2 ? `${key2}` : ""
                              }`
                            )}
                            onChangeValue={(e) => {
                              onChangeInput(item.key, idx)(e);
                            }}
                            isMultiple={!(item.mode == "onlyOne")}
                            trigger="click"
                            isShowSearch={
                              (Array.isArray(item.data) ? item.data : [])
                                .length > 15
                            }
                          />
                        </td>
                      ) : item.type == "sign" ? (
                        <td key={`${item.key}_${index}_${idx}`}>
                          <ImageSign
                            component={{
                              props: {
                                ...itemProps,
                                isMultipleSign: true,
                                viTri: get(
                                  state.value,
                                  `dsChiTiet[${idx}].stt`
                                ),
                                customText: "Ký",
                                dataSign: {
                                  id: form.id,
                                  soPhieu: form?.lichSuKy?.soPhieu,
                                  lichSuKyId: form?.lichSuKy?.id,
                                },
                              },
                            }}
                            form={{
                              ...combineFields(form),
                            }}
                          />
                        </td>
                      ) : (
                        <td key={`${item.key}_${index}_${idx}`}>
                          <DeboundInput
                            readOnly={false}
                            value={get(
                              state.value,
                              `dsChiTiet[${idx}]${key1}${
                                key2 ? `[${key2}]` : ""
                              }`
                            )}
                            onChange={onChangeInput(item.key, idx)}
                            type="multipleline"
                            lineHeightText={1.5}
                            fontSize={9}
                            minHeight={9 + 6}
                            markSpanRow={false}
                            contentAlign="center"
                            inputNumber={item.type === "number"}
                            typeNumber={"float"}
                          />
                        </td>
                      );
                    })}
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      </>
    );
  }, [
    state.value,
    onChangeValueChung,
    refValueForm,
    rowSpanSinhTon,
    dataCopy,
    tableIndex,
    listTr,
  ]);
  return render;
};

export default RenderTable;
