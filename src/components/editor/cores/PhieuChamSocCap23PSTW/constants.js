import { data } from "pages/thuNgan/hoaDonDienTu/TaoHoaDonNhieuNguoi/data";
import React from "react";

const convertArr = (arr, labelByKey) => {
  return arr.map((el, index) => ({
    label: el,
    value: index + 1,
    labelByKey: labelByKey || "label",
  }));
};
export const DS_THUC_HIEN_Y_LENH = [
  {
    label: "Theo dõi nhịp tim thai và cơn co tử cung bằng monitor sản khoa",
    value: 1,
  },
  { label: "Kh<PERSON>m, xác định và theo dõi các giai đoạn của chuyển dạ", value: 2 },
  { label: "Xử trí sa dày rau", value: 3 },
  { label: "Đỡ đẻ thường ngồi chồm", value: 4 },
  {
    label: "Thực hiện nghiệm pháp bong rau, đỡ rau, kiểm tra bánh rau",
    value: 5,
  },
  { label: "<PERSON><PERSON><PERSON> soát tử cung *", value: 6 },
  { label: "Cắt và khâu tăng sinh môn", value: 7 },
  { label: "Đón bé sau mổ", value: 8 },
  { label: "Kỹ thuật da kề (ngay sau đẻ, mổ đẻ)", value: 9 },
  { label: "Chèn gạc âm đạo cầm máu", value: 10 },
  { label: "Xử trí ban đầu bằng huyết sau sinh đường âm đạo", value: 11 },
  { label: "Khâu phục hồi rách âm đạo, tăng sinh môn độ 1, 2", value: 12 },
  { label: "Khâu phục hồi rách âm hộ", value: 13 },
  { label: "Xử trí vết rách có tử cung đơn thuần *", value: 14 },
  { label: "Theo dõi và truyền oxytocin trong chuyển dạ", value: 15 },
  { label: "Xử trí tích cực giai đoạn 3 cuộc chuyển dạ", value: 16 },
  { label: "Xử trí ban đầu những trường hợp bất thường trong đẻ", value: 17 },
  { label: "Chăm sóc, theo dõi vết khâu tăng sinh môn sau đẻ", value: 18 },
  { label: "Làm thuốc vết khâu tăng sinh môn nhiễm khuẩn", value: 19 },
  { label: "Làm thuốc tăng sinh môn sau đẻ", value: 20 },
  { label: "Làm thuốc âm hộ: âm đạo sau đẻ", value: 21 },
  { label: "Xoa bóp vú thông tuyến sữa sau sinh", value: 22 },
  { label: "Thực hiện y lệnh thuốc", value: 23 },
  { label: "Thực hiện y lệnh xét nghiệm", value: 24 },
  { label: "Thực hiện y lệnh chẩn đoán hình ảnh", value: 25 },
  { label: "Phụ giúp BS làm thủ thuật", value: 26 },
  { label: "Chuẩn bị người bệnh trước phẫu thuật", value: 27 },
  { label: "Thụt tháo", value: 28 },
  { label: "Làm sạch vùng da trước phẫu thuật", value: 29 },
  { label: "Thay băng vết thương, vết mổ chiều dài ≤ 15cm", value: 30 },
  {
    label: "Thay băng vết thương, vết mổ chiều dài từ trên 30 cm đến 50 cm*",
    value: 31,
  },
  {
    label: "Thay băng vết thương, vết mổ chiều dài trên 15cm đến 30 cm",
    value: 32,
  },
  {
    label: "Thay băng vết thương, vết mổ nhiễm trùng chiều dài dưới 15 cm",
    value: 33,
  },
  {
    label:
      "Hỗ trợ điều trị vết thương 15-20cm bằng tia Plasma áp dụng cho vết mổ thành bụng",
    value: 34,
  },
  {
    label:
      "Hỗ trợ điều trị vết thương dưới 5cm bằng tia Plasma áp dụng cho vết thương tầng sinh môn",
    value: 35,
  },
  { label: "Đặt ống thông tiểu lưu bằng quang", value: 36 },
  { label: "Đặt ống thông hậu môn", value: 37 },
  { label: "Đặt sonde dạ dày", value: 38 },
  { label: "Rút sonde các loại", value: 39 },
  { label: "Bơm rửa bàng quang", value: 40 },
  { label: "Làm thuốc âm hộ; âm đạo", value: 41 },
  { label: "Làm thuốc khâu tầng sinh môn nhiễm khuẩn", value: 42 },
  { label: "Đánh giá huyết áp", value: 43 },
  { label: "Đánh giá mạch", value: 44 },
  { label: "Đánh giá nhịp thở", value: 45 },
  { label: "Đánh giá tình trạng dinh dưỡng", value: 46 },
  { label: "Theo dõi thân nhiệt", value: 47 },
  { label: "Theo dõi huyết áp không xâm lấn bằng máy", value: 48 },
  { label: "Theo dõi SPO2 liên tục tại giường", value: 49 },
  { label: "Theo dõi SPO2 liên tục tại giường ≤ 8 giờ", value: 50 },
  { label: "Chăm sóc catheter tĩnh mạch trung tâm", value: 51 },
  { label: "Chăm sóc ống nội khí quản", value: 52 },
  { label: "Chăm sóc catheter tĩnh mạch ngoại vi", value: 53 },
  { label: "Chăm sóc vết loét", value: 54 },
  { label: "Gội đầu cho người bệnh tại giường", value: 55 },
  { label: "Đánh giá độ đau bằng các thang điểm", value: 56 },
  { label: "Cho người bệnh thở oxy qua mặt nạ hoặc qua sonde mũi.", value: 57 },
  { label: "Theo dõi áp lực tĩnh mạch trung tâm", value: 58 },
  { label: "Theo dõi điện tim cấp cứu tại giường liên tục ≤ 8 giờ", value: 59 },
  { label: "Truyền tĩnh mạch", value: 60 },
  { label: "Truyền tĩnh mạch qua máy", value: 61 },
  { label: "Truyền máu", value: 62 },
  { label: "Truyền thuốc chống ung thư đường tĩnh mạch", value: 63 },
  { label: "Tiêm bắp thuốc điều trị ung thư", value: 64 },
  { label: "Tiêm dưới da thuốc chống ung thư", value: 65 },
  { label: "Truyền thuốc chống ung thư qua catheter*", value: 66 },
  { label: "Truyền thuốc giảm đau bằng bơm tiêm điện*", value: 67 },
  { label: "Gội đầu khô cho người bệnh tại giường bằng mũ gội khô", value: 68 },
  { label: "Tắm khô cho người bệnh tại giường bằng găng tay khô", value: 69 },
  { label: "Đặt catheter tĩnh mạch ngoại biên", value: 70 },
  { label: "Chăm sóc dẫn lưu", value: 71 },
  { label: "Kỹ thuật lấy máu tĩnh mạch (một lần chọc kim qua da)", value: 72 },
  { label: "Kỹ thuật đặt ống thông tiểu một lần", value: 73 },
  { label: "Ghi điện tim thường", value: 74 },
  { label: "Giảm đau chuyển dạ bằng gây tê ngoài màng cứng", value: 75 },
  { label: "Giảm đau sau mổ bằng gây tê ngoài màng cứng", value: 76 },
  { label: "Sử dụng túi đo máu sau đẻ", value: 77 },
  {
    label: "Quy trình ngâm thuốc y học cổ truyền bộ phận (Ngâm chân)",
    value: 78,
  },
  { label: "Tháo dụng cụ tử cung", value: 79 },
  { label: "Phá thai từ 7-12 tuần bằng phương pháp hút chân không", value: 80 },
  {
    label: "Phá thai đến hết 7 tuần bằng phương pháp hút chân không",
    value: 81,
  },
  { label: "Đặt dụng cụ tử cung (Chứa đồng hoặc nội tiết)", value: 82 },
  { label: "Cấy hoặc tháo thuốc tránh thai (1 nang; nhiều nang)", value: 83 },
  { label: "Xông hơi tầng sinh môn sau sinh", value: 84 },
  { label: "Chườm nóng", value: 85 },
  { label: "Chườm lạnh", value: 86 },
];

export const DS_THUC_HIEN_CHAM_SOC = [
  { label: "Hướng dẫn vận động", value: 1 },
  { label: "Hướng dẫn vận động sớm sau 6 giờ", value: 2 },
  { label: "Hướng dẫn SP ăn thức ăn để tiêu hóa táo bón", value: 3 },
  {
    label:
      "Hướng dẫn NB và người nhà phát hiện mục số dấu hiệu bất thường xảy ra sau PT",
    value: 4,
  },
  { label: "Hướng dẫn sp vệ sinh bộ phận SD", value: 5 },
  { label: "Cung cấp các kiến thức cần thiết cho NB", value: 6 },
  {
    label: "HD người bệnh nằm nghỉ hoàn toàn tại nơi yên tĩnh, thoáng, ấm",
    value: 7,
  },
  {
    label: "Giảm lo lắng, động viên người bệnh yên tâm tin tưởng điều trị",
    value: 8,
  },
  { label: "Giảm nguy cơ chân thương, ngã: HD kẻo thành chân chảy", value: 9 },
  { label: "Hướng dẫn thủ tục ra viện", value: 10 },
  {
    label: "Tư vấn cho NB về gia đình những thủ thuật có thể làm cho NB",
    value: 11,
  },
  { label: "Hướng dẫn thay đổi tư thế", value: 12 },
];

export const DS_DANH_GIA = [
  { label: "NB ổn định", value: 1 },
  { label: "NB cần theo dõi dấu sát", value: 2 },
  { label: "Thực hiện y lệnh thuốc an toàn", value: 3 },
  { label: "Thực hiện thủ thuật an toàn", value: 4 },
  { label: "NB nắm được kiến thức", value: 5 },
  { label: "NB yên tâm", value: 6 },
  { label: "NB ngủ được", value: 7 },
  { label: "NB đau đớn", value: 8 },
];
export const DS_BAN_GIAO = [
  { label: "Theo dõi DHST", value: 1 },
  { label: "Theo dõi dấu hiệu đau bụng, ra máu", value: 2 },
  { label: "Theo dõi sản dịch: màu, mùi, số lượng", value: 3 },
  { label: "TD lượng dịch vào, ra của NB", value: 4 },
  { label: "Theo dõi NB có nôn không, có bị trung, đại tiện", value: 5 },
  {
    label: "Theo dõi vết khâu tăng sinh môn: tình trạng chân tay đỏ, chảy dịch",
    value: 6,
  },
  {
    label: "Theo dõi toàn trạng và các dấu hiệu nhiễm khuẩn toàn thân",
    value: 7,
  },
  { label: "Theo dõi vết khâu tăng sinh môn", value: 8 },
  {
    label:
      "Theo dõi sự hồi phục từ: kích thước, mật độ, khả năng vận động, mức độ đau",
    value: 9,
  },
  { label: "Theo dõi lượng nước tiểu 24h/ngày", value: 10 },
];
export const DS_VAN_DE = [
  {
    stt: 1,
    key: "1 (Da, Niêm mạc)",
  },
  {
    stt: 2,
    key: "2 (Tri giác)",
  },
  {
    stt: 3,
    key: "3 (Hô hấp)",
  },
  {
    stt: 4,
    key: "4 (Tuần hoàn)",
  },
  {
    stt: 5,
    key: "5 (Tinh thần)",
  },
  {
    stt: 6,
    key: "6 (Vận động)",
  },
  {
    stt: 7,
    key: "7 (Vệ sinh cá nhân)",
  },
  {
    stt: 8,
    key: "8 (Giấc ngủ)",
  },
  {
    stt: 9,
    key: "9 (Tiêu hóa)",
  },
  {
    stt: 10,
    key: "10 (Tiết niệu)",
  },
  {
    stt: 11,
    key: "11 (Dinh dưỡng)",
  },
  {
    stt: 12,
    key: "12 (Trước đẻ)",
  },
  {
    stt: 13,
    key: "13 (Sau đẻ)",
  },
  {
    stt: 14,
    key: "14 (Trước PT)",
  },
  {
    stt: 15,
    key: "15 (Sau phẫu thuật)",
  },
  {
    stt: 16,
    key: "16 (Đau)",
  },
  {
    stt: 17,
    key: "17 (Nguy cơ té ngã)",
  },
  {
    stt: 18,
    key: "18 (Loét do tỳ đè)",
  },
  {
    stt: 19,
    key: "19 (GDSK)",
  },
];
export const DS_CHUAN_DOAN_BY_VAN_DE = {
  1: {
    key: "daNiemMac2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB có chỉ định truyền máu liên quan bệnh lý",
            id: 1,
          },
        ],
        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label: "Da tổn thương liên quan vệ sinh kém",
            id: 2,
          },
        ],
        minValue: 5,
        maxValue: 8,
      },
      {
        listNoiDung: [
          {
            label: "Da tổn thương liên quan tì đè",
            id: 3,
          },
        ],
        minValue: 9,
        maxValue: 12,
      },
      {
        listNoiDung: [
          {
            label: "NB có dấu hiệu liên quan  bệnh lý ",
            id: 4,
          },
          {
            label: "Da tổn thương liên quan bệnh lý da",
            id: 5,
          },
        ],

        minValue: 13,
        maxValue: 18,
      },
      {
        listNoiDung: [
          {
            label: "Phản ứng liên quan dị ứng",
            id: 6,
          },
        ],
        minValue: 19,
        maxValue: 21,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Thực hiện truyền máu an toàn",
            id: 1,
          },
        ],
        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label: "Da trở lại bình thường, Da sạch sẽ",
            id: 2,
          },
        ],

        minValue: 5,
        maxValue: 21,
      },
    ],

    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Thực hiện các y lệnh cận lâm sàng",
            id: 1,
          },
          {
            label: "Theo dõi toàn trạng, DHST trước, trong và sau truyền máu",
            id: 2,
          },
          {
            label: "Thực hiện y lệnh truyền máu",
            id: 3,
          },
          {
            label:
              "Theo dõi sát phát hiện các dấu hiệu bất thường: sốt, lạnh run",
            id: 4,
          },
          {
            label:
              "Theo dõi sát phát hiện các dấu hiệu bất thường: sốt, lạnh run, khó thở, nổi mẫn đỏ",
            id: 5,
          },
          {
            label:
              "Hướng dẫn NB các dấu hiệu bất thường cần báo ngay: ngứa, nổi mẩn, khó thở, run, lạnh.",
            id: 6,
          },
        ],
        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label: "Vệ sinh da thường xuyên",
            id: 7,
          },
          {
            label: "Dùng kem chống hăm",
            id: 8,
          },
          {
            label: "Giữ da khô thoáng",
            id: 9,
          },
          {
            label: "Vệ sinh quần áo và làm sạch giường bệnh, phòng bệnh",
            id: 10,
          },
          {
            label: "HD NB tránh sử dụng các loại hóa chấ",
            id: 11,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 12,
          },
        ],
        key: "daNiemMac",
        id: 2,
        minValue: 5,
        maxValue: 8,
      },
      {
        listNoiDung: [
          {
            label: "Vệ sinh da thường xuyên",
            id: 13,
          },
          {
            label: "Giữ da khô thoáng",
            id: 14,
          },
          {
            label: "Vệ sinh quần áo và làm sạch giường bệnh, phòng bệnh",
            id: 15,
          },
          {
            label: "Thay đổi tư thế 2h/lần",
            id: 16,
          },
          {
            label:
              "Xoa bóp phục hổi lưu thông tuần hoàn vùng da kém nuôi dưỡng",
            id: 17,
          },

          {
            label: "Thực hiện y lệnh thuốc",
            id: 18,
          },
          {
            label: "Dùng các phương tiện chống loét",
            id: 19,
          },
        ],
        key: "daNiemMac",

        minValue: 9,
        maxValue: 12,
      },
      {
        listNoiDung: [
          {
            label: "Vệ sinh da thường xuyên",
            id: 20,
          },
          {
            label: "Giữ da khô thoáng",
            id: 21,
          },
          {
            label: "Thực hiện các y lện cận lâm sàng",
            id: 22,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 23,
          },
        ],

        minValue: 13,
        maxValue: 18,
      },
      {
        listNoiDung: [
          {
            label: "Thực hiện các y lệnh cận lâm sàng",
            id: 24,
          },
          {
            label: "Theo dõi toàn trạng, DHST",
            id: 25,
          },
          {
            label:
              "Theo dõi sát phát hiện các dấu hiệu bất thường: sốt, lạnh run, khó thở, nổi mẫn đỏ",
            id: 26,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 27,
          },
        ],

        minValue: 19,
        maxValue: 21,
      },
    ],
  },
  2: {
    key: "triGiac2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Rối loạn tri giác liên quan đến bệnh lý",
            id: 1,
          },
        ],

        minValue: 3,
        maxValue: 9,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Theo dõi sát diễn biến tri giác",
            id: 1,
          },
          {
            label: "Đảm bảo an toàn phòng chống té ngã",
            id: 2,
          },
        ],
        minValue: 3,
        maxValue: 9,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Theo dõi DHST ",
            id: 1,
          },
          {
            label: "Đảm bảo thông đường thở và hô hấp hiệu quả",
            id: 2,
          },
          {
            label: "Đảm bảo an toàn phòng chống té ngã",
            id: 3,
          },
          {
            label: "Theo dõi tình trạng chóng mặt",
            id: 4,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 5,
          },
          {
            label: "Hỗ trợ NB di chuyển đi lại phòng ngừa nguy cơ té ngã",
            id: 6,
          },
          {
            label:
              "Đảm bảo cơ sở vật chất trong phòng bệnh giảm nguy cơ té ngã",
            id: 7,
          },
        ],
        minValue: 3,
        maxValue: 9,
      },
    ],
  },
  3: {
    key: "khoTho2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Khó thở liên mức độ vừa quan đến bệnh lý",
            id: 1,
          },
          {
            label: "Suy hô hấp liên quan đến bệnh lý",
            id: 2,
          },
        ],
        minValue: 1,
        maxValue: 22,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB giảm hết viêm hô hấp",
            id: 1,
          },
          {
            label: "Người bệnh giảm hết ho",
            id: 2,
          },
          {
            label: "Người bệnh thở hiệu quả",
            id: 3,
          },
          {
            label: "Người bệnh được cung cấp oxy đầy đủ",
            id: 4,
          },
          {
            label: "Người bệnh hô hấp hiệu quả",
            id: 5,
          },
        ],

        minValue: 1,
        maxValue: 22,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Theo dõi đánh giá tình trạng ho",
            id: 1,
          },
          {
            label: "Theo dõi tình trạng hô hấp của NB",
            id: 2,
          },
          {
            label: "Theo dõi sát DHST",
            id: 3,
          },
          {
            label: "Hướng dẫn NB súc họng vệ sinh miệng 3 lần/ngày",
            id: 4,
          },
          {
            label: "Cho NB thở oxy theo chỉ định",
            id: 5,
          },
          {
            label: "Cho NB nằm tư thế đầu cao 30°",
            id: 6,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 7,
          },
          {
            label: "Theo dõi tình trạng hô hấp của NB",
            id: 8,
          },
          {
            label: "Theo dõi sát DHST",
            id: 9,
          },
          {
            label: "Cho NB thở oxy theo chỉ định",
            id: 10,
          },
          {
            label: "Cho NB nằm tư thế đầu cao 30°",
            id: 11,
          },
          {
            label: "Thở máy theo y lệnh",
            id: 12,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 13,
          },
          {
            label: "Theo dõi sát các thông số máy thở ",
            id: 14,
          },
        ],
        minValue: 1,
        maxValue: 22,
      },
    ],
  },
  4: {
    key: "tuanHoan2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB tăng/hạ huyết áp do bệnh lý",
            id: 1,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "Rối loạn nhịp do bệnh lý",
            id: 2,
          },
        ],
        minValue: 6,
        maxValue: 11,
      },
      {
        listNoiDung: [
          {
            label: "Phù liên quan đến bệnh lý",
            id: 3,
          },
        ],
        minValue: 12,
        maxValue: 17,
      },
      {
        listNoiDung: [
          {
            label: "Đau ngực do tình trạng bệnh lý",
            id: 4,
          },
        ],
        minValue: 18,
        maxValue: 22,
      },
      {
        listNoiDung: [
          {
            label: "Xuất huyết liên quan bệnh lý/dùng thuốc",
            id: 5,
          },
        ],
        minValue: 23,
        maxValue: 26,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Huyết áp ổn định ",
            id: 1,
          },
          {
            label: "Kiểm soát HA",
            id: 2,
          },
          {
            label: "Phát hiện sớm các nguy cơ",
            id: 3,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "Mạch ổn định ",
            id: 4,
          },
          {
            label: "Kiểm soát mạch trong giới hạn bình thường",
            id: 5,
          },
          {
            label: "Phát hiện sớm các nguy cơ",
            id: 6,
          },
        ],
        minValue: 6,
        maxValue: 11,
      },
      {
        listNoiDung: [
          {
            label: "Giảm /hết phù",
            id: 7,
          },
          {
            label: "Kiểm soát tình trạng phù",
            id: 8,
          },
        ],
        minValue: 12,
        maxValue: 17,
      },
      {
        listNoiDung: [
          {
            label: "Giảm đau ngực",
            id: 9,
          },
        ],
        minValue: 18,
        maxValue: 22,
      },
      {
        listNoiDung: [
          {
            label: "Kiểm soát tình trạng xuất huyết",
            id: 10,
          },
        ],
        minValue: 23,
        maxValue: 26,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Đặt NB tư thế nằm đầu bằng",
            id: 1,
          },
          {
            label: "Theo dõi DHST",
            id: 2,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 3,
          },
          {
            label: "Giải thích, tư vấn, chăm sóc tâm l",
            id: 4,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "Theo dõi DHST",
            id: 5,
          },
          {
            label: "Thực hiện y lệnh thuốc ",
            id: 6,
          },
          {
            label: "Giải thích, tư vấn, chăm sóc tâm lý",
            id: 7,
          },
          {
            label: "Hướng dẫn chế độ nghỉ ngơi, vận động",
            id: 8,
          },
          {
            label: "Theo dõi tác dụng thuốc vận mạch",
            id: 9,
          },
        ],
        minValue: 6,
        maxValue: 11,
      },
      {
        listNoiDung: [
          {
            label: "Theo dõi mức độ tưới máu chi",
            id: 10,
          },
          {
            label: "HD NB mang tất y khoa trong trường hợp NB suy tĩnh mạch",
            id: 11,
          },
          {
            label:
              "Hạn chế đặt đường truyền, lấy máu chi phù, không tạo áp lực, garo",
            id: 12,
          },
          {
            label:
              "Vận động chi, kê chân cao, xoay trở NB mỗi 2 giờ Theo dõi nước tiểu 24h",
            id: 13,
          },
          {
            label: "HD chế độ ăn phù hợp tình trạng bệnh lý",
            id: 14,
          },
        ],
        minValue: 12,
        maxValue: 17,
      },
      {
        listNoiDung: [
          {
            label: "Thực hiện y lệnh cận lâm sàng…",
            id: 15,
          },
          {
            label: "Thực hiện y lệnh thuốc…",
            id: 16,
          },
          {
            label: "Hướng dẫn nghỉ ngơi, vận động",
            id: 17,
          },
        ],
        minValue: 18,
        maxValue: 22,
      },
      {
        listNoiDung: [
          {
            label: "Hạn chế vận động, tránh va chạm",
            id: 18,
          },
          {
            label: "Thực hiện xét nghiệm theo y lệnh",
            id: 19,
          },
          {
            label: "Theo dõi các dấu hiệu xuất huyết nội tại các cơ quan khác",
            id: 20,
          },
          {
            label: "Hướng dẫn NB và NN các dấu hiệu chảy máu",
            id: 21,
          },
        ],
        minValue: 23,
        maxValue: 26,
      },
    ],
  },
  5: {
    key: "tinhThan2",
    dsChanDoan: [],
    dsKeHoach: [],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Theo dõi NB",
            id: 1,
          },
          {
            label: "Thực hiện thuốc theo y lệnh",
            id: 2,
          },
          {
            label: "Báo BS khi phát hiện dấu hiệu bất thường",
            id: 3,
          },
        ],
        minValue: 1,
        maxValue: 2,
      },
      {
        listNoiDung: [
          {
            label: "Thực hiện thuốc an thần theo y lệnh (nếu có)",
            id: 4,
          },
          {
            label:
              "Theo dõi phát hiện kịp thời tác dụng phụ của thuốc (nếu có) tư vấn, giải thích các vấn đề tâm lý, băn khoăn của NB giúp NB giảm lo lắng",
            id: 5,
          },
        ],
        minValue: 3,
        maxValue: 9,
      },
    ],
  },
  6: {
    key: "vanDong",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Người bệnh bị hạn chế khả năng đi lại",
            id: 1,
          },
        ],
        minValue: 2,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "Người bệnh có chỉ định hạn chế đi lại",
            id: 2,
          },
        ],
        minValue: 6,
        maxValue: 7,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Người bệnh có thể tự chăm sóc cá nhân, đi lại quanh giường",
            id: 1,
          },
        ],
        minValue: 2,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "NB thực hiện đúng hướng dẫn",
            id: 2,
          },
          {
            label: "Đảm bảo an toàn phòng chống té ngã",
            id: 3,
          },
        ],
        minValue: 6,
        maxValue: 7,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "TD các hoạt động sinh hoạt hằng ngày",
            id: 1,
          },
          {
            label: "Hỗ trợ NB trong sinh hoạt",
            id: 2,
          },
          {
            label: "Hỗ trợ người bệnh khi cần di chuyển ",
            id: 3,
          },
          {
            label: "Theo dõi và chăm sóc phòng ngừa té ngã",
            id: 4,
          },
          {
            label: "HD NB tập vận động",
            id: 5,
          },
        ],
        minValue: 2,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "HD NB nghỉ ngơi tại giường, hạn chế vận động",
            id: 6,
          },
          {
            label: "Hỗ trợ NB trong sinh hoạt",
            id: 7,
          },
          {
            label: "Hỗ trợ người bệnh khi cần di chuy",
            id: 8,
          },
          {
            label: "Theo dõi và chăm sóc phòng ngừa té ngã",
            id: 9,
          },
        ],
        minValue: 6,
        maxValue: 7,
      },
    ],
  },
  7: {
    key: "veSinhCaNhan2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB không tự vệ sinh cá nhân được",
            id: 1,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label:
              "Giúp NB không bị loét và hăm bộ phận sinh dục, không nhiễm trùng tiểu",
            id: 1,
          },
          {
            label: "NB đảm bảo vệ sinh sạch sẽ tránh nhiễm trùng vết mổ",
            id: 2,
          },
          {
            label: "NB Xoay trở thay tã tránh hăm tã và tổn thương da.",
            id: 3,
          },
          {
            label: "NB Xoay trở thay tã tránh hăm tã và tổn thương da.",
            id: 4,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Vệ sinh các nhân tắm, gội đầu cho NB trước phẫu thuật",
            id: 1,
          },
          {
            label:
              "Hướng dẫn, hỗ trợ NB vệ sinh cá nhân, vệ sinh da, tắm gội đầu",
            id: 2,
          },
          {
            label: "Vệ sinh bộ phận sinh dục ngày 2 lần",
            id: 3,
          },
          {
            label: "Vệ sinh da toàn thân",
            id: 4,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
  },
  8: {
    key: "giacNgu2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Mất ngủ do bệnh lý",
            id: 1,
          },
          {
            label: "Lo lắng căng thẳng, stress tâm lý.",
            id: 2,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Giúp người bệnh dễ đi vào giấc ngủ và ngủ sâu giấc hơn",
            id: 1,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Tư vấn NB thay đổi lối sống",
            id: 1,
          },
          {
            label: "Tạo môi trường phòng ngủ yên tĩnh, thoáng mát",
            id: 2,
          },
          {
            label: " Tránh căng thẳng trước khi ngủ ",
            id: 3,
          },
          {
            label:
              "Hạn chế ngủ nhiều vào ban ngày, chỉ ngủ trưa ngắn tầm 30 phút đến 1 giờ",
            id: 4,
          },
          {
            label: "Duy trì thói quen ngủ – thức đúng giờ",
            id: 5,
          },
          {
            label: "Không ăn no muộn vào buổi tối trước khi ngủ",
            id: 6,
          },
          {
            label:
              "Hạn chế việc sử dụng các chất kích thích như trà, cà phê, rượu, thuốc lá,… vào buổi tối.",
            id: 7,
          },
          {
            label:
              "Tập thể dục thường xuyên khoảng 30 phút khoảng 1 giờ trước ngủ.",
            id: 8,
          },
          {
            label: "Ngủ và thức dậy đúng giờ mỗi ngày",
            id: 9,
          },
          {
            label: "Báo bác sĩ để bổ sung thêm các điều trị cần thiết",
            id: 10,
          },
          {
            label: "Tập thể dục thường xuyên và đều đặn mỗi ngày",
            id: 11,
          },
          {
            label:
              "Tránh ăn thức ăn khó tiêu vào bữa tối, không ăn quá no trước khi đi ngủ",
            id: 12,
          },
          {
            label:
              "Không sử dụng các thiết bị điện tử như máy tính xách tay, điện thoại trước khi đi ngủ",
            id: 13,
          },
        ],
        minValue: 2,
        maxValue: 2,
      },
    ],
  },
  9: {
    key: "tieuHoa",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB có rối loạn TH liên quan bệnh lý",
            id: 1,
          },
          {
            label: "NB có rối loạn tiêu hóa liên quan chế độ ăn",
            id: 2,
          },
          {
            label: "NB tiêu chảy liên quan đến nhiễm trùng tiêu hóa",
            id: 3,
          },
          {
            label: "NB có rối loạn tiêu hóa liên qua dùng thuốc",
            id: 4,
          },
          {
            label: "NB nôn, buồn nôn do thai nghén",
            id: 5,
          },
        ],
        minValue: 3,
        maxValue: 18,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB được kiểm soát tình trạng tiêu chảy",
            id: 1,
          },
          {
            label: "NB được kiểm soát tình trạng táo bón",
            id: 2,
          },
          {
            label: "NB được bù nước bù dịch hiệu quả",
            id: 3,
          },
        ],

        minValue: 3,
        maxValue: 18,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Thực hiện y lệnh thuốc",
            id: 1,
          },
          {
            label: "Tăng cường bù nước nếu NB có thê uống được",
            id: 2,
          },
          {
            label: "Tư vấn giáo dục sức khoẻ hướng dẫn ché độ ăn uống",
            id: 3,
          },
          {
            label: "Theo dõi lượng nước xuất nhập 24 giờ",
            id: 4,
          },
          {
            label:
              "Theo dõi lượng nước xuất nhập 24 giờ, đánh giá mức độ mất nước",
            id: 5,
          },
          {
            label:
              "NB hướng dẫn NB uống đủ nước, ăn chế độ tăng cường chất xơ, tập đi tiêu mỗi ngày",
            id: 6,
          },
          {
            label:
              "Thụt tháo nếu có chỉ định HD chế độ ăn giúp giảm buồn nôn và nôn",
            id: 7,
          },
        ],

        minValue: 3,
        maxValue: 18,
      },
    ],
  },
  10: {
    key: "tietNieu",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Bí tiểu liên quan đến nhiễm trùng tiểu",
            id: 1,
          },
          {
            label: "Đau do bí tiểu",
            id: 2,
          },
        ],
        minValue: 3,
        maxValue: 9,
      },
      {
        listNoiDung: [
          {
            label: "NB có thông tiểu sau PT… ngày thứ",
            id: 3,
          },
          {
            label: "Nguy cơ nhiễm trùng tiểu liên quan đến lưu ống thông tiểu ",
            id: 4,
          },
          {
            label: "NB theo dõi chảy máu do....",
            id: 5,
          },
        ],
        minValue: 10,
        maxValue: 19,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Phòng ngừa nhiễm trùng tiểu",
            id: 1,
          },
          {
            label: "NB tự tiểu bình thường",
            id: 2,
          },
          {
            label: "NB giảm hết tình trạng nhiễm trùng tiểu",
            id: 3,
          },
        ],
        minValue: 3,
        maxValue: 10,
      },
      {
        listNoiDung: [
          {
            label: "Phòng ngừa nhiễm trùng tiểu",
            id: 4,
          },
          {
            label: "NB được bơm rửa bàng quang an toàn",
            id: 5,
          },
          {
            label: "NB giảm hết tình trạng nhiễm trùng tiểu",
            id: 6,
          },
        ],
        minValue: 11,
        maxValue: 19,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Chườm ấm bàng quang",
            id: 1,
          },
          {
            label: "Theo dõi chảy máu: số lượng tính chất màu sắc nước tiểu",
            id: 2,
          },
          {
            label: "Thực hiện y lệnh thuốc",
            id: 3,
          },
          {
            label: "Theo dõi nước tiểu: màu sắc, số lượng",
            id: 4,
          },
          {
            label: "Theo dõi mạch, nhiệt độ, HA",
            id: 5,
          },
          {
            label: "Thực hiện CLS theo y lệnh ",
            id: 6,
          },
          {
            label: "Theo dõi đánh giá thang điểm đau",
            id: 7,
          },
        ],
        minValue: 3,
        maxValue: 10,
      },
      {
        listNoiDung: [
          {
            label: "Theo dõi mạch, nhiệt độ, HA",
            id: 8,
          },
          {
            label: "Thực hiện CLS theo y lệnh ",
            id: 9,
          },
          {
            label: "Theo dõi đánh giá thang điểm đau",
            id: 10,
          },
          {
            label:
              "Đặt thông tiểu, chăm sóc ống thông tiểu: vệ sinh bộ phận sinh dục ",
            id: 11,
          },
          {
            label:
              "Theo dõi tần suất đi tiểu, cảm giác mót tiểu, tiểu gắt, đau ",
            id: 12,
          },
          {
            label: "Ghi nhận số lượng, tính chất, màu sắc nước tiểu ",
            id: 13,
          },
          {
            label:
              "Theo dõi hệ thống ống thông tiểu: tính chất, màu sắc, số lượng ",
            id: 14,
          },
          {
            label: "Bơm rửa bàng quang",
            id: 15,
          },
          {
            label: "Theo dõi tình trạng: ống thông, túi chứa nước tiểu",
            id: 16,
          },
          {
            label: "Cố đinh ống thông đúng vị trí",
            id: 17,
          },
          {
            label: "Rút thông tiểu theo chỉ định, tuân thủ quy trình",
            id: 18,
          },
          {
            label: "Theo dõi tự tiểu sau rút thông",
            id: 14,
          },
        ],
        minValue: 11,
        maxValue: 19,
      },
    ],
  },
  11: {
    key: "dinhDuong2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB có nguy cơ thiếu dinh dưỡng",
            id: 1,
          },
        ],

        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label:
              "NB có nguy cơ thiếu hụt dinh dưỡng do chế độ ăn qua sonde dạ dày",
            id: 2,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "NB nhịn ăn theo chỉ định",
            id: 3,
          },
        ],
        minValue: 6,
        maxValue: 6,
      },
      {
        listNoiDung: [
          {
            label: "NB có chế độ ăn theo chỉ định",
            id: 4,
          },
        ],
        minValue: 7,
        maxValue: 7,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB được cung cấp đủ dinh dưỡng",
            id: 1,
          },
        ],

        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label: "NB được cung cấp đủ dinh dưỡng",
            id: 2,
          },
          {
            label: "NB không có nguy cơ hít sặc trong quá trình cho ăn",
            id: 3,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label: "NB không có nguy cơ bị hạ đường huyết",
            id: 4,
          },
          {
            label: "NB được cung cấp đủ dinh dưỡng",
            id: 5,
          },
        ],
        minValue: 6,
        maxValue: 6,
      },
      {
        listNoiDung: [
          {
            label: "Đảm bảo dinh dưỡng cho NB",
            id: 6,
          },
          {
            label: "NB thực hiện chế độ ăn đúng",
            id: 7,
          },
        ],
        minValue: 7,
        maxValue: 7,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "HD NB ăn, uống phù hợp với tình trạng bệnh lý",
            id: 1,
          },
          {
            label: "HD NB ăn chế độ tăng cường dinh dưỡng",
            id: 2,
          },
        ],

        minValue: 4,
        maxValue: 4,
      },
      {
        listNoiDung: [
          {
            label: "Kiểm tra vị trí sonde dạ dày",
            id: 3,
          },
          {
            label: "Kiểm tra dịch dạ dày tồn lưu",
            id: 4,
          },
          {
            label: "Thực hiện cho NB ăn theo chỉ định ",
            id: 5,
          },
          {
            label: "Theo dõi dịch dạ dày theo chỉ định ",
            id: 6,
          },
          {
            label: "Thay ống sonde theo  quy định",
            id: 7,
          },
        ],
        minValue: 5,
        maxValue: 5,
      },
      {
        listNoiDung: [
          {
            label:
              "Theo dõi dấu hiệu hạ đường huyết: Vã mồ hôi, chân tay lạnh, hoa mắt chóng mặt,…",
            id: 8,
          },
          {
            label:
              "Giải thích cho NB về lý do tạm nhịn ăn, các nguy cơ biến chứng có thể xảy ra",
            id: 9,
          },
          {
            label: "Thực hiện truyền dinh dưỡng theo y lệnh",
            id: 10,
          },
        ],
        minValue: 6,
        maxValue: 6,
      },
      {
        listNoiDung: [
          {
            label: "Chế độ ăn: Nhịn",
            id: 11,
          },
          {
            label: "Chế độ ăn: Cơm",
            id: 12,
          },
          {
            label: " Chế độ ăn: Cháo",
            id: 13,
          },
          {
            label: " Chế độ ăn:  Phở ",
            id: 14,
          },
          {
            label: " Chế độ ăn: Cháo loãng",
            id: 16,
          },
          {
            label: " Chế độ ăn: Nước cháo",
            id: 17,
          },
          {
            label: " Chế độ ăn: Sữa",
            id: 18,
          },
          {
            label: " Chế độ ăn: Hạn chế muối",
            id: 19,
          },
        ],
        minValue: 7,
        maxValue: 7,
      },
    ],
  },
  12: {
    key: "truocDe",
    isShowSearch: true,
    dsKey: [
      "raNuocAmDao",
      "raMauAmDao",
      "tinhTrangDauOi",
      "cuDongThai",
      "tinhTrangCoTuCung",
    ],
    dsChanDoan: [
      {
        label: "Nguy cơ chảy máu, sót rau",
        id: 1,
      },
      {
        label:
          "Nguy cơ nhiễm khuẩn do ra máu âm đạo/ nguy cơ thiếu máu do ra máu kéo dài",
        id: 2,
      },
      {
        label: "Người bệnh lo lắng về can thiệp thủ thuật sắp thực hiện",
        id: 3,
      },
      {
        label: "Bn mất ngủ do tâm lý lo lắng",
        id: 4,
      },
      {
        label: "Nguy cơ cho thai do giảm tuần hoàn máu rau thai",
        id: 5,
      },
      {
        label: "Nguy cơ tổn thương cho thai do chảy máu và rau bong non",
        id: 6,
      },
      {
        label:
          "Đau do tình trạng RBN gây nên.Thiếu hụt kiến thức do chưa thích nghi với điều kiện mới",
        id: 8,
      },
      {
        label:
          "Nguy cơ suy dinh dưỡng do nôn nghén nhiều/Nguy cơ mất nước do nôn, có dịch màng bụng",
        id: 9,
      },
      {
        label: "Nguy cơ thiếu máu do mất máu sau đẻ, sau mổ",
        id: 10,
      },
      {
        label: "Nguy cơ tăng đường huyết/hạ đường huyết do tiểu đường thai kỳ",
        id: 11,
      },
      {
        label: "Vệ sinh kém do thiếu kiến thức, tập tục, thói quen",
        id: 12,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Giúp người bệnh yên tâm tin tưởng",
            id: 1,
          },
          {
            label: "Giảm nguy cơ chảy máu",
            id: 2,
          },
          {
            label: "Giảm nguy cơ nhiễm khuẩn",
            id: 3,
          },
          {
            label: "NB ngủ đủ giấc",
            id: 4,
          },
          {
            label: "Giảm nguy cơ sảy thai",
            id: 5,
          },
          {
            label: "Cung cấp đủ kiến thức cho NB",
            id: 6,
          },
        ],
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "HD người bệnh nằm nghiêng trái.",
            id: 1,
          },
          {
            label: "Giảm nguy cơ chấn thương, ngã: HD kéo thành chắn giường",
            id: 2,
          },
          {
            label: "Tránh kích thích vào vú, bụng, không quan hệ tình dục",
            id: 3,
          },
          {
            label: "Tắm cho người bệnh",
            id: 4,
          },
          {
            label: "Chăm sóc, theo dõi sản phụ chuyển dạ đẻ",
            id: 5,
          },
        ],
      },
    ],
  },
  13: {
    key: "sauDe",
    isShowSearch: true,
    dsChanDoan: [
      {
        label: "Nguy cơ chảy máu, mất máu",
        id: 1,
      },
      {
        label: "Đau và các khó chịu khác do cắt, khâu tầng sinh môn",
        id: 2,
      },
      {
        label: "Đau do co bóp tử cung",
        id: 3,
      },
      {
        label: "Đau do cương sữa sau đẻ",
        id: 4,
      },
      {
        label: "Nguy cơ nhiễm khuẩn do ra máu âm đạo",
        id: 5,
      },
      {
        label: "Người bệnh lo lắng về can thiệp thủ thuật sắp thực hiện",
        id: 6,
      },
      {
        label: "Thiếu hụt kiến thức do chưa thích nghi với điều kiện mới ",
        id: 7,
      },
      {
        label:
          "Thiếu hụt kiến thức trong việc chăm sóc bản thân và chăm sóc trẻ sau đẻ",
        id: 8,
      },

      {
        label: "Mệt mỏi liên quan quá trình chuyển dạ kéo dài",
        id: 9,
      },
      {
        label: "Nguy cơ chảy máu sau sinh do sinh khó",
        id: 10,
      },
      {
        label: "Nguy cơ nhiễm trùng liên quan can thiệp thủ thuật",
        id: 11,
      },
      {
        label: "Nguy cơ trầm cảm sau sinh liên quan mất cân bằng nội tiết",
        id: 12,
      },
      {
        label: "Nguy cơ trầm cảm sau sinh do thiếu hỗ trợ tinh thần",
        id: 13,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Giảm đau do vết khâu TSM",
            id: 1,
          },
          {
            label: "Giảm nguy cơ rối loạn đại tiểu tiện",
            id: 2,
          },
          {
            label: "Giảm đau do cương sữa",
            id: 3,
          },
          {
            label: "Giảm nguy cơ nhiễm khuẩn",
            id: 4,
          },
          {
            label: "Cung cấp kiến thức",
            id: 5,
          },
        ],
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label:
              "Hướng dẫn vận động/ hướng dẫn tư thế nằm phù hợp với sự thích nghi của từng sản phụ",
            id: 1,
          },
          {
            label:
              "Hướng dẫn sản phụ và người nhà phát hiện một số dấu hiệu bất thường xảy ra sau đẻ",
            id: 2,
          },
          {
            label: "Hướng dẫn SP ăn thức ăn dễ tiêu tránh táo bón",
            id: 3,
          },
          {
            label: "Hướng dẫn bà mẹ cách chăm sóc 2 bầu vú và cách cho con bú",
            id: 4,
          },
          {
            label:
              "Hướng dẫn bà mẹ cách đề phòng và sử lý tình trạng cương sữa",
            id: 5,
          },
          {
            label:
              "HD người bệnh nằm nghỉ hoàn toàn tại nơi yên tĩnh, thoáng, ấm.",
            id: 6,
          },
          {
            label: "Hướng dẫn vận động sớm sau 6 giờ",
            id: 7,
          },
          {
            label: "Hướng dẫn sp vệ sinh bộ phận SD",
            id: 8,
          },
          {
            label: "Cung cấp các kiến thức cần thiết cho NB",
            id: 9,
          },
        ],
      },
    ],
  },
  14: {
    key: "truocPt",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB có chỉ định PT cấp cứu",
            id: 1,
          },
          {
            label: "NB có chỉ định PT kế hoạch",
            id: 2,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB an tâm phẫu thuật",
            id: 1,
          },
          {
            label: "NB được chuyển phòng mổ an toàn, đúng kế hoạch",
            id: 2,
          },
          {
            label: "NB được đảm bảo an toàn trước và trong phẫu thuật",
            id: 3,
          },
          {
            label:
              "NB được chuẩn bị đủ các bước theo quy trình chuẩn bị trước PTKH",
            id: 4,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label:
              "Thay quần áo trước phẫu thuật/Thực hiện chuẩn bị NB trước phẫu thuật",
            id: 1,
          },
          {
            label: "Khai thác tiền sử dị ứng",
            id: 2,
          },
          {
            label: "Tiền sử phẫu thủ thuật đầy đủ",
            id: 3,
          },
          {
            label: "Giải thích, tư vấn cho NB trước phẫu thuật",
            id: 4,
          },
          {
            label: "HD NB nhịn ăn",
            id: 5,
          },
          {
            label: "Đặt đường truyền tĩnh mạch",
            id: 6,
          },
          {
            label:
              "Thực hiện chỉ định thuốc trước mổ/Thực hiện chuẩn bị NB trước phẫu thuật khẩn trương kịp thời/đánh dấu vị trí phẫu thuật theo quy định/ Hướng dẫn NB tắm trước phẫu thuật",
            id: 7,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
    ],
  },
  15: {
    key: "vetMoSauPt",
    isShowSearch: true,
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Nguy cơ chảy máu sau phẫu thuật",
            id: 1,
          },
          {
            label: "Hô hấp kém hiệu quả liên quan đến đau sau phẫu thuật",
            id: 2,
          },
          {
            label: "Nguy cơ sốc mất máu do chảy máu sau phẫu thuật",
            id: 3,
          },
          {
            label: "NB có dẫn lưu vết",
            id: 4,
          },
          { label: "NB đau vết mổ", id: 5 },
          {
            label: "Nhiễm trùng vết mổ nông",
            id: 6,
          },
        ],
        minValue: 1,
        maxValue: 3,
        key: "vetMoSauPt",
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "Phát hiện sớm dấu hiệu chảy máu sau phẫu thuật",
            id: 1,
          },
          {
            label: "Nguy cơ diễn biến nặng do tình trạng bệnh lý",
            id: 2,
          },
          {
            label: "NB giảm đau VAS < 3",
            id: 3,
          },

          { label: "NB không chảy máu vết mổ", id: 4 },
          { label: "NB giảm sưng nề cảm giác dễ chịu", id: 5 },
        ],
        minValue: 1,
        maxValue: 3,
        key: "vetMoSauPt",
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Động viên giải thích cho NB",
            id: 1,
          },
          {
            label: "Báo Bs đánh giá lại tình trạng NB",
            id: 2,
          },
          {
            label: "Phụ giúp Bs thực hiện can thiệp giảm đau sau phẫu thuật",
            id: 3,
          },

          { label: "Theo dõi đánh giá sát tình trạng NB mỗi 15ph/l", id: 4 },
          { label: "Tình trạng chảy máu sau mổ", id: 5 },
          { label: "Thay đổi phương tiện hỗ trợ hô hấp hiệu quả hơn ", id: 6 },
          { label: "Chuẩn bị dự trù máu, truyền máu khi có y lệnh ", id: 7 },
          { label: "Chăm sóc vết mổ", id: 7 },
          { label: "Kiểm soát đau sau phẫu thuật", id: 8 },
          {
            label:
              "Theo dõi phát hiện sớm tình trạng chảy máu ngoại khoa liên quan đến phẫu thuật",
            id: 9,
          },
        ],
        minValue: 1,
        maxValue: 3,
        key: "vetMoSauPt",
      },
    ],
  },
  16: {
    key: "danhGiaDauSauPt",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB đau mức độ nhẹ liên quan đến bệnh lý",
            id: 1,
          },
          {
            label: "Hô hấp kém hiệu quả liên quan đến đau sau phẫu thuật",
            id: 2,
          },
          {
            label: "NB đau mức độ nặng liên quan đến bệnh lý",
            id: 3,
          },
          {
            label: "NB đau do vết mổ",
            id: 4,
          },
          {
            label: "NB đau do co TC",
            id: 5,
          },
          {
            label: "NB đau do căng tức vú",
            id: 6,
          },
        ],
        minValue: 1,
        maxValue: 2,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB được giảm đau và kiểm soát đau tốt",
            id: 1,
          },
        ],
        minValue: 1,
        maxValue: 2,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            id: 1,
            label:
              "Theo dõi tái đánh giá đau mỗi 6 giờ/ lần hoặc đánh giá bất kỳ lúc nào khi NB có đau hoặc đau tăng lên",
          },
          {
            id: 2,
            label:
              "Hướng dẫn NB thông báo cho nhân viên y tế khi có biểu hiện đau",
          },
          { id: 3, label: "Thực hiện thuốc giảm đau theo chỉ định" },
          {
            id: 4,
            label:
              "Kết hợp các biện pháp giảm đau không dùng thuốc, biện pháp vật lý",
          },
          { id: 5, label: "Tư vấn giáo dục sức khỏe, động viên tinh thần NB" },
          {
            id: 6,
            label:
              "Thực hiện các biện pháp giảm đau duy trì thuốc liên tục theo chỉ định điều trị",
          },
          {
            id: 7,
            label:
              "Hướng dẫn NB ăn thức ăn mềm tránh chua cay, ăn nguội giảm nguy cơ chảy máu",
          },
          {
            id: 8,
            label: "Giải thích cho NB các triệu chứng gặp phải sau phẫu thuật",
          },
          { id: 9, label: "Dự phòng nguy cơ té ngã" },
        ],
        minValue: 1,
        maxValue: 2,
      },
    ],
  },
  17: {
    key: "nguyCoTeNga",
    isShowSearch: true,
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB có nguy cơ té ngã thấp",
            id: 1,
          },
          {
            label: "NB có nguy cơ té ngã trung bình ",
            id: 2,
          },
          {
            label: "NB có nguy cơ té ngã cao",
            id: 3,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB an toàn không té ngã",
            id: 1,
          },
          {
            label:
              "NB được tư vấn giáo dục sức khỏe hướng dẫn phòng ngừa té ngã",
            id: 2,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          { id: 1, label: "Áp dụng các can thiệp hỗ trợ NB" },
          { id: 2, label: "Khoa phòng cung cấp thông tin phòng tránh té ngã" },
          {
            id: 3,
            label: "Tư vấn giáo dục sức khỏe, hướng phòng ngừa té ngã",
          },
          {
            id: 4,
            label: "Hỗ trợ NB di chuyển, đi vệ sinh, và thay đổi tư thế",
          },
          { id: 5, label: "Nâng thanh chắn giường bệnh, hạ thấp giường bệnh" },
          {
            id: 6,
            label: "Dán nhãn cảnh báo té ngã trên HSBA và trên vòng đeo tay",
          },
          {
            id: 7,
            label: "Cung cấp các phương tiện hỗ trợ NB sát bên giường bệnh",
          },
          {
            id: 8,
            label:
              "Tư vấn giáo dục sức khỏe, khuyến khích NB chủ động tìm kiếm sự hỗ trợ từ nhân viên y tế hoặc người thân",
          },
        ],
        minValue: 1,
        maxValue: 1,
        key: "nguyCoTeNga",
      },
    ],
  },
  18: {
    key: "loetDoTyDe",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "Nguy cơ loét tì đè thấp",
            id: 1,
          },
          {
            label: "Nguy cơ loét tì đè trung bình do không tự vận động được",
            id: 2,
          },
          {
            label:
              "NB có nguy cơ loét tì đè mức độ cao do nằm lâu, hạn chế vận động liên quan đến bệnh lý",
            id: 3,
          },
        ],
        minValue: 1,
        maxValue: 1,
        key: "loetDoTyDe",
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB cải thiện hoặc không tăng mức độ loét tì đè",
            id: 1,
          },
          { label: "Vết thương lành tốt, băng vết thương khô sạch", id: 2 },
          { label: "Dẫn lưu vết thương NB được chăm sóc ổn", id: 3 },
          { label: "Giảm hết tình trạng nhiễm trùng vết mổ", id: 4 },
        ],
        minValue: 1,
        maxValue: 1,
        key: "loetDoTyDe",
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          { id: 1, label: "Thường xuyên xoay trở theo quy định 2h/lần" },
          { id: 2, label: "Sử dụng dụng cụ kê, chống loét tì đè" },
          { id: 3, label: "Nằm, ngồi trên bề mặt nệm giảm áp lực tì đè" },
          { id: 4, label: "Bảo vệ gót, khuỷu bằng dùng kem chống loét" },
          { id: 5, label: "Kiểm soát độ ẩm của da" },
          { id: 6, label: "Kiểm soát dinh dưỡng" },
          { id: 7, label: "Phòng ngừa trượt, rách da" },

          { id: 8, label: "Thay băng vết mổ, tuân thủ nguyên tắc vô khuẩn" },
          { id: 9, label: "Theo dõi tình trạng vết mổ" },
          {
            id: 10,
            label:
              "Thay băng vết mổ hằng ngày, hoặc khi thấm dịch, tuân thủ nguyên tắc vô khuẩn",
          },
          { id: 11, label: "Thực hiện thuốc giảm đau, kháng sinh" },
          { id: 12, label: "Chăm sóc vết thương có dẫn lưu" },
          { id: 13, label: "Theo dõi dịch dẫn lưu, tính chất số lượng dịch" },
          { id: 14, label: "Theo dõi vị trí chân ống dẫn lưu" },
          { id: 15, label: "Băng ép vết thương cầm máu" },
          { id: 16, label: "Thực hiện thuốc giảm đau, truyền dịch" },
          { id: 17, label: "Lấy máu xét nghiệm" },
          { id: 18, label: "Thực hiện xét nghiệm, cấy dịch vết mổ (nếu có)" },
          {
            id: 19,
            label: "Thay băng hằng ngày hoặc khi thấm dịch, dán băng phù hợp",
          },
          { id: 20, label: "Đánh giá tình trạng nhiễm trùng vết thương" },
          {
            id: 21,
            label: "Thực hiện thuốc giảm đau, kháng sinh, truyền dịch",
          },
        ],
        minValue: 1,
        maxValue: 1,
        key: "loetDoTyDe",
      },
    ],
  },
  19: {
    key: "giaoDucSucKhoe2",
    dsChanDoan: [
      {
        listNoiDung: [
          {
            label: "NB nhập viện nội trú ngày đầu chưa biết nội quy",
            id: 1,
          },
        ],
        minValue: 1,
        maxValue: 1,
      },
      {
        listNoiDung: [
          {
            label: "NB điều trị nội trú cần tư vấn giáo dục sức khoẻ",
            id: 2,
          },
        ],

        minValue: 2,
        maxValue: 2,
      },
      {
        listNoiDung: [
          {
            label:
              "NB có kế hoạch ra viện cần tư vấn giáo dục sức khoẻ trước ra viện",
            id: 3,
          },
        ],
        minValue: 3,
        maxValue: 3,
      },
    ],
    dsKeHoach: [
      {
        listNoiDung: [
          {
            label: "NB có kiến thức về dịch vụ nội trú",
            id: 1,
          },
          {
            label: "NB giảm lo lắng trong ngày đầu nhập viện",
            id: 2,
          },
        ],

        minValue: 1,
        maxValue: 1,
      },
      {
        listNoiDung: [
          {
            label:
              "NB nhận được tư vấn hướng dẫn giáo dục sức khoẻ trong thời gian nhập viện",
            id: 3,
          },
          {
            label: "NB yên tâm điều trị",
            id: 4,
          },
        ],
        id: 2,
        minValue: 2,
        maxValue: 2,
      },
      {
        listNoiDung: [
          {
            label:
              "NB hiểu rõ các vần đề cần theo dõi, chăm sóc sau khi xuất viện",
            id: 5,
          },
        ],
        minValue: 3,
        maxValue: 3,
      },
    ],
    dsNoiDungChamSoc: [
      {
        listNoiDung: [
          {
            label: "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu nhập viện",
            id: 1,
          },
          {
            label: "Hướng dẫn nội quy khoa phòng",
            id: 2,
          },
          {
            label: "Tư vấn sử dụng dịch vụ phòng bệnh nội trú",
            id: 3,
          },
          {
            label: "Hướng dẫn chế độ BHYT",
            id: 4,
          },
          {
            label: "Phổ biến nội quy, quy định của Bệnh viện, Khoa",
            id: 5,
          },
          {
            label:
              "Hướng dẫn chế độ ăn phù hợp với tình trạng bệnh và trước phẫu thuật",
            id: 6,
          },
        ],

        minValue: 1,
        maxValue: 1,
      },
      {
        listNoiDung: [
          {
            label:
              "Giải thích các kỹ thuật, thủ thuật trước, trong và sau khi thực hiện trên người bệnh liên quan đến công tác chăm sóc",
            id: 7,
          },
          {
            label: "Hướng dẫn sử dụng thuốc an toàn và hiệu quả.",
            id: 8,
          },
          {
            label:
              "Hướng dẫn tự phát hiện và theo dõi các triệu chứng khác thường",
            id: 9,
          },
          {
            label:
              "Hướng dẫn chế độ vận động theo theo bệnh lý và tình trạng bệnh, các bài tập vật lý trị liệu, nghỉ ngơi hợp lý",
            id: 10,
          },
          {
            label: "HD sản phụ nuôi con bằng sữa mẹ",
            id: 11,
          },
          {
            label: "HD sản phụ cho con bú đúng ",
            id: 12,
          },
        ],
        id: 2,
        minValue: 2,
        maxValue: 2,
      },
      {
        listNoiDung: [
          {
            label: "Cung cấp các tài liệu giáo dục sức khỏe",
            id: 13,
          },
          {
            label: "Hướng dẫn NB chăm sóc vết mổ, thay băng cắt chỉ theo hẹn",
            id: 14,
          },
          {
            label: "Hướng dẫn và vệ sinh cá nhân sau mổ",
            id: 15,
          },
          {
            label: "Hướng dẫn NB phòng ngừa biến chứng",
            id: 16,
          },
          {
            label:
              "Chuẩn bị và cung cấp đầy đủ các giấy tờ liên quan: Giấy ra viện, giấy chứng nhận phẫu thuật các kết quả xét nghiệm đã thực hiện",
            id: 17,
          },
          {
            label: "HD sản phụ nuôi con bằng sữa mẹ",
            id: 18,
          },
          {
            label: "HD sản phụ cho con bú đúng ",
            id: 19,
          },
        ],
        minValue: 3,
        maxValue: 3,
      },
    ],
  },
};
export const LIST_TR_GHU_CHU_BAN_GIAO = [
  "SPO2 ≥ 95% (Thở khí trời > 10 phút)",
  "Thở 14-28 l/p thở sâu, ho được",
  "Tỉnh hoặc để đánh thức",
  "Cựa 2 chân bình thường",
  "Thay đổi HA > 20% HA cũ",
  "Thay đổi nhịp tim < 20% nhịp tim cũ",
  "T° > 36°",
  "Không vân tím dưới da",
  "Không nghỉ chảy máu",
  "Không rét run",
  "Không kích động",
  "Không đau hoặc ít đau",
  "Không nôn hoặc ít nôn",
  "Không có cầy bàng quang",
];

export const CHI_SO_BO_SUNG = [
  {
    label1: "Chỉ số bổ sung",
    label2: "Spo2 (%)",
    key: "chiSoSong.spo2",
    colSpan2: 2,
    type: "number",
    rowSpan1: 12,
  },
  {
    label2: "Fio2 (%)",
    key: "fio2",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở mask",
    key: "thoMask",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Cơn co TC(cơn/10p)",
    key: "conCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Độ mở cổ TC (cm)",
    key: "doMoCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch âm đạo (ml)",
    key: "dichAmDao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở oxy (l/p)",
    key: "thoOxy",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Glassgow",
    key: "glassgow",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dạ dày (ml)",
    key: "dichDaDay",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dẫn lưu (ml)",
    key: "dichDanLuu",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nước tiểu (ml)",
    key: "soLuongNuocTieu",
    colSpan2: 2,
    type: "number",
  },
];
export const CHI_SO_BO_SUNG2 = [
  {
    label1: "Chỉ số bổ sung",
    label2: "Spo2 (%)",
    key: "chiSoSong.spo2",
    colSpan2: 2,
    type: "number",
    rowSpan1: 12,
  },
  {
    label2: "Fio2 (%)",
    key: "fio2",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở mask",
    key: "thoMask",
    colSpan2: 2,
    type: "number",
  },

  {
    label2: "Cơn co TC(cơn/10p)",
    key: "conCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Độ mở cổ TC (cm)",
    key: "doMoCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch âm đạo (ml)",
    key: "dichAmDao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở oxy (l/p)",
    key: "thoOxy",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Glassgow",
    key: "glassgow",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dạ dày (ml)",
    key: "dichDaDay",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dẫn lưu (ml)",
    key: "dichDanLuu",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nước tiểu (ml)",
    key: "soLuongNuocTieu",
    colSpan2: 2,
    type: "number",
  },
];
export const HO_HAP = [
  {
    label1: "Hô hấp",
    label2: "Khó thở",
    key: "khoTho2",
    colSpan2: 2,
    rowSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Không khó thở",
      "Không ho",
      "Khó thở nhẹ",
      "Ho khan",
      "Ho có đờm",
      "Khó thở khi gắng sức",
      "Khó thở kèm rút lõm cơ hô hấp",
      "Thở qua Mask",
      "Thở oxi",
      "Khác",
    ]),
  },
  {
    label2: "Ho",
    key: "ho",
    type: "droplist",
    data: convertArr(["Không ho", "Ho khan", "Ho có đờm"]),
    colSpan2: 2,
    mode: "onlyOne",
  },
  {
    label2: "Đờm",
    key: "dom2",
    type: "droplist",
    data: convertArr([
      "Không có đờm ",
      "Đờm loãng- Đờm đặc",
      "Đờm trong",
      "Đờm xanh",
      "Đờm vàng",
      "Đờm lẫn máu",
      "Khác…",
    ]),
    colSpan2: 2,
  },
];
export const PHU = [
  {
    label1: "Phù (Vị trí/mức độ)",
    label2: "Vị trí",
    key: "viTriPhu2",
    type: "droplist",
    rowSpan1: 3,
    colSpan2: 2,
    data: convertArr([
      "Không phù",
      "Phù chi trên",
      "Phù chi dưới",
      "Phù toàn thân",
      "Phù mắt cá chân",
      "Phù mi mắt",
    ]),
  },
  {
    label2: "Mức độ",
    key: "mucDoPhu",
    type: "string",
    colSpan2: 2,
  },
  {
    label2: "Khác",
    key: "phuKhac",
    type: "string",
    colSpan2: 2,
  },
];

export const GIAI_DOAN_DE = [
  {
    label1: "Giai đoạn đẻ",
    label2: "Ra nước âm đạo",
    key: "truocDe.raNuocAmDao",
    type: "droplist",
    rowSpan1: 18,
    rowSpan2: 3,
    hint: ["Có", "Không"],
    data: convertArr(["Có", "Không"]),
    listKey: [
      "truocDe.raNuocAmDao",
      "truocDe.mauSacNuocAmDao",
      "truocDe.muiNuocAmDao",
    ],
  },
  {
    key: "truocDe.mauSacNuocAmDao",
    type: "droplist",
    hint: ["Màu sắc"],
    data: convertArr(["Hồng", "Lờ lờ máu cá", "Xanh", "Trong"]),
  },
  {
    key: "truocDe.muiNuocAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Không mùi", "Tanh Nồng", "Hôi", "Khai"]),
  },
  {
    label2: "Ra máu âm đạo",
    key: "truocDe.raMauAmDao",
    type: "droplist",
    hint: ["Có", "Không"],
    data: convertArr(["Có", "Không"]),
    rowSpan2: 3,
    listKey: [
      "truocDe.mauSacMauAmDao",
      "truocDe.muiMauAmDao",
      "truocDe.raMauAmDao",
    ],
  },
  {
    key: "truocDe.mauSacMauAmDao",
    type: "droplist",
    hint: ["Màu sắc"],
    data: convertArr(["Hồng", "Lờ lờ máu cá", "Xanh", "Trong"]),
  },
  {
    key: "truocDe.muiMauAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Không mùi", "Tanh Nồng", "Hôi", "Khai"]),
  },
  {
    label2: "Tình trạng ối",
    key: "truocDe.tinhTrangDauOi",
    type: "droplist",
    hint: ["Đầu ối"],
    data: convertArr(["Dẹt", "Phồng", "Quả lê", "Rỉ ối"]),
    rowSpan2: 3,
    listKey: [
      "truocDe.tinhTrangDauOi",
      "truocDe.tinhTrangNuocOi",
      "truocDe.tinhTrangOi",
    ],
  },
  {
    key: "truocDe.tinhTrangNuocOi",
    type: "droplist",
    hint: ["Nước ối"],
    data: convertArr(["Trong", "Xanh bẩn", "Lẫn máu"]),
  },
  {
    key: "truocDe.tinhTrangOi",
    type: "droplist",
    hint: ["Còn ối", "Vớ ối", "Khác"],
    data: convertArr(["Còn ối", "Vớ ối"]),
  },
  {
    label2: "Cử động thai",
    key: "truocDe.cuDongThai",
    type: "droplist",
    colSpan2: 2,
    data: convertArr(["Có", "Không", "Cử động ít", "Cử động nhiều"]),
  },
  {
    label2: "Cổ TC",
    key: "truocDe.tinhTrangCoTuCung",
    type: "droplist",
    hint: ["Đóng kín - Đang xóa - Mở - Khác"],
    data: convertArr(["Đóng kín", "Đang xóa", "Mở"]),
    rowSpan2: 2,
    listKey: ["truocDe.tinhTrangCoTuCung", "truocDe.tuTheCoTuCung"],
  },
  {
    key: "truocDe.tuTheCoTuCung",
    type: "droplist",
    hint: ["Tư thế"],
    data: convertArr([
      "Trung gian",
      "Chúc ra sau",
      "Đổ ra trước",
      "Mềm",
      "Cứng",
    ]),
  },

  {
    label2: "TC",
    key: "sauDe.coTuCung",
    type: "droplist",
    hint: ["Chắc - Mềm nhão - Khác"],
    data: convertArr(["Chắc", "Mềm nhão"]),
  },
  {
    label2: "Ra máu AĐ",
    key: "sauDe.raMauAmDao",
    type: "droplist",
    hint: ["Có - Không"],
    data: convertArr(["Có", "Không"]),
    rowSpan2: 3,
    listKey: ["sauDe.raMauAmDao", "sauDe.mauMauAmDao", "sauDe.muiMauAmDao"],
  },
  {
    key: "sauDe.mauMauAmDao",
    type: "droplist",
    hint: ["Màu"],
    data: convertArr(["Đỏ tươi", "Đỏ sẫm", "Lờ lờ máu cá", "Đen sẫm"]),
  },
  {
    key: "sauDe.muiMauAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Tanh nồng", "Hôi"]),
  },
  {
    label2: "Tình trạng vú",
    key: "sauDe.tinhTrangVu",
    type: "droplist",
    data: convertArr([
      "Chưa tiết sữa",
      "Núm vú tụt",
      "Căng tức",
      "Tắc tia sữa",
      "Sưng, nóng, đỏ",
      "Tiết sữa tốt",
      "2 vú mềm",
      "2 vú bình thường",
    ]),
    colSpan2: 2,
  },
  {
    label2: "Âm hộ, TSM",
    key: "sauDe.amHo",
    type: "droplist",
    data: convertArr([
      "Vết khâu khô, liền tốt",
      "Vết khâu sưng nề, đỏ",
      "Vết khâu chảy mủ",
    ]),
    colSpan2: 2,
  },
];
export const DICH_NHAP = [
  {
    label1: "Dịch Nhập",
    rowSpan1: 5,
    label2: "Thuốc, dịch truyền (ml)",
    colSpan2: 2,
    key: "dichNhap2.thuocDichTruyen",
  },
  {
    label2: "Máu chế phẩm máu (ml)",
    colSpan2: 2,
    key: "dichNhap2.chePhamMau",
  },
  {
    label2: "Ăn uống (ml)",
    colSpan2: 2,
    key: "dichNhap2.anUong",
  },
  {
    label2: "khác (ml)",
    colSpan2: 2,
    key: "dichNhap2.khac",
  },
];
export const DICH_XUAT = [
  {
    label1: "Dịch xuất",
    rowSpan1: 3,
    label2: "Nước tiểu (ml)",
    colSpan2: 2,
    key: "soLuongNuocTieu",
  },
  {
    label2: "Dịch dẫn lưu (ml)",
    colSpan2: 2,
    key: "dichDanLuu",
  },
];
export const TR = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    key: "ngayThucHien",
    colSpan1: 3,
  },

  {
    label1: "Phân cấp chăm sóc",
    colSpan1: 3,
    key: "phanCapChamSoc",
    type: "droplist",
    data: convertArr([2, 3]),
    mode: "onlyOne",
  },
  {
    label1: "Nhận định, theo dõi",
    colSpan1: 3,
    key: "nhanDinhTheoDoi",
    type: "string",
    // disable: true,
  },
  {
    label1: "Chỉ số sinh tồn, sinh trắc",
    rowSpan1: 8,
    label2: "Mạch (lần/ phút)",
    colSpan2: 2,
    key: "chiSoSong.mach",
    type: "number",
  },
  {
    label2: "Nhiệt độ (℃)",
    key: "chiSoSong.nhietDo",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Huyết áp (mmHg)",
    key: "chiSoSong.huyetAp",
    colSpan2: 2,
  },

  {
    label2: "BMI",
    key: "chiSoSong.bmi",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Đau",
    key: "dau",
    colSpan2: 2,
    type: "string",
  },
  {
    label2: "Cân nặng (kg)",
    key: "chiSoSong.canNang",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Chiều cao (cm)",
    key: "chiSoSong.chieuCao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Tim thai (l/p)",
    key: "timThai",
    colSpan2: 2,
    type: "number",
  },
  {
    label1: "Chỉ số bổ sung",
    key: "chiSoBoSung",
    listRow: CHI_SO_BO_SUNG,
  },
  {
    label1: "Toàn thân",
    key: "toanThan",
    colSpan1: 3,
  },
  // {
  //   label1: "Toàn thân khác",
  //   key: "toanThanKhac",
  //   colSpan1: 3,
  // },
  {
    label1: "Tri giác",
    key: "triGiac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Tỉnh",
      "Tiếp xúc tốt",
      "Mệt mỏi",
      "Lơ mơ",
      "Li bì",
      "Dùng thuốc an thần",
      "Chóng mặt",
      "Mất thăng bằng",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tri giác khác",
  //   key: "triGiacKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Da, niêm mạc",
    key: "daNiemMac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Da, niêm mạc hồng",
      "Khô, ấm ",
      "Không có vết thương/vết loét",
      "Da xanh, niêm mạc nhợt",
      "Nóng",
      "Sưng nề",
      "Có dấu hiện hăm đỏ",
      "Mẩn ngứa",
      "Ấm lạnh",
      "Có nguy cơ loét do tì đè",
      "Nóng đỏ",
      "Chợt loét",
      "Da niêm mạc vàng",
      "Có nốt xuất huyết",
      "Phát ban",
      "Nổi u cục",
      "Viêm da",
      "Mẩn ngứa",
      "Mề đay",
      "Phát ban",
    ]),
  },
  // {
  //   label1: "Da niêm mạc khác",
  //   key: "daNiemMacKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Hô hấp",
    key: "khoTho",
    listRow: HO_HAP,
  },
  {
    label1: "Phù (Vị trí/mức độ)",
    key: "phu",
    listRow: PHU,
  },

  {
    label1: "Tuần hoàn",
    key: "tuanHoan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Mạch Đều, rõ",
      "không xuất huyết",
      "HA bình thường",
      "Chỉ số HA ngoài giới hạn bình thường",
      "Mạch không đều",
      "Nhanh nhỏ",
      "Khó bắt",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Không phù",
      "Mạch nhanh",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Đau ngực âm ỉ, liên tục",
      "Sử dụng thuốc kháng đông",
      "Dấu hiệu bầm tím trên da",
      "Xuất huyết dưới da",
      "các nốt chấm xuất huyết",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tuần hoàn khác",
  //   key: "tuanHoanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tinh thần",
    key: "tinhThan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Yên tâm, thoải mái",
      "Lạc quan",
      "Lo lắng",
      "buồn phiền",
      "Giảm hào hứng trong giao tiếp",
      "Lo lắng quá mức",
      "Trầm cảm",
      "Kích động, la hét",
      "Lo lắng liên quan đến thiếu kiến thức về thai kỳ, sinh nở, làm mẹ",
      "Lo lắng liên quan đến thai kỳ ngoài ý muốn",
      "Lo lắng liên quan đến biến chứng thai kỳ",
      "Lo lắng liên quan đến không có người thân bên cạnh",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tinh thần khác",
  //   key: "tinhThanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vận động",
    key: "vanDong",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB có thể thực hiện các hoạt động sinh hoạt hằng ngày",
      "VĐ hạn chế",
      "Đau khi vận động",
      "Không vận động được",
      "NB đang dùng thuốc mê, tê",
      "NB có chỉ định hạn chế VĐ",
      "NB có chỉ định nghỉ tại giường",
    ]),
  },
  // {
  //   label1: "Vận động khác",
  //   key: "vanDongKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vệ sinh cá nhân",
    key: "veSinhCaNhan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Vệ sinh cá nhân sạch sẽ",
      "NB không tự vệ sinh cá nhân được",
    ]),
  },
  // {
  //   label1: "Vệ sinh cá nhân khác",
  //   key: "veSinhCaNhanKhac2",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Giấc ngủ",
    key: "giacNgu2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB ngủ tốt",
      "NB ngủ không đủ thời gian sinh lý cần thiết",
      "Mất ngủ liên quan lo âu, chăm sóc trẻ ban đêm",
      "Rối loạn giấc ngủ do mất cân bằng nội tiết",
    ]),
  },
  // {
  //   label1: "Giấc ngủ khác",
  //   key: "giacNguKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiêu hóa",
    key: "tieuHoa",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Bình thường",
      "Đã trung tiện",
      "Chưa trung tiện",
      "Viêm loét miệng",
      "Đầy bụng",
      "khó tiêu",
      "đau bụng",
      "Táo bón",
      "Tiêu chảy",
      "Phân lẫn nhày",
      "Phân lẫn máu",
      "Nôn",
      "Buồn nôn",
      "Nôn ra thức ăn",
      "Nôn khan",
      "Nôn ra máu",
    ]),
  },
  // {
  //   label1: "Tiêu hóa khác",
  //   key: "tieuHoaKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiết niệu",
    key: "tietNieu",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Tiểu bình thường",
      "Nước tiểu vàng trong",
      "Tiểu khó",
      "Bí tiểu",
      "Tiểu ít",
      "Không có nước tiểu",
      "Tiểu buốt",
      "Tiểu rắt",
      "Són tiểu",
      "NT vàng sẫm",
      "NT màu đỏ sẫm",
      "NT có máu",
      "Đặt sond tiểu",
      "Có CĐ bơm rửa bàng quang",
      "Có CĐ rút sonde tiểu",
    ]),
  },
  // {
  //   label1: "Tiết niệu khác",
  //   key: "tietNieuKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Dinh dưỡng",
    key: "dinhDuong2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "BMI trong giới hạn",
      "NB ăn ngon miệng",
      "NB ăn uống bình thường",
      "NB ăn ít, không ngon miệng",
      "NB có chỉ định ăn sonde",
      "NB nhịn ăn theo chỉ định: Trước phẫu thuật",
      "NB nhịn ăn theo chỉ định: Sau phẫu thuật",
      "NB có chế độ ăn theo tình trạng bệnh lý",
      "Nguy cơ thiếu dinh dưỡng liên quan đến buồn nôn, nôn nghén",
      "Nguy cơ thiếu dinh dưỡng liên quan đến kiêng khem thực phẩm do quan niệm sai lệch",
    ]),
  },
  // {
  //   label1: "Dinh dưỡng khác",
  //   key: "dinhDuong",
  //   type: "string",
  //   colSpan1: 3,
  // },

  {
    label1: "Giai đoạn đẻ",
    key: "giaiDoanDe",
    type: "droplist",
    colSpan1: 3,
    listRow: GIAI_DOAN_DE,
  },

  {
    label1: "Trước PT",
    key: "truocPt",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "NB có chỉ định PT cấp cứu",
      "NB có chỉ định PT kế hoạch",
    ]),
  },
  // {
  //   label1: "Trước PT khác",
  //   key: "truocPtKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Sau PT",
    label2: "Vết mổ",
    key: "vetMoSauPt",
    rowSpan1: 1,
    type: "droplist",
    colSpan2: 2,
    data: convertArr([
      "NB có vết mổ",
      "NB có dẫn lưu",
      "Đau sau phẫu thuật",
      "Vết mổ khô, liền tốt",
      "Băng vết mổ khô",
      "Vết mổ thấm máu vừa",
      "Thấm dịch tiết",
      "Thấm máu ít",
      "Băng thấm đẫm máu",
      "Vết mổ sưng nề",
      "Chảy mủ",
      "Khác",
    ]),
  },
  {
    label1: "Đánh giá đau",
    key: "danhGiaDauSauPt",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["NB không đau", "Có đau"]),
  },
  {
    label1: "Nguy cơ té ngã",
    key: "nguyCoTeNga",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Nguy cơ ngã", "Không"]),
  },
  {
    label1: "Loét do tỳ đè",
    key: "loetDoTyDe",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Loét do tỳ đè", "Không"]),
  },

  {
    label1: "Khác",
    key: "sauPtKhac",
    type: "string",
    colSpan1: 3,
  },
  {
    label1: "GDSK",
    hint: [
      // "1. Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
      // "2. Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
      // "3. Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
    ],
    key: "giaoDucSucKhoe2",
    type: "droplist",
    data: convertArr(
      [
        "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
      ],
      "value"
    ),
    colSpanHint: 2,
  },
  // {
  //   label1: "GDSK khác",
  //   key: "giaoDucSucKhoe",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Catheter",
    label2: "TM ngoại biên",
    colSpan2: 2,
    key: "tmNgoaiBien",
    type: "droplist",
    rowSpan1: 2,
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },
  {
    label2: "TM trung tâm",
    colSpan2: 2,
    key: "tmTrungTam",
    type: "droplist",
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },

  {
    label1: "Danh sách vấn đề",
    colSpan1: 3,
    key: "dsVanDe",
    type: "string",
  },

  {
    label1: "Thực hiện y lệnh",
    colSpan1: 3,
    key: "dsThucHienYLenh",
    type: "droplist",
    data: DS_THUC_HIEN_Y_LENH,
    isShowSearch: true,
  },
  {
    label1: "Thực hiện y lệnh khác",
    colSpan1: 3,
    key: "thucHienYLenhKhac",
    type: "string",
  },
  {
    label1: "Chăm sóc",
    colSpan1: 3,
    key: "dsChamSoc",
    type: "droplist",
    data: DS_THUC_HIEN_CHAM_SOC,
  },
  {
    label1: "Chăm sóc khác",
    colSpan1: 3,
    key: "chamSocKhac",
    type: "string",
  },
  {
    label1: "Đánh giá",
    colSpan1: 3,
    key: "dsDanhGia",
    type: "droplist",
    data: DS_DANH_GIA,
  },
  {
    label1: "Đánh giá khác",
    colSpan1: 3,
    key: "danhGiaKhac",
    type: "string",
  },
  {
    label1: "Bàn giao",
    colSpan1: 3,
    key: "dsBanGiao",
    type: "droplist",
    data: DS_BAN_GIAO,
  },
  {
    label1: "Bàn giao khác",
    colSpan1: 3,
    key: "banGiaoKhac",
    type: "string",
  },
  {
    label1: "Tên điều dưỡng thực hiện",
    colSpan1: 3,
    type: "sign",
  },
];
export const TR_PHIEU_CAP1 = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    key: "ngayThucHien",
    colSpan1: 3,
  },

  {
    label1: "Phân cấp chăm sóc",
    colSpan1: 3,
    key: "phanCapChamSoc",
    type: "droplist",
    data: convertArr([2, 3]),
    mode: "onlyOne",
  },
  {
    label1: "Nhận định, theo dõi",
    colSpan1: 3,
    key: "nhanDinhTheoDoi",
    type: "string",
    // disable: true,
  },
  {
    label1: "Chỉ số sinh tồn, sinh trắc",
    rowSpan1: 20,
    label2: "Mạch (lần/ phút)",
    colSpan2: 2,
    key: "chiSoSong.mach",
    type: "number",
  },
  {
    label2: "Nhiệt độ (℃)",
    key: "chiSoSong.nhietDo",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Huyết áp (mmHg)",
    key: "chiSoSong.huyetAp",
    colSpan2: 2,
  },

  {
    label2: "BMI",
    key: "chiSoSong.bmi",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Đau",
    key: "dau",
    colSpan2: 2,
    type: "string",
  },
  {
    label2: "Cân nặng (kg)",
    key: "chiSoSong.canNang",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Chiều cao (cm)",
    key: "chiSoSong.chieuCao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Tim thai (l/p)",
    key: "timThai",
    colSpan2: 2,
    type: "number",
  },
  {
    label1: "Chỉ số bổ sung",
    key: "chiSoBoSung",
    listRow: CHI_SO_BO_SUNG2,
  },
  {
    label1: "Toàn thân",
    key: "toanThan",
    colSpan1: 3,
  },
  // {
  //   label1: "Toàn thân khác",
  //   key: "toanThanKhac",
  //   colSpan1: 3,
  // },
  {
    label1: "Tri giác",
    key: "triGiac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Tỉnh",
      "Tiếp xúc tốt",
      "Mệt mỏi",
      "Lơ mơ",
      "Li bì",
      "Dùng thuốc an thần",
      "Chóng mặt",
      "Mất thăng bằng",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tri giác khác",
  //   key: "triGiacKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Da, niêm mạc",
    key: "daNiemMac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Da, niêm mạc hồng",
      "Khô, ấm ",
      "Không có vết thương/vết loét",
      "Da xanh, niêm mạc nhợt",
      "Nóng",
      "Sưng nề",
      "Có dấu hiện hăm đỏ",
      "Mẩn ngứa",
      "Ấm lạnh",
      "Có nguy cơ loét do tì đè",
      "Nóng đỏ",
      "Chợt loét",
      "Da niêm mạc vàng",
      "Có nốt xuất huyết",
      "Phát ban",
      "Nổi u cục",
      "Viêm da",
      "Mẩn ngứa",
      "Mề đay",
      "Phát ban",
    ]),
  },
  {
    label1: "Hô hấp",
    key: "khoTho",
    listRow: HO_HAP,
  },
  {
    label1: "Phù (Vị trí/mức độ)",
    key: "phu",
    listRow: PHU,
  },

  {
    label1: "Tuần hoàn",
    key: "tuanHoan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Mạch Đều, rõ",
      "không xuất huyết",
      "HA bình thường",
      "Chỉ số HA ngoài giới hạn bình thường",
      "Mạch không đều",
      "Nhanh nhỏ",
      "Khó bắt",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Không phù",
      "Mạch nhanh",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Đau ngực âm ỉ, liên tục",
      "Sử dụng thuốc kháng đông",
      "Dấu hiệu bầm tím trên da",
      "Xuất huyết dưới da",
      "các nốt chấm xuất huyết",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tuần hoàn khác",
  //   key: "tuanHoanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tinh thần",
    key: "tinhThan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Yên tâm, thoải mái",
      "Lạc quan",
      "Lo lắng",
      "buồn phiền",
      "Giảm hào hứng trong giao tiếp",
      "Lo lắng quá mức",
      "Trầm cảm",
      "Kích động, la hét",
      "Lo lắng liên quan đến thiếu kiến thức về thai kỳ, sinh nở, làm mẹ",
      "Lo lắng liên quan đến thai kỳ ngoài ý muốn",
      "Lo lắng liên quan đến biến chứng thai kỳ",
      "Lo lắng liên quan đến không có người thân bên cạnh",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tinh thần khác",
  //   key: "tinhThanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vận động",
    key: "vanDong",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB có thể thực hiện các hoạt động sinh hoạt hằng ngày",
      "VĐ hạn chế",
      "Đau khi vận động",
      "Không vận động được",
      "NB đang dùng thuốc mê, tê",
      "NB có chỉ định hạn chế VĐ",
      "NB có chỉ định nghỉ tại giường",
    ]),
  },
  // {
  //   label1: "Vận động khác",
  //   key: "vanDongKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vệ sinh cá nhân",
    key: "veSinhCaNhan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Vệ sinh cá nhân sạch sẽ",
      "NB không tự vệ sinh cá nhân được",
    ]),
  },
  // {
  //   label1: "Vệ sinh cá nhân khác",
  //   key: "veSinhCaNhanKhac2",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Giấc ngủ",
    key: "giacNgu2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB ngủ tốt",
      "NB ngủ không đủ thời gian sinh lý cần thiết",
      "Mất ngủ liên quan lo âu, chăm sóc trẻ ban đêm",
      "Rối loạn giấc ngủ do mất cân bằng nội tiết",
    ]),
  },
  // {
  //   label1: "Giấc ngủ khác",
  //   key: "giacNguKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiêu hóa",
    key: "tieuHoa",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Bình thường",
      "Đã trung tiện",
      "Chưa trung tiện",
      "Viêm loét miệng",
      "Đầy bụng",
      "khó tiêu",
      "đau bụng",
      "Táo bón",
      "Tiêu chảy",
      "Phân lẫn nhày",
      "Phân lẫn máu",
      "Nôn",
      "Buồn nôn",
      "Nôn ra thức ăn",
      "Nôn khan",
      "Nôn ra máu",
    ]),
  },
  // {
  //   label1: "Tiêu hóa khác",
  //   key: "tieuHoaKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiết niệu",
    key: "tietNieu",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Tiểu bình thường",
      "Nước tiểu vàng trong",
      "Tiểu khó",
      "Bí tiểu",
      "Tiểu ít",
      "Không có nước tiểu",
      "Tiểu buốt",
      "Tiểu rắt",
      "Són tiểu",
      "NT vàng sẫm",
      "NT màu đỏ sẫm",
      "NT có máu",
      "Đặt sond tiểu",
      "Có CĐ bơm rửa bàng quang",
      "Có CĐ rút sonde tiểu",
    ]),
  },
  // {
  //   label1: "Tiết niệu khác",
  //   key: "tietNieuKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Dinh dưỡng",
    key: "dinhDuong2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "BMI trong giới hạn",
      "NB ăn ngon miệng",
      "NB ăn uống bình thường",
      "NB ăn ít, không ngon miệng",
      "NB có chỉ định ăn sonde",
      "NB nhịn ăn theo chỉ định: Trước phẫu thuật",
      "NB nhịn ăn theo chỉ định: Sau phẫu thuật",
      "NB có chế độ ăn theo tình trạng bệnh lý",
      "Nguy cơ thiếu dinh dưỡng liên quan đến buồn nôn, nôn nghén",
      "Nguy cơ thiếu dinh dưỡng liên quan đến kiêng khem thực phẩm do quan niệm sai lệch",
    ]),
  },
  // {
  //   label1: "Dinh dưỡng khác",
  //   key: "dinhDuong",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Giai đoạn đẻ",
    key: "giaiDoanDe",
    type: "droplist",
    colSpan1: 3,
    listRow: GIAI_DOAN_DE,
  },
  {
    label1: "Trước PT",
    key: "truocPt",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "NB có chỉ định PT cấp cứu",
      "NB có chỉ định PT kế hoạch",
    ]),
  },
  // {
  //   label1: "Trước PT khác",
  //   key: "truocPtKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Sau PT",
    label2: "Vết mổ",
    key: "vetMoSauPt",
    rowSpan1: 1,
    type: "droplist",
    colSpan2: 2,
    data: convertArr([
      "NB có vết mổ",
      "NB có dẫn lưu",
      "Đau sau phẫu thuật",
      "Vết mổ khô, liền tốt",
      "Băng vết mổ khô",
      "Vết mổ thấm máu vừa",
      "Thấm dịch tiết",
      "Thấm máu ít",
      "Băng thấm đẫm máu",
      "Vết mổ sưng nề",
      "Chảy mủ",
      "Khác",
    ]),
  },
  {
    label1: "Đánh giá đau",
    key: "danhGiaDauSauPt",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["NB không đau", "Có đau"]),
  },
  {
    label1: "Nguy cơ té ngã",
    key: "nguyCoTeNga",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Nguy cơ ngã", "Không"]),
  },
  {
    label1: "Loét do tỳ đè",
    key: "loetDoTyDe",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Loét do tỳ đè", "Không"]),
  },

  {
    label1: "Khác",
    key: "sauPtKhac",
    type: "string",
    colSpan1: 3,
  },
  {
    label1: "GDSK",
    hint: [
      // "1. Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
      // "2. Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
      // "3. Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
    ],
    key: "giaoDucSucKhoe2",
    type: "droplist",
    data: convertArr(
      [
        "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
      ],
      "value"
    ),
    colSpanHint: 2,
  },
  // {
  //   label1: "GDSK khác",
  //   key: "giaoDucSucKhoe",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Catheter",
    label2: "TM ngoại biên",
    colSpan2: 2,
    key: "tmNgoaiBien",
    type: "droplist",
    rowSpan1: 2,
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },
  {
    label2: "TM trung tâm",
    colSpan2: 2,
    key: "tmTrungTam",
    type: "droplist",
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },
  {
    label1: "Dịch nhập",
    key: "dichNhap",
    listRow: DICH_NHAP,
  },
  {
    label1: "Tổng nhập (ml)",
    colSpan1: 3,
    key: "dichNhap2.tongNhap",
  },
  {
    label1: "Dịch xuất",
    key: "dichXuat",
    listRow: DICH_XUAT,
  },
  {
    label1: "Tổng xuất (ml)",
    colSpan1: 3,
    key: "dichXuat2.tongXuat",
  },
  {
    label1: "Danh sách vấn đề",
    colSpan1: 3,
    key: "dsVanDe",
    type: "string",
  },

  {
    label1: "Thực hiện y lệnh",
    colSpan1: 3,
    key: "dsThucHienYLenh",
    type: "droplist",
    data: DS_THUC_HIEN_Y_LENH,
    isShowSearch: true,
  },
  {
    label1: "Thực hiện y lệnh khác",
    colSpan1: 3,
    key: "thucHienYLenhKhac",
    type: "string",
  },
  {
    label1: "Chăm sóc",
    colSpan1: 3,
    key: "dsChamSoc",
    type: "droplist",
    data: DS_THUC_HIEN_CHAM_SOC,
  },
  {
    label1: "Chăm sóc khác",
    colSpan1: 3,
    key: "chamSocKhac",
    type: "string",
  },
  {
    label1: "Đánh giá",
    colSpan1: 3,
    key: "dsDanhGia",
    type: "droplist",
    data: DS_DANH_GIA,
  },
  {
    label1: "Đánh giá khác",
    colSpan1: 3,
    key: "danhGiaKhac",
    type: "string",
  },
  {
    label1: "Bàn giao",
    colSpan1: 3,
    key: "dsBanGiao",
    type: "droplist",
    data: DS_BAN_GIAO,
  },
  {
    label1: "Bàn giao khác",
    colSpan1: 3,
    key: "banGiaoKhac",
    type: "string",
  },
  {
    label1: "Tên điều dưỡng thực hiện",
    colSpan1: 3,
    type: "sign",
  },
];
export const TR_PHIEU_CAP1_PSHN = [
  {
    label1: (
      <div>
        <div>
          <b>Giờ:</b>
        </div>
        <div>
          <b>Ngày /tháng /năm</b>
        </div>
      </div>
    ),
    key: "ngayThucHien",
    colSpan1: 3,
  },

  {
    label1: "Phân cấp chăm sóc",
    colSpan1: 3,
    key: "phanCapChamSoc",
    type: "droplist",
    data: convertArr([2, 3]),
    mode: "onlyOne",
  },
  {
    label1: "Nhận định, theo dõi",
    colSpan1: 3,
    key: "nhanDinhTheoDoi",
    type: "string",
    // disable: true,
  },
  {
    label1: "Chỉ số sinh tồn, sinh trắc",
    rowSpan1: 20,
    label2: "Mạch (lần/ phút)",
    colSpan2: 2,
    key: "chiSoSong.mach",
    type: "number",
  },
  {
    label2: "Nhiệt độ (℃)",
    key: "chiSoSong.nhietDo",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Huyết áp (mmHg)",
    key: "chiSoSong.huyetAp",
    colSpan2: 2,
  },

  {
    label2: "BMI",
    key: "chiSoSong.bmi",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Đau",
    key: "dau",
    colSpan2: 2,
    type: "string",
  },
  {
    label2: "Cân nặng (kg)",
    key: "chiSoSong.canNang",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Chiều cao (cm)",
    key: "chiSoSong.chieuCao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Spo2 (%)",
    key: "chiSoSong.spo2",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Fio2 (%)",
    key: "fio2",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở mask",
    key: "thoMask",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Tim thai (l/p)",
    key: "timThai",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Cơn co TC(cơn/10p)",
    key: "conCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Độ mở cổ TC (cm)",
    key: "doMoCoTuCung",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch âm đạo (ml)",
    key: "dichAmDao",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Thở oxy (l/p)",
    key: "thoOxy",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Glassgow",
    key: "glassgow",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dạ dày (ml)",
    key: "dichDaDay",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Dịch dẫn lưu (ml)",
    key: "dichDanLuu",
    colSpan2: 2,
    type: "number",
  },
  {
    label2: "Nước tiểu (ml)",
    key: "soLuongNuocTieu",
    colSpan2: 2,
    type: "number",
  },
  {
    label1: "Toàn thân",
    key: "toanThan",
    colSpan1: 3,
  },
  // {
  //   label1: "Toàn thân khác",
  //   key: "toanThanKhac",
  //   colSpan1: 3,
  // },
  {
    label1: "Tri giác",
    key: "triGiac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Tỉnh",
      "Tiếp xúc tốt",
      "Mệt mỏi",
      "Lơ mơ",
      "Li bì",
      "Dùng thuốc an thần",
      "Chóng mặt",
      "Mất thăng bằng",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tri giác khác",
  //   key: "triGiacKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Da, niêm mạc",
    key: "daNiemMac2",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Da, niêm mạc hồng",
      "Khô, ấm ",
      "Không có vết thương/vết loét",
      "Da xanh, niêm mạc nhợt",
      "Nóng",
      "Sưng nề",
      "Có dấu hiện hăm đỏ",
      "Mẩn ngứa",
      "Ấm lạnh",
      "Có nguy cơ loét do tì đè",
      "Nóng đỏ",
      "Chợt loét",
      "Da niêm mạc vàng",
      "Có nốt xuất huyết",
      "Phát ban",
      "Nổi u cục",
      "Viêm da",
      "Mẩn ngứa",
      "Mề đay",
      "Phát ban",
    ]),
  },
  // {
  //   label1: "Da niêm mạc khác",
  //   key: "daNiemMacKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Hô hấp",
    label2: "Khó thở",
    key: "khoTho2",
    colSpan2: 2,
    rowSpan1: 3,
    type: "droplist",
    data: convertArr([
      "Không khó thở",
      "Không ho",
      "Khó thở nhẹ",
      "Ho khan",
      "Ho có đờm",
      "Khó thở khi gắng sức",
      "Khó thở kèm rút lõm cơ hô hấp",
      "Thở qua Mask",
      "Thở oxi",
      "Khác",
    ]),
  },
  {
    label2: "Ho",
    key: "ho",
    type: "droplist",
    data: convertArr(["Không ho", "Ho khan", "Ho có đờm"]),
    colSpan2: 2,
    mode: "onlyOne",
  },
  {
    label2: "Đờm",
    key: "dom2",
    type: "droplist",
    data: convertArr([
      "Không có đờm ",
      "Đờm loãng- Đờm đặc",
      "Đờm trong",
      "Đờm xanh",
      "Đờm vàng",
      "Đờm lẫn máu",
      "Khác…",
    ]),
    colSpan2: 2,
  },
  // {
  //   label1: "Khác",
  //   key: "hoHapKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Phù (Vị trí/mức độ)",
    label2: "Vị trí",
    key: "viTriPhu2",
    type: "droplist",
    rowSpan1: 3,
    colSpan2: 2,
    data: convertArr([
      "Không phù",
      "Phù chi trên",
      "Phù chi dưới",
      "Phù toàn thân",
      "Phù mắt cá chân",
      "Phù mi mắt",
    ]),
  },
  {
    label2: "Mức độ",
    key: "mucDoPhu",
    type: "string",
    colSpan2: 2,
  },
  {
    label2: "Khác",
    key: "phuKhac",
    type: "string",
    colSpan2: 2,
  },
  {
    label1: "Tuần hoàn",
    key: "tuanHoan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Mạch Đều, rõ",
      "không xuất huyết",
      "HA bình thường",
      "Chỉ số HA ngoài giới hạn bình thường",
      "Mạch không đều",
      "Nhanh nhỏ",
      "Khó bắt",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Không phù",
      "Mạch nhanh",
      "Chậm",
      "Hồi hộp, đánh trống ngực",
      "Đau ngực từng cơn",
      "Đau ngực âm ỉ, liên tục",
      "Sử dụng thuốc kháng đông",
      "Dấu hiệu bầm tím trên da",
      "Xuất huyết dưới da",
      "các nốt chấm xuất huyết",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tuần hoàn khác",
  //   key: "tuanHoanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tinh thần",
    key: "tinhThan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Yên tâm, thoải mái",
      "Lạc quan",
      "Lo lắng",
      "buồn phiền",
      "Giảm hào hứng trong giao tiếp",
      "Lo lắng quá mức",
      "Trầm cảm",
      "Kích động, la hét",
      "Lo lắng liên quan đến thiếu kiến thức về thai kỳ, sinh nở, làm mẹ",
      "Lo lắng liên quan đến thai kỳ ngoài ý muốn",
      "Lo lắng liên quan đến biến chứng thai kỳ",
      "Lo lắng liên quan đến không có người thân bên cạnh",
      "Khác",
    ]),
  },
  // {
  //   label1: "Tinh thần khác",
  //   key: "tinhThanKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vận động",
    key: "vanDong",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB có thể thực hiện các hoạt động sinh hoạt hằng ngày",
      "VĐ hạn chế",
      "Đau khi vận động",
      "Không vận động được",
      "NB đang dùng thuốc mê, tê",
      "NB có chỉ định hạn chế VĐ",
      "NB có chỉ định nghỉ tại giường",
    ]),
  },
  // {
  //   label1: "Vận động khác",
  //   key: "vanDongKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Vệ sinh cá nhân",
    key: "veSinhCaNhan2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Vệ sinh cá nhân sạch sẽ",
      "NB không tự vệ sinh cá nhân được",
    ]),
  },
  // {
  //   label1: "Vệ sinh cá nhân khác",
  //   key: "veSinhCaNhanKhac2",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Giấc ngủ",
    key: "giacNgu2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "NB ngủ tốt",
      "NB ngủ không đủ thời gian sinh lý cần thiết",
      "Mất ngủ liên quan lo âu, chăm sóc trẻ ban đêm",
      "Rối loạn giấc ngủ do mất cân bằng nội tiết",
    ]),
  },
  // {
  //   label1: "Giấc ngủ khác",
  //   key: "giacNguKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiêu hóa",
    key: "tieuHoa",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Bình thường",
      "Đã trung tiện",
      "Chưa trung tiện",
      "Viêm loét miệng",
      "Đầy bụng",
      "khó tiêu",
      "đau bụng",
      "Táo bón",
      "Tiêu chảy",
      "Phân lẫn nhày",
      "Phân lẫn máu",
      "Nôn",
      "Buồn nôn",
      "Nôn ra thức ăn",
      "Nôn khan",
      "Nôn ra máu",
    ]),
  },
  // {
  //   label1: "Tiêu hóa khác",
  //   key: "tieuHoaKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Tiết niệu",
    key: "tietNieu",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "Tiểu bình thường",
      "Nước tiểu vàng trong",
      "Tiểu khó",
      "Bí tiểu",
      "Tiểu ít",
      "Không có nước tiểu",
      "Tiểu buốt",
      "Tiểu rắt",
      "Són tiểu",
      "NT vàng sẫm",
      "NT màu đỏ sẫm",
      "NT có máu",
      "Đặt sond tiểu",
      "Có CĐ bơm rửa bàng quang",
      "Có CĐ rút sonde tiểu",
    ]),
  },
  // {
  //   label1: "Tiết niệu khác",
  //   key: "tietNieuKhac",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Dinh dưỡng",
    key: "dinhDuong2",
    type: "droplist",
    colSpan1: 3,
    data: convertArr([
      "BMI trong giới hạn",
      "NB ăn ngon miệng",
      "NB ăn uống bình thường",
      "NB ăn ít, không ngon miệng",
      "NB có chỉ định ăn sonde",
      "NB nhịn ăn theo chỉ định: Trước phẫu thuật",
      "NB nhịn ăn theo chỉ định: Sau phẫu thuật",
      "NB có chế độ ăn theo tình trạng bệnh lý",
      "Nguy cơ thiếu dinh dưỡng liên quan đến buồn nôn, nôn nghén",
      "Nguy cơ thiếu dinh dưỡng liên quan đến kiêng khem thực phẩm do quan niệm sai lệch",
    ]),
  },
  // {
  //   label1: "Dinh dưỡng khác",
  //   key: "dinhDuong",
  //   type: "string",
  //   colSpan1: 3,
  // },

  {
    label1: "Trước đẻ",
    label2: "Ra nước âm đạo",
    key: "truocDe.raNuocAmDao",
    type: "droplist",
    rowSpan1: 13,
    rowSpan2: 3,
    hint: ["Có", "Không"],
    data: convertArr(["Có", "Không"]),
  },
  {
    key: "truocDe.mauSacNuocAmDao",
    type: "droplist",
    hint: ["Màu sắc"],
    data: convertArr(["Hồng", "Lờ lờ máu cá", "Xanh", "Trong"]),
  },
  {
    key: "truocDe.muiNuocAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Không mùi", "Tanh Nồng", "Hôi", "Khai"]),
  },
  {
    label2: "Ra máu âm đạo",
    key: "truocDe.raMauAmDao",
    type: "droplist",
    hint: ["Có", "Không"],
    data: convertArr(["Có", "Không"]),
    rowSpan2: 3,
  },
  {
    key: "truocDe.mauSacMauAmDao",
    type: "droplist",
    hint: ["Màu sắc"],
    data: convertArr(["Hồng", "Lờ lờ máu cá", "Xanh", "Trong"]),
  },
  {
    key: "truocDe.muiMauAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Không mùi", "Tanh Nồng", "Hôi", "Khai"]),
  },
  {
    label2: "Tình trạng ối",
    key: "truocDe.tinhTrangDauOi",
    type: "droplist",
    hint: ["Đầu ối"],
    data: convertArr(["Dẹt", "Phồng", "Quả lê", "Rỉ ối"]),
    rowSpan2: 3,
  },
  {
    key: "truocDe.tinhTrangNuocOi",
    type: "droplist",
    hint: ["Nước ối"],
    data: convertArr(["Trong", "Xanh bẩn", "Lẫn máu"]),
  },
  {
    key: "truocDe.tinhTrangOi",
    type: "droplist",
    hint: ["Còn ối", "Vớ ối"],
    data: convertArr(["Còn ối", "Vớ ối"]),
  },
  {
    label2: "Cử động thai",
    key: "truocDe.cuDongThai",
    type: "droplist",
    colSpan2: 2,
    data: convertArr(["Có", "Không", "Cử động ít", "Cử động nhiều"]),
  },
  {
    label2: "Cổ TC",
    key: "truocDe.tinhTrangCoTuCung",
    type: "droplist",
    hint: ["Đóng kín - Đang xóa - Mở"],
    data: convertArr(["Đóng kín", "Đang xóa", "Mở"]),
    rowSpan2: 2,
  },
  {
    key: "truocDe.tuTheCoTuCung",
    type: "droplist",
    hint: ["Tư thế"],
    data: convertArr([
      "Trung gian",
      "Chúc ra sau",
      "Đổ ra trước",
      "Mềm",
      "Cứng",
    ]),
  },
  {
    label2: "Khác",
    key: "truocDe.truocDeKhac",
    type: "string",
    colSpan2: 2,
  },
  {
    label1: "Sau đẻ/sau mổ và phụ khoa",
    rowSpan1: 11,
    label2: "Tình trạng vú",
    key: "sauDe.tinhTrangVu",
    type: "droplist",
    data: convertArr([
      "Chưa tiết sữa",
      "Núm vú tụt",
      "Căng tức",
      "Tắc tia sữa",
      "Sưng, nóng, đỏ",
      "Tiết sữa tốt",
      "Vú mềm",
      "Vú bình thường",
    ]),
    colSpan2: 2,
  },
  {
    label2: "Tử Cung",
    rowSpan2: 2,
    key: "sauDe.tuCung",
    type: "droplist",
    hint: ["Chắc - Mềm nhão - Khác"],
    data: convertArr(["Chắc", "Mềm nhão", "Khác"]),
  },
  {
    label2: "Khác (miêu tả)",
    key: "sauDe.ctTuCung",
    type: "string",
  },
  {
    label2: "Ra máu AĐ",
    key: "sauDe.raMauAmDao",
    type: "droplist",
    hint: ["Có - Không"],
    data: convertArr(["Có", "Không"]),
    rowSpan2: 4,
  },
  {
    key: "sauDe.slAmDao",
    type: "string",
    hint: ["Số lượng"],
  },
  {
    key: "sauDe.mauMauAmDao",
    type: "droplist",
    hint: ["Màu sắc"],
    data: convertArr(["Đỏ tươi", "Đỏ sẫm", "Lờ lờ máu cá", "Đen sẫm", "Khác"]),
  },

  {
    key: "sauDe.muiMauAmDao",
    type: "droplist",
    hint: ["Mùi"],
    data: convertArr(["Tanh nồng", "Hôi", "Khác"]),
  },
  {
    label2: "Vết thương",
    rowSpan2: 3,
    hint: ["Vết khâu TSM"],
    key: "sauDe.vetKhauTsm",
    type: "droplist",
    data: convertArr([
      "Vết khâu khô, liền tốt",
      "Vết khâu sưng nề, đỏ",
      "Vết khâu chảy mủ",
    ]),
    mode: "onlyOne",
  },

  {
    hint: ["Vết mổ"],
    key: "sauDe.vetMo",
    type: "droplist",
    data: convertArr([
      "Vết mổ khô, liền tốt",
      "Vết mổ sưng nề, đỏ",
      "Vết ổ chảy mủ",
    ]),
    mode: "onlyOne",
  },
  {
    hint: ["Các tổn thương khác"],
    key: "sauDe.tonThuongKhac",
    type: "string",
  },
  {
    label2: "Các vấn đề khác",
    colSpan2: 2,
    key: "sauDe.sauDeKhac",
    type: "string",
  },
  {
    label1: "Trước PT",
    key: "truocPt",
    colSpan1: 3,
    type: "droplist",
    data: convertArr([
      "NB có chỉ định PT cấp cứu",
      "NB có chỉ định PT kế hoạch",
    ]),
  },
  // {
  //   label1: "Trước PT khác",
  //   key: "truocPtKhac",
  //   colSpan1: 3,
  //   type: "string",
  // },
  {
    label1: "Sau PT",
    label2: "Vết mổ",
    key: "vetMoSauPt",
    rowSpan1: 1,
    type: "droplist",
    colSpan2: 2,
    data: convertArr([
      "NB có vết mổ",
      "NB có dẫn lưu",
      "Đau sau phẫu thuật",
      "Vết mổ khô, liền tốt",
      "Băng vết mổ khô",
      "Vết mổ thấm máu vừa",
      "Thấm dịch tiết",
      "Thấm máu ít",
      "Băng thấm đẫm máu",
      "Vết mổ sưng nề",
      "Chảy mủ",
      "Khác",
    ]),
  },
  {
    label1: "Đánh giá đau",
    key: "danhGiaDauSauPt",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["NB không đau", "Có đau"]),
  },
  {
    label1: "Nguy cơ té ngã",
    key: "nguyCoTeNga",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Nguy cơ ngã", "Không"]),
  },
  {
    label1: "Loét do tỳ đè",
    key: "loetDoTyDe",
    type: "droplist",
    colSpan1: 3,
    data: convertArr(["Loét do tỳ đè", "Không"]),
  },

  {
    label1: "Khác",
    key: "sauPtKhac",
    type: "string",
    colSpan1: 3,
  },
  {
    label1: "GDSK",
    hint: [
      // "1. Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
      // "2. Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
      // "3. Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
    ],
    key: "giaoDucSucKhoe2",
    type: "droplist",
    data: convertArr(
      [
        "Tư vấn giáo dục sức khoẻ cho NB trong ngày đầu vào viện",
        "Tư vấn giáo dục sức khoẻ cho NB trong thời gian nằm viện",
        "Tư vấn giáo dục sức khoẻ cho NB chuẩn bị ra viện",
      ],
      "value"
    ),
    colSpanHint: 2,
  },
  // {
  //   label1: "GDSK khác",
  //   key: "giaoDucSucKhoe",
  //   type: "string",
  //   colSpan1: 3,
  // },
  {
    label1: "Catheter",
    label2: "TM ngoại biên",
    colSpan2: 2,
    key: "tmNgoaiBien",
    type: "droplist",
    rowSpan1: 2,
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },
  {
    label2: "TM trung tâm",
    colSpan2: 2,
    key: "tmTrungTam",
    type: "droplist",
    data: convertArr(["Thông tốt", "Bán tắc", "Tắc"]),
  },
  {
    label1: "Dịch Nhập",
    rowSpan1: 5,
    label2: "Thuốc, dịch truyền (ml)",
    colSpan2: 2,
    key: "dichNhap2.thuocDichTruyen",
  },
  {
    label2: "Máu chế phẩm máu (ml)",
    colSpan2: 2,
    key: "dichNhap2.chePhamMau",
  },
  {
    label2: "Ăn uống (ml)",
    colSpan2: 2,
    key: "dichNhap2.anUong",
  },
  {
    label2: "khác (ml)",
    colSpan2: 2,
    key: "dichNhap2.khac",
  },
  {
    label2: "Tổng nhập (ml)",
    colSpan2: 2,
    key: "dichNhap2.tongNhap",
  },
  {
    label1: "Dịch xuất",
    rowSpan1: 3,
    label2: "Nước tiểu (ml)",
    colSpan2: 2,
    key: "soLuongNuocTieu",
  },
  {
    label2: "Dịch dẫn lưu (ml)",
    colSpan2: 2,
    key: "dichDanLuu",
  },

  {
    label2: "Tổng xuất (ml)",
    colSpan2: 2,
    key: "dichXuat2.tongXuat",
  },

  {
    label1: "Danh sách vấn đề",
    colSpan1: 3,
    key: "dsVanDe",
    type: "string",
  },

  {
    label1: "Thực hiện y lệnh",
    colSpan1: 3,
    key: "dsThucHienYLenh",
    type: "droplist",
    data: DS_THUC_HIEN_Y_LENH,
    isShowSearch: true,
  },
  {
    label1: "Thực hiện y lệnh khác",
    colSpan1: 3,
    key: "thucHienYLenhKhac",
    type: "string",
  },
  {
    label1: "Chăm sóc",
    colSpan1: 3,
    key: "dsChamSoc",
    type: "droplist",
    data: DS_THUC_HIEN_CHAM_SOC,
  },
  {
    label1: "Chăm sóc khác",
    colSpan1: 3,
    key: "chamSocKhac",
    type: "string",
  },
  {
    label1: "Đánh giá",
    colSpan1: 3,
    key: "dsDanhGia",
    type: "droplist",
    data: DS_DANH_GIA,
  },
  {
    label1: "Đánh giá khác",
    colSpan1: 3,
    key: "danhGiaKhac",
    type: "string",
  },

  {
    label1: "Ghi chú / bàn giao",
    colSpan1: 3,
    key: "ghiChuBanGiao",
    type: "string",
  },
  {
    label1: "Tên điều dưỡng thực hiện",
    colSpan1: 3,
    type: "sign",
  },
];
