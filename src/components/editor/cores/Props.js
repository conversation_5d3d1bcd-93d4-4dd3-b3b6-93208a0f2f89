import TableProps from "./Table/Properties";
import BarcodeProps from "./Barcode/Properties";
import QRcodeProps from "./QRcode/Properties";
import InputNumberProps from "./InputNumber/Properties";
import InputTuoiThaiProps from "./InputTuoiThai/Properties";
import TinhTongProps from "./TinhTong/Properties";
import DateProps from "./DatePicker/Properties";
import LabelProps from "./Label/Properties";
import CheckGroupsProps from "./CheckGroups/Properties";
import LayoutProps from "./Layout/Properties";
import TextFieldProps from "./TextField/Properties";
import CodeInputProps from "./CodeInput/Properties";
import Image from "./Image/Properties";
import InputGroupProps from "./InputGroup/Properties";
import ArrayConverterProps from "./ArrayConverter/Properties";
import BreakLineProps from "./BreakLine/Properties";
import TitleProps from "./Title/Properties";
import TitleParamProps from "./TitleParam/Properties";
import InputProps from "./Input/Properties";
import NameWrapperProps from "./FieldsWrapper/Properties";
import PageProps from "./Page/Properties";
import DropDownListProps from "./DropDownList/Properties";
import BangTheoDoiBenhNhanHSTCProps from "./BangTheoDoiBenhNhanHSTC/Properties";
import BangTheoDoiBenhNhanGMHSProps from "./BangTheoDoiBenhNhanGMHS/Properties";
import BangTheoDoiHoiTinhProps from "./BangTheoDoiHoiTinh/Properties";
import ToDieuTriProps from "./ToDieuTri/Properties";
import FormConfig from "components/editor/config/FormInfo/FormConfig";
import BangKeChiPhiProperties from "./BangKeChiPhi/Properties";
import PhieuYLenhProperties from "./PhieuYLenh/Properties";
import FooterAndHeaderProps from "./FooterAndHeader/Properties";
import PhieuTruyenMauProps from "./PhieuTruyenMau/Properties";
import PhieuChamSocNangProps from "./PhieuChamSocNang/Properties";
import PhieuChamSocNheProps from "./PhieuChamSocNhe/Properties";
import BaoCaoADRProps from "./BaoCaoADR/Properties";
import PhieuHuyThuocProps from "./PhieuHuyThuoc/Properties";
import PhieuTheoDoiPhucHoiChucNangProps from "./PhieuTheoDoiPhucHoiChucNang/Properties";
import PhieuThucHienVaCongKhaiThuocProps from "./PhieuThucHienVaCongKhaiThuoc/Properties";
import ImageSignProp from "./ImageSign/Properties";
import ViewPdfProps from "./ViewPdf/Properties";
import ElectricSignatureMultipleProps from "./ElectronicSignatureMultiple/Properties";
import BangTheoDoiBenhNhanGMHSProps2 from "./BangTheoDoiBenhNhanGMHS2/Properties";
import PhieuChamSocCap23Properties from "./PhieuChamSocCap23/Properties";
import PhieuChamSocCap23OldProperties from "./PhieuChamSocCap23_old/Properties";
import BangTheoDoiVaChamSocProperties from "./BangTheoDoiVaChamSoc/Properties";
import BangTheoDoiVaChamSoc2Properties from "./BangTheoDoiVaChamSoc2/Properties";
import BangTheoDoiVaChamSocCap1Properties from "./BangTheoDoiChamSocCap1/Properties";
import BangChePhamDDProperties from "./BangChePhamDD/Properties";
import KetQuaXNBenhAnSaoProperties from "./KetQuaXnBenhAnSao/Properties";
import NoiDungTuVanProps from "./NoiDungTuVanTinhTrangNguoiBenh/Properties";
import BangHoSoXaTriProps from "./BangHoSoXaTri/Properties";
import BangCamDoanPHCNProps from "./BangCamDoanPHCN/Properties";
import TheoDoiVaChamSocNheCap3Props from "./TheoDoiVaChamSocNheCap3/Properties";
import TheoDoiVaChamSocNheCap3OldProps from "./TheoDoiVaChamSocNheCap3_Old/Properties";
import BangChamSocSanPhuSauDeProps from "./BangChamSocSanPhuSauDe/Properties";
import TheoDoiVaChamSocNangCap12Props from "./TheoDoiVaChamSocNangCap12/Properties";
import BangTheoDoiTaiPhongHoiTinhProps from "./BangTheoDoiTaiPhongHoiTinh/Properties";
import BangDanhGiaHoiTinhProps from "./BangDanhGiaHoiTinh/Properties";
import BangCanThiepDuocProps from "./BangCanThiepDuoc/Properties";
import BangApgarProps from "./BangApgar/Properties";
import PhieuChamSocKhoiNgoaiProps from "./PhieuChamSocKhoiNgoai/Properties";
import PhieuChamSocCap23NTProps from "./PhieuChamSocCap23NT/Properties";
import BangChamSocC1TongHopProps from "./BangChamSocCap1TongHop/Properties";
import BangThucHienThuThuatProps from "./BangThucHienThuThuat/Properties";
import BangTheoDoiECMOProps from "./BangTheoDoiECMO/Properties";
import PhieuChamSocCap23TDProps from "./PhieuChamSocCap23TD/Properties";
import BmiProps from "./Bmi/Properties";
import BangTheoDoiDichTruyenBvpProps from "./BangTheoDoiDichTruyenBvp/Properties";
import BangKiemNguyCoNgaMorseProps from "./BangKiemNguyCoNgaMorse/Properties";
import BangKiemNguyCoNgaMorseDDProps from "./BangKiemNguyCoNgaMorseDD/Properties";
import PhieuChamSocCap23PSTWProps from "./PhieuChamSocCap23PSTW/Properties";
import PhieuChamSocCap23PhuKhoaPSTWProps from "./PhieuChamSocCap23PhuKhoaPSTW/Properties";
import PhieuChamSocTreSoSinhProps from "./PhieuChamSocTreSoSinh/Properties";
import BangDauHieuSinhTonProps from "./BangDauHieuSinhTon/Properties";
import PhieuChamSocCap23CRProps from "./PhieuChamSocCap23CR/Properties";
import BangTheoDoiChamSocCap1CRProps from "./BangTheoDoiChamSocCap1CR/Properties";
import BieuDoChuyenDaProps from "./BieuDoChuyenDa/Properties";
import BangTheoDoiVaChamSocNbProps from "./PhieuTheoDoiVaChamSocNb/Properties";
import PhieuTheoDoiTNTProps from "./PhieuTheoDoiTNT/Properties";
import BangDanhGiaNguyCoTiDeProps from "./BangDanhGiaNguyCoTiDe/Properties";
import PhieuChamSocSoSinhNamCungMeProps from "./PhieuChamSocSoSinhNamCungMe/Properties";
import PhieuChamSocCap23PSHNProps from "./PhieuChamSocCap23PSHN/Properties";
import BangPhacDoKhangSinhYeuCauProps from "./BangPhacDoKhangSinhYeuCau/Properties";
import BangTheoDoiBenhNhanGMHSPSTWProps from "./BangTheoDoiBenhNhanGMHSPSTW/Properties";
import BangDanhGiaNguyCoTiDeDongDaProps from "./BangDanhGiaNguyCoTiDeDongDa/Properties";
import BangTheoDoiGiamDauProps from "./BangTheoDoiGiamDau/Properties";
import PhieuChamSocSanPhu23TDProps from "./PhieuChamSocSanPhu23TD/Properties";
import PhieuChamSocCap23HHProps from "./PhieuChamSocCap23HH/Properties";
import BangTheoDieuTriThanNhanTaoProps from "./BangTheoDieuTriThanNhanTao/Properties";
import PhieuCsCap1NoiNgoaiUbProps from "./PhieuCsCap1NoiNgoaiUb/Properties";
import PhieuCsCap23NoiNgoaiUbProps from "./PhieuCsCap23NoiNgoaiUb/Properties";
import PhieuCsCap1NhiBVPProps from "./PhieuCsCap1NhiBVP/Properties";
import PhieuCsCap23NhiBVPProps from "./PhieuCsCap23NhiBVP/Properties";

export default {
  label: LabelProps,
  textField: TextFieldProps,
  date: DateProps,
  barcode: BarcodeProps,
  qrcode: QRcodeProps,
  layout: LayoutProps,
  groupCheck: CheckGroupsProps,
  table: TableProps,
  inputCombo: CodeInputProps,
  inputNumber: InputNumberProps,
  inputTuoiThai: InputTuoiThaiProps,
  tinhTong: TinhTongProps,
  image: Image,
  inputGroup: InputGroupProps,
  arrayConverter: ArrayConverterProps,
  title: TitleProps,
  titleParam: TitleParamProps,
  input: InputProps,
  nameWrapper: NameWrapperProps,
  electronicSignature: ImageSignProp,
  viewPdf: ViewPdfProps,
  breakLine: BreakLineProps,
  page: PageProps,
  dropDownList: DropDownListProps,
  bangTheoDoiBenhNhanHSTC: BangTheoDoiBenhNhanHSTCProps,
  bangTheoDoiBenhNhanGMHS: BangTheoDoiBenhNhanGMHSProps,
  bangTheoDoiHoiTinh: BangTheoDoiHoiTinhProps,
  toDieuTri: ToDieuTriProps,
  bangKeChiPhi: BangKeChiPhiProperties,
  phieuYLenh: PhieuYLenhProperties,
  formConfig: FormConfig,
  footerAndHeader: FooterAndHeaderProps,
  phieuTruyenMau: PhieuTruyenMauProps,
  phieuChamSocNang: PhieuChamSocNangProps,
  phieuChamSocNhe: PhieuChamSocNheProps,
  baoCaoADR: BaoCaoADRProps,
  phieuHuyThuoc: PhieuHuyThuocProps,
  phieuTheoDoiPhucHoiChucNang: PhieuTheoDoiPhucHoiChucNangProps,
  phieuThucHienVaCongKhaiThuoc: PhieuThucHienVaCongKhaiThuocProps,
  imageSign: ImageSignProp,
  electronicSignatureMultiple: ElectricSignatureMultipleProps,
  bangTheoDoiBenhNhanGMHS2: BangTheoDoiBenhNhanGMHSProps2,
  phieuChamSocCap23: PhieuChamSocCap23Properties,
  phieuChamSocCap23Old: PhieuChamSocCap23OldProperties,
  bangTheoDoiVaChamSoc: BangTheoDoiVaChamSocProperties,
  bangTheoDoiVaChamSoc2: BangTheoDoiVaChamSoc2Properties,
  bangTheoDoiVaChamSocCap1: BangTheoDoiVaChamSocCap1Properties,
  bangChePhamDD: BangChePhamDDProperties,
  ketQuaXnBenhAnSao: KetQuaXNBenhAnSaoProperties,
  noiDungTuVanTinhTrangNguoiBenh: NoiDungTuVanProps,
  bangHoSoXaTri: BangHoSoXaTriProps,
  bangCamDoanPHCN: BangCamDoanPHCNProps,
  theoDoiVaChamSocNheCap3: TheoDoiVaChamSocNheCap3Props,
  theoDoiVaChamSocNheCap3Old: TheoDoiVaChamSocNheCap3OldProps,
  bangChamSocSanPhuSauDe: BangChamSocSanPhuSauDeProps,
  theoDoiVaChamSocNangCap12: TheoDoiVaChamSocNangCap12Props,
  bangTheoDoiTaiPhongHoiTinh: BangTheoDoiTaiPhongHoiTinhProps,
  bangDanhGiaHoiTinh: BangDanhGiaHoiTinhProps,
  bangCanThiepDuoc: BangCanThiepDuocProps,
  bangApgar: BangApgarProps,
  phieuChamSocKhoiNgoai: PhieuChamSocKhoiNgoaiProps,
  phieuChamSocCap23NT: PhieuChamSocCap23NTProps,
  bangChamSocC1TongHop: BangChamSocC1TongHopProps,
  bangThucHienThuThuat: BangThucHienThuThuatProps,
  bangTheoDoiECMO: BangTheoDoiECMOProps,
  phieuChamSocCap23TD: PhieuChamSocCap23TDProps,
  bmi: BmiProps,
  bangTheoDoiDichTruyenBvp: BangTheoDoiDichTruyenBvpProps,
  bangKiemNguyCoNgaMorse: BangKiemNguyCoNgaMorseProps,
  bangKiemNguyCoNgaMorseDD: BangKiemNguyCoNgaMorseDDProps,
  phieuChamSocCap23PSTW: PhieuChamSocCap23PSTWProps,
  phieuChamSocCap23PhuKhoaPSTW: PhieuChamSocCap23PhuKhoaPSTWProps,
  phieuChamSocTreSoSinh: PhieuChamSocTreSoSinhProps,
  bangDauHieuSinhTon: BangDauHieuSinhTonProps,
  phieuChamSocCap23CR: PhieuChamSocCap23CRProps,
  bangTheoDoiChamSocCap1CR: BangTheoDoiChamSocCap1CRProps,
  bieuDoChuyenDa: BieuDoChuyenDaProps,
  bangTheoDoiVaChamSocNb: BangTheoDoiVaChamSocNbProps,
  phieuTheoDoiTNTProps: PhieuTheoDoiTNTProps,
  bangDanhGiaNguyCoTiDe: BangDanhGiaNguyCoTiDeProps,
  phieuChamSocSoSinhNamCungMe: PhieuChamSocSoSinhNamCungMeProps,
  phieuChamSocCap23PSHN: PhieuChamSocCap23PSHNProps,
  bangPhacDoKhangSinhYeuCau: BangPhacDoKhangSinhYeuCauProps,
  bangTheoDoiBenhNhanGMHSPSTW: BangTheoDoiBenhNhanGMHSPSTWProps,
  bangDanhGiaNguyCoTiDeDongDa: BangDanhGiaNguyCoTiDeDongDaProps,
  bangTheoDoiGiamDau: BangTheoDoiGiamDauProps,
  phieuChamSocSanPhu23TD: PhieuChamSocSanPhu23TDProps,
  phieuChamSocCap23HH: PhieuChamSocCap23HHProps,
  bangTheoDieuTriThanNhanTao: BangTheoDieuTriThanNhanTaoProps,
  phieuCsCap1NoiNgoaiUb: PhieuCsCap1NoiNgoaiUbProps,
  phieuCsCap23NoiNgoaiUb: PhieuCsCap23NoiNgoaiUbProps,
  phieuCsCap1NhiBVP: PhieuCsCap1NhiBVPProps,
  phieuCsCap23NhiBVP: PhieuCsCap23NhiBVPProps,
};
