import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { TR, TR_PHIEU_CAP1 } from "../constants";
import { cloneDeep, get } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { updateObject } from "utils";
import { But<PERSON>, Col, message, Row } from "antd";
import CheckGroups from "../../CheckGroups";
import { useStore } from "hooks";
import { SVG } from "assets";
import PopoverSelect from "../../Components/PopoverSelect";
import RenderChanDoan from "./RenderChanDoanDieuDuong";
import ImageSign from "../../ImageSign";
import { combineFields, MODE } from "utils/editor-utils";
import RenderBmi from "../../PhieuChamSocCap23PSTW/components/RenderBmi";
import RenderNhapXuat from "../../PhieuChamSocCap23PSTW/components/RenderNhapXuat";
import CopyPasteCol from "../../Components/CopyPasteCol";
import { refConfirm } from "app";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import { useTranslation } from "react-i18next";
import HeaderFormChamSocC2PSHN from "../../Components/HeaderFormChamSocC2PSHN";
import SelectRow from "./SelectRow";
import { PlusOutlined } from "@ant-design/icons";

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  itemProps,
  form,
  dataCopy,
  setDataCopy,
  arr,
  showIconAdd,
  handleAdd,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refChanDoan = useRef();
  const refValue = useRef();
  const refBmi = useRef();
  const refTongXuat = useRef();
  const refTongNhap = useRef();

  useEffect(() => {
    refValue.current = item;
    setState({
      value: item || {},
    });
  }, [item]);

  const onChangeHuyetAp = (index) => (value) => {
    if (value.split("/").length - 1 !== 1) {
      message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 120/90 ");
      return;
    }
    const arr = value.split("/");
    const huyetApTamThu = +arr[0];
    const huyetApTamTruong = +arr[1];
    if (!refValue.current["dsChiTiet"][index]?.chiSoSong) {
      refValue.current["dsChiTiet"][index].chiSoSong = {
        chiDinhTuLoaiDichVu: 201,
        khoaChiDinhId: form.khoaChiDinhId,
        nbDotDieuTriId: form.nbDotDieuTriId,
      };
    }
    if (huyetApTamThu < huyetApTamTruong) {
      message.error("Huyết áp tâm thu cần lớn hơn huyết áp tâm trương");
    } else {
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamThu`,
        huyetApTamThu
      );
      updateObject(
        refValue.current["dsChiTiet"][index].chiSoSong,
        `huyetApTamTruong`,
        huyetApTamTruong
      );
    }
  };

  const onChangeVE = (index) => (value) => {
    const arr = value.split("/");
    const ve = +arr[0];

    updateObject(refValue.current["dsChiTiet"][index], `ve`, ve);
    if (arr.length > 1) {
      const vh = +arr[1];
      updateObject(refValue.current["dsChiTiet"][index], `vh`, vh);
    }
  };

  const onChangeInput = useCallback(
    (key, idx) => (value) => {
      const keySplit = (key || "").split(".");
      let key1, key2;
      key1 = keySplit[0];
      if (keySplit.length > 1) {
        key2 = keySplit[1];
      }
      if (
        !key.includes("thoiGianThucHien") &&
        !refValue.current.dsChiTiet[idx]?.thoiGianThucHien
      ) {
        message.error("Vui lòng nhập thời gian thực hiện!");
      }
      if (key.includes("huyetAp")) {
        onChangeHuyetAp(idx)(value);
      } else if (key == "veh") {
        onChangeVE(idx)(value);
      } else {
        if (!key2) {
          updateObject(refValue.current.dsChiTiet[idx], key1, value);
        } else {
          if (!refValue.current.dsChiTiet[idx][key1]) {
            if (key1.includes("chiSoSong")) {
              refValue.current.dsChiTiet[idx][key1] = {
                chiDinhTuLoaiDichVu: 201,
                khoaChiDinhId: form.khoaChiDinhId,
                nbDotDieuTriId: form.nbDotDieuTriId,
              };
            } else {
              refValue.current.dsChiTiet[idx][key1] = {};
            }
          }
          if (get(refValue.current.dsChiTiet[idx], `${key1}`)) {
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          } else {
            refValue.current.dsChiTiet[idx][key1] = {};
            updateObject(
              refValue.current.dsChiTiet[idx][key1],
              `${key2}`,
              value
            );
          }
        }
      }
      refChanDoan.current.changeKey(key, idx, value);
      if (key === "thoiGianThucHien") {
        const dsChiTietExitTG = refValue.current.dsChiTiet.filter(
          (el) => el.thoiGianThucHien
        );
        const dsChiTietNotExitTG = refValue.current.dsChiTiet.filter(
          (el) => !el.thoiGianThucHien
        );
        dsChiTietExitTG.sort(
          (a, b) =>
            (a.thoiGianThucHien ? new Date(a.thoiGianThucHien) : new Date()) -
            (b.thoiGianThucHien ? new Date(b.thoiGianThucHien) : new Date())
        );
        refValue.current.dsChiTiet = dsChiTietExitTG.concat(dsChiTietNotExitTG);
        setState({
          value: cloneDeep(refValue.current),
        });
      }
      if (["tmNgoaiBien", "tmTrungTam"].includes(key)) {
        setState({
          value: cloneDeep(refValue.current),
        });
      }
      if (["chiSoSong.canNang", "chiSoSong.chieuCao"].includes(key)) {
        const { canNang, chieuCao } = get(
          refValue.current,
          `dsChiTiet[${idx}].chiSoSong`,
          {}
        );
        if (canNang && chieuCao) {
          const bmi = canNang / ((chieuCao / 100) * (chieuCao / 100));
          refValue.current["dsChiTiet"][idx]["chiSoSong"]["bmi"] =
            bmi.toFixed(2);
          refBmi.current.setValue(refValue.current);
        }
      }
      if (key.includes("dichNhap2")) {
        const { thuocDichTruyen, chePhamMau, anUong, khac } = get(
          refValue.current,
          `dsChiTiet[${idx}].dichNhap2`,
          {}
        );
        const tongNhap =
          (thuocDichTruyen ? parseFloat(thuocDichTruyen) : 0) +
          (chePhamMau ? parseFloat(chePhamMau) : 0) +
          (anUong ? parseFloat(anUong) : 0) +
          (khac ? parseFloat(khac) : 0);
        updateObject(
          refValue.current.dsChiTiet[idx]["dichNhap2"],
          `tongNhap`,
          tongNhap
        );
        refTongNhap.current.setValue(refValue.current);
      }

      if (key.includes("dichXuat2")) {
        const { nuocTieu, dichDanLuu, khac } = get(
          refValue.current,
          `dsChiTiet[${idx}].dichXuat2`,
          {}
        );
        const tongXuat =
          (nuocTieu ? parseFloat(nuocTieu) : 0) +
          (dichDanLuu ? parseFloat(dichDanLuu) : 0) +
          (khac ? parseFloat(khac) : 0);
        updateObject(
          refValue.current.dsChiTiet[idx]["dichXuat2"],
          `tongXuat`,
          tongXuat
        );
        refTongXuat.current.setValue(refValue.current);
      }
      formChangeValue();
    },
    [refValueForm, form, Object.keys(formChange || {}).length]
  );

  const formChangeValue = () => {
    const indexKhung = refValueForm.findIndex((el) => el.stt === tableIndex);
    refValueForm[indexKhung] = refValue.current;
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
  };

  const onChangeValueChung = useCallback(
    (key) => (value) => {
      formChange[key](value);
    },
    [Object.keys(formChange || {}).length]
  );

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    if (dataCopy.chiSoSong) {
      delete dataCopy.chiSoSong.id;
      delete dataCopy.chiSoSong.createdAt;
      delete dataCopy.chiSoSong.createdBy;
      delete dataCopy.chiSoSong.nguoiThucHienId;
      delete dataCopy.chiSoSong.updatedAt;
      delete dataCopy.chiSoSong.updatedBy;
    }

    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dataCurrent = cloneDeep(refValue.current.dsChiTiet[idx]);
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);

    refValue.current.dsChiTiet[idx].stt = dataCurrent.stt;
    setState({
      value: cloneDeep(refValue.current),
    });
    formChangeValue();
  };

  const handleDelete = (idx) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSong?.id) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSong?.id
              );
            }
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            formChangeValue();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const render = useMemo(() => {
    return (
      <>
        {itemProps.vienPSHN ? (
          <HeaderFormChamSocC2PSHN
            form={form}
            mode={mode}
            formChange={formChange}
            tableIndex={tableIndex}
            loaiPhieu={itemProps.loaiPhieu}
          ></HeaderFormChamSocC2PSHN>
        ) : (
          <div className="header-form">
            <Row className="flex">
              <Col span={6} className="content-title-left">
                <div className="center">{form?.tieuDeTrai1}</div>
                <div className="center">
                  <b>{form?.tieuDeTrai2}</b>
                </div>
                <div className="center">{form?.tenKhoaNb}</div>
              </Col>
              <Col span={12} className="title-center">
                <div className="ten-phieu">PHIẾU THEO DÕI VÀ CHĂM SÓC</div>
                <div>
                  {itemProps.loaiPhieu == 2 ? "(Cấp 1)" : "(Cấp 2 - 3)"}{" "}
                </div>
                <DeboundInput
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ width: 100 }}
                  label="Tờ số  : "
                  onChange={onChangeInput("toSo")}
                  value={item.stt}
                  inputNumber={true}
                  readOnly={true}
                />
              </Col>
              <Col span={6} className="title-right">
                <div>Số vào viện: {form?.maBenhAn}</div>
                <div>Mã LK: {form?.maHoSo}</div>
              </Col>
            </Row>
            <Row className="name-patient">
              <Col span={12}>
                Họ tên người bệnh: ​ <b>{form.tenNb}</b>
              </Col>
              <Col span={4}>Tuổi: {form.tuoi}</Col>
              <Col span={8}>
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Giới tính: Nam",
                          value: 1,
                        },
                        {
                          label: "Nữ",
                          value: 2,
                        },
                      ],
                      fieldName: "gioiTinh",
                      readOnly: true,
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10, minWidth: 200 }}>
                  Phòng: {form.tenPhong}
                </span>
                <span>Giường: {form.soHieuGiuong}</span>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <span style={{ marginRight: 10 }}>
                  Chẩn đoán: {form.enCdChinh}
                </span>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="flex">
                <CheckGroups
                  component={{
                    props: {
                      direction: "rtl",
                      type: "onlyOne",
                      checkList: [
                        {
                          label: "Tiền sử dị ứng: ",
                          value: 1,
                        },
                        {
                          label: "Chưa ghi nhận",
                          value: 2,
                        },
                      ],
                      fieldName: "tienSuDiUng",
                    },
                  }}
                  mode={mode}
                  form={form}
                  formChange={formChange}
                />
                <DeboundInput
                  //   disabled={disabled}
                  size={"small"}
                  value={get(form, "ghiRo", "")}
                  onChange={onChangeValueChung("ghiRo")}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={24}
                  styleMain={{ flex: 1 }}
                  label="Có, Dị nguyên: "
                />
              </Col>
            </Row>
          </div>
        )}

        <table style={{ marginTop: 20 }}>
          <tbody>
            {(itemProps.loaiPhieu === 2 ? TR_PHIEU_CAP1 : TR).map(
              (item, index) => {
                const keySplit = (item?.key || "").split(".");
                let key1, key2;
                key1 = keySplit[0];
                if (keySplit.length > 1) {
                  key2 = keySplit[1];
                }

                if (item.key == "dsVanDe") {
                  return (
                    <RenderChanDoan
                      ref={refChanDoan}
                      onChangeInput={onChangeInput}
                      data={state.value}
                      refValue={refValue}
                      formChangeValue={formChangeValue}
                    ></RenderChanDoan>
                  );
                } else if (item.key === "chiSoSong.bmi") {
                  return (
                    <tr>
                      <td colSpan={2}>BMI</td>
                      <RenderBmi
                        ref={refBmi}
                        value={state.value}
                        arr={arr}
                      ></RenderBmi>
                    </tr>
                  );
                } else if (
                  ["dichXuat2.tongXuat", "dichNhap2.tongNhap"].includes(
                    item.key
                  )
                ) {
                  return (
                    <tr>
                      <td colSpan={3}>
                        <b>
                          {item.key === "dichXuat2.tongXuat"
                            ? "Tổng xuất (ml)"
                            : "Tổng nhập (ml)"}
                        </b>
                      </td>
                      <RenderNhapXuat
                        ref={
                          item.key === "dichXuat2.tongXuat"
                            ? refTongXuat
                            : refTongNhap
                        }
                        value={state.value}
                        formChangeValue={formChangeValue}
                        refValue={refValue}
                        refValueForm={refValueForm}
                        tableIndex={tableIndex}
                        formChange={formChange}
                        keyItem={item.key}
                        arr={arr}
                      />
                    </tr>
                  );
                } else if (item.listRow) {
                  return (
                    <SelectRow
                      itemRow={item}
                      value={state.value}
                      formChangeValue={formChangeValue}
                      refValue={refValue}
                      refValueForm={refValueForm}
                      tableIndex={tableIndex}
                      formChange={formChange}
                      arr={arr}
                      onChangeInput={onChangeInput}
                    ></SelectRow>
                  );
                } else {
                  return (
                    <tr key={`${item.key}_${index}`}>
                      {item.label1 && (
                        <td
                          rowSpan={item.rowSpan1 || 1}
                          colSpan={item.colSpan1 || 1}
                          style={{
                            position: "relative",
                            width: 50,
                            minWidth: 50,
                            maxWidth: 50,
                          }}
                        >
                          {mode !== MODE.config && showIconAdd && !index && (
                            <Button
                              className="btn-add"
                              icon={<PlusOutlined />}
                              onClick={handleAdd}
                              size={"small"}
                              type="primary"
                            ></Button>
                          )}
                          {/* {tableIndex && !index ? (
                            <div>
                              <SVG.IcDelete
                                className="ic-remove"
                                onClick={onRemove(tableIndex)}
                              />
                            </div>
                          ) : null} */}
                          <b>{item.label1}</b>
                        </td>
                      )}
                      {item.label2 && (
                        <td
                          rowSpan={item.rowSpan2 || 1}
                          colSpan={item.colSpan2 || 1}
                        >
                          {item.label2}
                        </td>
                      )}
                      {item.hint && (
                        <td
                          className="hint"
                          rowSpan={item.rowSpanHint || 1}
                          colSpan={item.colSpanHint || 1}
                          style={{ width: 100, minWidth: 100 }}
                        >
                          {(item.hint || []).map((item, index) => (
                            <span key={index}>{item}</span>
                          ))}
                        </td>
                      )}

                      {arr.map((el, idx) => {
                        let dataDroplist = cloneDeep(item.data);
                        const tmNgoaiBien = get(
                          refValue.current,
                          `dsChiTiet[${idx}].tmNgoaiBien`,
                          []
                        );
                        const tmTrungTam = get(
                          refValue.current,
                          `dsChiTiet[${idx}].tmTrungTam`,
                          []
                        );

                        if (
                          ["dsChamSoc"].includes(item.key) &&
                          ((tmTrungTam || []).some((x) => [2, 3].includes(x)) ||
                            (tmNgoaiBien || []).some((x) => [2, 3].includes(x)))
                        ) {
                          dataDroplist = dataDroplist.concat([
                            {
                              label: "Thông tráng catherter bằng DD NaCL0,9%",
                              value: 13,
                            },
                            {
                              label: "Đặt lại catheter",
                              value: 14,
                            },
                          ]);
                        }
                        return item.disable ? (
                          <td key={`${item.key}_${index}_${idx}`}></td>
                        ) : item.key == "ngayThucHien" ? (
                          <td
                            key={`${item.key}_${index}_${idx}`}
                            style={{
                              width: 160,
                              minWidth: 160,
                              maxWidth: 160,
                              position: "relative",
                            }}
                            className="col-element"
                          >
                            <CopyPasteCol
                              colIndex={idx}
                              handlePaste={hanldePaste(idx)}
                              handleCopy={handleCopy(idx)}
                              dataCopy={dataCopy}
                              handleDelete={handleDelete(idx)}
                              showSinhHieu={false}
                            />
                            <AppDatePicker
                              component={{
                                props: {
                                  contentAlign: "center",
                                  dateTimeFormat: "HH:mm D/M/Y",
                                  fieldName: "value",
                                  disableOnblur: true,
                                  fontSize: 8,
                                },
                              }}
                              form={{
                                value: get(
                                  state.value,
                                  `dsChiTiet[${idx}].thoiGianThucHien`
                                ),
                              }}
                              mode={mode}
                              formChange={{
                                value: (e) => {
                                  try {
                                    onChangeInput(`thoiGianThucHien`, idx)(e);
                                  } catch (error) {}
                                },
                              }}
                            />
                          </td>
                        ) : item.type === "droplist" ? (
                          <td>
                            <PopoverSelect
                              data={dataDroplist}
                              value={get(
                                state.value,
                                `dsChiTiet[${idx}][${key1}]${
                                  key2 ? `${key2}` : ""
                                }`
                              )}
                              onChangeValue={(e) => {
                                onChangeInput(item.key, idx)(e);
                              }}
                              isMultiple={!(item.mode == "onlyOne")}
                              trigger="click"
                              isShowSearch={
                                (Array.isArray(item.data) ? item.data : [])
                                  .length > 15
                              }
                            />
                          </td>
                        ) : item.type == "sign" ? (
                          <td key={`${item.key}_${index}_${idx}`}>
                            <ImageSign
                              component={{
                                props: {
                                  ...itemProps,
                                  isMultipleSign: true,
                                  viTri: get(
                                    state.value,
                                    `dsChiTiet[${idx}].stt`
                                  ),
                                  customText: "Ký",
                                  dataSign: {
                                    id: form.id,
                                    soPhieu: form?.lichSuKy?.soPhieu,
                                    lichSuKyId: form?.lichSuKy?.id,
                                  },
                                },
                              }}
                              form={{
                                ...combineFields(form),
                              }}
                            />
                          </td>
                        ) : (
                          <td key={`${item.key}_${index}_${idx}`}>
                            <DeboundInput
                              readOnly={false}
                              value={get(
                                state.value,
                                `dsChiTiet[${idx}]${key1}${
                                  key2 ? `[${key2}]` : ""
                                }`
                              )}
                              onChange={onChangeInput(item.key, idx)}
                              type="multipleline"
                              lineHeightText={1.5}
                              fontSize={9}
                              minHeight={9 + 6}
                              markSpanRow={false}
                              contentAlign="center"
                              inputNumber={item.type === "number"}
                              typeNumber={"float"}
                            />
                          </td>
                        );
                      })}
                    </tr>
                  );
                }
              }
            )}
          </tbody>
        </table>
      </>
    );
  }, [state.value, onChangeValueChung, refValueForm, dataCopy]);

  return render;
};

const RenderNguoiThucHien = ({
  value,
  idx,
  formChangeValue,
  refModalSelect,
  title,
  keyItem,
}) => {
  const [state, _setState] = useState({
    data: null,
    itemSelected: {},
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);

  useEffect(() => {
    const itemSelected = listAllNhanVien.find((item) => item.id == value);

    setState({
      itemSelected,
      data: value,
    });
  }, [value, listAllNhanVien]);

  const handleClick = () => {
    refModalSelect.show({ value: state.data, title }, (id) => {
      const itemSelected = listAllNhanVien.find((item) => item.id == id);
      setState({
        itemSelected,
        data: id,
        title,
      });
      formChangeValue(`dsChiTiet.${keyItem}`, idx)(id);
    });
  };
  return (
    <div style={{ width: "100%", minHeight: 24 }} onClick={handleClick}>
      {state.itemSelected?.ten}
    </div>
  );
};

export default RenderTable;
