import React, { useEffect, useMemo, useRef, useState } from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { get } from "lodash";
import { refConfirm } from "app";

export const NUM_OF_COLS = 4;
const arr = new Array(NUM_OF_COLS).fill({});

const convertSttDuplicates = (data) => {
  // Tạo bản sao để không làm thay đổi dữ liệu gốc
  const result = JSON.parse(JSON.stringify(data));

  // Tìm stt lớn nhất hiện tại
  let maxStt = 0;
  const usedStt = new Set();

  // Lần đầu: thu thập tất cả stt đã sử dụng và tìm max
  result.forEach((item) => {
    if (item.dsChiTiet && Array.isArray(item.dsChiTiet)) {
      item.dsChiTiet.forEach((chiTiet) => {
        if (chiTiet.stt !== undefined) {
          maxStt = Math.max(maxStt, chiTiet.stt);
        }
      });
    }
  });

  // Lần thứ hai: xử lý các stt bị lặp theo thứ tự từ cuối lên đầu
  for (let i = result.length - 1; i >= 0; i--) {
    const item = result[i];
    if (item.dsChiTiet && Array.isArray(item.dsChiTiet)) {
      // Xử lý từng chi tiết trong dsChiTiet
      item.dsChiTiet.forEach((chiTiet) => {
        if (chiTiet.stt !== undefined) {
          // Nếu stt này đã được sử dụng trước đó
          if (usedStt.has(chiTiet.stt)) {
            // Tạo stt mới
            maxStt++;
            chiTiet.stt = maxStt;
          } else {
            // Đánh dấu stt này đã được sử dụng
            usedStt.add(chiTiet.stt);
          }
        }
      });
    }
  }

  return result;
};
const PhieuChamSocCap23PhuKhoaPSTW = (props) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsTheoDoi: [{}],
  });
  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refValueForm = useRef();
  const refModalSelect = useRef();

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  function setSTT(arr) {
    const len = arr.length;
    return arr.map((item, index) => ({
      ...item,
      stt: len - index,
    }));
  }

  useEffect(() => {
    if (form) {
      refValueForm.current = form.dsTheoDoi || [{}];
      refValueForm.current = setSTT(refValueForm.current);
      if (refValueForm.current.every((el) => !el.stt)) {
        refValueForm.current = refValueForm.current.reverse();
      }
      refValueForm.current.forEach((item, indexDs) => {
        if (!item.stt) item.stt = indexDs;
        item.dsChiTiet = arr.map((el, index) => {
          const element = get(item, `dsChiTiet[${index}]`, {});
          if (!element.stt) {
            element.stt = indexDs * NUM_OF_COLS + index + 1;
          }
          if (element?.chiSoSong) {
            element.chiSoSong.huyetAp =
              element.chiSoSong.huyetApTamThu &&
              element.chiSoSong.huyetApTamTruong
                ? `${element.chiSoSong.huyetApTamThu}/${element.chiSoSong.huyetApTamTruong}`
                : "";
          }

          if (element?.chiSoSong?.thoiGianThucHien) {
            element.thoiGianThucHien = element?.chiSoSong?.thoiGianThucHien;
          }

          //set khoa chỉ định cho sinh hiệu mặc định
          if (element?.chiSoSong && !element?.chiSoSong?.khoaChiDinhId) {
            element.chiSoSong.khoaChiDinhId = form?.khoaChiDinhId;
          }

          if (element.ve && element.vh) {
            element.veh = `${element.ve}/${element.vh}`;
          }
          if (!element.dsVanDe) {
            element.dsVanDe = [];
          }

          return element;
        });
        const dsChiTietExitTG = item.dsChiTiet.filter(
          (el) => el.thoiGianThucHien
        );
        const dsChiTietNotExitTG = item.dsChiTiet.filter(
          (el) => !el.thoiGianThucHien
        );
        dsChiTietExitTG.sort(
          (a, b) =>
            (a.thoiGianThucHien ? new Date(a.thoiGianThucHien) : new Date()) -
            (b.thoiGianThucHien ? new Date(b.thoiGianThucHien) : new Date())
        );
        item.dsChiTiet = dsChiTietExitTG.concat(dsChiTietNotExitTG);
      });
      refValueForm.current = convertSttDuplicates(refValueForm.current);
      setState({
        dsTheoDoi: refValueForm.current,
      });
    }
  }, [JSON.stringify(form || {})]);

  const handleAdd = () => {
    const thietLapPrev = refValueForm.current[0];
    const maxStt =
      Math.max(
        ...refValueForm.current
          .map((el) => el.dsChiTiet.map((e) => e.stt))
          .flat(Infinity)
      ) || 1;
    const maxSttBang = Math.max(...refValueForm.current.map((el) => el.stt));
    refValueForm.current = [
      {
        canThiepDieuDuong: [],
        dsChiTiet: arr.map((_, idx) => ({
          dsVanDe: [],
          stt: maxStt + idx + 1,
        })),
        vanDe: [],
        stt: maxSttBang + 1,
        thietLap: thietLapPrev,
      },
      ...refValueForm.current,
    ];
    setState({
      dsTheoDoi: refValueForm.current,
    });
    formChange["dsTheoDoi"](refValueForm.current);
  };

  const handleRemove = (index) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          const dsTheoDoi = refValueForm.current.filter(
            (item, idx) => idx !== index
          );
          refValueForm.current = dsTheoDoi;
          setState({
            dsTheoDoi,
          });
          formChange["dsTheoDoi"](dsTheoDoi);
        }
      );
  };

  return (
    <Main
      className="phieu-cham-soc-cap23"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-cham-soc-cap23"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {state.dsTheoDoi.map((item, index) => {
        return (
          <div
            className="form"
            style={{
              pageBreakAfter:
                index === state.dsTheoDoi.length - 1 ? "avoid" : "always",
            }}
            key={index}
          >
            <RenderTable
              key={index}
              mode={mode}
              tableIndex={item.stt}
              refValueForm={refValueForm.current}
              item={item}
              formChange={formChange}
              onRemove={handleRemove}
              itemProps={itemProps}
              form={form}
              dataCopy={dataCopy}
              setDataCopy={setDataCopy}
              arr={arr}
              showIconAdd={!index}
              handleAdd={handleAdd}
            />
          </div>
        );
      })}
    </Main>
  );
};

PhieuChamSocCap23PhuKhoaPSTW.propTypes = {};

export default PhieuChamSocCap23PhuKhoaPSTW;
