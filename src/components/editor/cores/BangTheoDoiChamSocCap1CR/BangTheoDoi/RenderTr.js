import React, { useEffect, useMemo, useState, memo } from "react";
import { SVG } from "assets";
import AppDatePicker from "../../DatePicker";
import { get } from "lodash";
import { DeboundInput } from "components/editor/config";
import PopoverSelect from "../../Components/PopoverSelect";
import RenderGhiChu from "./RenderGhiChu";
import RenderBanGiao from "./RenderBanGiao";
import { LIST_KEY_NHAP_XUAT } from "../contanst";
const RenderTr = memo(
  ({
    item,
    arr,
    index,
    mode,
    data,
    onChangeInput,
    tableIndex,
    isShowAdd,
    isShowRemove,
    onAddKhung,
    onRemoveKhung,
    onRemove,
    indexKhung,
    isTrMultiple,
    refTongNhapXuat,
    onChangeValueChung,
    itemProps,
    form,
  }) => {
    const [value, setValue] = useState({});
    useEffect(() => {
      setValue(data);
    }, [data]);

    const keySplit = (item?.key || "").split(".");
    let key1, key2;
    key1 = keySplit[0];
    if (keySplit.length > 1) {
      key2 = keySplit[1];
    }

    const renderTongNhapXuat = (item, idx, index, value) => {
      if (
        [
          "dichTruyen",
          "anUong",
          "dichVaoKhac",
          "nuocTieu",
          "dichLocMau",
          "dichDaDay",
          "danLuu",
          "phan",
          "dichRaKhac",
        ].includes(item.key)
      ) {
        const keyTong = "tong" + item.key[0]?.toUpperCase() + item.key.slice(1);
        return (
          <span style={{ display: "block", minWidth: 20, marginTop: 3 }}>
            /
            <RenderTongValue
              key={`${keyTong}_${index}_${idx}`}
              keyTong={keyTong}
              keyValue={item.key}
              refTongNhapXuat={refTongNhapXuat}
              indexTd={idx}
              data={value}
            />
          </span>
        );
      }
      return null;
    };

    const disabled = useMemo(() => {
      const dsChuKy = get(form?.lichSuKy, "dsChuKy", []);
      const isSignLastTable = dsChuKy.some(
        (el) =>
          el.chuKySo == 1 &&
          el.viTri === tableIndex * 500 + 1 + 23 &&
          el.trangThai >= 50
      );
      return isSignLastTable;
    }, [form, tableIndex]);

    return (
      <tr key={`${item.key}_${index}`}>
        {item.label1 && (
          <td
            rowSpan={item.rowSpan1 || 1}
            colSpan={item.colSpan1 || 1}
            style={{
              position: "relative",
              textAlign: "center",
              verticalAlign: "middle",
            }}
            className={`col-1 ${
              item.colSpan1 == 3
                ? "sticky-col sticky-col-2"
                : "sticky-col sticky-col-1"
            }`}
          >
            {(tableIndex && !index) || isShowRemove ? (
              <div>
                <SVG.IcDelete
                  className="ic-remove ic-remove-khung"
                  onClick={isShowRemove ? onRemoveKhung : onRemove}
                />
              </div>
            ) : null}
            {isShowAdd && (
              <SVG.IcAdd
                onClick={onAddKhung(index)}
                className="btn-add btn-add-khung"
              />
            )}
            <b>{item.label1}</b>
          </td>
        )}
        {item.label2 && (
          <td
            rowSpan={item.rowSpan2 || 1}
            colSpan={item.colSpan2 || 1}
            className={
              item.colSpan2 == 4
                ? `sticky-col sticky-col-1`
                : item.colSpan3 == 1
                ? `sticky-col sticky-col-3`
                : `sticky-col sticky-col-2`
            }
            style={{
              verticalAlign: "middle",
              ...(item.td2Props?.style || {}),
            }}
          >
            {item.label2}
          </td>
        )}
        {item.label3 && (
          <td
            rowSpan={item.rowSpan3 || 1}
            colSpan={item.colSpan3 || 1}
            style={{
              verticalAlign: "middle",
              ...(item.key === "huyetApTrungBinh"
                ? { textAlign: "right", paddingRight: 5 }
                : {}),
            }}
            className={
              item.colSpan4 == 2 && item.colSpan3 != 2
                ? `sticky-col sticky-col-2`
                : `sticky-col sticky-col-3`
            }
          >
            {item.label3}
          </td>
        )}

        {item.hint && (
          <td
            rowSpan={item.rowSpan4 || 1}
            colSpan={item.colSpan4 || 1}
            style={{
              verticalAlign: "middle",
            }}
            className={`sticky-col ${
              item.colSpan4 == 2
                ? "sticky-col-3"
                : item.colSpan4 == 3
                ? "sticky-col-2"
                : "sticky-col-4"
            }`}
          >
            <div
              className={`hint ${
                item.numberItemHint ? "hint-with-number" : ""
              }`}
            >
              {(item.hint || []).map((item, index) => (
                <span key={index}>{item}</span>
              ))}
            </div>
          </td>
        )}
        {arr.map((el, idx) => {
          const viTri = idx + 1 + tableIndex * 500;
          const dsChuKy = get(form, "lichSuKy.dsChuKy", []);
          const disableColumn = (dsChuKy || []).some(
            (chuKy) =>
              chuKy.viTri == viTri &&
              chuKy.trangThai >= 50 &&
              chuKy.chuKySo == 1
          );

          return item.disabled ? (
            <td key={`${item.key}_${index}_${idx}`}></td>
          ) : item.type == "time" ? (
            <td key={`${item.key}_${index}_${idx}`} className="col-lv4">
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                    allowClear: true,
                    readOnly: disableColumn,
                  },
                }}
                form={{
                  value: get(
                    value,
                    isTrMultiple
                      ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                      : `dsChiTiet[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                  ),
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChangeInput(
                        `dsChiTiet.${item.key}`,
                        idx,
                        indexKhung
                      )(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </td>
          ) : item.type === "droplist" ? (
            <td className="col-lv4" key={`${item.key}_${index}_${idx}`}>
              <PopoverSelect
                data={item.data}
                value={get(
                  value,
                  isTrMultiple
                    ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                    : `dsChiTiet[${idx}][${key1}]${key2 ? `[${key2}]` : ""}`
                )}
                onChangeValue={(e) => {
                  onChangeInput(
                    `dsChiTiet.${item.key}`,
                    idx,
                    indexKhung
                  )(item.mode == "onlyOne" ? [e] : e);
                }}
                isMultiple={!(item.mode == "onlyOne")}
                trigger="click"
                labelByKey={item.labelByKey || "value"}
                isShowSearch={true}
                disabled={disableColumn}
              />
            </td>
          ) : ["tongDichVao", "tongDichRa", "tongDichVaoRa"].includes(
              item.key
            ) ? (
            <RenderTong
              key={`${item.key}_${index}_${idx}`}
              keyValue={item.key}
              keyTong={item.key}
              refTongNhapXuat={refTongNhapXuat}
              indexTd={idx}
              data={value}
            ></RenderTong>
          ) : (
            <td key={`${item.key}_${index}_${idx}`} className="col-lv4">
              <div
                className={`flex f-5 ${
                  LIST_KEY_NHAP_XUAT.includes(key1) ? "input-nhap-xuat" : ""
                }`}
              >
                <DeboundInput
                  readOnly={disableColumn}
                  isHideLineMark={disableColumn}
                  value={get(
                    value,
                    isTrMultiple
                      ? `[${idx}][${key1}][${indexKhung}][${key2}]`
                      : `dsChiTiet[${idx}][${key1}]${key2 ? `[${key2}]` : ""}`
                  )}
                  onChange={onChangeInput(
                    `dsChiTiet.${item.key}`,
                    idx,
                    indexKhung
                  )}
                  type={
                    LIST_KEY_NHAP_XUAT.includes(key1)
                      ? "number"
                      : "multipleline"
                  }
                  lineHeightText={1.5}
                  fontSize={9}
                  minHeight={9 + 6}
                  markSpanRow={false}
                  contentAlign="center"
                  styleMain={
                    LIST_KEY_NHAP_XUAT.includes(key1)
                      ? null
                      : { flex: 1, textAlign: "center" }
                  }
                ></DeboundInput>
                {renderTongNhapXuat(item, idx, index, value)}
              </div>
            </td>
          );
        })}

        {!isTrMultiple ? (
          index == 30 ? (
            <td className={`center  class_${index}`}>
              <b>Ghi chú</b>
            </td>
          ) : index == 31 ? (
            <td rowSpan={29} className={`class_${index}`}>
              <RenderGhiChu
                value={value}
                formChangeValue={onChangeValueChung}
                form={form}
                tableIndex={tableIndex}
                itemProps={itemProps}
                disabled={disabled}
              ></RenderGhiChu>
            </td>
          ) : index == 60 ? (
            <td className={`center  class_${index}`}>
              <b>Bàn giao</b>
            </td>
          ) : index == 61 ? (
            <td rowSpan={14}>
              <RenderBanGiao
                value={value}
                formChangeValue={onChangeValueChung}
                form={form}
                tableIndex={tableIndex}
                itemProps={itemProps}
                disabled={disabled}
              />
            </td>
          ) : index >= 75 ? (
            <td className={"class_" + index}></td>
          ) : null
        ) : (
          <td className={"class_" + index}></td>
        )}
      </tr>
    );
  }
);

const RenderTong = memo(
  ({ data, indexTd, keyValue, keyTong, refTongNhapXuat, colSpan }, ref) => {
    return (
      <td colSpan={colSpan || 1} className={`col-lv4 center`}>
        <RenderTongValue
          data={data}
          indexTd={indexTd}
          keyValue={keyValue}
          keyTong={keyTong}
          refTongNhapXuat={refTongNhapXuat}
        />
      </td>
    );
  }
);
const RenderTongValue = memo(
  ({ data, indexTd, keyTong, keyValue, refTongNhapXuat }, ref) => {
    const [value, setValue] = useState();

    useEffect(() => {
      setValue(data);
    }, [data]);
    refTongNhapXuat.current[`${indexTd}_${keyTong}`] = setValue;

    const tong = useMemo(() => {
      keyValue =
        keyTong == "tongDichVao"
          ? "dichVao"
          : keyTong == "tongDichRa"
          ? "dichRa"
          : keyTong == "tongDichVaoRa"
          ? "dichVaoRa"
          : keyValue;
      const dataValue = get(value, `dsChiTiet[${indexTd}][${keyValue}]`, "");
      const tong = get(value, `dsChiTiet[${indexTd}][${keyTong}]`, "");
      if (dataValue !== 0 && !dataValue) return "";
      return tong && tong !== "0" ? tong : "";
    }, [value, indexTd, keyTong, keyValue]);

    return tong || "";
  }
);

RenderTr.propTypes = {};

export default RenderTr;
