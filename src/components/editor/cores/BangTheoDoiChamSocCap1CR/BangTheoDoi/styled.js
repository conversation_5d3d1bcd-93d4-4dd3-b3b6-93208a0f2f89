import styled from "styled-components";

const Main = styled("div")`
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  /* Table highlighting styles */
  table {
    position: relative;
    width: 100%;
    table-layout: fixed;

    /* Add transitions for smoother highlighting */
    td {
      transition: background-color 0.15s ease, box-shadow 0.15s ease;
    }

    /* Column and row highlighting */
    .highlight-col,
    tr.highlight-row td {
      background-color: rgba(193, 240, 219, 0.4) !important;
      position: relative;
    }

    /* Cell highlighting */
    td.highlight-cell {
      background-color: rgba(193, 240, 219, 0.8) !important;
      box-shadow: inset 0 0 0 2px rgba(0, 150, 136, 0.5);
      z-index: 2 !important; /* Higher z-index to ensure highlighted cell appears above others */
    }

    /* Row cell highlighting for cells with rowspan */
    td.highlight-row-cell {
      background-color: rgba(193, 240, 219, 0.4) !important;
    }

    /* Special handling for cells with rowspan/colspan */
    td[rowspan],
    td[colspan] {
      position: relative;
      z-index: 1;

      &.highlight-col,
      &.highlight-cell,
      &.highlight-row-cell {
        position: relative;
        z-index: 2;
        /* box-shadow: inset 0 0 0 2px rgba(0, 150, 136, 0.3); */
      }

      /* Stronger highlight for cells that are both in highlighted row and column */
      &.highlight-col.highlight-row-cell {
        background-color: rgba(193, 240, 219, 0.6) !important;
        /* box-shadow: inset 0 0 0 2px rgba(0, 150, 136, 0.4); */
      }

      /* When hovering directly on a cell with rowspan/colspan, make it more prominent */
      &.highlight-cell {
        background-color: rgba(193, 240, 219, 0.8) !important;
        /* box-shadow: inset 0 0 0 2px rgba(0, 150, 136, 0.6); */
        z-index: 3;
      }
    }

    /* Exclude vital signs chart from highlighting */
    .vital-sign-chart-row td[rowspan] {
      background-color: transparent !important;
      box-shadow: none !important;

      &:hover,
      &.highlight-col,
      &.highlight-cell {
        background-color: transparent !important;
        box-shadow: none !important;
      }
    }

    /* Canvas container should not be affected by highlighting */
    td.vital-sign-canvas-cell {
      background-color: transparent !important;
      box-shadow: none !important;

      &:hover,
      &.highlight-col,
      &.highlight-cell {
        background-color: transparent !important;
        box-shadow: none !important;
      }
    }

    /* Hover styles - using classes instead of :hover for better control */
    tr:hover
      td:not(.highlight-cell):not(.highlight-col):not(.highlight-row-cell):not(
        .vital-sign-canvas-cell
      ) {
      background-color: rgba(193, 240, 219, 0.2);
    }

    td:hover:not(.highlight-cell):not(.vital-sign-canvas-cell) {
      background-color: rgba(193, 240, 219, 0.5) !important;
      cursor: pointer;
    }

    /* Intersection of row and column highlight */
    tr.highlight-row td.highlight-col:not(.highlight-cell) {
      background-color: rgba(193, 240, 219, 0.6) !important;
    }

    /* Ensure vital signs chart row is not affected by highlighting */
    tr.vital-sign-chart-row:hover td {
      background-color: transparent;
    }
  }

  .vital-sign-chart-row {
    .vital-sign-header-cell {
      height: 30px;
      margin-top: 2px;
    }
  }
  .btn-remove-table {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
  }
  table:hover {
    .btn-remove-table {
      display: block;
    }
  }
  & .canvas-vital-signs {
    margin-top: -2px;
  }
  & canvas {
    background-color: transparent;
  }
  .first-row {
    height: 28px;
    display: flex;
    align-items: flex-end;
    line-height: 1;
    justify-content: center;
  }
  & table {
    border-collapse: collapse;
    color: #000 !important;
    line-height: 1.2;
    & td {
      height: 24px;
      position: relative;
    }
    .border-top {
      border-top: 1px solid #000;
    }
    .ant-select {
      width: 100%;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border: none !important;
      height: 20px;
      line-height: 1.2;
      font-size: 13px;
    }
    .ant-select-selection-item {
      display: flex;
      align-items: center;
    }
    & .col-lv0 {
      width: 20px !important;
      /* line-height: 13px; */
    }
    & .col-lv1 {
      padding: 0 3px;
      width: 20px;
    }
    & .col-lv2 {
      padding: 0 3px;
      width: 50px !important;
      border-left: 1px solid #000;
      vertical-align: middle;
    }
    & .col-lv3 {
      width: 127px;
      padding: 0 3px;
      box-sizing: border-box;
      & > div.ant-select {
        width: 100%;
        & .ant-select-selection {
          height: 15px;
          border: none;
          outline: none;
          & svg {
            width: 7px;
            height: 7px;
          }
          & .ant-select-selection__rendered {
            line-height: 15px;
            & .ant-select-selection-selected-value {
              font-size: 11px;
            }
          }
        }
      }
    }
    & .col-lv4,
    .col-lv3 {
      & .drop-list {
        min-height: 11px;
      }
      & .signature {
        border: none;
        width: 100% !important;
        & .sign-body {
          width: 100%;
        }
        .text-field-label:after {
          margin-right: 0px;
        }
        & .btn-signature {
          font-size: 9px;
          line-height: 11px;
          height: 15px;
          span {
            font-size: 9px;
            line-height: 11px;
          }
        }
      }
      & .ant-input-number {
        padding: 0;
        width: auto;
        border: none;
        height: 11px;
        font-size: 11px;
        line-height: 11px;
        & .ant-input-number-handler-wrap {
          display: none;
        }
        & .ant-input-number-input-wrap {
          height: 11px;
        }
      }
      & input {
        color: #000 !important;
        padding: 0;
        font-size: 11px;
        line-height: 11px;
        text-align: center;
        height: 11px;
        border: none;
      }
      & textarea {
        resize: none;
        padding: 0;
        border: none;
        height: 20px;
        font-size: 9px;
        line-height: 9px;
        min-height: 9px;
      }
    }
    & .col-lv4 {
      /* width: 60px !important; */
      & .ant-input.red {
        color: red !important;
      }
    }
    & .center {
      text-align: center;
      vertical-align: middle;
    }
    & .left {
      text-align: left;
    }
    & .verticalBottom {
      vertical-align: bottom;
    }
    & .verticalTop {
      vertical-align: top;
    }
    & .verticalMiddle {
      vertical-align: middle;
    }
    & .bold {
      font-weight: bold;
    }

    & .red {
      color: red;
    }

    & .vertical {
      text-align: center;
    }
    & .no-border-left {
      border-left: none !important;
    }
    & .no-border-right {
      border-right: none !important;
    }
    & .left-column-row-height {
      height: 14px;
    }
    & .flex {
      display: flex;
    }
    & .f1 {
      flex: 1;
    }
    & .jcenter {
      justify-content: center;
    }
    & .acenter {
      align-items: center;
    }
    & .vamid {
      vertical-align: middle;
    }

    & .lh15 {
      line-height: 15px;
    }

    & .w75 {
      width: 75px;
    }
    & .pa {
      position: absolute;
    }
    & .pr {
      position: relative;
    }
    & .table-left-vital-signs {
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      width: calc(100% + 2px);
    }
    & .old-value {
      width: 30px !important;
      font-weight: bold;
      margin-right: 10px;
      & .ant-input-number-input {
        padding: 0;
        font-weight: bold;
      }
    }
    & .leftColumnWidth {
      width: ${(props) => props.leftColumnWidth}px;
    }
    .ic-remove-khung {
      left: 0px;
      top: 0px;
      position: absolute;
    }
    .btn-add-khung {
      position: absolute;
      left: 0px;
      top: 20px;
      width: 20px;
      height: 20px;
    }
    @media print {
      .ic-remove-khung,
      .btn-add-khung {
        display: none;
      }
    }
  }
  ${() => {
    return new Array(24).fill("").map(
      (item, idx) => `.col-${idx}:hover {
    .copy-paste-col-${idx} {
      display: block;
    }
  }`
    );
  }}
  & .btn-tool {
    position: absolute;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 100;
    background: red;
  }
  .copy-paste-element {
    display: none;
  }
  .flex {
    display: flex;
  }

  &.table-scroll {
    overflow: auto;
    position: relative;
    whitespace: "nowrap";
    cursor: "grab";
    @media screen {
      max-height: calc(100vh - 300px);
    }
    table {
      border-collapse: separate;
      border-spacing: 0;
      width: 2330px;
      th,
      td {
        background: white;
        border: 1px solid #000; // nếu bạn muốn viền rõ
      }
      // Đảm bảo header nằm trên cùng
      thead th:nth-child(-n + 4) {
        z-index: 11;
      }
      .sticky-col {
        position: sticky !important;
        background: white !important;
        z-index: 4 !important;
      }
      .sticky-col-1 {
        left: 0px !important;
      }
      .sticky-col-2 {
        left: 90px !important;
      }
      .sticky-col-3 {
        left: 180px !important;
      }
      .sticky-col-4 {
        left: 270px !important;
      }
    }
  }

  .input-nhap-xuat {
    height: 20px;
    & .ant-input-number {
      height: 100% !important;
      flex: 1;
      & .ant-input-number-input-wrap {
        height: 100% !important;
        & input {
          height: 100%;
          font-size: 13px !important;
        }
      }
    }
  }
`;

const TrStyled = styled("tr")`
  .icon-delete,
  .icon-add {
    cursor: pointer;
    display: block;
    width: 20px;
    position: absolute;
    @media print {
      display: none;
    }
  }
  .icon-add {
    top: 20px;
    right: 24px;
  }
  .icon-delete {
    right: 0;
    top: 23px;
    path {
      fill: red;
    }
  }
  & .ant-select {
    font-size: ${(props) => props.fontSize || 12}pt;
    font-family: initial;
    display: block;
    background-color: transparent;
    flex: 1;
    .ant-select-arrow {
      display: none !important;
    }

    & .ant-select-selector {
      height: 100%;
      padding: 0;
      .ant-select-selection-item {
        background: unset !important;
        border: unset !important;
        white-space: break-spaces !important;
        overflow: unset;
        height: auto;
        word-break: break-word;
      }
      .ant-select-selection-item-content {
        white-space: break-spaces !important;
        overflow: unset;
      }
      .ant-select-selection-item-remove {
        .anticon-close {
          display: none !important;
        }
      }
      & .ant-select-selection-overflow-item {
        :nth-child(n) {
          .ant-select-selection-item-remove {
            &::after {
              content: ", ";
              margin-right: 2px;
              color: #000;
              font-size: 14px;
            }
          }
        }
        :nth-last-child(-n + 2) {
          .ant-select-selection-item-remove {
            &::after {
              content: "";
              margin-right: 2px;
              color: #000;
              font-size: 14px;
            }
          }
        }
      }

      background-color: transparent;
      border: none;
      @media print {
        background: none;
        @page {
          margin-top: 20px;
          /* margin-bottom: 20px; */
        }
      }
      &--single {
        height: auto;
        & .ant-select-arrow {
          display: none;
        }
      }
    }
  }
`;
const StyleQuyUoc = styled("div")``;

export { Main, TrStyled, StyleQuyUoc };
