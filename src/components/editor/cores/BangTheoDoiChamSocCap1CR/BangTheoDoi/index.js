import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  memo,
  useCallback,
} from "react";
import { cloneDeep, get, isEmpty } from "lodash";
import { But<PERSON>, message } from "antd";
import { SIZE, SIZE_2 } from "../VitalSigns/utils/vital-signs/constants";
import {
  BLOOD_PRESSURE,
  SPO2,
  MACHS2,
  NHIETS2,
  SPO2_2,
  MACHS3,
  NHIETS,
} from "utils/vital-signs/constants";
import { handleBloodPressure } from "utils/vital-signs/canvas-utils";
import { CloseOutlined } from "@ant-design/icons";
import { updateObject } from "utils";
import moment from "moment";
import ImageSign from "../../ImageSign";
import { LIST_KEY_NHAP_XUAT, LIST_TR, LIST_TR_2, SAU_DE } from "../contanst";
import RenderChanDoan from "../RenderChanDoan";
import CopyPasteCol from "../../Components/CopyPasteCol";
import { refConfirm } from "app";
import { useTranslation } from "react-i18next";
import { useQueryString } from "hooks";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import HeaderForm from "../HeaderForm";
import VitalSign from "components/editor/cores/BangTheoDoiBenhNhanGMHS/GMHS/VitalSigns";
import ListTrMultiple from "./ListTrMultiple";
import ModalChangeDate from "../ModalChangeDate";
import RenderThuoc from "./RenderThuoc";
import RenderTr from "./RenderTr";
import { Main } from "./styled";

/**
 * Custom hook to add table highlighting functionality
 */
const useTableHighlight = () => {
  const tableRef = useRef(null);
  const lastCellRef = useRef(null);

  useEffect(() => {
    if (!tableRef.current) return;

    const table = tableRef.current;

    // Function to handle cell hover events
    const handleCellHover = (event) => {
      // Chỉ xử lý nếu target là td hoặc nằm trong td
      const cell = event.target.closest("td");
      if (!cell) return;

      // Nếu là cùng một ô như trước thì không xử lý lại
      if (cell === lastCellRef.current) return;

      // Bỏ qua highlight cho canvas hoặc vital-sign-canvas-cell
      if (
        cell.querySelector("canvas") ||
        cell.classList.contains("vital-sign-canvas-cell")
      ) {
        // Xoá highlight khi di chuyển vào vùng canvas
        clearAllHighlights();
        lastCellRef.current = cell;
        return;
      }

      // Xoá highlight cũ
      clearAllHighlights();

      // Cập nhật ô cuối cùng
      lastCellRef.current = cell;

      // Thêm highlight cho ô hiện tại
      cell.classList.add("highlight-cell");

      // Tạo ma trận
      const matrix = buildTableMatrix(table);

      // Tìm ô trong ma trận
      const cellPosition = findCellInMatrix(matrix, cell);
      if (cellPosition) {
        // Kiểm tra xem ô này có phải là ô chính (không phải là một phần của rowspan/colspan khác)
        // hoặc có rowspan/colspan riêng không
        const rowSpan = cell.rowSpan || 1;
        const colSpan = cell.colSpan || 1;

        if (rowSpan > 1 || colSpan > 1) {
          // Ô này có rowspan hoặc colspan - highlight tất cả các dòng/cột bị ảnh hưởng

          // Highlight tất cả các dòng bị span bởi ô này
          for (let r = 0; r < rowSpan; r++) {
            highlightRow(matrix, cellPosition.rowIdx + r);
          }

          // Highlight tất cả các cột bị span bởi ô này
          for (let c = 0; c < colSpan; c++) {
            highlightColumn(matrix, cellPosition.colIdx + c);
          }
        } else {
          // Ô thường - chỉ highlight dòng và cột của nó
          highlightRow(matrix, cellPosition.rowIdx);
          highlightColumn(matrix, cellPosition.colIdx);
        }
      }
    };

    // Hàm xoá tất cả highlight
    const clearAllHighlights = () => {
      const highlightedCells = table.querySelectorAll(".highlight-cell");
      highlightedCells.forEach((el) => el.classList.remove("highlight-cell"));

      const highlightedRows = table.querySelectorAll(".highlight-row");
      highlightedRows.forEach((el) => el.classList.remove("highlight-row"));

      const highlightedCols = table.querySelectorAll(".highlight-col");
      highlightedCols.forEach((el) => el.classList.remove("highlight-col"));

      const highlightedRowCells = table.querySelectorAll(".highlight-row-cell");
      highlightedRowCells.forEach((el) =>
        el.classList.remove("highlight-row-cell")
      );
    };

    // Hàm tạo ma trận cho table
    const buildTableMatrix = (table) => {
      const rows = table.querySelectorAll("tr");
      const matrix = [];

      rows.forEach((row, rowIdx) => {
        if (!matrix[rowIdx]) {
          matrix[rowIdx] = [];
        }

        let colIdx = 0;

        for (let i = 0; i < row.cells.length; i++) {
          const cell = row.cells[i];
          const colspan = cell.colSpan || 1;
          const rowspan = cell.rowSpan || 1;

          // Bỏ qua các ô đã được điền (do rowspan từ trên xuống)
          while (matrix[rowIdx][colIdx]) {
            colIdx++;
          }

          // Đánh dấu vị trí ô trong ma trận
          for (let r = 0; r < rowspan; r++) {
            for (let c = 0; c < colspan; c++) {
              if (!matrix[rowIdx + r]) {
                matrix[rowIdx + r] = [];
              }
              matrix[rowIdx + r][colIdx + c] = {
                cell,
                isMainCell: r === 0 && c === 0,
                rowIdx: rowIdx,
                colIdx: colIdx,
              };
            }
          }

          colIdx += colspan;
        }
      });

      return matrix;
    };

    // Hàm tìm ô trong ma trận
    const findCellInMatrix = (matrix, cell) => {
      for (let rowIdx = 0; rowIdx < matrix.length; rowIdx++) {
        const row = matrix[rowIdx];
        if (!row) continue;

        for (let colIdx = 0; colIdx < row.length; colIdx++) {
          const cellInfo = row[colIdx];
          if (cellInfo && cellInfo.cell === cell) {
            return {
              rowIdx: cellInfo.rowIdx,
              colIdx: cellInfo.colIdx,
            };
          }
        }
      }

      return null;
    };

    // Hàm highlight dòng sử dụng ma trận
    const highlightRow = (matrix, rowIdx) => {
      // Thêm highlight cho dòng chứa ô
      const row = matrix[rowIdx];
      if (!row) return;

      // Find the TR element
      let trElement = null;
      for (let colIdx = 0; colIdx < row.length; colIdx++) {
        const cellInfo = row[colIdx];
        if (cellInfo && cellInfo.isMainCell) {
          trElement = cellInfo.cell.closest("tr");
          break;
        }
      }

      if (trElement) {
        trElement.classList.add("highlight-row");
      }

      // Highlight tất cả các ô thuộc dòng này
      row.forEach((cellInfo) => {
        if (cellInfo) {
          cellInfo.cell.classList.add("highlight-row-cell");
        }
      });
    };

    // Hàm highlight cột sử dụng ma trận
    const highlightColumn = (matrix, colIdx) => {
      // Highlight tất cả các ô trong cột
      matrix.forEach((row) => {
        if (!row) return;

        const cellInfo = row[colIdx];
        if (cellInfo) {
          cellInfo.cell.classList.add("highlight-col");
        }
      });
    };

    // Hàm xử lý khi chuột rời khỏi ô
    const handleCellMouseOut = (event) => {
      // Lấy phần tử mà chuột chuyển đến
      const toElement = event.relatedTarget;
      const fromCell = event.target.closest("td");

      // Nếu chuột rời khỏi bảng hoàn toàn, xoá highlight
      if (!toElement || !toElement.closest("table")) {
        clearAllHighlights();
        lastCellRef.current = null;
        return;
      }

      // Nếu chuyển sang ô khác, cho mouseover của ô đó xử lý
      const toCell = toElement.closest("td");
      if (toCell && toCell !== fromCell) {
        // Không xoá highlight, mouseover sẽ xử lý
        return;
      }

      // Nếu chuyển sang phần tử không phải ô trong bảng (như khoảng trống giữa các ô), giữ nguyên highlight
    };

    // Hàm xử lý khi chuột rời khỏi bảng hoàn toàn
    const handleTableMouseLeave = () => {
      clearAllHighlights();
      lastCellRef.current = null;
    };

    // Thêm event listener cho mouseover và mouseout trên từng ô
    const cells = table.querySelectorAll("td");
    cells.forEach((cell) => {
      cell.addEventListener("mouseover", handleCellHover);
      cell.addEventListener("mouseout", handleCellMouseOut);
    });

    // Thêm event listener cho mouseleave trên bảng
    table.addEventListener("mouseleave", handleTableMouseLeave);

    // Sử dụng MutationObserver để xử lý khi có dòng/ô được thêm động
    const observer = new MutationObserver(() => {
      // Khi nội dung thay đổi, xoá tất cả highlight
      clearAllHighlights();
      lastCellRef.current = null;

      // Gắn lại event listener cho tất cả các ô
      const cells = table.querySelectorAll("td");
      cells.forEach((cell) => {
        cell.removeEventListener("mouseover", handleCellHover);
        cell.removeEventListener("mouseout", handleCellMouseOut);

        cell.addEventListener("mouseover", handleCellHover);
        cell.addEventListener("mouseout", handleCellMouseOut);
      });
    });

    observer.observe(table, { childList: true, subtree: true });

    return () => {
      // Clean up event listeners
      const cells = table.querySelectorAll("td");
      cells.forEach((cell) => {
        cell.removeEventListener("mouseover", handleCellHover);
        cell.removeEventListener("mouseout", handleCellMouseOut);
      });

      table.removeEventListener("mouseleave", handleTableMouseLeave);
      observer.disconnect();
    };
  }, []);

  return tableRef;
};

/**
 * Renders signature cell for each column
 */
const RenderSign = memo(({ mode, index, form, indexTable, itemProps }) => {
  const viTri = index + 1 + indexTable * 500;
  return (
    <td>
      <ImageSign
        mode={mode}
        component={{
          props: {
            ...itemProps,
            fontSize: 12,
            capKy: 1,
            loaiKy: 1,
            width: 60,
            height: 40,
            isMultipleSign: true,
            viTri,
            customText: "Ký",
            showTenVietTat: itemProps?.showTenVietTat,
            alwaysShow: true,
            dataSign: {
              id: form.id,
              soPhieu: form.soPhieu,
              lichSuKyId: form?.lichSuKy?.id,
            },
          },
        }}
        form={{
          lichSuKy: form.lichSuKy,
        }}
      />
    </td>
  );
});

/**
 * Renders a column header cell with time input
 */
const TimeColumnHeader = memo(
  ({
    idx,
    onChangeThoiGian,
    refValue,
    dataCopy,
    handleCopy,
    hanldePaste,
    handleDelete,
    disableColumn,
    width,
    itemProps,
  }) => {
    return (
      <td
        className={`col-lv4 center col-${idx}`}
        key={idx}
        style={{
          position: "relative",
          width,
          minWidth: width,
          maxWidth: width,
        }}
      >
        <CopyPasteCol
          colIndex={idx}
          handlePaste={hanldePaste(idx)}
          handleCopy={handleCopy(idx)}
          dataCopy={dataCopy}
          handleDelete={handleDelete(idx)}
        />
        <ModalChangeDate
          component={() => import("../ModalChangeDate")}
          value={get(
            refValue.current,
            `dsChiTiet[${idx}].thoiGianThucHien`,
            null
          )}
          onChange={onChangeThoiGian(idx)}
          format={itemProps.mauForm != 2 ? "HH:mm" : "HH:m DD/MM/YYYY"}
          onlyTime={itemProps.mauForm != 2 ? true : false}
          disable={disableColumn}
        />
      </td>
    );
  }
);

/**
 * Renders a vital sign row header
 */

const LIST_SPO2 = {
  1: SPO2,
  2: SPO2_2,
};
const LIST_MACH = {
  1: MACHS2,
  2: MACHS3,
};

const LIST_NHIET = {
  1: NHIETS,
  2: NHIETS2,
};

const VitalSignHeaderCell = memo(
  ({ type, index, rangeBloodPressure, className, itemProps, height }) => {
    const getContent = () => {
      switch (type) {
        case "spo2":
          return LIST_SPO2[itemProps?.loaiBangChiSoSong || 1][index];
        case "machs":
          return LIST_MACH[itemProps?.loaiBangChiSoSong || 1][index];
        case "huyetap":
          return rangeBloodPressure[index];
        case "nhiet":
          return LIST_NHIET[itemProps?.loaiBangChiSoSong || 1][index];
        default:
          return "";
      }
    };

    return (
      <td
        className={`center col verticalTop vital-sign-header-cell ${
          className || ""
        }`}
        style={{
          height: height,
          maxHeight: height,
        }}
      >
        {getContent()}
      </td>
    );
  }
);

/**
 * Main component for BangTheoDoiChamSocCap1
 */
const BangTheoDoiChamSocCap1 = (
  {
    itemProps,
    form,
    values,
    formChange,
    block,
    mode,
    indexTable,
    refValuesAll,
    refModalNhapChiSo,
    removeTable,
    refDataCopy,
    dataCopy,
    setDataCopy,
    arr,
    onRemove,
    ...props
  },
  refs
) => {
  const { t } = useTranslation();

  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const TOTAL_COL = 24;
  const LEFT_VITAL_SIGNS = 325;
  const refValue = useRef();
  const refState = useRef({
    values: new Array(24).fill({}).map((item) => ({})),
    rangeBloodPressure: BLOOD_PRESSURE[6].listShow,
  });
  const refMain = useRef(null);
  const refTongNhapXuat = useRef({});
  const [state, _setState] = useState(refState.current);

  // Use our custom hook for table highlighting
  const tableRef = useTableHighlight();

  const setState = (data = {}) => {
    _setState((state) => {
      refState.current = { ...refState.current, ...data };
      return refState.current;
    });
  };

  const CONFIG_SIZE = useMemo(() => {
    return itemProps.loaiBangChiSoSong == 2 ? SIZE_2 : SIZE;
  }, [itemProps.loaiBangChiSoSong]);

  const tinhTongNhapXuat = () => {
    const getTong = (index, key, keyTong) => {
      return (
        (index == 0
          ? 0
          : Number(refValue.current.dsChiTiet[index - 1][keyTong] || 0)) +
        Number(refValue.current.dsChiTiet[index][key] || 0)
      );
    };
    refValue.current.dsChiTiet.forEach((item, index) => {
      item.tongDichTruyen = getTong(index, "dichTruyen", "tongDichTruyen");
      item.tongAnUong = getTong(index, "anUong", "tongAnUong");
      item.tongDichVaoKhac = getTong(index, "dichVaoKhac", "tongDichVaoKhac");
      item.dichVao =
        item.dichTruyen || item.anUong || item.dichVaoKhac
          ? (item.dichTruyen || 0) +
            (item.anUong || 0) +
            (item.dichVaoKhac || 0)
          : "";
      item.tongDichVao =
        item.tongDichTruyen || item.tongAnUong || item.tongDichVaoKhac
          ? (item.tongDichTruyen || 0) +
            (item.tongAnUong || 0) +
            (item.tongDichVaoKhac || 0)
          : "";

      item.tongNuocTieu = getTong(index, "nuocTieu", "tongNuocTieu");
      item.tongDichLocMau = getTong(index, "dichLocMau", "tongDichLocMau");
      item.tongDichDaDay = getTong(index, "dichDaDay", "tongDichDaDay");
      item.tongDanLuu = getTong(index, "danLuu", "tongDanLuu");
      item.tongDichRaKhac = getTong(index, "dichRaKhac", "tongDichRaKhac");
      item.tongPhan = getTong(index, "phan", "tongPhan");
      item.dichRa =
        item.nuocTieu ||
        item.dichLocMau ||
        item.dichDaDay ||
        item.danLuu ||
        item.dichRaKhac ||
        item.phan
          ? (item.nuocTieu || 0) +
            (item.dichLocMau || 0) +
            (item.dichDaDay || 0) +
            (item.danLuu || 0) +
            (item.dichRaKhac || 0) +
            (item.phan || 0)
          : "";
      item.tongDichRa =
        item.tongNuocTieu ||
        item.tongDichLocMau ||
        item.tongDichDaDay ||
        item.tongDanLuu ||
        item.tongDichRaKhac ||
        item.tongPhan
          ? (item.tongNuocTieu || 0) +
            (item.tongDichLocMau || 0) +
            (item.tongDichDaDay || 0) +
            (item.tongDanLuu || 0) +
            (item.tongDichRaKhac || 0) +
            (item.tongPhan || 0)
          : "";
      item.dichVaoRa =
        item.dichVao || item.dichRa
          ? (item.dichVao || 0) - (item.dichRa || 0)
          : "";
      item.tongDichVaoRa =
        item.tongDichVao || item.tongDichRa
          ? (item.tongDichVao || 0) - (item.tongDichRa || 0)
          : "";
    });
  };

  useEffect(() => {
    if (values) {
      refValue.current = values;
      tinhTongNhapXuat();
      setState({
        values,
        rangeBloodPressure: getRangeBloodPressure(values),
      });
    }
  }, [values, itemProps]);

  const canvasWidth = 70 * TOTAL_COL;

  const columnWidth = useMemo(() => {
    return canvasWidth / (itemProps.soLuongCot || 24);
  }, [itemProps.soLuongCot]);

  const canvasHeight = useMemo(() => {
    return (
      CONFIG_SIZE.rowHeight * CONFIG_SIZE.countRow + CONFIG_SIZE.headerHeight
    );
  }, [CONFIG_SIZE]);

  const onChangeValue = useCallback(
    (key, idx) =>
      (value = "") => {
        if (!formChange) return;
        const keySplit = (key || "").split(".");
        let key1, key2;
        key1 = keySplit[0];
        if (keySplit.length > 1) {
          key2 = keySplit[1];
        }
        if (!key2) {
          updateObject(refValue.current, key1, value);
        } else {
          if (!refValue.current[key1]) refValue.current[key1] = [];
          if (get(refValue.current, `${key1}[${idx}]`)) {
            updateObject(refValue.current[key1][idx], `${key2}`, value);
          } else {
            refValue.current[key1][idx] = {};
            updateObject(refValue.current[key1][idx], `${key2}`, value);
          }
        }
        if (
          LIST_KEY_NHAP_XUAT.some((el) => {
            return `dsChiTiet.${el}` === key;
          })
        ) {
          tinhTongNhapXuat();
          const refValueClone = cloneDeep(refValue.current);
          Object.values(refTongNhapXuat.current).forEach((el) => {
            el(refValueClone);
          });
        }

        formChange["dsTheoDoi"] &&
          formChange["dsTheoDoi"](refValuesAll.current);
      },
    [formChange, state.values]
  );

  const getRangeBloodPressure = () => {
    const values = new Array(24).fill({}).map(() => ({}));
    values.forEach((item, index) => {
      const tamThu = get(state.values, `dsChiSoSong[${index}].huyetApTamThu`);
      const tamTruong = get(
        state.values,
        `dsChiSoSong[${index}].huyetApTamTruong`
      );
      if (tamThu && tamTruong) {
        item.huyetAp = `${tamThu}/${tamTruong}`;
      }
    });
    const cloneValues = cloneDeep(values);
    const indexOfLastItemHasValue =
      cloneValues.length -
      1 -
      cloneValues.reverse().findIndex((item) => !!item.huyetAp);
    const newValue = handleBloodPressure(
      values[indexOfLastItemHasValue] && values[indexOfLastItemHasValue].huyetAp
    );

    const listShow =
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ) &&
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ).listShow;

    if (!isEmpty(listShow)) {
      return listShow || [];
    } else {
      if (itemProps?.loaiBangChiSoSong == 1 || !itemProps?.loaiBangChiSoSong) {
        return BLOOD_PRESSURE[6].listShow;
      } else if (itemProps?.loaiBangChiSoSong == 2) {
        return BLOOD_PRESSURE[7].listShow;
      }
    }
  };

  const onChangeThoiGian = (index) => (value) => {
    let data = value ? moment(value).format("YYYY-MM-DD HH:mm:00") : null;

    //nếu có giá trị ngày thì set ngày
    if (
      refValue.current.ngayThucHien &&
      value &&
      moment(value).format("YYYY-MM-DD") &&
      itemProps.mauForm !== 2
    ) {
      const years = refValue.current.ngayThucHien.split("-")[0];
      const months = refValue.current.ngayThucHien.split("-")[1] - 1;
      const dates = refValue.current.ngayThucHien.split("-")[2];
      data = moment(value)
        .set({
          years,
          months,
          dates,
        })
        .format("YYYY-MM-DD HH:mm:00");
    }

    refValue.current.dsChiTiet[index].thoiGianThucHien = data;

    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
  };

  const onChangeValueChung = useCallback(
    (key) => (value) => {
      refValue.current[key] = value;
      formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
    },
    [formChange, state.values]
  );

  const formChangeValue = () => {
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
  };

  const handleClickBackground = () => {
    const dsChiSoSong = refValue.current.dsChiTiet.map(
      (el) => el?.chiSoSong || {}
    );

    refModalNhapChiSo.current &&
      refModalNhapChiSo.current.show(
        {
          isChamSocCap1: true,
          values: dsChiSoSong,
          form,
          indexTable,
        },
        onChangeMultiCss
      );
  };

  const onChangeMultiCss = (data) => {
    refValue.current.dsChiTiet.forEach((item, index) => {
      Object.keys(data[index]).forEach((key) => {
        if (!item.chiSoSong) {
          item.chiSoSong = {
            chiDinhTuLoaiDichVu: 201,
            nbDotDieuTriId,
            khoaChiDinhId: form.khoaChiDinhId,
          };
        }
        item.chiSoSong[key] = data[index][key];
        if (key === "thoiGianThucHien") {
          item.thoiGianThucHien = data[index][key];
        }
      });
      let chiSoSong = {};
      Object.keys(item.chiSoSong).forEach((key) => {
        if (item.chiSoSong[key] || item.chiSoSong[key] === "") {
          chiSoSong[key] = item.chiSoSong[key];
        }
      });
      item.chiSoSong = chiSoSong;
      if (Object.keys(item.chiSoSong).length < 3) {
        delete item.chiSoSong;
      }
    });

    setState({
      values: cloneDeep(refValue.current),
    });
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
  };

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    if (dataCopy.chiSoSong) {
      delete dataCopy.chiSoSong.id;
      delete dataCopy.chiSoSong.createdAt;
      delete dataCopy.chiSoSong.createdBy;
      delete dataCopy.chiSoSong.nguoiThucHienId;
      delete dataCopy.chiSoSong.updatedAt;
      delete dataCopy.chiSoSong.updatedBy;
    }
    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
    const viTri = indexTable * 500 + 1 + idx;
    if (
      dsChuKy.some(
        (el) => el.viTri === viTri && el.trangThai >= 50 && el.chuKySo === 1
      )
    ) {
      message.error(t("editor.cotDaKyKhongTheChinhSua"));
      return;
    } else {
      refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);
      setState({
        values: cloneDeep(refValue.current),
      });
      formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
    }
  };

  const handleDelete = (idx) => () => {
    const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
    const viTri = indexTable * 500 + 1 + idx;
    if (
      dsChuKy.some(
        (el) => el.viTri === viTri && el.trangThai >= 50 && el.chuKySo === 1
      )
    ) {
      message.error(t("editor.cotDaKyKhongTheChinhSua"));
      return;
    }
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSong?.id) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSong?.id
              );
            }
            refValue.current.dsChiTiet[idx] = {};
            setState({
              values: cloneDeep(refValue.current),
            });
            formChange["dsTheoDoi"] &&
              formChange["dsTheoDoi"](refValuesAll.current);
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const valuesChiSoSong = useMemo(() => {
    const dsChiTiet = get(state.values, "dsChiTiet", []);
    return dsChiTiet.map((el) => el?.chiSoSong || {});
  }, [state.values]);

  const onChangeNgayThucHien = (value) => {
    refValue.current.ngayThucHien = value;
    const ngayThucHien = moment(value).format("YYYY-MM-DD");

    (refValue.current.dsChiTiet || []).forEach((el) => {
      if (moment(el?.thoiGianThucHien).format("YYYY-MM-DD") !== ngayThucHien) {
        const years = ngayThucHien.split("-")[0];
        const months = ngayThucHien.split("-")[1] - 1;
        const dates = ngayThucHien.split("-")[2];
        el.thoiGianThucHien = moment(el.thoiGianThucHien).set({
          years,
          months,
          dates,
        });
      }
    });
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValuesAll.current);
  };

  /**
   * Renders the table header row with date selection
   */
  const renderHeaderRow = () => (
    <tr>
      <td className="bold center sticky-col sticky-col-1">
        SP02
        {!!indexTable && (
          <Button
            danger
            icon={<CloseOutlined />}
            className="btn-remove-table"
            onClick={removeTable(indexTable)}
            type="primary"
            size="small"
          />
        )}
      </td>
      <td className="bold center  sticky-col sticky-col-2">M</td>
      <td className="bold center  sticky-col sticky-col-3">H/A</td>
      <td className="bold center  sticky-col sticky-col-4">T°</td>
      <td colSpan={itemProps.soLuongCot} className="center">
        {itemProps.mauForm !== 2 && (
          <div className="flex">
            <div className="bold center">Ngày:</div>
            <div style={{ marginLeft: 10 }}>
              <ModalChangeDate
                value={
                  get(refValue.current, `ngayThucHien`)
                    ? moment(
                        get(refValue.current, `ngayThucHien`),
                        "YYYY-MM-DD"
                      ).format()
                    : null
                }
                onChange={onChangeNgayThucHien}
                format="DD/MM/YYYY"
                onlyTime={false}
              />
            </div>
          </div>
        )}
      </td>
      <td className="center">
        <div className="bold"></div>
      </td>
    </tr>
  );

  /**
   * Renders the first row with time inputs
   */
  const renderTimeRow = ({ soLuongCot, columnWidth, itemProps }) => (
    <tr style={{ position: "sticky", top: 0, zIndex: 10 }}>
      <td colSpan={4} className="sticky-col sticky-col-1">
        <div className="hide-print">
          Giữ shift + lăn chuột để scroll sang trái hoặc phải
        </div>
      </td>
      {new Array(soLuongCot || 24).fill({}).map((_, idx) => {
        const viTri = idx + 1 + indexTable * 500;
        const dsChuKy = get(form, "lichSuKy.dsChuKy", []);
        const disableColumn = (dsChuKy || []).some(
          (chuKy) =>
            chuKy.viTri == viTri && chuKy.trangThai >= 50 && chuKy.chuKySo == 1
        );

        return (
          <TimeColumnHeader
            key={idx}
            idx={idx}
            onChangeThoiGian={onChangeThoiGian}
            refValue={refValue}
            dataCopy={dataCopy}
            handleCopy={handleCopy}
            hanldePaste={hanldePaste}
            handleDelete={handleDelete}
            disableColumn={disableColumn}
            width={columnWidth}
            itemProps={itemProps}
          />
        );
      })}

      <td
        rowSpan={itemProps.loaiBangChiSoSong == 2 ? 40 : 39}
        className="col"
        style={{ minWidth: 300 }}
      >
        <div>
          <RenderChanDoan
            formChangeValue={formChangeValue}
            value={state.values}
            refValue={refValue}
            form={form}
            tableIndex={indexTable}
            itemProps={itemProps}
          />
        </div>
      </td>
    </tr>
  );

  /**
   * Renders the vital signs chart row
   */
  const renderVitalSignsChartRow = (height, soLuongCot) => (
    <tr className="vital-sign-chart-row">
      <VitalSignHeaderCell
        type="spo2"
        index={0}
        className="sticky-col sticky-col-1"
        itemProps={itemProps}
        height={height}
      />
      <VitalSignHeaderCell
        type="machs"
        index={0}
        className=" sticky-col sticky-col-2"
        itemProps={itemProps}
        height={height}
      />
      <VitalSignHeaderCell
        type="huyetap"
        index={0}
        rangeBloodPressure={state.rangeBloodPressure}
        className=" sticky-col sticky-col-3"
        itemProps={itemProps}
        height={height}
      />
      <VitalSignHeaderCell
        type="nhiet"
        index={0}
        className=" sticky-col sticky-col-4"
        itemProps={itemProps}
        height={height}
      />
      <td
        colSpan={soLuongCot || 24}
        rowSpan={itemProps.loaiBangChiSoSong == 2 ? 9 : 8}
        style={{
          position: "relative",
          height: `${canvasHeight - 3}px`,
        }}
        className="vital-sign-canvas-cell"
      >
        <VitalSign
          canvasWidth={canvasWidth}
          canvasHeight={canvasHeight}
          columnWidth={columnWidth}
          rangeBloodPressure={
            state.rangeBloodPressure.filter(
              (item, index) => index !== state.rangeBloodPressure.length - 1
            ) || []
          }
          values={valuesChiSoSong}
          bonusSize={1.2}
          handleClickBackground={handleClickBackground}
          showSpo2={true}
          startValueMach={CONFIG_SIZE.startValueMach}
          startValueNhiet={CONFIG_SIZE.startValueNhiet}
          startValueSpo2={CONFIG_SIZE.startValueSpo2}
          totalRow={CONFIG_SIZE.countRow}
          minValueSpo2={CONFIG_SIZE.minValueSpo2}
          maxValueSpo2={CONFIG_SIZE.maxValueSpo2}
        />
      </td>
    </tr>
  );

  /**
   * Renders the remaining vital sign scale rows
   */
  const renderVitalSignScaleRows = (height) => {
    const numberRow = itemProps?.loaiBangChiSoSong == 2 ? 8 : 7;
    return new Array(numberRow).fill({}).map((_, index) => {
      const rowIndex = index + 1;
      return (
        <tr key={rowIndex}>
          <VitalSignHeaderCell
            type="spo2"
            index={rowIndex}
            className=" sticky-col sticky-col-1"
            itemProps={itemProps}
            height={height}
          />
          <VitalSignHeaderCell
            type="machs"
            index={rowIndex}
            className=" sticky-col sticky-col-2"
            itemProps={itemProps}
            height={height}
          />
          <VitalSignHeaderCell
            type="huyetap"
            index={rowIndex}
            rangeBloodPressure={state.rangeBloodPressure}
            className=" sticky-col sticky-col-3"
            height={height}
          />
          <VitalSignHeaderCell
            type="nhiet"
            index={rowIndex}
            className=" sticky-col sticky-col-4"
            itemProps={itemProps}
            height={height}
          />
        </tr>
      );
    });
  };

  /**
   * Renders the vital signs rows (scales and chart)
   */
  const renderVitalSignsRows = () => {
    const height =
      (CONFIG_SIZE.countRow * CONFIG_SIZE.rowHeight) / CONFIG_SIZE.numberRow;

    return (
      <>
        {renderTimeRow({
          soLuongCot: itemProps.soLuongCot,
          columnWidth,
          itemProps,
        })}
        {renderVitalSignsChartRow(
          height + CONFIG_SIZE.headerHeight,
          itemProps.soLuongCot
        )}
        {renderVitalSignScaleRows(height)}
      </>
    );
  };

  /**
   * Renders the data rows for various measurements
   */

  const listTr = useMemo(() => {
    const _listTr = itemProps.mauForm == 2 ? LIST_TR_2 : LIST_TR;
    if (itemProps.showSauDe) {
      const viTriTuanHoanKhac = _listTr.findIndex(
        (el) => el.key === "tuanHoanKhac"
      );
      _listTr.splice(viTriTuanHoanKhac + 1, 0, ...SAU_DE);
      return _listTr;
    } else {
      return _listTr;
    }
  }, [itemProps.showSauDe, itemProps.mauForm]);
  const renderDataRows = () => {
    return listTr.map((item, index) => {
      if (item.isMultilRow) {
        return (
          <ListTrMultiple
            key={index}
            item={item}
            arr={arr}
            index={index}
            mode={mode}
            onChangeInput={onChangeValue}
            value={state.values}
            tableIndex={indexTable}
            listTr={item.listTr}
            refValue={refValue}
            formChangeValue={formChangeValue}
            form={form}
            itemProps={itemProps}
          />
        );
      } else if (item.key === "thuocDichTruyen") {
        if (itemProps.anThuocDichTruyenTrenPhieu) {
          return null;
        }
        return (
          <RenderThuoc
            key={index}
            refValue={refValue}
            values={state.values}
            mode={mode}
            tableIndex={indexTable}
            form={form}
            itemProps={itemProps}
            onChangeValueChung={onChangeValueChung}
          />
        );
      } else {
        return (
          <RenderTr
            key={index}
            item={item}
            arr={arr}
            index={index}
            mode={mode}
            onChangeInput={onChangeValue}
            data={state.values}
            tableIndex={indexTable}
            itemProps={itemProps}
            form={form}
            refTongNhapXuat={refTongNhapXuat}
            onChangeValueChung={onChangeValueChung}
            showSkeleton
          />
        );
      }
    });
  };

  /**
   * Renders the signature row with nurse names
   */
  const renderSignatureRow = () => (
    <tr>
      <td colSpan={4} className="center left sticky-col sticky-col-1">
        <b>Tên điều dưỡng</b>
      </td>
      {new Array(itemProps.soLuongCot).fill({}).map((_, index) => (
        <RenderSign
          key={index}
          mode={mode}
          index={index}
          form={form}
          indexTable={indexTable}
          itemProps={itemProps}
        />
      ))}
      <td></td>
    </tr>
  );

  return (
    <Main
      id="table-scroll"
      className="table-scroll"
      ref={refMain}
      leftColumnWidth={LEFT_VITAL_SIGNS}
      style={indexTable ? { pageBreakAfter: "always" } : {}}
    >
      {itemProps.showHeaderForm && (
        <HeaderForm
          form={form}
          mode={mode}
          formChange={formChange}
          tableIndex={indexTable}
          itemProps={itemProps}
        />
      )}

      <table ref={tableRef} id="main-table" class="main-table">
        <colgroup>
          <col style={{ width: 90 }} />
          <col style={{ width: 90 }} />
          <col style={{ width: 90 }} />
          <col style={{ width: 150 }} />
          {new Array(itemProps.soLuongCot || 24).fill({}).map((_, index) => (
            <col style={{ width: columnWidth }} key={index} />
          ))}
          <col />
        </colgroup>
        <tbody>
          {renderHeaderRow()}
          {renderVitalSignsRows()}
          {renderDataRows()}
          {renderSignatureRow()}
        </tbody>
      </table>
    </Main>
  );
};

export default BangTheoDoiChamSocCap1;
