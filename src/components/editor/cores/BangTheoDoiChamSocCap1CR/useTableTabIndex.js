import { useEffect, useRef, useCallback } from "react";

export function useTableTabIndex(dep) {
  const observerRef = useRef(null);
  const debounceTimerRef = useRef(null);

  // Hàm cập nhật tabindex (có thể gọi cả tự động lẫn thủ công)
  const updateTableTabIndex = useCallback(() => {
    const TABLE_SELECTOR = ".table-scroll";
    const CONTROL_SELECTOR = ".popover-select, .editing-content , input";

    const tables = document.querySelectorAll(TABLE_SELECTOR);
    if (!tables.length) return;

    tables.forEach((table) => {
      const rows = Array.from(table.querySelectorAll("tbody tr"));
      if (!rows.length) return;

      // số cột tối đa có control
      const numCols = Math.max(
        0,
        ...rows.map((r) => r.querySelectorAll(CONTROL_SELECTOR).length)
      );

      // reset tabindex cũ
      rows.forEach((r) =>
        r
          .querySelectorAll(CONTROL_SELECTOR)
          .forEach((el) => el.removeAttribute("tabindex"))
      );

      let tabIndex = 1000;
      const startRow = 0;
      const startCol = 0;

      // column-first
      for (let col = startCol; col < numCols; col++) {
        for (let row = startRow; row < rows.length; row++) {
          const controls = rows[row].querySelectorAll(CONTROL_SELECTOR);
          const el = controls[col];
          if (el) {
            el.setAttribute("tabindex", String(tabIndex));
            tabIndex++;
          }
        }
      }
    });
  }, []);

  useEffect(() => {
    observerRef.current = new MutationObserver((mutations) => {
      let changed = false;
      mutations.forEach((m) => {
        if (m.type === "childList" && m.target.tagName === "TBODY") {
          changed = true;
        }
      });
      if (changed) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = setTimeout(() => {
          updateTableTabIndex();
        }, 100);
      }
    });

    const tables = document.querySelectorAll(".table-scroll");
    tables.forEach((table) => {
      const tbody = table.querySelector("tbody");
      if (tbody) {
        observerRef.current.observe(tbody, { childList: true });
      }
    });

    // chạy lần đầu
    updateTableTabIndex();

    return () => {
      if (observerRef.current) observerRef.current.disconnect();
      clearTimeout(debounceTimerRef.current);
    };
  }, [dep, updateTableTabIndex]);

  // trả ra hàm để gọi thủ công
  return { updateTableTabIndex };
}
