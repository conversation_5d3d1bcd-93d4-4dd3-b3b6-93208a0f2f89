import React from "react";
import { Col, Row } from "antd";
import { DeboundInput } from "components/editor/config";
import { cloneDeep } from "lodash";

const LIST_ARR = {
  PHAN_CAP_CS: {
    PHAN_CAP_CS: [
      { label: "CS cấp 2", value: 1 },
      { label: "CS cấp 3", value: 2 },
    ],
  },
  TOAN_THAN: {
    DA_NIEM_MAC: [
      { label: "Hồng", value: 1 },
      { label: "Vàng", value: 2 },
      { label: "Nhợt nhạt", value: 3 },
      { label: "Tím tái", value: 4 },
    ],
    VET_THUONG: [
      { label: "Có", value: 1 },
      { label: "Không", value: 2 },
    ],
    TINH_TRANG_VT: [
      { label: "Sạch", value: 1 },
      { label: "Nhiễm khuẩn", value: 2 },
      { label: "Hoại tử", value: 3 },
    ],
    TRI_GIAC: [
      { label: "Tỉnh táo", value: 1 },
      { label: "Tiếp xúc tốt", value: 2 },
      { label: "Tiếp xúc chậm", value: 3 },
    ],
  },
  HO_HAP: {
    KIEU_THO: [
      { label: "Êm", value: 1 },
      { label: "Nhanh,Nông", value: 2 },
      { label: "Khò khè", value: 3 },
      { label: "Khó thở", value: 4 },
      { label: "Co kéo cơ hô hấp", value: 5 },
    ],
    HO: [
      { label: "Ho khan", value: 1 },
      { label: "Ho đờm", value: 2 },
      { label: "Ho máu", value: 3 },
    ],
    MAU_SAC_TINH_CHAT_DOM: [
      { label: "Trắng đục", value: 1 },
      { label: "Vàng", value: 2 },
      { label: "Xanh", value: 3 },
      { label: "Lẫn máu", value: 4 },
    ],
  },
  TUAN_HOAN: {
    TINH_CHAT_MACH: [
      { label: "Đều, rõ", value: 1 },
      { label: "Không đều", value: 2 },
      { label: "Rời rạc", value: 3 },
      { label: "Khó bắt", value: 4 },
      { label: "Không rõ mạch", value: 5 },
    ],
    PHU: [
      { label: "Độ 1", value: 1 },
      { label: "Độ 2", value: 2 },
      { label: "Độ 3", value: 3 },
      { label: "Độ 4", value: 4 },
    ],
  },
  TIEU_HOA_DINH_DUONG: {
    TINH_TRANG_DD: [
      { label: "Bình thường", value: 1 },
      { label: "Chán ăn", value: 2 },
      { label: "Nhịn ăn/truyền", value: 3 },
    ],
    DAU_HIEU_KHAC: [
      { label: "Đầy bụng, khó tiêu", value: 1 },
      { label: "Buồn nôn", value: 2 },
      { label: "Ợ chua/hơi", value: 3 },
      { label: "Tiêu chảy", value: 4 },
      { label: "Táo bón", value: 5 },
      { label: "Tiêu máu", value: 6 },
      { label: "Nôn", value: 7 },
    ],
  },
  GIAC_NGU_NGHI_NGOI: {
    TINH_TRANG_GIAC_NGU: [
      { label: "Bình thường", value: 1 },
      { label: "Rối loạn giấc ngủ", value: 2 },
      { label: "Mất ngủ", value: 3 },
    ],
  },
  TIET_NIEU: {
    TIEU_TIEN: [
      { label: "Tự chủ", value: 1 },
      { label: "Không tự chủ", value: 2 },
      { label: "Sonde tiểu", value: 3 },
    ],
    MAU_SAC_NUOC_TIEU: [
      { label: "Trong", value: 1 },
      { label: "Vàng", value: 2 },
      { label: "Hồng", value: 3 },
      { label: "Đỏ tươi", value: 4 },
      { label: "Xanh", value: 5 },
    ],
    TINH_TRANG_TIEU: [
      { label: "Bình thường", value: 1 },
      { label: "Bí tiểu", value: 2 },
      { label: "Tiểu rát/buốt", value: 3 },
      { label: "Tiểu máu", value: 4 },
    ],
  },
  VE_SINH_CA_NHAN: {
    TINH_TRANG: [
      { label: "Tự phục vụ", value: 1 },
      { label: "Phụ thuộc một phần", value: 2 },
      { label: "Phụ thuộc hoàn toàn", value: 3 },
    ],
    RANG_MIENG: [
      { label: "Sạch", value: 1 },
      { label: "Không sạch", value: 2 },
      { label: "Đủ răng", value: 3 },
      { label: "Răng giả", value: 4 },
    ],
    THAN_THE: [
      { label: "Sạch", value: 1 },
      { label: "Chưa sạch", value: 2 },
    ],
  },
  THAN_KINH: {
    TINH_TRANG: [
      { label: "Bình thường", value: 1 },
      { label: "YL toàn thân", value: 2 },
      { label: "YL ½ người T", value: 3 },
      { label: "YL ½ người P", value: 4 },
      { label: "YL hai chi dưới", value: 5 },
    ],
    LOI_NOI: [
      { label: "Bình thường", value: 1 },
      { label: "Nói khàn", value: 2 },
      { label: "Mất tiếng", value: 3 },
    ],
    TE_BI_CHAN_TAY: [
      { label: "Có", value: 1 },
      { label: "Không", value: 2 },
      { label: "Mất cảm giác", value: 3 },
      { label: "Giảm cảm giác", value: 4 },
    ],
    DAU_HIEU_KHAC: [
      { label: "Run", value: 1 },
      { label: "Co giật", value: 2 },
      { label: "Múa giật", value: 3 },
      { label: "Loạn động", value: 4 },
      { label: "Động kinh", value: 5 },
      { label: "Chóng mặt", value: 6 },
    ],
  },
  XUONG_KHOP_VAN_DONG_PHCN: {
    XUONG_KHOP: [
      { label: "Bình thường", value: 1 },
      { label: "Sưng/viêm", value: 2 },
      { label: "Biến dạng", value: 3 },
      { label: "Cứng khớp", value: 4 },
    ],
    TAM_VAN_DONG: [
      { label: "Không hạn chế", value: 1 },
      { label: "Có hạn chế", value: 2 },
    ],
  },
  CAN_THIEP_DIEU_DUONG: {
    THUC_HIEN_THUOC: [
      { label: "Thuốc uống", value: 1 },
      { label: "Thuốc tiêm", value: 2 },
      { label: "Truyền máu", value: 3 },
      { label: "Truyền TC", value: 4 },
      { label: "Truyền thuốc", value: 5 },
    ],
    THUC_HIEN_CHI_DINH_CLS: [
      { label: "MRI", value: 1 },
      { label: "CT-Scan", value: 2 },
      { label: "Holter tim", value: 3 },
      { label: "Điện não đồ", value: 4 },
      { label: "Điện tâm đồ", value: 5 },
      { label: "Siêu âm tim", value: 6 },
      { label: "Soi dạ dày", value: 7 },
      { label: "Soi đại tràng", value: 8 },
      { label: "Holter HA", value: 9 },
      { label: "X-Quang", value: 10 },
      { label: "XN máu", value: 11 },
      { label: "Khí máu", value: 12 },
      { label: "Cấy máu", value: 13 },
      { label: "Nước tiểu", value: 14 },
      { label: "Khám RHM", value: 15 },
      { label: "Khám TMH", value: 16 },
      { label: "Khám mắt", value: 17 },
    ],
    CHAM_SOC_DIEU_DUONG: [
      { label: "Thở oxy", value: 1 },
      { label: "Vỗ rung", value: 2 },
      { label: "Ăn qua sonde", value: 3 },
      { label: "Đặt sonde dạ dày", value: 4 },
      { label: "Đặt sonde tiểu", value: 5 },
      { label: "Đặt catheter TM", value: 6 },
      { label: "TB cắt chỉ", value: 7 },
      { label: "TB-RVT", value: 8 },
      { label: "TB-RVT-CL", value: 9 },
      { label: "Thụt tháo", value: 10 },
      { label: "CS loét", value: 11 },
      { label: "Rút dẫn lưu", value: 12 },
      { label: "Gội đầu", value: 13 },
      { label: "Tắm", value: 14 },
      { label: "Rút meches", value: 15 },
      { label: "PHCN", value: 16 },
      { label: "Hạ sốt", value: 17 },
      { label: "Vệ sinh", value: 18 },
    ],
  },
  THEO_DOI_NGUOI_BENH: {
    THEO_DOI_NGUOI_BENH: [
      { label: "DHST", value: 1 },
      { label: "Cân nặng", value: 2 },
      { label: "Dinh dưỡng", value: 3 },
      { label: "ĐMMM", value: 4 },
      { label: "Hô hấp", value: 5 },
      { label: "Nước tiểu", value: 6 },
      { label: "Vệ sinh", value: 7 },
      { label: "Vết mổ", value: 8 },
      { label: "Vận động", value: 9 },
      { label: "Lọc máu", value: 10 },
      { label: "Dịch dẫn lưu", value: 11 },
      { label: "Dấu hiệu khàn tiếng", value: 12 },
    ],
  },
};

export const THEAD = [
  {
    label: "Ngày :",
    colSpan: 2,
    key: "ngayThucHien",
  },
  {
    label: "Giờ :",
    colSpan: 2,
    key: "thoiGian",
  },
];

const renderListArr = (value, exitsKhac, keyKhac) => {
  return value.map((el, index) => {
    const data = {
      label: `${el.value} - ${el.label}`,
      value: el.value,
    };
    if (exitsKhac) {
      data.keyKhac = keyKhac;
      if (index + 1 == value.length) {
        data.valueKeyKhac = index + 1;
      }
    }

    return data;
  });
};

export const TR = [
  {
    label: "I. NHẬN ĐỊNH TÌNH TRẠNG NGƯỜI BỆNH",
    colSpan: 3,
    disable: true,
  },
  {
    label: "Phân cấp CS",
    colSpan: 2,
    key: "phanCapChamSoc",
    hint: (
      <Row>
        {LIST_ARR.PHAN_CAP_CS.PHAN_CAP_CS.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.PHAN_CAP_CS.PHAN_CAP_CS),
  },
  //Chỉ số sinh trắc
  {
    labelParent: "Chỉ số sinh trắc",
    rowSpan: 4,
    label: "Cân nặng",
    key: "chiSoSong.canNang",
    type: "number",
    hint: <span>Chỉ số cân nặng hàng ngày(kg)</span>,
  },
  {
    label: "Chiều cao",
    isChild: true,
    key: "chiSoSong.chieuCao",
    type: "number",
    hint: <span>Chỉ số chiều cao(m)</span>,
  },
  {
    label: "BMI",
    isChild: true,
    key: "chiSoSong.bmi",
    type: "number",
    hint: <span>Chỉ số khối cơ thể(kg/m2)</span>,
  },
  {
    label: "VE/VH",
    isChild: true,
    key: "veh",
    hint: <span>Chỉ số vòng eo/ Chỉ số vòng hông(cm)</span>,
  },
  //Toàn thân
  {
    labelParent: "Toàn thân",
    rowSpan: 6,
    label: "Da, niêm mạc",
    key: "daNiemMac",
    hint: (
      <Row>
        {LIST_ARR.TOAN_THAN.DA_NIEM_MAC.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TOAN_THAN.DA_NIEM_MAC),
  },
  {
    label: "Vết thương",
    key: "vetThuong",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TOAN_THAN.VET_THUONG.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TOAN_THAN.VET_THUONG),
  },
  {
    label: "KT",
    key: "kichThuocVetThuong",
    isChild: true,
    hint: <span>Kích thước vết thương(cmxcm)</span>,
  },
  {
    label: "Tình trạng VT",
    key: "tinhTrangVetThuong",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TOAN_THAN.TINH_TRANG_VT.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TOAN_THAN.TINH_TRANG_VT),
  },
  {
    label: "Tri giác",
    key: "triGiac",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TOAN_THAN.TRI_GIAC.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TOAN_THAN.TRI_GIAC),
  },
  {
    label: "Khác",
    key: "toanThanTuNhap",
    isChild: true,
    keyLabel: "tenToanThanTuNhap",
  },
  //Hô hấp
  {
    labelParent: "Hô hấp",
    label: "NT",
    key: "chiSoSong.nhipTho",
    rowSpan: 7,
    hint: <span>Chỉ số nhịp thở (lần/phút)</span>,
  },
  {
    label: "Kiểu thở",
    key: "kieuTho",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.HO_HAP.KIEU_THO.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.HO_HAP.KIEU_THO),
  },
  {
    label: "SpO2(%)",
    key: "chiSoSong.spo2",
    isChild: true,
    hint: <span>Chỉ số SpO2(%)</span>,
  },
  {
    label: "Ho",
    key: "ho",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.HO_HAP.HO.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.HO_HAP.HO),
  },
  // .
  {
    label: "SL đờm(ml)",
    key: "soLuongDom",
    isChild: true,
  },
  {
    label: "Màu sắc, tính chất đờm",
    key: "mauSacSom",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.HO_HAP.MAU_SAC_TINH_CHAT_DOM.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.HO_HAP.MAU_SAC_TINH_CHAT_DOM),
  },
  {
    label: "Khác",
    key: "hoHapKhac",
    isChild: true,
  },
  //Tuần hoàn
  {
    labelParent: "Tuần hoàn",
    label: "Mạch",
    key: "chiSoSong.mach",
    rowSpan: 6,
    hint: <span>Chỉ số mạch(lần/phút)</span>,
  },
  {
    label: "Tính chất mạch",
    key: "tinhChatMach",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TUAN_HOAN.TINH_CHAT_MACH.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.TUAN_HOAN.TINH_CHAT_MACH),
  },
  {
    label: "Nhiệt độ",
    key: "chiSoSong.nhietDo",
    isChild: true,
    hint: <span>Chỉ số nhiệt độ(0C)</span>,
  },
  {
    label: "HA",
    key: "chiSoSong.huyetAp",
    isChild: true,
    hint: <span>Chỉ số huyết áp(mmHg)</span>,
  },
  {
    label: "Phù",
    key: "phu",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TUAN_HOAN.PHU.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TUAN_HOAN.PHU),
  },
  {
    label: "Khác",
    key: "phuKhac",
    isChild: true,
  },
  //Tiêu hóa, dinh dưỡng
  {
    labelParent: "Tiêu hóa, dinh dưỡng",
    label: "Chế độ ăn",
    key: "cheDoAn",
    rowSpan: 4,
  },
  {
    label: "Tình trạng DD",
    key: "tinhTrangDinhDuong",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TIEU_HOA_DINH_DUONG.TINH_TRANG_DD.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TIEU_HOA_DINH_DUONG.TINH_TRANG_DD),
  },
  {
    label: "Bệnh lý tiêu hóa",
    key: "benhTieuHoa",
    isChild: true,
  },
  {
    label: "Dấu hiệu khác",
    key: "dinhDuong2",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TIEU_HOA_DINH_DUONG.DAU_HIEU_KHAC.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.TIEU_HOA_DINH_DUONG.DAU_HIEU_KHAC),
    mode: "multiple",
  },
  //Giấc ngủ, nghỉ ngơi
  {
    labelParent: "Giấc ngủ, nghỉ ngơi",
    label: "Tình trạng giấc ngủ",
    key: "tinhTrangGiacNgu",
    rowSpan: 2,
    hint: (item, callback) => (
      <Row>
        {LIST_ARR.GIAC_NGU_NGHI_NGOI.TINH_TRANG_GIAC_NGU.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
        <Col span={8}>
          <DeboundInput
            label={"(4) "}
            value={item?.moTaTinhTrangGiacNgu || ""}
            onChange={callback("moTaTinhTrangGiacNgu")}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            styleMain={{ minWidth: "50px" }}
          />
        </Col>
      </Row>
    ),
    type: "droplist",
    data: renderListArr(
      LIST_ARR.GIAC_NGU_NGHI_NGOI.TINH_TRANG_GIAC_NGU,
      true,
      "moTaTinhTrangGiacNgu"
    ),
  },
  {
    label: "Sử dụng thuốc ngủ",
    key: "thuocNgu",
    isChild: true,
  },
  //Tiết niệu
  {
    labelParent: "Tiết niệu",
    label: "Tiểu tiện",
    key: "tinhTrangTietNieu",
    rowSpan: 5,
    hint: (
      <Row>
        {LIST_ARR.TIET_NIEU.TIEU_TIEN.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.TIET_NIEU.TIEU_TIEN),
  },
  {
    label: "SL nước tiểu",
    key: "soLuongNuocTieu",
    isChild: true,
    hint: <span>Số lượng nước tiểu(ml)</span>,
  },
  {
    label: "Màu sắc nước tiểu",
    key: "mauSacNuocTieu",
    isChild: true,
    hint: (item, callback) => (
      <Row>
        {LIST_ARR.TIET_NIEU.MAU_SAC_NUOC_TIEU.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
        <Col span={8}>
          <DeboundInput
            label={"(6) "}
            value={item?.moTaMauSacNuocTieu || ""}
            onChange={callback("moTaMauSacNuocTieu")}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            styleMain={{ minWidth: "50px" }}
          />
        </Col>
      </Row>
    ),
    type: "droplist",
    data: renderListArr(
      LIST_ARR.TIET_NIEU.MAU_SAC_NUOC_TIEU,
      true,
      "moTaMauSacNuocTieu"
    ),
  },
  {
    label: "Tình trạng tiểu",
    key: "tinhTrangTieu",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.TIET_NIEU.TINH_TRANG_TIEU.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.TIET_NIEU.TINH_TRANG_TIEU),
  },
  {
    label: "Khác",
    key: "tietNieuKhac",
    isChild: true,
  },
  //Vệ sinh cá nhân
  {
    labelParent: "Vệ sinh cá nhân",
    label: "Tình trạng",
    key: "veSinhCaNhan",
    rowSpan: 4,
    hint: (
      <Row>
        {LIST_ARR.VE_SINH_CA_NHAN.TINH_TRANG.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.VE_SINH_CA_NHAN.TINH_TRANG),
  },
  {
    label: "Răng miệng",
    key: "rangMieng",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.VE_SINH_CA_NHAN.RANG_MIENG.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.VE_SINH_CA_NHAN.RANG_MIENG),
  },
  {
    label: "Thân thể",
    key: "thanThe",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.VE_SINH_CA_NHAN.THAN_THE.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.VE_SINH_CA_NHAN.THAN_THE),
  },
  {
    label: "Khác",
    key: "veSinhCaNhanKhac",
    isChild: true,
  },
  //Thần kinh
  {
    labelParent: "Thần kinh",
    label: "Tình trạng",
    key: "tinhThan",
    rowSpan: 4,
    hint: (
      <Row>
        {LIST_ARR.THAN_KINH.TINH_TRANG.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.THAN_KINH.TINH_TRANG),
  },
  {
    label: "Lời nói",
    key: "loiNoi",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.THAN_KINH.LOI_NOI.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.THAN_KINH.LOI_NOI),
  },
  {
    label: "Tê bì tay chân",
    key: "teBiTayChan",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.THAN_KINH.TE_BI_CHAN_TAY.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.THAN_KINH.TE_BI_CHAN_TAY),
  },
  {
    label: "Dấu hiệu khác",
    key: "thanKinhKhac",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.THAN_KINH.DAU_HIEU_KHAC.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.THAN_KINH.DAU_HIEU_KHAC),
  },
  //Xương khớp, Vận động, PHCN
  {
    labelParent: "Xương khớp, Vận động, PHCN",
    label: "Xương khớp",
    key: "xuongKhop",
    rowSpan: 5,
    hint: (
      <Row>
        {LIST_ARR.XUONG_KHOP_VAN_DONG_PHCN.XUONG_KHOP.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.XUONG_KHOP_VAN_DONG_PHCN.XUONG_KHOP),
  },
  {
    label: "Vị trí",
    key: "viTriTonThuongXuongKhop",
    isChild: true,
  },
  {
    label: "Tầm vận động",
    key: "tamVanDong",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.XUONG_KHOP_VAN_DONG_PHCN.TAM_VAN_DONG.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.XUONG_KHOP_VAN_DONG_PHCN.TAM_VAN_DONG),
  },
  {
    label: "Khác",
    key: "xuongKhopKhac",
    isChild: true,
  },
  {
    label: "Nguy cơ ngã",
    key: "nguyCoNga",
    isChild: true,
  },
  //Theo dõi khác
  {
    labelParent: "Theo dõi khác",
    rowSpan: 3,
    label: "Đau",
    key: "dau",
  },

  {
    label: "Loét",
    key: "tinhTrangLoet",
    isChild: true,
  },
  {
    label: "VIP Score",
    key: "theoDoiKhac",
    isChild: true,
  },

  //Can thiệp điều dưỡng
  {
    label: "II. LẬP KẾ HOẠCH VÀ CAN THIỆP ĐIỀU DƯỠNG",
    colSpan: 3,
    disable: true,
  },
  {
    label: "Thực hiện thuốc",
    colSpan: 2,
    key: "thucHienThuoc",
    key2: "thucHienThuoc2",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.CAN_THIEP_DIEU_DUONG.THUC_HIEN_THUOC.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(LIST_ARR.CAN_THIEP_DIEU_DUONG.THUC_HIEN_THUOC),
  },
  {
    label: "Thực hiện theo chỉ định CLS",
    colSpan: 2,
    key: "thucHienCls",
    key2: "thucHienCls2",
    isChild: true,
    hint: (item, callback) => (
      <Row>
        {LIST_ARR.CAN_THIEP_DIEU_DUONG.THUC_HIEN_CHI_DINH_CLS.map(
          (item, index) => (
            <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
          )
        )}
        <Col span={8}>
          <DeboundInput
            label={"(18) "}
            value={item?.moTaThucHienCls || ""}
            onChange={callback("moTaThucHienCls")}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            styleMain={{ minWidth: "50px" }}
          />
        </Col>
      </Row>
    ),
    type: "droplist",
    data: (item) => {
      let listData = cloneDeep(LIST_ARR.CAN_THIEP_DIEU_DUONG.THUC_HIEN_CHI_DINH_CLS);
      if (item?.moTaThucHienCls && !listData.find((x) => x.value === 18)) {
        listData.push({ value: 18, label: item.moTaThucHienCls });
      }
      return renderListArr(listData);
    },
    mode: "multiple",
  },
  {
    label: "Chăm sóc điều dưỡng",
    colSpan: 2,
    key: "chamSocDieuDuong",
    key2: "chamSocDieuDuong2",
    isChild: true,
    hint: (
      <Row>
        {LIST_ARR.CAN_THIEP_DIEU_DUONG.CHAM_SOC_DIEU_DUONG.map(
          (item, index) => (
            <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
          )
        )}
      </Row>
    ),
    type: "droplist",
    data: renderListArr(LIST_ARR.CAN_THIEP_DIEU_DUONG.CHAM_SOC_DIEU_DUONG),
    mode: "multiple",
  },
  {
    label: "Khác",
    colSpan: 2,
    key: "canThiepDieuDuongKhac",
  },
  //Theo dõi người bệnh
  {
    label: "III. THEO DÕI NGƯỜI BỆNH",
    colSpan: 3,
    disable: true,
  },
  {
    label: "",
    colSpan: 2,
    key: "theoDoi",
    isChild: true,
    hint: (item, callback) => (
      <Row>
        {LIST_ARR.THEO_DOI_NGUOI_BENH.THEO_DOI_NGUOI_BENH.map((item, index) => (
          <Col key={index} span={8}>{`(${item.value}) ${item.label}`}</Col>
        ))}
        <Col span={8}>
          <DeboundInput
            label={"(13) Khác: "}
            value={item?.moTaTheoDoi || ""}
            onChange={callback("moTaTheoDoi")}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            styleMain={{ minWidth: "50px" }}
          />
        </Col>
      </Row>
    ),
    type: "droplist",
    mode: "multiple",
    data: renderListArr(
      LIST_ARR.THEO_DOI_NGUOI_BENH.THEO_DOI_NGUOI_BENH,
      true,
      "moTaTheoDoi"
    ),
  },
  {
    label: "Điều dưỡng thực hiện",
    colSpan: 2,
    key: "sign",
  },
];
