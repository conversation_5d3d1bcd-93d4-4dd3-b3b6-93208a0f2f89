import React, { memo, useEffect, useRef, useState } from "react";
import { THEAD, TR } from "../constants";
import { cloneDeep, get } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { updateObject } from "utils";
import moment from "moment";
import { SVG } from "assets";
import ImageSign from "../../ImageSign";
import { combineFields } from "utils/editor-utils";
import { message } from "antd";
import CopyPasteCol from "../../Components/CopyPasteCol";
import { refConfirm } from "app";
import { useTranslation } from "react-i18next";
import { useQueryString } from "hooks";
import PopoverSelect from "./PopoverSelect";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";

const NUM_OF_COLS = 6;
const arr = new Array(NUM_OF_COLS).fill({});

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  handleRemove,
  refModalChangeHuyetAp,
  itemProps,
  form,
  setDataCopy,
  dataCopy,
  refModalSinhHieu,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const [khoaChiDinhId] = useQueryString("khoaChiDinhId");

  const refValue = useRef();
  useEffect(() => {
    refValue.current = item || {};
    setState({
      value: refValue.current,
    });
  }, [item]);

  const onChangeVE = (index) => (value) => {
    if (value.split("/").length - 1 !== 1) {
      message.error("Nhập sai quy tắc. Nhập đúng ví dụ: 60/90 ");
      return;
    }
    const arr = value.split("/");
    const ve = +arr[0];

    updateObject(refValue.current["dsChiTiet"][index], `ve`, ve);
    if (arr.length > 1) {
      const vh = +arr[1];
      updateObject(refValue.current["dsChiTiet"][index], `vh`, vh);
    }
  };

  const onChangeInput = (key, idx) => (value) => {
    const keySplit = (key || "").split(".");
    let key1, key2;
    key1 = keySplit[0];
    if (keySplit.length > 1) {
      key2 = keySplit[1];
    }

    // thời gian thực hiện của bản ghi chỉ số sống

    let thoiGianThucHien = get(
      refValue.current,
      `dsChiTiet[${idx}].chiSoSong.thoiGianThucHien`
    );
    if (
      !thoiGianThucHien &&
      (get(refValue.current, `dsChiTiet[${idx}].ngayThucHien`) ||
        get(refValue.current, `dsChiTiet[${idx}].thoiGian`))
    ) {
      if (!refValue.current.dsChiTiet[idx].chiSoSong) {
        refValue.current.dsChiTiet[idx].chiSoSong = {};
      }

      const ngayThucHien =
        get(refValue.current, `dsChiTiet[${idx}].ngayThucHien`) ||
        moment().format("YYYY-MM-DD");
      const thoiGian =
        get(refValue.current, `dsChiTiet[${idx}].thoiGian`) ||
        moment().format("HH:mm:00");

      thoiGianThucHien = ngayThucHien + " " + thoiGian;
      refValue.current.dsChiTiet[idx].chiSoSong.thoiGianThucHien = moment(
        thoiGianThucHien,
        "YYYY-MM-DD HH:mm:00"
      ).format();
    }

    if (["thoiGian"].includes(key) && value) {
      const hours = value.split(":")[0];
      const minutes = value.split(":")[1];

      thoiGianThucHien = (
        thoiGianThucHien ? moment(thoiGianThucHien) : moment()
      )
        .set({
          hours,
          minutes,
          seconds: 0,
        })
        .format();

      refValue.current.dsChiTiet[idx].chiSoSong.thoiGianThucHien =
        thoiGianThucHien;
      refValue.current.dsChiTiet[idx].thoiGianThucHien = thoiGianThucHien;
    } else if (["ngayThucHien"].includes(key) && value) {
      const years = value.split("-")[0];
      const month = value.split("-")[1] - 1;
      const date = value.split("-")[2];
      thoiGianThucHien = (
        thoiGianThucHien ? moment(thoiGianThucHien) : moment()
      )
        .set({
          years,
          month,
          date,
        })
        .format();
      refValue.current.dsChiTiet[idx].chiSoSong.thoiGianThucHien =
        thoiGianThucHien;
      refValue.current.dsChiTiet[idx].thoiGianThucHien = thoiGianThucHien;
    } else if (key == "veh") {
      onChangeVE(idx)(value);
    }
    if (!key2) {
      updateObject(refValue.current.dsChiTiet[idx], key, value);
    } else {
      updateObject(refValue.current.dsChiTiet[idx][key1], `${key2}`, value);
    }
    formChangeValue();
  };

  const formChangeValue = () => {
    refValueForm[tableIndex] = refValue.current;
    const values = cloneDeep(refValueForm);
    values.forEach((el) => {
      el.dsChiTiet.forEach((chiTiet) => {
        Object.keys(chiTiet.chiSoSong || {}).forEach((key) => {
          if (!chiTiet.chiSoSong[key]) {
            delete chiTiet.chiSoSong[key];
          }
        });
        if (Object.keys(chiTiet.chiSoSong || {}).length <= 3) {
          chiTiet.chiSoSong = null;
        }
      });
    });

    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](values);
  };

  const handleCopy = (idx) => () => {
    const dsChiTiet = cloneDeep(get(refValue.current, `dsChiTiet[${idx}]`, {}));
    if (dsChiTiet.chiSoSong) {
      dsChiTiet.chiSoSong.id = null;
      dsChiTiet.chiSoSong.nguoiThucHienId = null;
      dsChiTiet.chiSoSong.createdAt = null;
      dsChiTiet.chiSoSong.updatedAt = null;
      dsChiTiet.chiSoSong.updatedBy = null;
    }
    dsChiTiet.chiSoSongId = null;

    setDataCopy({
      col: idx,
      data: dsChiTiet,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy?.data);
    formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
    setState({
      value: refValue.current,
    });
  };

  const handleDelete = (idx) => () => {
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            if (refValue.current.dsChiTiet[idx]?.chiSoSongId) {
              await nbChiSoSongProvider.onDelete(
                refValue.current.dsChiTiet[idx]?.chiSoSongId
              );
            }

            refValue.current.dsChiTiet[idx] = {};
            const values = cloneDeep(refValueForm);
            refValue.current.dsChiTiet[idx] = {
              chiSoSong: {
                chiDinhTuLoaiDichVu: 201,
                nbDotDieuTriId: nbDotDieuTriId,
                khoaChiDinhId: khoaChiDinhId,
              },
            };
            formChange["dsTheoDoi"] && formChange["dsTheoDoi"](values);
            setState({
              value: refValue.current,
              valueClone: cloneDeep(refValue.current),
            });
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const onSelectSinhHieu = (idx) => () => {
    refModalSinhHieu?.current &&
      refModalSinhHieu?.current.show(
        {
          nbDotDieuTriId,
          khoaChiDinhId,
        },
        (sinhHieu) => {
          const ngayThucHien = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
            : "";
          const thoiGian = sinhHieu.thoiGianThucHien
            ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
            : "";
          const allChiTiet = refValueForm
            .map((el, index) => {
              return el.dsChiTiet;
            })
            .flat(Infinity);
          let chiTietOld = allChiTiet.find(
            (el) =>
              el.chiSoSong?.thoiGianThucHien &&
              moment(el.chiSoSong?.thoiGianThucHien).format(
                "DD/MM/YYYY HH:mm"
              ) === moment(sinhHieu.thoiGianThucHien).format("DD/MM/YYYY HH:mm")
          );
          if (chiTietOld) {
            message.error(
              t("editor.nbDaTonTaiSinhHieu", {
                time: moment(chiTietOld.chiSoSong.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
              })
            );
          } else {
            refValue.current.dsChiTiet[idx]["chiSoSong"] = sinhHieu;
            refValue.current.dsChiTiet[idx].ngayThucHien = ngayThucHien;
            refValue.current.dsChiTiet[idx].thoiGian = thoiGian;
            refValue.current.dsChiTiet[idx].thoiGianThucHien = moment(
              sinhHieu.thoiGianThucHien
            ).format("YYYY-MM-DD HH:mm:00");
            setState({
              value: cloneDeep(refValue.current),
            });
            formChange["dsTheoDoi"] && formChange["dsTheoDoi"](refValueForm);
          }
        }
      );
  };
  console.log("state.value", state.value);
  return (
    <table key={tableIndex} style={{ marginTop: 10 }}>
      <thead>
        {THEAD.map((item, index) => {
          return (
            <tr key={`${tableIndex} th_${THEAD.length * index}_${index}`}>
              <td
                style={{ position: "relative" }}
                colSpan={item.colSpan}
                className="bold"
              >
                {item.label}
                {item.key === "ngayThucHien" && tableIndex > 0 ? (
                  <SVG.IcDelete
                    className="ic-remove"
                    onClick={handleRemove(tableIndex)}
                  ></SVG.IcDelete>
                ) : null}
              </td>
              {item.key === "ngayThucHien" ? (
                <td
                  rowSpan={2}
                  className="bold"
                  style={{ width: 250, minWidth: 250, maxWidth: 250 }}
                ></td>
              ) : null}
              {arr.map((el, idx) => {
                let timeValue = get(
                  state.value,
                  `dsChiTiet[${idx}].${item.key}`
                );

                if (item.key === "thoiGian" && timeValue) {
                  const hours = timeValue.split(":")[0];
                  const minutes = timeValue.split(":")[1];
                  timeValue = moment()
                    .set({
                      hours,
                      minutes,
                    })
                    .format();
                }
                return (
                  <td
                    key={`${tableIndex} ${item.key}_${
                      THEAD.length * index + idx + 1
                    }`}
                    style={{ position: "relative" }}
                    className={`bold col-${idx}`}
                  >
                    {!index && (
                      <CopyPasteCol
                        colIndex={idx}
                        handlePaste={hanldePaste(idx)}
                        handleCopy={handleCopy(idx)}
                        dataCopy={dataCopy}
                        handleDelete={handleDelete(idx)}
                        onSelectSinhHieu={onSelectSinhHieu(idx)}
                      ></CopyPasteCol>
                    )}
                    <AppDatePicker
                      component={{
                        props: {
                          contentAlign: "center",
                          dateTimeFormat:
                            item.key === "ngayThucHien" ? "D/M/Y" : "HH:mm",
                          fieldName: "value",
                          fontSize: 8,
                        },
                      }}
                      form={{
                        value: timeValue,
                      }}
                      mode={mode}
                      formChange={{
                        value: (e) => {
                          try {
                            let value = e;
                            if (e) {
                              if (item.key === "ngayThucHien") {
                                value = moment(e).format("YYYY-MM-DD");
                              } else {
                                value = moment(e).format("HH:mm:ss");
                              }
                            }
                            onChangeInput(`${item.key}`, idx)(value);
                          } catch (error) {}
                        },
                      }}
                    ></AppDatePicker>
                  </td>
                );
              })}
            </tr>
          );
        })}
      </thead>
      <tbody>
        {TR.map((item, index) => {
          const keySplit = (item?.key2 || item.key || "").split(".");
          let key1, key2;
          key1 = keySplit[0];
          if (keySplit.length > 1) {
            key2 = keySplit[1];
          }
          const keyPrevSplit = (item.key || "").split(".");
          let key1Prev = keyPrevSplit[0];

          const renderItem = (idx) => {
            switch (true) {
              case item.type === "droplist":
                let isValueKey2 = false;
                const getValueItem = (idx) => {
                  if (key2) {
                    return get(
                      state.value,
                      `dsChiTiet[${idx}].${key1}.${key2}`
                    );
                  } else {
                    const data = get(state.value, `dsChiTiet[${idx}].${key1}`);
                    const dataOld = get(
                      state.value,
                      `dsChiTiet[${idx}].${key1Prev}`
                    );
                    if (!data && item.key2) {
                      if (dataOld) {
                        isValueKey2 = true;
                        return dataOld;
                      } else {
                        return [];
                      }
                    } else {
                      return data;
                    }
                  }
                };
                const list =
                  typeof item.data === "function"
                    ? item.data({
                        moTaThucHienCls:
                          state?.value?.dsChiTiet?.[0]?.moTaThucHienCls,
                      })
                    : item.data;
                return (
                  <PopoverSelect
                    data={list}
                    value={getValueItem(idx)}
                    isMultiple={item.mode === "multiple"}
                    refValue={refValue}
                    onChangeInput={onChangeInput}
                    onChangeValue={(e) => {
                      onChangeInput(item.key2 || item.key, idx)(e);
                    }}
                    labelByKey={item.labelByKey || "label"}
                    isValueKey2={isValueKey2}
                  />
                );

              default:
                return (
                  <DeboundInput
                    rows={1}
                    readOnly={false}
                    value={
                      key2
                        ? get(
                            state.value,
                            `dsChiTiet[${idx}][${key1}][${key2}]`
                          )
                        : get(state.value, `dsChiTiet[${idx}][${key1}]`)
                    }
                    onChange={onChangeInput(item.key, idx)}
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={9}
                    minHeight={9 + 6}
                    markSpanRow={false}
                    inputNumber={item.type === "number"}
                    minValue={-1}
                    tabIndex={
                      tableIndex * (NUM_OF_COLS * TR.length) +
                      (index + 1 + idx * TR.length)
                    }
                  />
                );
            }
          };

          return (
            <tr
              key={`${tableIndex}  tr_${item.key}_${
                tableIndex * TR.length + index
              }`}
            >
              {item.rowSpan ? (
                <td
                  rowSpan={item.rowSpan}
                  className={`bold`}
                  style={{ width: 60, minWidth: 60, maxWidth: 60 }}
                >
                  {item.labelParent}
                </td>
              ) : null}
              <td
                colSpan={item.colSpan || 1}
                className={item.isChild || item.rowSpan ? "" : "bold"}
                style={{ width: 80, minWidth: 80, maxWidth: 80 }}
              >
                {item.labelIsInput ? (
                  <DeboundInput
                    rows={1}
                    readOnly={false}
                    value={get(state.value, item.keyLabel)}
                    onChange={onChangeInput(item.keyLabel)}
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={9}
                    minHeight={9 + 6}
                    markSpanRow={false}
                  />
                ) : (
                  item.label
                )}
              </td>
              {!item.disable && (
                <td className="hint">
                  {typeof item.hint === "function"
                    ? item.hint(
                        {
                          moTaThucHienCls:
                            state?.value?.dsChiTiet?.[0]?.moTaThucHienCls,
                        },
                        (key) => (value) => {
                          console.log(key, value);
                          onChangeInput(key, 0)(value);
                        }
                      )
                    : item.hint}
                </td>
              )}

              {arr.map((el, idx) => (
                <td
                  key={`${tableIndex} td_${item.key} ${
                    index + (idx * index + idx)
                  }`}
                >
                  {item.key === "chiSoSong.huyetAp" ? (
                    <RenderHuyetAp
                      idx={idx}
                      refModalChangeHuyetAp={refModalChangeHuyetAp}
                      formChangeValue={formChangeValue}
                      value={state.value}
                      onChangeInput={onChangeInput}
                      valueClone={state.valueClone}
                    ></RenderHuyetAp>
                  ) : null}

                  {!item.disable &&
                  item.key !== "sign" &&
                  item.key !== "chiSoSong.huyetAp"
                    ? renderItem(idx)
                    : null}

                  {item.key === "sign" ? (
                    <ImageSign
                      component={{
                        props: {
                          ...itemProps,
                          isMultipleSign: true,
                          viTri: tableIndex * NUM_OF_COLS + idx + 1,
                          customText: "Ký",
                          dataSign: {
                            id: form.id,
                            soPhieu: form?.lichSuKy?.soPhieu,
                            lichSuKyId: form?.lichSuKy?.id,
                          },
                        },
                      }}
                      form={{
                        ...combineFields(form),
                      }}
                    />
                  ) : null}
                </td>
              ))}
            </tr>
          );
        })}
      </tbody>
    </table>
  );
};

const RenderHuyetAp = ({
  value,
  idx,
  formChangeValue,
  refModalChangeHuyetAp,
  onChangeInput,
  valueClone,
}) => {
  const [text, setText] = useState("");
  const [huyetAp, setHuyetAp] = useState({
    huyetApTamThu: "",
    huyetApTamTruong: "",
  });
  useEffect(() => {
    const huyetApTamThu = get(
      value,
      `dsChiTiet[${idx}].chiSoSong.huyetApTamThu`,
      ""
    );
    const huyetApTamTruong = get(
      value,
      `dsChiTiet[${idx}].chiSoSong.huyetApTamTruong`
    );
    setText(`${huyetApTamThu || ""}/${huyetApTamTruong || ""}`);
    setHuyetAp({
      huyetApTamThu,
      huyetApTamTruong,
    });
  }, [value, valueClone]);

  const onShowModalChangeHuyetAp = () => {
    refModalChangeHuyetAp &&
      refModalChangeHuyetAp.show(huyetAp, (data) => {
        onChangeInput("chiSoSong.huyetApTamThu", idx)(data.huyetApTamThu);
        onChangeInput("chiSoSong.huyetApTamTruong", idx)(data.huyetApTamTruong);
        setText(`${data.huyetApTamThu || ""}/${data.huyetApTamTruong || ""}`);
        formChangeValue();
      });
  };
  return (
    <div
      style={{ width: "100%", height: "100%", minHeight: 25 }}
      onClick={onShowModalChangeHuyetAp}
    >
      <div className="center">{text}</div>
    </div>
  );
};

export default memo(RenderTable);
