import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { DATA_TABLE } from "../configData";
import { cloneDeep, get, set } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { message } from "antd";
import PopoverSelect from "../../Components/PopoverSelect";
import ImageSign from "../../ImageSign";
import { combineFields } from "utils/editor-utils";
import CopyPasteCol from "../../Components/CopyPasteCol";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { refConfirm } from "app";
import RenderBmi from "./RenderBmi";

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  itemProps,
  form,
  onChangeForm,
  COL,
  dataCopy,
  setDataCopy,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refBmi = useRef();

  const refValue = useRef();
  useEffect(() => {
    refValue.current = item;
    setState({
      value: item,
    });
  }, [item]);

  const onChangeInput = useCallback(
    (key, idx) => (e) => {
      let value = e;
      set(refValue.current.dsChiTiet, `[${idx}].${key}`, value);
      if (["chiSoSong.canNang", "chiSoSong.chieuCao"].includes(key)) {
        const { canNang, chieuCao } = get(
          refValue.current,
          `dsChiTiet[${idx}].chiSoSong`,
          {}
        );
        if (canNang && chieuCao) {
          const bmi = canNang / (chieuCao * chieuCao);
          refValue.current["dsChiTiet"][idx]["chiSoSong"]["bmi"] =
            bmi.toFixed(2);
          refBmi.current.setValue(refValue.current);
        }
      }
      onChangeForm();
    },
    [refValueForm, form, Object.keys(formChange || {}).length]
  );

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);

    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dataCurrent = cloneDeep(refValue.current.dsChiTiet[idx]);
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);

    refValue.current.dsChiTiet[idx].stt = dataCurrent.stt;
    setState({
      value: cloneDeep(refValue.current),
    });
    onChangeForm();
  };

  const handleDelete = (idx) => () => {
    let isSign = (form.lichSuKy?.dsChuKy || []).some((el) => {
      return (
        el.viTri === refValue.current.dsChiTiet[idx].stt && el.chuKySo === 1
      );
    });
    if (isSign) {
      message.error("Vui lòng hủy ký trước khi xóa!");
      return;
    }

    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            onChangeForm();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const render = useMemo(() => {
    return (
      <>
        <table style={{ marginTop: 20 }}>
          {/* {renderHeader()} */}
          <tbody>
            {DATA_TABLE.map((item, index) => {
              const keySplit = (item?.key || "").split(".");
              let key1, key2;
              key1 = keySplit[0];
              if (keySplit.length > 1) {
                key2 = keySplit[1];
              }
              if (item.key === "chiSoSong.bmi") {
                return (
                  <tr>
                    <td colSpan={2}>BMI</td>
                    <RenderBmi
                      ref={refBmi}
                      arr={new Array(COL).fill({})}
                      value={state.value}
                    />
                  </tr>
                );
              } else {
                return (
                  <tr key={`${item.key}_${index}`}>
                    {item.label1 && (
                      <td
                        rowSpan={item.rowSpan || 1}
                        colSpan={item.colSpan1 || 1}
                        style={{
                          position: "relative",
                          textAlign: "left",
                        }}
                      >
                        <b>{item.label1}</b>
                      </td>
                    )}
                    {item.label2 && (
                      <td
                        colSpan={item.colSpan2 || 1}
                        style={{
                          position: "relative",
                          textAlign: "left",
                        }}
                      >
                        {item.label2}
                      </td>
                    )}
                    {item.hint && (
                      <td
                        colSpan={item.colSpan3 || 1}
                        style={{
                          position: "relative",
                          textAlign: "left",
                        }}
                      >
                        <div className="hint">
                          {item.hint.map((el) => (
                            <span key={el}>{el}</span>
                          ))}
                        </div>
                      </td>
                    )}

                    {new Array(COL).fill({}).map((el, idx) => {
                      return item.disable ? (
                        <td
                          className="col-element"
                          key={`${item.key}_${index}_${idx}`}
                        ></td>
                      ) : item.type === "time" ? (
                        <td
                          className="col-element"
                          key={`${item.key}_${index}_${idx}`}
                        >
                          <AppDatePicker
                            component={{
                              props: {
                                contentAlign: "center",
                                dateTimeFormat: "HH:mm D/M/Y",
                                fieldName: "value",
                                disableOnblur: true,
                              },
                            }}
                            form={{
                              value: get(
                                state.value?.dsChiTiet,
                                `[${idx}].thoiGianThucHien`
                              ),
                            }}
                            mode={mode}
                            formChange={{
                              value: (e) => {
                                try {
                                  onChangeInput(`thoiGianThucHien`, idx)(e);
                                } catch (error) {}
                              },
                            }}
                          />
                        </td>
                      ) : item.type === "droplist" ? (
                        <td className="col-element">
                          <PopoverSelect
                            data={Array.isArray(item.data) ? item.data : []}
                            value={get(
                              state.value?.dsChiTiet,
                              `[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                            )}
                            onChangeValue={(e) => {
                              onChangeInput(item.key, idx)(e);
                            }}
                            isMultiple={item.mode == "multiple"}
                            trigger="click"
                            labelByKey="value"
                          ></PopoverSelect>
                        </td>
                      ) : item.type == "sign" ? (
                        <td
                          className="col-element"
                          key={`${item.key}_${index}_${idx}`}
                        >
                          <ImageSign
                            component={{
                              props: {
                                ...itemProps,
                                isMultipleSign: true,
                                viTri: get(
                                  state.value?.dsChiTiet,
                                  `[${idx}].stt`
                                ),
                                customText: "Ký",
                                dataSign: {
                                  id: form.id,
                                  soPhieu: form?.lichSuKy?.soPhieu,
                                  lichSuKyId: form?.lichSuKy?.id,
                                },
                              },
                            }}
                            form={{
                              ...combineFields(form),
                            }}
                          />
                        </td>
                      ) : item.key == "chiSoSong.huyetAp" ? (
                        <td
                          className="col-element"
                          key={`${item.key}_${index}_${idx}`}
                        >
                          <div style={{ display: "flex" }}>
                            <DeboundInput
                              readOnly={false}
                              value={get(
                                state.value?.dsChiTiet,
                                `[${idx}].chiSoSong.huyetApTamThu`
                              )}
                              onChange={onChangeInput(
                                "chiSoSong.huyetApTamThu",
                                idx
                              )}
                              type="multipleline"
                              lineHeightText={1.5}
                              fontSize={9}
                              minHeight={9 + 6}
                              markSpanRow={false}
                              contentAlign="center"
                              styleMain={{ minWidth: 20 }}
                            ></DeboundInput>
                            <DeboundInput
                              label="/"
                              readOnly={false}
                              value={get(
                                state.value?.dsChiTiet,
                                `[${idx}].chiSoSong.huyetApTamTruong`
                              )}
                              onChange={onChangeInput(
                                "chiSoSong.huyetApTamTruong",
                                idx
                              )}
                              type="multipleline"
                              lineHeightText={1.5}
                              fontSize={9}
                              minHeight={9 + 6}
                              markSpanRow={false}
                              contentAlign="center"
                              styleMain={{ minWidth: 20 }}
                            ></DeboundInput>
                          </div>
                        </td>
                      ) : (
                        <td
                          className="col-element"
                          key={`${item.key}_${index}_${idx}`}
                        >
                          <DeboundInput
                            readOnly={false}
                            value={get(
                              state.value?.dsChiTiet,
                              `[${idx}]${key1}${key2 ? `[${key2}]` : ""}`
                            )}
                            onChange={onChangeInput(item.key, idx)}
                            type="multipleline"
                            lineHeightText={1.5}
                            fontSize={9}
                            minHeight={9 + 6}
                            markSpanRow={false}
                            contentAlign="center"
                            inputNumber={item.type === "number"}
                          ></DeboundInput>
                        </td>
                      );
                    })}
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      </>
    );
  }, [state.value, refValueForm, dataCopy]);
  return render;
};

export default RenderTable;
