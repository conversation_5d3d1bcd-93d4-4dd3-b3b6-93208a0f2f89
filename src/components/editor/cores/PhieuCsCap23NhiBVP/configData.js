export const DATA_TABLE = [
  {
    label1: "Thời gian thực hiện",
    key: "thời gian thực hiện",
    type: "time",
    colSpan1: 3,
  },
  {
    label1: "<PERSON><PERSON> cấp chăm sóc",
    key: "phanCapChamSoc",
    type: "droplist",
    hint: ["(1) III", "(2) IIB", "(3) IIA"],
    data: [
      {
        label: "III",
        value: 1,
      },
      {
        label: "IIB",
        value: 2,
      },
      {
        label: "IIA",
        value: 3,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chỉ số sinh tồn",
    rowSpan: 6,
    label2: "<PERSON>ạ<PERSON> (lần/phút)",
    key: "chiSoSong.mach",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Nhiệt độ (0C)",
    key: "chiSoSong.nhietDo",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "HA (mmHg)",
    key: "chiSoSong.huyetAp",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "SpO2",
    key: "chiSoSong.spo2",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "Nước tiểu (ml)",
    key: "chiSoSong.nuocTieu",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Sinh trắc",
    rowSpan: 2,
    label2: "Cân nặng (kg)",
    key: "sinhTrac.canNang",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Chiều cao (cm)",
    key: "sinhTrac.chieuCao",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Toàn trạng",
    rowSpan: 4,
    label2: "Tri giác",
    key: "toanTrang.triGiac",
    type: "droplist",
    hint: ["(1) Tỉnh", "(2) Quấy khóc"],
    data: [
      {
        label: "Tỉnh",
        value: 1,
      },
      {
        label: "Quấy khóc",
        value: 2,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Da, niêm mạc",
    key: "toanTrang.daNiemMac",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hồng",
      "(2) Vàng",
      "(3) Nhợt nhạt",
      "(4) Nổi mẩn đỏ",
      "(5) Xuất huyết",
    ],
    data: [
      {
        label: "Hồng",
        value: 1,
      },
      {
        label: "Vàng",
        value: 2,
      },
      {
        label: "Nhợt nhạt",
        value: 3,
      },
      {
        label: "Nổi mẩn đỏ",
        value: 4,
      },
      {
        label: "Xuất huyết",
        value: 5,
      },
    ],
  },
  {
    label2: "Phù",
    key: "toanTrang.phu",
    type: "droplist",
    hint: ["(1) Không", "(2) Có"],
    data: [
      {
        label: "Không",
        value: 1,
      },
      {
        label: "Có",
        value: 2,
      },
    ],
  },
  {
    label2: "Vị trí phù",
    key: "toanTrang.viTriPhu",
    type: "text",
    colSpan2: 2,
  },
  {
    label1: "Hô hấp",
    rowSpan: 4,
    label2: "Mức độ",
    key: "hoHap.mucDo",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Khó thở"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Khó thở",
        value: 2,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Phương pháp",
    key: "hoHap.phuongPhap",
    type: "droplist",
    hint: ["(1) Tự thở", "(2) Thở oxy"],
    data: [
      {
        label: "Tự thở",
        value: 1,
      },
      {
        label: "Thở oxy",
        value: 2,
      },
    ],
  },
  {
    label2: "Ho",
    key: "hoHap.ho",
    type: "droplist",
    hint: ["(1) Không ho", "(2) Ho khan", "(3) Ho đờm/máu"],
    data: [
      {
        label: "Không ho",
        value: 1,
      },
      {
        label: "Ho khan",
        value: 2,
      },
      {
        label: "Ho đờm/máu",
        value: 3,
      },
    ],
  },
  {
    label2: "Đờm",
    key: "hoHap.dom",
    type: "droplist",
    hint: ["(1) Đờm loãng", "(2) Đờm đặc", "(3) Đuôi khái huyết"],
    data: [
      {
        label: "Đờm loãng",
        value: 1,
      },
      {
        label: "Đờm đặc",
        value: 2,
      },
      {
        label: "Đuôi khái huyết",
        value: 3,
      },
    ],
  },
  {
    label1: "Tuần hoàn",
    rowSpan: 1,
    label2: "Tính chất mạch",
    key: "tuanHoan.tinhChatMach",
    type: "droplist",
    hint: ["(1) Đều, rõ", "(2) Không đều"],
    data: [
      {
        label: "Đều, rõ",
        value: 1,
      },
      {
        label: "Không đều",
        value: 2,
      },
    ],
    colSpan1: 1,
  },
  {
    label1: "Tiêu hóa",
    rowSpan: 2,
    label2: "Tình trạng",
    key: "tieuHoa.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bụng mềm", "(2) Bụng chướng", "(3) Buồn nôn/nôn", "(4) Ăn kém"],
    data: [
      {
        label: "Bụng mềm",
        value: 1,
      },
      {
        label: "Bụng chướng",
        value: 2,
      },
      {
        label: "Buồn nôn/nôn",
        value: 3,
      },
      {
        label: "Ăn kém",
        value: 4,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Đường nuôi ăn",
    key: "tieuHoa.duongNuoiAn",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Ăn qua miệng",
      "(2) Bú sữa mẹ",
      "(3) Ăn qua thông dạ dày",
      "(4) Tĩnh mạch",
    ],
    data: [
      {
        label: "Ăn qua miệng",
        value: 1,
      },
      {
        label: "Bú sữa mẹ",
        value: 2,
      },
      {
        label: "Ăn qua thông dạ dày",
        value: 3,
      },
      {
        label: "Tĩnh mạch",
        value: 4,
      },
    ],
  },
  {
    label1: "Theo dõi khác",
    rowSpan: 3,
    label2: "Vị trí đau",
    key: "theoDoiKhac.viTriDau",
    type: "text",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Điểm Wongbaker",
    key: "theoDoiKhac.diemWongbaker",
    type: "droplist",
    hint: [
      "(1) Không đau",
      "(2) Đau nhẹ",
      "(3) Đau vừa phải",
      "(4) Đau nhiều",
      "(5) Đau dữ dội",
      "(6) Đau khủng khiếp",
    ],
    data: [
      {
        label: "Không đau",
        value: 1,
      },
      {
        label: "Đau nhẹ",
        value: 2,
      },
      {
        label: "Đau vừa phải",
        value: 3,
      },
      {
        label: "Đau nhiều",
        value: 4,
      },
      {
        label: "Đau dữ dội",
        value: 5,
      },
      {
        label: "Đau khủng khiếp",
        value: 6,
      },
    ],
  },
  {
    label2: "Nguy cơ ngã (thang Humpty Dumpty/Morse)",
    key: "theoDoiKhac.nguyCoNga",
    type: "droplist",
    hint: ["(1) Nguy cơ thấp", "(2) Nguy cơ trung bình", "(3) Nguy cơ cao"],
    data: [
      {
        label: "Nguy cơ thấp",
        value: 1,
      },
      {
        label: "Nguy cơ trung bình",
        value: 2,
      },
      {
        label: "Nguy cơ cao",
        value: 3,
      },
    ],
  },
  {
    label1: "Xương khớp",
    rowSpan: 1,
    label2: "Vận động",
    key: "xuongKhop.vanDong",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Hạn chế"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Hạn chế",
        value: 2,
      },
    ],
    colSpan1: 1,
  },
  {
    label1: "Vết mổ/vết thương",
    rowSpan: 1,
    label2: "Tình trạng",
    key: "vetMo.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bình thường", "(2) Dịch thấm băng", "(3) Sưng nề"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Dịch thấm băng",
        value: 2,
      },
      {
        label: "Sưng nề",
        value: 3,
      },
    ],
    colSpan1: 1,
  },
  {
    label1: "Dẫn lưu",
    rowSpan: 4,
    label2: "Vị trí",
    key: "danLuu.viTri",
    type: "text",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Tình trạng",
    key: "danLuu.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) DL thông", "(2) Sủi khí", "(3) DL tắc"],
    data: [
      {
        label: "DL thông",
        value: 1,
      },
      {
        label: "Sủi khí",
        value: 2,
      },
      {
        label: "DL tắc",
        value: 3,
      },
    ],
  },
  {
    label2: "Màu sắc dịch",
    key: "danLuu.mauSacDich",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Chân dẫn lưu",
    key: "danLuu.chanDanLuu",
    type: "droplist",
    hint: ["(1) Khô, sạch", "(2) Sưng nề", "(3) Dịch thấm băng"],
    data: [
      {
        label: "Khô, sạch",
        value: 1,
      },
      {
        label: "Sưng nề",
        value: 2,
      },
      {
        label: "Dịch thấm băng",
        value: 3,
      },
    ],
  },
  {
    label1: "Nhận định khác",
    key: "nhanDinhKhac",
    type: "text",
    colSpan1: 3,
  },
  {
    label1: "CAN THIỆP ĐIỀU DƯỠNG",
    key: "_section_canThiep",
    type: "text",
    disable: true,
    colSpan1: 3,
  },
  {
    label1: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Thở oxy gọng kính",
      "(2) Thở mask",
      "(3) Thực hiện y lệnh thuốc",
      "(4) Truyền dịch",
      "(5) Truyền máu",
      "(6) Vỗ rung lồng ngực",
      "(7) XN/CĐHA/PHCN",
      "(8) Hạn chế vận động",
      "(9) Hướng dẫn NB ho hiệu quả",
      "(10) Hướng dẫn NB uống nhiều nước",
    ],
    data: [
      {
        label: "Thở oxy gọng kính",
        value: 1,
      },
      {
        label: "Thở mask",
        value: 2,
      },
      {
        label: "Thực hiện y lệnh thuốc",
        value: 3,
      },
      {
        label: "Truyền dịch",
        value: 4,
      },
      {
        label: "Truyền máu",
        value: 5,
      },
      {
        label: "Vỗ rung lồng ngực",
        value: 6,
      },
      {
        label: "XN/CĐHA/PHCN",
        value: 7,
      },
      {
        label: "Hạn chế vận động",
        value: 8,
      },
      {
        label: "Hướng dẫn NB ho hiệu quả",
        value: 9,
      },
      {
        label: "Hướng dẫn NB uống nhiều nước",
        value: 10,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Thực hiện kỹ thuật/ thủ thuật",
    key: "kyThuatThuThuat",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hút đờm",
      "(2) Thay băng",
      "(3) Khí dung",
      "(4) Đặt thông dạ dày",
      "(5) Lấy dịch dạ dày",
      "(6) Đặt thông tiểu",
      "(7) Chọc dịch MP",
      "(8) Chọc dịch não tủy",
      "(9) Đặt NKQ",
      "(10) Đặt Catheter",
      "(11) Mở màng phổi",
      "(12) Chọc hút khí MP",
      "(13) Rút dẫn lưu",
    ],
    data: [
      {
        label: "Hút đờm",
        value: 1,
      },
      {
        label: "Thay băng",
        value: 2,
      },
      {
        label: "Khí dung",
        value: 3,
      },
      {
        label: "Đặt thông dạ dày",
        value: 4,
      },
      {
        label: "Lấy dịch dạ dày",
        value: 5,
      },
      {
        label: "Đặt thông tiểu",
        value: 6,
      },
      {
        label: "Chọc dịch MP",
        value: 7,
      },
      {
        label: "Chọc dịch não tủy",
        value: 8,
      },
      {
        label: "Đặt NKQ",
        value: 9,
      },
      {
        label: "Đặt Catheter",
        value: 10,
      },
      {
        label: "Mở màng phổi",
        value: 11,
      },
      {
        label: "Chọc hút khí MP",
        value: 12,
      },
      {
        label: "Rút dẫn lưu",
        value: 13,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Tư thế",
    key: "tuThe",
    type: "droplist",
    hint: [
      "(1) Thích hợp",
      "(2) Fowler",
      "(3) Đầu thấp",
      "(4) Nghiêng phải",
      "(5) Nghiêng trái",
    ],
    data: [
      {
        label: "Thích hợp",
        value: 1,
      },
      {
        label: "Fowler",
        value: 2,
      },
      {
        label: "Đầu thấp",
        value: 3,
      },
      {
        label: "Nghiêng phải",
        value: 4,
      },
      {
        label: "Nghiêng trái",
        value: 5,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chăm sóc đau",
    key: "chamSocDau",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) HD/Chườm ấm vùng đau", "(2) HD/Xoa vùng đau"],
    data: [
      {
        label: "HD/Chườm ấm vùng đau",
        value: 1,
      },
      {
        label: "HD/Xoa vùng đau",
        value: 2,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chăm sóc tâm lý",
    key: "chamSocTamLy",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Động viên tinh thần", "(2) Giải đáp thắc mắc"],
    data: [
      {
        label: "Động viên tinh thần",
        value: 1,
      },
      {
        label: "Giải đáp thắc mắc",
        value: 2,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Dinh dưỡng",
    key: "dinhDuong",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Cơm", "(2) Cháo", "(3) Soup"],
    data: [
      {
        label: "Cơm",
        value: 1,
      },
      {
        label: "Cháo",
        value: 2,
      },
      {
        label: "Soup",
        value: 3,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "GDSK",
    key: "gdsk",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bệnh", "(2) Dinh dưỡng", "(3) Vệ sinh", "(4) Vận động"],
    data: [
      {
        label: "Bệnh",
        value: 1,
      },
      {
        label: "Dinh dưỡng",
        value: 2,
      },
      {
        label: "Vệ sinh",
        value: 3,
      },
      {
        label: "Vận động",
        value: 4,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Tình trạng NB",
    key: "tinhTrangNb",
    type: "droplist",
    hint: [
      "(1) Ổn định ra viện",
      "(2) Chuyển khoa",
      "(3) Chuyển viện",
      "(4) Xin về",
      "(5) Tử vong",
    ],
    data: [
      {
        label: "Ổn định ra viện",
        value: 1,
      },
      {
        label: "Chuyển khoa",
        value: 2,
      },
      {
        label: "Chuyển viện",
        value: 3,
      },
      {
        label: "Xin về",
        value: 4,
      },
      {
        label: "Tử vong",
        value: 5,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chăm sóc khác",
    key: "chamSocKhac",
    type: "text",
    colSpan1: 3,
  },
  {
    label1: "ĐIỀU DƯỠNG THỰC HIỆN",
    key: "ddThucHien",
    type: "sign",
    colSpan1: 3,
  },
];
