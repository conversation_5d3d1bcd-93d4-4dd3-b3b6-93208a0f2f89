import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { MODE } from "utils/editor-utils";
import RenderTable from "./components/RenderTable";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { Button, message, Pagination } from "antd";
import { cloneDeep, get } from "lodash";
import { refConfirm } from "app";
import { SVG } from "assets";

// import ModalSelectSinhHieu from "../Components/ModalSelectSinhHieu";
const COL = 6;

const PhieuTheoDoiTNT = (props) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsChamSoc: [{}],
    currentPage: 1, // Thêm state cho trang hiện tại
  });
  const [dataCopy, setDataCopy] = useState();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refValueForm = useRef();

  const { component, mode, form = {}, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    const getData = () => {
      refValueForm.current = cloneDeep(
        form?.duLieu1?.dsChamSoc || [
          {
            dsChiTiet: new Array(COL).fill(cloneDeep({})).map((el, index) => ({
              stt: index + 1,
            })),
            tableIndex: 0,
            dsChiSoSong: [],
          },
        ]
      );
      setState({
        dsChamSoc: refValueForm.current,
      });
    };
    getData();
    if (formChange) {
      formChange["duLieu1_dsChamSoc"] &&
        formChange["duLieu1_dsChamSoc"](refValueForm.current);
    }
  }, [JSON.stringify(form || {}), itemProps, formChange]);

  const onAdd = () => {
    const maxTableIndex = Math.max(
      ...refValueForm.current.map((el) => el.tableIndex)
    );

    const newTableIndex = maxTableIndex + 1;
    refValueForm.current = [
      ...refValueForm.current,
      {
        dsChiTiet: new Array(COL)
          .fill({})
          .map((_, idx) => ({ stt: newTableIndex * COL + idx + 1 })),
        stt: newTableIndex,
      },
    ];
    const dsChamSoc = refValueForm.current;
    setState({
      dsChamSoc,
    });
    formChange["duLieu1_dsChamSoc"](refValueForm.current);
  };
  const checkSign = (position) => {
    const dsChiTiet = refValueForm.current.find(
      (el) => el.stt == position
    )?.dsChiTiet;
    const dsChuKy = get(form, "lichSuKy.dsChuKy");
    return (dsChiTiet || []).every((el) => {
      return dsChuKy.some((i) => i.viTri === el.stt);
    });
  };
  const handleRemove = (index) => () => {
    if (checkSign(index)) {
      message.error("Bảng đã được ký xin vui lòng hủy ký trước khi xóa bảng!");
      return;
    }
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refValueForm.current = refValueForm.current.filter(
            (item, idx) => item.tableIndex !== index
          );
          const dsChamSoc = refValueForm.current.slice(0, state.size);
          setState({
            dsChamSoc,
          });
          formChange["duLieu1_dsChamSoc"](refValueForm.current);
        }
      );
  };

  const onChangeForm = () => {
    formChange["duLieu1_dsChamSoc"] &&
      formChange["duLieu1_dsChamSoc"](refValueForm.current);
  };

  return (
    <Main
      className="phieu-theo-doi-va-cham-soc-nb"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-theo-doi-va-cham-soc-nb"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {(state.dsChamSoc || []).map((item, index) => (
        <RenderTable
          key={item.tableIndex}
          mode={mode}
          tableIndex={item.tableIndex}
          refValueForm={refValueForm.current}
          item={item}
          formChange={formChange}
          onRemove={handleRemove}
          itemProps={itemProps}
          form={form}
          onChangeForm={onChangeForm}
          COL={COL}
          dataCopy={dataCopy}
          setDataCopy={setDataCopy}
        ></RenderTable>
      ))}

      <SVG.IcAdd className="icon-add hide-print-btn" onClick={onAdd} />
    </Main>
  );
};

PhieuTheoDoiTNT.propTypes = {};

export default PhieuTheoDoiTNT;
