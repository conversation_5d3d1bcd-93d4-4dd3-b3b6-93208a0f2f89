export const DATA_TABLE = [
  {
    label1: "Thời gian",
    key: "thoiGianthuc<PERSON>ien",
    type: "time",
    colSpan1: 3,
  },
  {
    label1: "<PERSON><PERSON> cấp chăm sóc",
    key: "phanCapChamSoc",
    type: "droplist",
    hint: ["(1) IB", "(2) IA"],
    data: [
      {
        label: "IB",
        value: 1,
      },
      {
        label: "IA",
        value: 2,
      },
    ],
    colSpan1: 2,
  },
  {
    label1: "Chỉ số sinh tồn",
    rowSpan: 6,
    label2: "<PERSON><PERSON><PERSON> (lần/phút)",
    key: "chiSoSong.mach",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "HA (mmHg)",
    key: "chiSoSong.huyetAp",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "SpO₂ (%)",
    key: "chiSoSong.spo2",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "CVP (cmH₂O)",
    key: "cvp",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Sinh trắc",
    rowSpan: 3,
    label2: "Cân nặng (kg)",
    key: "chiSoSong.canNang",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Chiều cao (m)",
    key: "chiSoSong.chieuCao",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "BMI",
    key: "chiSoSong.bmi",
    type: "calc",
    colSpan2: 2,
  },
  {
    label1: "Toàn trạng",
    rowSpan: 4,
    label2: "Trí giác",
    key: "triGiac",
    type: "droplist",
    hint: [
      "(1) Tỉnh",
      "(2) Lơ mơ",
      "(3) Vật vã, kích thích",
      "(4) An thần",
      "(5) Hôn mê",
    ],
    data: [
      {
        label: "Tỉnh",
        value: 1,
      },
      {
        label: "Lơ mơ",
        value: 2,
      },
      {
        label: "Vật vã, kích thích",
        value: 3,
      },
      {
        label: "An thần",
        value: 4,
      },
      {
        label: "Hôn mê",
        value: 5,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Da, niêm mạc",
    key: "daNiemMac",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hồng",
      "(2) Vàng",
      "(3) Nhợt nhạt",
      "(4) Nổi mẩn đỏ",
      "(5) Xuất huyết, tụ máu",
      "(6) Tím tái",
    ],
    data: [
      {
        label: "Hồng",
        value: 1,
      },
      {
        label: "Vàng",
        value: 2,
      },
      {
        label: "Nhợt nhạt",
        value: 3,
      },
      {
        label: "Nổi mẩn đỏ",
        value: 4,
      },
      {
        label: "Xuất huyết, tụ máu",
        value: 5,
      },
      {
        label: "Tím tái",
        value: 6,
      },
    ],
  },
  {
    label2: "Phù",
    key: "phu",
    type: "droplist",
    hint: ["(1) Không", "(2) Có"],
    data: [
      {
        label: "Không",
        value: 1,
      },
      {
        label: "Có",
        value: 2,
      },
    ],
  },
  {
    label2: "Vị trí phù",
    key: "viTriPhu",
    type: "text",
    colSpan2: 2,
  },
  {
    label1: "Hô hấp",
    rowSpan: 6,
    label2: "Mức độ",
    key: "mucDoHoHap",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Bình thường",
      "(2) Khó thở",
      "(3) Co kéo cơ hô hấp",
      "(4) Suy hô hấp",
      "(5) Ngừng thở",
    ],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Khó thở",
        value: 2,
      },
      {
        label: "Co kéo cơ hô hấp",
        value: 3,
      },
      {
        label: "Suy hô hấp",
        value: 4,
      },
      {
        label: "Ngừng thở",
        value: 5,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Phương pháp thở",
    key: "ppTho",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Tự thở",
      "(2) Gọng kính",
      "(3) Thở mask",
      "(4) HFNC",
      "(5) T-Tube",
      "(6) Thở máy không xâm nhập",
      "(7) Thở máy xâm nhập",
    ],
    data: [
      {
        label: "Tự thở",
        value: 1,
      },
      {
        label: "Gọng kính",
        value: 2,
      },
      {
        label: "Thở mask",
        value: 3,
      },
      {
        label: "HFNC",
        value: 4,
      },
      {
        label: "T-Tube",
        value: 5,
      },
      {
        label: "Thở máy không xâm nhập",
        value: 6,
      },
      {
        label: "Thở máy xâm nhập",
        value: 7,
      },
    ],
  },
  {
    label2: "Ho",
    key: "ho",
    type: "droplist",
    hint: ["(1) Không ho", "(2) Ho khan", "(3) Ho đờm/máu"],
    data: [
      {
        label: "Không ho",
        value: 1,
      },
      {
        label: "Ho khan",
        value: 2,
      },
      {
        label: "Ho đờm/máu",
        value: 3,
      },
    ],
  },
  {
    label2: "Đờm",
    key: "dom",
    type: "droplist",
    hint: ["(1) Đờm loãng", "(2) Đờm đặc", "(3) Đờm lẫn máu"],
    data: [
      {
        label: "Đờm loãng",
        value: 1,
      },
      {
        label: "Đờm đặc",
        value: 2,
      },
      {
        label: "Đờm lẫn máu",
        value: 3,
      },
    ],
  },
  {
    label2: "Ho ra máu",
    key: "hoRaMau",
    type: "droplist",
    hint: ["(1) Đuôi khái huyết", "(2) Đỏ thẫm", "(3) Đỏ tươi"],
    data: [
      {
        label: "Đuôi khái huyết",
        value: 1,
      },
      {
        label: "Đỏ thẫm",
        value: 2,
      },
      {
        label: "Đỏ tươi",
        value: 3,
      },
    ],
  },
  {
    label2: "Số lượng máu ho ra (ml)",
    key: "luongMauHoRa",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Tuần hoàn",
    rowSpan: 1,
    label2: "Tính chất mạch",
    key: "tinhChatMach",
    type: "droplist",
    hint: ["(1) Đều, rõ", "(2) Không đều", "(3) Khó bắt", "(4) Không bắt được"],
    data: [
      {
        label: "Đều, rõ",
        value: 1,
      },
      {
        label: "Không đều",
        value: 2,
      },
      {
        label: "Khó bắt",
        value: 3,
      },
      {
        label: "Không bắt được",
        value: 4,
      },
    ],
    colSpan1: 1,
  },
  {
    label1: "Tiêu hóa",
    rowSpan: 2,
    label2: "Tình trạng",
    key: "tieuHoaTinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Bụng mềm",
      "(2) Bụng chướng",
      "(3) Buồn nôn/nôn",
      "(4) Xuất huyết tiêu hóa",
      "(5) Ăn kém",
    ],
    data: [
      {
        label: "Bụng mềm",
        value: 1,
      },
      {
        label: "Bụng chướng",
        value: 2,
      },
      {
        label: "Buồn nôn/nôn",
        value: 3,
      },
      {
        label: "Xuất huyết tiêu hóa",
        value: 4,
      },
      {
        label: "Ăn kém",
        value: 5,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Đường nuôi ăn",
    key: "duongNuoiAn",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Ăn qua miệng",
      "(2) Ăn qua thông dạ dày",
      "(3) Mở thông dạ dày",
      "(4) Tĩnh mạch",
    ],
    data: [
      {
        label: "Ăn qua miệng",
        value: 1,
      },
      {
        label: "Ăn qua thông dạ dày",
        value: 2,
      },
      {
        label: "Mở thông dạ dày",
        value: 3,
      },
      {
        label: "Tĩnh mạch",
        value: 4,
      },
    ],
  },
  {
    label1: "Theo dõi khác",
    rowSpan: 7,
    label2: "Vị trí đau",
    key: "viTriDau",
    type: "text",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Điểm VAS",
    key: "diemVAS",
    type: "droplist",
    hint: [
      "(1) 1",
      "(2) 2",
      "(3) 3",
      "(4) 4",
      "(5) 5",
      "(6) 6",
      "(7) 7",
      "(8) 8",
      "(9) 9",
      "(10) 10",
    ],
    data: [
      {
        label: "1",
        value: 1,
      },
      {
        label: "2",
        value: 2,
      },
      {
        label: "3",
        value: 3,
      },
      {
        label: "4",
        value: 4,
      },
      {
        label: "5",
        value: 5,
      },
      {
        label: "6",
        value: 6,
      },
      {
        label: "7",
        value: 7,
      },
      {
        label: "8",
        value: 8,
      },
      {
        label: "9",
        value: 9,
      },
      {
        label: "10",
        value: 10,
      },
    ],
  },
  {
    label2: "Vị trí loét",
    key: "viTriLoet",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Đầu",
      "(2) Vai",
      "(3) Chi trên",
      "(4) Chi dưới",
      "(5) Cùng cụt/ gai chậu",
      "(6) Khác",
    ],
    data: [
      {
        label: "Đầu",
        value: 1,
      },
      {
        label: "Vai",
        value: 2,
      },
      {
        label: "Chi trên",
        value: 3,
      },
      {
        label: "Chi dưới",
        value: 4,
      },
      {
        label: "Cùng cụt/ gai chậu",
        value: 5,
      },
      {
        label: "Khác",
        value: 6,
      },
    ],
  },
  {
    label2: "Mức độ loét (thang Braden)",
    key: "mucDoLoetBraden",
    type: "droplist",
    hint: ["(1) Độ 1", "(2) Độ 2", "(3) Độ 3", "(4) Độ 4"],
    data: [
      {
        label: "Độ 1",
        value: 1,
      },
      {
        label: "Độ 2",
        value: 2,
      },
      {
        label: "Độ 3",
        value: 3,
      },
      {
        label: "Độ 4",
        value: 4,
      },
    ],
  },
  {
    rowSpan: 1,
    label2: "Độ viêm tĩnh mạch (VIP)",
    key: "vip",
    type: "droplist",
    hint: ["(1) Độ 1", "(2) Độ 2", "(3) Độ 3", "(4) Độ 4", "(5) Độ 5"],
    data: [
      {
        label: "Độ 1",
        value: 1,
      },
      {
        label: "Độ 2",
        value: 2,
      },
      {
        label: "Độ 3",
        value: 3,
      },
      {
        label: "Độ 4",
        value: 4,
      },
      {
        label: "Độ 5",
        value: 5,
      },
    ],
  },
  {
    label2: "Nguy cơ ngã (thang Morse)",
    key: "nguyCoNgaMorse",
    type: "droplist",
    hint: ["(1) Nguy cơ thấp", "(2) Nguy cơ trung bình", "(3) Nguy cơ cao"],
    data: [
      {
        label: "Nguy cơ thấp",
        value: 1,
      },
      {
        label: "Nguy cơ trung bình",
        value: 2,
      },
      {
        label: "Nguy cơ cao",
        value: 3,
      },
    ],
  },
  {
    label2: "Tràn khí dưới da",
    key: "tranKhiDuoiDa",
    type: "droplist",
    hint: ["(1) Không", "(2) Có"],
    data: [
      {
        label: "Không",
        value: 1,
      },
      {
        label: "Có",
        value: 2,
      },
    ],
  },
  {
    label1: "Xương khớp",
    rowSpan: 3,
    label2: "Tình trạng",
    key: "xuongKhopTinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Bình thường",
      "(2) Viêm khớp",
      "(3) Cứng khớp",
      "(4) Biến dạng",
    ],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Viêm khớp",
        value: 2,
      },
      {
        label: "Cứng khớp",
        value: 3,
      },
      {
        label: "Biến dạng",
        value: 4,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Vị trí",
    key: "xuongKhopViTri",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Vận động",
    key: "xuongKhopVanDong",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Hạn chế", "(3) Bất động"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Hạn chế",
        value: 2,
      },
      {
        label: "Bất động",
        value: 3,
      },
    ],
  },
  {
    label1: "Tiết niệu",
    rowSpan: 2,
    label2: "Màu sắc nước tiểu",
    key: "mauSacNuocTieu",
    type: "droplist",
    hint: ["(1) Vàng trong", "(2) Vàng sẫm, đục", "(3) Đỏ"],
    data: [
      {
        label: "Vàng trong",
        value: 1,
      },
      {
        label: "Vàng sẫm, đục",
        value: 2,
      },
      {
        label: "Đỏ",
        value: 3,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Tình trạng",
    key: "tietNieuTinhTrang",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Tiểu qua thông", "(3) Bí tiểu/tiểu khó"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Tiểu qua thông",
        value: 2,
      },
      {
        label: "Bí tiểu/tiểu khó",
        value: 3,
      },
    ],
  },
  {
    label1: "Vết mổ/vết thương",
    rowSpan: 1,
    label2: "Tình trạng",
    key: "vetMoTinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bình thường", "(2) Dịch thấm băng", "(3) Sưng nề"],
    data: [
      {
        label: "Bình thường",
        value: 1,
      },
      {
        label: "Dịch thấm băng",
        value: 2,
      },
      {
        label: "Sưng nề",
        value: 3,
      },
    ],
    colSpan1: 1,
  },
  {
    label1: "Dẫn lưu",
    rowSpan: 4,
    label2: "Vị trí",
    key: "danLuuViTri",
    type: "text",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Tình trạng",
    key: "danLuuTinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) DL thông", "(2) Sủi khí", "(3) DL tắc"],
    data: [
      {
        label: "DL thông",
        value: 1,
      },
      {
        label: "Sủi khí",
        value: 2,
      },
      {
        label: "DL tắc",
        value: 3,
      },
    ],
  },
  {
    label2: "Màu sắc dịch",
    key: "danLuuMauSacDich",
    type: "droplist",
    hint: [
      "(1) Hồng nhạt",
      "(2) Trắng sữa",
      "(3) Vàng chanh",
      "(4) Đỏ",
      "(5) Mủ trắng/xanh",
    ],
    data: [
      {
        label: "Hồng nhạt",
        value: 1,
      },
      {
        label: "Trắng sữa",
        value: 2,
      },
      {
        label: "Vàng chanh",
        value: 3,
      },
      {
        label: "Đỏ",
        value: 4,
      },
      {
        label: "Mủ trắng/xanh",
        value: 5,
      },
    ],
  },
  {
    label2: "Chân dẫn lưu",
    key: "danLuuChanDanLuu",
    type: "droplist",
    hint: ["(1) Khô sạch", "(2) Sưng nề", "(3) Dịch thấm băng"],
    data: [
      {
        label: "Khô sạch",
        value: 1,
      },
      {
        label: "Sưng nề",
        value: 2,
      },
      {
        label: "Dịch thấm băng",
        value: 3,
      },
    ],
  },
  {
    label1: "Dịch vào (ml)",
    rowSpan: 3,
    label2: "Thuốc truyền",
    key: "vaoThuocTruyen",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Ăn",
    key: "vaoAn",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "Khác",
    key: "vaoKhac",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Tổng dịch vào",
    key: "tongVao",
    type: "calc",
    colSpan1: 3,
  },
  {
    label1: "Dịch ra (ml)",
    rowSpan: 3,
    label2: "Nước tiểu",
    key: "raNuocTieu",
    type: "number",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label2: "Sonde dẫn lưu",
    key: "raSondeDanLuu",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "Khác",
    key: "raKhac",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "Tổng dịch ra",
    key: "tongRa",
    type: "calc",
    colSpan1: 3,
  },
  {
    label1: "Bilan",
    rowSpan: 1,
    label2: "Tổng dịch vào – Tổng dịch ra",
    key: "bilan",
    type: "calc",
    colSpan1: 1,
    colSpan2: 2,
  },
  {
    label1: "Nhận định khác",
    rowSpan: 1,
    key: "nhanDinhKhac",
    type: "text",
    colSpan1: 3,
  },
  {
    label1: "CAN THIỆP ĐIỀU DƯỠNG",
    rowSpan: 1,
    key: "canThiepDieuDuong",
    type: "text",
    disable: true,
    colSpan1: 3,
  },
  {
    label1: "Thực hiện y lệnh",
    rowSpan: 1,
    key: "thucHienYLenh",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Thở oxy gọng kính",
      "(2) Thở mask",
      "(3) HFNC",
      "(4) Thở máy không xâm nhập",
      "(5) Thở máy xâm nhập",
      "(6) Thực hiện y lệnh thuốc",
      "(7) Truyền dịch",
      "(8) Truyền máu",
      "(9) Chăm sóc canuyn NKQ/MKQ",
      "(10) Hướng dẫn NB tập thở",
      "(11) Vỗ rung lồng ngực",
      "(12) XN/CĐHA/PHCN",
      "(13) Hạn chế vận động",
      "(14) HD NB ho hiệu quả",
      "(15) HD NB uống nhiều nước",
      "(16) Vệ sinh răng miệng",
    ],
    data: [
      { label: "Thở oxy gọng kính", value: 1 },
      { label: "Thở mask", value: 2 },
      { label: "HFNC", value: 3 },
      { label: "Thở máy không xâm nhập", value: 4 },
      { label: "Thở máy xâm nhập", value: 5 },
      { label: "Thực hiện y lệnh thuốc", value: 6 },
      { label: "Truyền dịch", value: 7 },
      { label: "Truyền máu", value: 8 },
      { label: "Chăm sóc canuyn NKQ/MKQ", value: 9 },
      { label: "Hướng dẫn NB tập thở", value: 10 },
      { label: "Vỗ rung lồng ngực", value: 11 },
      { label: "XN/CĐHA/PHCN", value: 12 },
      { label: "Hạn chế vận động", value: 13 },
      { label: "HD NB ho hiệu quả", value: 14 },
      { label: "HD NB uống nhiều nước", value: 15 },
      { label: "Vệ sinh răng miệng", value: 16 },
    ],
    colSpan3: 2,
  },
  {
    label1: "Thực hiện kỹ thuật/thủ thuật",
    rowSpan: 1,
    key: "kyThuatThuThuat",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hút đờm",
      "(2) Thay băng",
      "(3) Khí dung",
      "(4) Đặt thông dạ dày",
      "(5) Đặt thông tiểu",
      "(6) Chọc dịch MP",
      "(7) Chọc dịch não tủy",
      "(8) Đặt NKQ",
      "(9) Mở khí quản",
      "(10) Đặt Catheter",
      "(11) Mở màng phổi",
      "(12) Giảm đau NMC",
      "(13) Chọc hút khí MP",
      "(14) Rút dẫn lưu",
    ],
    data: [
      { label: "Hút đờm", value: 1 },
      { label: "Thay băng", value: 2 },
      { label: "Khí dung", value: 3 },
      { label: "Đặt thông dạ dày", value: 4 },
      { label: "Đặt thông tiểu", value: 5 },
      { label: "Chọc dịch MP", value: 6 },
      { label: "Chọc dịch não tủy", value: 7 },
      { label: "Đặt NKQ", value: 8 },
      { label: "Mở khí quản", value: 9 },
      { label: "Đặt Catheter", value: 10 },
      { label: "Mở màng phổi", value: 11 },
      { label: "Giảm đau NMC", value: 12 },
      { label: "Chọc hút khí MP", value: 13 },
      { label: "Rút dẫn lưu", value: 14 },
    ],
    colSpan3: 2,
  },
  {
    label1: "Tư thế",
    rowSpan: 1,
    key: "tuThe",
    type: "droplist",
    hint: [
      "(1) Thích hợp",
      "(2) Fowler",
      "(3) Đầu thấp",
      "(4) Nghiêng phải",
      "(5) Nghiêng trái",
    ],
    data: [
      {
        label: "Thích hợp",
        value: 1,
      },
      {
        label: "Fowler",
        value: 2,
      },
      {
        label: "Đầu thấp",
        value: 3,
      },
      {
        label: "Nghiêng phải",
        value: 4,
      },
      {
        label: "Nghiêng trái",
        value: 5,
      },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chăm sóc đau",
    rowSpan: 1,
    key: "chamSocDau",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) HD/ Chườm ấm vùng đau", "(2) HD/ Xoa vùng đau"],
    data: [
      { label: "HD/ Chườm ấm vùng đau", value: 1 },
      { label: "HD/ Xoa vùng đau", value: 2 },
    ],
    colSpan3: 2,
  },
  {
    label1: "Chăm sóc tâm lý",
    rowSpan: 1,
    key: "chamSocTamLy",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Động viên tinh thần", "(2) Giải đáp thắc mắc"],
    data: [
      { label: "Động viên tinh thần", value: 1 },
      { label: "Giải đáp thắc mắc", value: 2 },
    ],
    colSpan3: 2,
  },
  {
    label1: "Dinh dưỡng",
    rowSpan: 2,
    label2: "Hình thức",
    key: "dinhDuongHinhThuc",
    type: "droplist",
    hint: [
      "(1) Bơm ăn qua thông dạ dày",
      "(2) Truyền nhỏ giọt qua thông dạ dày",
    ],
    data: [
      {
        label: "Bơm ăn qua thông dạ dày",
        value: 1,
      },
      {
        label: "Truyền nhỏ giọt qua thông dạ dày",
        value: 2,
      },
    ],
    colSpan1: 1,
  },
  {
    label2: "Số lượng (ml)",
    key: "dinhDuongSoLuong",
    type: "number",
    colSpan2: 2,
  },
  {
    label1: "GDSK",
    rowSpan: 1,
    key: "gdsk",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bệnh", "(2) Dinh dưỡng", "(3) Vệ sinh", "(4) Vận động"],
    data: [
      {
        label: "Bệnh",
        value: 1,
      },
      {
        label: "Dinh dưỡng",
        value: 2,
      },
      {
        label: "Vệ sinh",
        value: 3,
      },
      {
        label: "Vận động",
        value: 4,
      },
    ],
    colSpan3: 2,
  },

  {
    label1: "Tình trạng NB",
    rowSpan: 1,
    key: "tinhTrangNb",
    type: "droplist",
    hint: [
      "(1) Ổn định ra viện",
      "(2) Chuyển khoa",
      "(3) Chuyển viện",
      "(4) Xin về",
      "(5) Tử vong",
    ],
    data: [
      {
        label: "Ổn định ra viện",
        value: 1,
      },
      {
        label: "Chuyển khoa",
        value: 2,
      },
      {
        label: "Chuyển viện",
        value: 3,
      },
      {
        label: "Xin về",
        value: 4,
      },
      {
        label: "Tử vong",
        value: 5,
      },
    ],
    colSpan3: 2,
  },
  { label1: "Chăm sóc khác", key: "chamSocKhac", type: "text", colSpan1: 3 },
  {
    label1: "Điều dưỡng thực hiện",
    rowSpan: 1,
    key: "ddThucHien",
    type: "sign",
    colSpan1: 3,
  },
];
