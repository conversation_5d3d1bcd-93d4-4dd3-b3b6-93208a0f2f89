import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useState,
} from "react";
import { cloneDeep, get } from "lodash";

const RenderTongNhapXuat = (
  { value, keyItem, arr = [{}, {}, {}, {}, {}, {}] },
  ref
) => {
  const [data, setData] = useState({});
  useImperativeHandle(
    ref,
    () => ({
      setValue: (item) => {
        setData(cloneDeep(item));
      },
    }),
    [keyItem]
  );

  useEffect(() => {
    setData(value);
  }, [value]);

  return arr.map((el, idx) => {
    const value = get(data, `dsChiTiet[${idx}].${keyItem}`, "");
    return (
      <td key={idx} className="center">
        {value}
      </td>
    );
  });
};
export default forwardRef(RenderTongNhapXuat);
