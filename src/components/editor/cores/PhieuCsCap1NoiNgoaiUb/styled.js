import styled, { createGlobalStyle } from "styled-components";
export const GlobalStyle = createGlobalStyle``;
export const Main = styled.div`
  .form {
    @media screen {
      margin-top: 20px;
    }
  }
  table {
    width: 100%;
    margin-top: 50px;
    & .col-header-name,
    & .col-header-stt {
      font-weight: bold;
    }
    .col-element {
      text-align: center;
      vertical-align: middle !important;
      width: 80px;
    }
    & .col-header-stt {
      width: 60px;
      text-align: center;
      vertical-align: middle !important;
      position: relative;
    }
    & .col-header-name {
      padding: 0 4px;
      position: relative;
      & svg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
    }
    td {
      .hint {
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 3 cột đều nhau */
        gap: 4px; /* khoảng cách giữa các cột */
      }

      .hint span {
        display: block;
      }
    }
  }
  table,
  td,
  th {
    border: 1px solid;
    border-collapse: collapse;
    .icon-add {
      position: absolute;
      right: 0;
    }
  }
  & table {
    page-break-inside: auto;
  }
  & tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  .bold {
    font-weight: 700;
  }
  .flex {
    display: flex;
  }
  .box {
    width: 16px;
    height: 16px;
    border: 1px solid #000;
    margin-right: 5px;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .al-center {
    align-items: center;
  }
  .title-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    align-items: center;
  }
  .icon-add {
    size: 20px;
    width: 20px;
    height: 20px;
    @media print {
      display: none;
    }
  }
  .ic-remove {
    position: absolute;
    left: 0px;
    top: 0px;
    :hover {
      cursor: pointer;
    }
    @media print {
      display: none;
    }
  }
  .icon-add-item {
    position: absolute;
    right: 0;
    top: 0;
  }
  .icon-remove-item {
    position: absolute;
    right: 0px;
    top: 5px;
  }
  .icon-remove-item-2 {
    position: absolute;
    right: 25px;
    top: 0px;
  }
  .ic-remove-tim-thai {
    position: absolute;
    left: -20px;
    top: 0px;
    :hover {
      cursor: pointer;
    }
    @media print {
      display: none;
    }
  }
  .center {
    text-align: center;
  }
  .controls-container {
    display: flex;
    align-items: center;
    gap: 12px;
    border-radius: 8px;
    margin: 10px 0;
    @media print {
      display: none;
    }
  }

  .size-select {
    min-width: 150px;

    .ant-select-selector {
      border-radius: 6px !important;
      border: 1px solid #d9d9d9 !important;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff !important;
      }

      &:focus {
        border-color: #1890ff !important;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
      }
    }
  }

  @media print {
    .btn-add,
    .icon-remove,
    .icon-remove-item,
    .icon-add-item,
    .copy-paste-element {
      display: none;
    }
  }

  .col-element {
    position: relative;
  }
  .col-element:hover {
    .copy-paste-element {
      display: block !important;
    }
  }
`;
