import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { DATA_TABLE } from "../configData";
import { cloneDeep, get } from "lodash";
import { DeboundInput } from "components/editor/config";
import AppDatePicker from "../../DatePicker";
import { updateObject } from "utils";
import { message } from "antd";
import { SVG } from "assets";
import PopoverSelect from "../../Components/PopoverSelect";
import CopyPasteCol from "../../Components/CopyPasteCol";
import { useTranslation } from "react-i18next";
import { refConfirm } from "app";
import DropDownList from "../../DropDownList";
import { useListAll, useQueryAll } from "hooks";
import { query } from "redux-store/stores";
import { THIET_LAP_CHUNG } from "constants";

const RenderTable = ({
  item,
  mode,
  tableIndex,
  refValueForm,
  formChange,
  onRemove,
  itemProps,
  form,
  onChangeForm,
  COL,
  dataCopy,
  setDataCopy,
}) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    value: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refValue = useRef();
  const [listAllNhanVien] = useListAll(
    "nhanVien",
    {
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
    },
    true
  );

  const { data: listDieuDuong } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.DIEU_DUONG, THIET_LAP_CHUNG.Y_TA],
      },
    })
  );

  useEffect(() => {
    refValue.current = item;
    setState({
      value: item,
    });
  }, [item]);

  const onChangeInput = useCallback(
    (key, idx) => (e) => {
      let value = e;
      const keySplit = (key || "").split(".");
      let key1, key2;
      key1 = keySplit[0];
      if (keySplit.length > 1) {
        key2 = keySplit[1];
      }
      if (!key2) {
        updateObject(refValue.current.dsChiTiet[idx], key1, value);
      } else {
        updateObject(refValue.current.dsChiTiet[idx][key1], `${key2}`, value);
      }
      onChangeForm();
    },
    [refValueForm, form, Object.keys(formChange || {}).length]
  );

  const onChangeValueGhiChu = (key) => (value) => {
    updateObject(refValue.current, key, value);
    onChangeForm();
  };

  const handleCopy = (idx) => () => {
    const dataCopy = cloneDeep(refValue.current.dsChiTiet[idx]);
    setDataCopy({
      col: idx,
      data: dataCopy,
    });
    message.success("Copy dữ liệu thành công!");
  };

  const hanldePaste = (idx) => () => {
    const dataCurrent = cloneDeep(refValue.current.dsChiTiet[idx]);
    refValue.current.dsChiTiet[idx] = cloneDeep(dataCopy.data);

    refValue.current.dsChiTiet[idx].stt = dataCurrent.stt;
    setState({
      value: cloneDeep(refValue.current),
    });
    onChangeForm();
  };

  const handleDelete = (idx) => () => {
    let isSign = (form.lichSuKy?.dsChuKy || []).some((el) => {
      return (
        el.viTri === refValue.current.dsChiTiet[idx].stt && el.chuKySo === 1
      );
    });
    if (isSign) {
      message.error("Vui lòng hủy ký trước khi xóa!");
      return;
    }

    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")} cột ${idx + 1}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          try {
            refValue.current.dsChiTiet[idx] = {};
            setState({
              value: cloneDeep(refValue.current),
            });
            onChangeForm();
          } catch (error) {
            message.error(error?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
          }
        }
      );
  };

  const render = useMemo(() => {
    return (
      <>
        <table style={{ marginTop: 20 }}>
          {/* {renderHeader()} */}
          <tbody>
            {DATA_TABLE.map((item, index) => {
              const keySplit = (item?.key || "").split(".");
              let key1, key2;
              key1 = keySplit[0];
              if (keySplit.length > 1) {
                key2 = keySplit[1];
              }

              return (
                <tr key={`${item.key}_${index}`}>
                  {item.label1 && (
                    <td
                      rowSpan={item.rowSpan1 || 1}
                      colSpan={item.colSpan1 || 2}
                      style={{
                        position: "relative",
                        textAlign: item.rowSpan1 > 1 ? "center" : "left",
                        width: 50,
                      }}
                    >
                      <b>{item.label1}</b>
                      {tableIndex && !index ? (
                        <div>
                          <SVG.IcDelete
                            className="ic-remove"
                            onClick={onRemove(tableIndex)}
                          ></SVG.IcDelete>
                        </div>
                      ) : null}
                    </td>
                  )}
                  {item.label2 && (
                    <td
                      colSpan={1}
                      style={{
                        position: "relative",
                        textAlign: "center",
                      }}
                    >
                      <b>{item.label2}</b>
                    </td>
                  )}

                  {new Array(COL).fill({}).map((el, idx) => {
                    return item.disabled ? (
                      <td
                        className="col-element"
                        key={`${item.key}_${index}_${idx}`}
                      ></td>
                    ) : item.type === "time" ? (
                      <td
                        className="col-element"
                        key={`${item.key}_${index}_${idx}`}
                      >
                        <AppDatePicker
                          component={{
                            props: {
                              contentAlign: "center",
                              dateTimeFormat: "HH:mm D/M/Y",
                              fieldName: "value",
                              disableOnblur: true,
                              fontSize: 10,
                            },
                          }}
                          form={{
                            value: get(
                              state.value?.dsChiTiet,
                              `[${idx}].thoiGianThucHien`
                            ),
                          }}
                          mode={mode}
                          formChange={{
                            value: (e) => {
                              try {
                                onChangeInput(`thoiGianThucHien`, idx)(e);
                              } catch (error) {}
                            },
                          }}
                        />
                        <CopyPasteCol
                          colIndex={idx}
                          handlePaste={hanldePaste(idx)}
                          handleCopy={handleCopy(idx)}
                          dataCopy={dataCopy}
                          handleDelete={handleDelete(idx)}
                          showSinhHieu={false}
                        ></CopyPasteCol>
                      </td>
                    ) : item.type === "droplist" ? (
                      <td className="col-element">
                        <PopoverSelect
                          data={Array.isArray(item.data) ? item.data : []}
                          value={
                            get(
                              state.value?.dsChiTiet,
                              `[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                            ) || (item.valueDefault ? item.valueDefault : [])
                          }
                          onChangeValue={(e) => {
                            onChangeInput(item.key, idx)(e);
                          }}
                          isMultiple={!(item.mode == "onlyOne")}
                          trigger="click"
                        ></PopoverSelect>
                      </td>
                    ) : item.type == "sign" ? (
                      <td
                        className="col-element"
                        key={`${item.key}_${index}_${idx}`}
                      >
                        <DropDownList
                          component={{
                            props: {
                              checkList: (item.key == "dieuDuongTheoDoiId"
                                ? listDieuDuong
                                : listAllNhanVien
                              ).map((item) => ({
                                label: item.ten,
                                value: item.id,
                              })),
                              fieldName: "id",
                              type: "onlyOne",
                              noLabel: true,
                              listType: 2,
                              dropdownMatchSelectWidth: 300,
                            },
                          }}
                          blockWidth="80px"
                          form={{
                            id: get(
                              state.value?.dsChiTiet,
                              `[${idx}][${key1}]${key2 ? `${key2}` : ""}`
                            ),
                          }}
                          formChange={{
                            id: onChangeInput(item.key, idx),
                          }}
                        />
                      </td>
                    ) : (
                      <td
                        className="col-element"
                        key={`${item.key}_${index}_${idx}`}
                      >
                        <DeboundInput
                          readOnly={false}
                          value={
                            get(
                              state.value?.dsChiTiet,
                              `[${idx}]${key1}${key2 ? `[${key2}]` : ""}`
                            ) ||
                            item.valueDefault ||
                            ""
                          }
                          onChange={onChangeInput(item.key, idx)}
                          type="multipleline"
                          lineHeightText={1.5}
                          fontSize={9}
                          minHeight={9 + 6}
                          markSpanRow={false}
                          contentAlign="center"
                          inputNumber={item.type === "number"}
                        ></DeboundInput>
                      </td>
                    );
                  })}
                </tr>
              );
            })}
            <tr>
              <td colSpan={2} style={{ minHeight: 100 }}>
                Xử lý / ghi chú
              </td>
              <td colSpan={COL}>
                <DeboundInput
                  readOnly={false}
                  value={get(state.value.ghiChu)}
                  onChange={onChangeValueGhiChu("ghiChu")}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={9}
                  minHeight={9 + 6}
                  markSpanRow={false}
                  contentAlign="center"
                  inputNumber={item.type === "number"}
                ></DeboundInput>
              </td>
            </tr>
          </tbody>
        </table>
      </>
    );
  }, [state.value, refValueForm, dataCopy, listAllNhanVien, listDieuDuong]);
  return render;
};

export default RenderTable;
