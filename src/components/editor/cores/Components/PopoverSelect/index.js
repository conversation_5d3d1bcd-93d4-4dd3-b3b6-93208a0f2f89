import { Popover, Input } from "antd";
import React, {
  useState,
  useEffect,
  memo,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { get, isNumber } from "lodash";
import { DeboundInput } from "components/editor/config";
import { useRefFunc } from "hooks";
import { containText } from "utils";
import classNames from "classnames";
import { SVG } from "assets";
import { HOTKEY } from "constants";
import { GlobalStyle, PopoverStyled } from "./styled";

const PopoverSelect = forwardRef(
  (
    {
      value,
      onChangeValue,
      data,
      isMultiple,
      onChangeInput,
      refValue,
      labelByKey,
      isValueKey2,
      trigger,
      isShowSearch = false,
      disabled,
      splitStr = ", ",
      contentAlign,
      tabIndex,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const refIsChangeValue = useRef(false);
    const inputRef = useRef(null);
    const refElement = useRef();
    const [state, _setState] = useState({
      value: [],
      searchText: "",
      open: false,
      curIndex: 0,
    });
    const refOpen = useRef();
    const refChangeClick = useRef();

    const [open, _setOpen] = useState(false);
    const setOpen = (value) => {
      refOpen.current = value;
      _setOpen(value);
    };

    const setState = (data = {}) => {
      _setState((state) => ({ ...state, ...data }));
    };

    useImperativeHandle(ref, () => ({
      onOpenChange: (value) => {
        setState({
          open: value,
        });
      },
    }));

    useEffect(() => {
      return () => {
        setState({
          curIndex: 0,
          searchText: "",
        });
        refChangeClick.current = false;
      };
    }, [state.open]);

    const {
      phimTat: {
        onRegisterHotkey,
        onUnRegisterHotkey,
        onAddLayer,
        onRemoveLayer,
      },
    } = useDispatch();

    useEffect(() => {
      setState({
        value: value ? value : isMultiple ? [] : null,
      });
    }, [value]);

    const valueDisplay = useMemo(() => {
      if (isValueKey2 && !refIsChangeValue.current) {
        return <div dangerouslySetInnerHTML={{ __html: state.value }}></div>;
      } else {
        if (!state.value) return "";
        let itemSelecteds = [];

        if (Array.isArray(state.value)) {
          itemSelecteds = state.value
            .map((el) => {
              return (data || []).find((i) => i.value == el);
            })
            .filter((item) => item);
        } else {
          itemSelecteds = [state.value]
            .map((el) => {
              return (data || []).find((i) => i.value == el);
            })
            .filter((item) => item);
        }

        const itemExitsKeyKhac = itemSelecteds.find((el) => el.valueKeyKhac);
        setState({
          itemSelected: itemExitsKeyKhac || {},
        });
        const label = itemSelecteds
          .map((item) => (labelByKey ? item[labelByKey] : item.label))
          .join(splitStr || ", ");

        return label;
      }
    }, [data, state.value, labelByKey, isValueKey2]);

    const onClick = (el) => () => {
      let data = {
        value: state.value,
      };
      inputRef.current?.focus();
      refIsChangeValue.current = true;
      if (isMultiple) {
        if (state.value.includes(el.value)) {
          data.value = state.value.filter((item) => item != el.value);
        } else {
          data.value = [...data.value, el.value].filter((el) => isNumber(el));
        }
      } else {
        if (state.value == el.value) {
          data.value = null;
        } else {
          data.value = el.value;
          data.itemSelected = el;
        }
      }

      //nếu dạng chọn 1 thì cho đóng luôn popover sau khi chọn
      setState({
        ...data,
      });

      onChangeValue(data.value);
      if (!isMultiple) {
        focusElementByTabIndex();
        setOpen(false);
      }
    };

    const focusElementByTabIndex = () => {
      const curTabIndex = refElement.current.tabIndex;
      requestAnimationFrame(() => {
        const el = document.querySelector(`[tabindex="${curTabIndex + 1}"]`);
        el?.focus();
      });
    };

    const handleChangeSearchText = (e) => {
      setState({
        searchText: e.target.value,
        curIndex: 0,
      });
    };

    const handleOpenChange = (newOpen) => {
      refChangeClick.current = true;
      setOpen(newOpen);
    };

    useEffect(() => {
      const layerId = "layer-popover-select";
      const hotKeys = [
        {
          keyCode: HOTKEY.UP,
          onEvent: () => {
            onSelectRow(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: () => {
            onSelectRow(1);
          },
        },
        {
          keyCode: HOTKEY.ENTER,
          onEvent: (e) => {
            onEnter();
          },
        },
        {
          keyCode: HOTKEY.TAB,
          onEvent: (e) => {
            onNextTab();
          },
        },
      ];
      if (open) {
        onAddLayer({ layerId });
        onRegisterHotkey({
          layerId: layerId,
          hotKeys,
        });
      } else {
        onRemoveLayer({ layerId });
        onUnRegisterHotkey({
          layerId: layerId,
          hotKeys,
        });
        refChangeClick.current = false;
      }
    }, [open, state]);

    useEffect(() => {
      if (open) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      } else {
        inputRef.current?.blur();
      }
    }, [open]);

    const filterData = useMemo(() => {
      if (state.searchText) {
        return data.filter((item) => containText(item.label, state.searchText));
      }
      return data;
    }, [data, state.searchText]);

    const onEnter = useRefFunc(() => {
      onClick(filterData[state.curIndex])();
    });

    const onNextTab = useRefFunc(() => {
      setState({ isFocus: false });
      setOpen(false);
      focusElementByTabIndex();
    });

    const onSelectRow = useRefFunc((index) => {
      let indexNextItem = state.curIndex + index;
      if (indexNextItem === filterData.length) {
        indexNextItem = 0;
      } else if (indexNextItem < 0) {
        indexNextItem = filterData.length - 1;
      }

      setState({
        curIndex: indexNextItem,
      });
    });

    return (
      <div
        tabIndex={tabIndex}
        onFocus={(e) => {
          if (!disabled) {
            setTimeout(() => {
              if (!refOpen.current && !refChangeClick.current) {
                setOpen(true);
                setState({ isFocus: true });
              }
            }, 100);
          }
        }}
        style={{
          outline: "none",
          transition: "background 0.2s",
        }}
        className="popover-select"
        ref={refElement}
      >
        {disabled ? (
          <div
            style={{
              width: "100%",
              minHeight: "14px",
              textAlign: "center",
              wordBreak: "break-word",
            }}
          >
            {valueDisplay || ""}
          </div>
        ) : (
          <Popover
            overlayClassName={"select-custorm-popover"}
            trigger={trigger || "hover"}
            title=""
            open={open}
            onOpenChange={handleOpenChange}
            placement="bottom"
            // zIndex={-1}
            content={
              <PopoverStyled>
                {isShowSearch && (
                  <Input
                    ref={inputRef}
                    placeholder={t("Tìm kiếm")}
                    value={state.searchText}
                    onChange={handleChangeSearchText}
                    style={{}}
                  />
                )}

                <div className="content">
                  {(filterData || []).map((el, index) => {
                    const isActive = isMultiple
                      ? (state.value || []).includes(el.value)
                      : state.value == el.value;

                    const isFocusing = state.curIndex === index;

                    return (
                      <div
                        className={classNames("item", {
                          active: isActive,
                          focusing: isFocusing,
                        })}
                        onClick={onClick(el)}
                        key={index}
                        style={{
                          display: "flex",
                          alignContent: "center",
                          padding: "0 10px",
                        }}
                        onMouseEnter={() => {
                          setState({
                            curIndex: index,
                          });
                        }}
                      >
                        <span style={{ flex: 1, textAlign: "left" }}>
                          {el?.label}
                        </span>
                        {isActive && (
                          <SVG.IcDelete
                            className="ic-remove"
                            onClick={onClick(el)}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </PopoverStyled>
            }
          >
            <GlobalStyle></GlobalStyle>
            <div
              style={{
                width: "100%",
                minHeight: "14px",
                textAlign: contentAlign || "center",
                wordBreak: "break-word",
              }}
            >
              {valueDisplay || "...."}
            </div>
          </Popover>
        )}

        {state.itemSelected?.valueKeyKhac ? (
          <DeboundInput
            readOnly={false}
            value={get(refValue, state.itemSelected?.keyKhac, "")}
            onChange={(e) => {
              onChangeInput(state.itemSelected?.keyKhac)(e);
            }}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            contentAlign="center"
          />
        ) : null}
      </div>
    );
  }
);
export default memo(PopoverSelect);
