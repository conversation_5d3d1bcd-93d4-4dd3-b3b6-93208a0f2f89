import React, {
  forwardRef,
  memo,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { GlobalStyle, Main } from "./styled";
import { useDispatch } from "react-redux";
import { Button } from "antd";
import { MODE, combineFields, convert } from "utils/editor-utils";
import {
  SettingOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { useEnum, useListAll, useStore, useThietLap } from "hooks";
import { ENUM, THIET_LAP_CHUNG, TRANG_THAI_KY } from "constants/index";
import stringUtils from "mainam-react-native-string-utils";
import moment from "moment";
import ModalAddTime from "../../PhieuThucHienVaCongKhaiThuoc/components/ModalAddTime";
import {
  getTypeTime,
  getTimeTxt,
} from "../../PhieuThucHienVaCongKhaiThuoc/constants";
import { cloneDeep, flatten, groupBy, round, sortBy } from "lodash";
import GioSuDungs from "./components/Row";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import ImageSign from "../../ImageSign";

export const ContextPhieu = React.createContext();
export const ContextPhieuProvider = ContextPhieu.Provider;

const CHUKY_INDEX = {
  dsChuKyBacSi: 1,
  dsChuKyKiemTra: 2,
  dsChuKyDieuDuong: 3,
  dsChuKyNguoiNha: 4,
};

const PhieuThucHienVaCongKhaiThuoc = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    dsThuoc: [],
    dsNgayYLenh: [],
    showBoSung: true,
    dsChuKyBacSi: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
    dsChuKyDieuDuong: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
    dsChuKyKiemTra: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
    dsChuKyNguoiNha: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { tuThoiGian, denThoiGian } = getAllQueryString();
  const { component, mode, form, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const refModalAddTime = useRef();
  const refdsThoiGianSuDung = useRef([]);

  const [MA_DUONG_DUNG_DICH_TRUYEN, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_DUONG_DUNG_DICH_TRUYEN
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_SANG] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_SANG,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_CHIEU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_CHIEU,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_TOI] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_TOI,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_DEM] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_DEM,
    ""
  );
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };
  const refDataThuoc = useRef({});
  const refDsThuoc = useRef([]);
  const refDsChuKyBacSi = useRef([]);
  const refDsChuKyDieuDuong = useRef([]);
  const refChuKyKiemTra = useRef([]);
  const refDsChuKyNguoiNha = useRef([]);

  const configThoiGianBuoi = useMemo(() => {
    if (
      dataTHOI_GIAN_VA_TEN_BUOI_SANG &&
      dataTHOI_GIAN_VA_TEN_BUOI_CHIEU &&
      dataTHOI_GIAN_VA_TEN_BUOI_TOI &&
      dataTHOI_GIAN_VA_TEN_BUOI_DEM
    )
      return {
        SANG: dataTHOI_GIAN_VA_TEN_BUOI_SANG,
        CHIEU: dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
        TOI: dataTHOI_GIAN_VA_TEN_BUOI_TOI,
        DEM: dataTHOI_GIAN_VA_TEN_BUOI_DEM,
      };

    return null;
  }, [
    dataTHOI_GIAN_VA_TEN_BUOI_SANG,
    dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
    dataTHOI_GIAN_VA_TEN_BUOI_TOI,
    dataTHOI_GIAN_VA_TEN_BUOI_DEM,
  ]);

  useEffect(() => {
    if (Object.keys(form || {}).length && loadFinish) {
      const _listDichTruyen = (MA_DUONG_DUNG_DICH_TRUYEN || "").split(",");
      let _dsThuoc = form.dsThuoc || [];
      let dsIdThuocDichTruyen = [];
      if (!itemProps?.hienThuocDichTruyen) {
        _dsThuoc = _dsThuoc.filter(
          (x) => !_listDichTruyen.includes(x.maDuongDung)
        );
        dsIdThuocDichTruyen = (form.dsThuoc || [])
          .filter((x) => _listDichTruyen.includes(x.maDuongDung))
          .map((item) => item.id);
        _dsThuoc = _dsThuoc.filter(
          (x1) => !dsIdThuocDichTruyen.includes(x1.dungKemId)
        );
      }
      let dsThuoc =
        _dsThuoc.filter((x) => {
          const thoiGianThucHien = new Date(x.thoiGianThucHien).getTime();
          const tuNgay = new Date(tuThoiGian).getTime();
          const denNgay = new Date(denThoiGian).getTime();
          return thoiGianThucHien >= tuNgay && thoiGianThucHien <= denNgay;
        }) || [];
      dsThuoc.forEach((thuoc) => {
        thuoc.thoiGianThucHien2 = moment(thuoc.thoiGianThucHien).format(
          "DD/MM/YYYY"
        );
      });
      refdsThoiGianSuDung.current = dsThuoc.map((item) => {
        (item.dsThoiGianSuDung || []).forEach((item) => {
          item.id = stringUtils.guid();
        });
        return {
          ...item,
          id: item.id,
          loai: item.loai,
          dsThoiGianSuDung: item.dsThoiGianSuDung,
        };
      });
      const thuocsGroup = groupBy(dsThuoc, "thoiGianThucHien2");
      let dsNgayYLenh = Object.keys(thuocsGroup)
        .map((ngayYLenh) => ngayYLenh)
        .sort(
          (a, b) =>
            new Date(a.split("/").reverse().join("/")).getTime() -
            new Date(b.split("/").reverse().join("/")).getTime()
        );
      if (dsNgayYLenh.length < 3) {
        dsNgayYLenh.push(...new Array(3 - dsNgayYLenh.length).fill(""));
      }

      dsThuoc = dsThuoc.map((item) => ({
        ...item,
        dsThoiGianSuDung: (item.dsThoiGianSuDung || []).map((x) => ({
          ...x,
          type: getTypeTime(x.tuThoiGian, x.tuThoiGian, configThoiGianBuoi),
        })),
      }));

      dsThuoc = (dsThuoc || [])
        .filter((x) => !x.dungKemId)
        .map((item) => ({
          ...item,
          dsThuocDungKem: (dsThuoc || []).filter(
            (x) => x.dungKemId === item.id
          ),
        }));

      const merged = dsThuoc.reduce(
        (
          r,
          {
            tenDichVu,
            tenDuongDung,
            soLuong1Lan,
            soLan1Ngay,
            tenDonViTinh,
            tenDvtSuDung,
            loai,
            sttHienThi,
            sttDuongDung,
            dsThuocDungKem,
            chiDinhId,
            ...rest
          }
        ) => {
          /*SAKURA-37540 FE (OP) [Phiếu công khai và thực hiện thuốc] Lỗi gộp hiển thị sai thông tin thuốc
          nam.mn - 13/07/2025
          bổ sung thêm thoiGianYLenh vào key để không bị gộp chung các thuốc có thời gian y lệnh khác nhau
          */
          // const key = `${tenDichVu}-${tenDuongDung}-${soLuong1Lan}-${soLan1Ngay}-${
          //   (dsThuocDungKem || []).length
          // }-${rest.thoiGianYLenh}-${rest.thuocDaChiDinh}`;
          const key = `${chiDinhId}`;

          r[key] = r[key] || {
            dsThuoc: [],
          };
          r[key]["dsThuoc"].push({
            tenDichVu,
            tenDuongDung,
            soLuong1Lan,
            soLan1Ngay,
            thoiGianThucHien2: moment(rest.thoiGianThucHien).format(
              "DD/MM/YYYY"
            ),
            tenDonViTinh,
            tenDvtSuDung,
            loai,
            sttHienThi,
            sttDuongDung,
            dsThuocDungKem,
            ...rest,
          });
          return r;
        },
        {}
      );

      let boSung = "";

      //lấy thông tin bổ sung
      const _dsThuocTheoTgThucHien = groupBy(dsThuoc, "thoiGianThucHien");
      const _dsNgayYLenhHienThi = dsNgayYLenh.slice(0, 3);

      const _dsBoSung = Object.keys(_dsThuocTheoTgThucHien)
        .filter((key) => {
          if (key == "") return false;
          return _dsNgayYLenhHienThi.includes(moment(key).format("DD/MM/YYYY"));
        })
        .map((key) => _dsThuocTheoTgThucHien[key]);

      const _dsThuocTheoTgYLenh = groupBy(
        flatten(_dsBoSung),
        (x) => x.chiDinhTuDichVuId
      );

      boSung = Object.keys(_dsThuocTheoTgYLenh)
        .map((key) => _dsThuocTheoTgYLenh[key][0])
        .filter((x) => !!x.boSung)
        .map((x) => ({
          time: x.thoiGianYLenh,
          content: x.boSung,
        }));

      let dsThuoc2 = Object.keys(merged).map((thuoc) => {
        const ngayYLenh = groupBy(merged[thuoc].dsThuoc, "thoiGianThucHien2");
        /*
        nam.mn 13/07/2025 
        Lỗi liên quan đến việc có 2 thuốc giống nhau, mỗi thuốc có 1 ds dùng kèm riêng,
        Đang gom thuốc lại, nhưng đang không gom thuốc dùng kèm -> bị thiếu thuốc dùng kèm
        */
        // 1. tạo mảng ds các thuốc dùng kèm
        let dsThuocDungKem = [];

        Object.keys(ngayYLenh).forEach((item) => {
          const tc = ngayYLenh[item].reduce((a, b) => {
            return (a || 0) + (b.soLuong || 0);
          }, 0);
          // 2. thực hiện gom hết tất cả các thuốc dùng kèm lại
          dsThuocDungKem = [
            ...dsThuocDungKem,
            ...flatten(ngayYLenh[item].map((item2) => item2.dsThuocDungKem)),
          ];
          ngayYLenh[item].forEach((el) => {
            el.tc = round(tc, 3);
          });
        });
        let tongLieu = 0;

        Object.keys(ngayYLenh).forEach((item) => {
          if (!ngayYLenh[item][0].thuocDaChiDinh) {
            tongLieu += ngayYLenh[item][0]?.tc;
          }
        });
        // 3. gom theo dichVuId và cộng dồn số lượng
        dsThuocDungKem = Object.entries(
          groupBy(dsThuocDungKem, "dichVuId")
        ).map(([k, v]) => {
          if (v.length == 1) return v[0];
          v[0].soLuong = v.reduce((a, b) => (a += b.soLuong), 0);
          return v[0];
        });

        const firstThuoc = merged[thuoc].dsThuoc[0];
        firstThuoc.dsThuocDungKem = dsThuocDungKem;
        // Lấy ds Thuốc dùng kèm đã gộp để hiển thị ra phiếu
        const tongSoLuongHuy = dsThuoc
          .filter((x) => x.dichVuId === firstThuoc.dichVuId)
          .reduce((a, b) => (a || 0) + (b.soLuongHuy || 0), 0);
        const data = {
          tenThuoc: renderThuoc({ data: firstThuoc, tongSoLuongHuy }),
          dsNgayYLenh: ngayYLenh,
          tongLieu: round(tongLieu, 3),
          donVi: firstThuoc.tenDvtSoCap,
          lieuDung: `${firstThuoc.soLuong1Lan ? firstThuoc.soLuong1Lan : ""} ${
            firstThuoc.tenDvtSuDung ? firstThuoc?.tenDvtSuDung : ""
          }${firstThuoc.soLan1Ngay ? ` x ${firstThuoc?.soLan1Ngay}` : ""}`,
          tenDuongDung: firstThuoc.tenDuongDung,
          id: stringUtils.guid(),
          tenDonViTinh: firstThuoc.tenDonViTinh,
          loai: firstThuoc.loai,
          sttHienThi: firstThuoc.sttHienThi,
          thoiGianThucHien: firstThuoc.thoiGianThucHien,
          sttDuongDung: firstThuoc.sttDuongDung,
          dsThuocDungKem: dsThuocDungKem,
          tenDvtSuDung: firstThuoc.tenDvtSuDung,
          tenDvtSoCap: firstThuoc.tenDvtSoCap,
        };
        return data;
      });

      dsThuoc2 = sortBy(dsThuoc2, [
        "thoiGianThucHien",
        "sttHienThi",
        "sttDuongDung",
      ]);

      refDsThuoc.current = dsThuoc2;
      refDataThuoc.current = dsThuoc2;

      setState({
        dsThuoc: dsThuoc2,
        dsNgayYLenh: dsNgayYLenh.slice(0, 3),
        boSung,
      });
    }
  }, [
    form,
    tuThoiGian,
    denThoiGian,
    itemProps,
    MA_DUONG_DUNG_DICH_TRUYEN,
    loadFinish,
    configThoiGianBuoi,
  ]);

  useEffect(() => {
    if (Object.keys(form).length && state.dsNgayYLenh.length) {
      const ngay1 = moment(state.dsNgayYLenh[0], "DD/MM/YYYY").isValid()
        ? moment(state.dsNgayYLenh[0], "DD/MM/YYYY").format("YYYY-MM-DD")
        : null;
      const ngay2 = moment(state.dsNgayYLenh[1], "DD/MM/YYYY").isValid()
        ? moment(state.dsNgayYLenh[1], "DD/MM/YYYY").format("YYYY-MM-DD")
        : null;
      const ngay3 = moment(state.dsNgayYLenh[2], "DD/MM/YYYY").isValid()
        ? moment(state.dsNgayYLenh[2], "DD/MM/YYYY").format("YYYY-MM-DD")
        : null;
      const dsChuKyBacSi = [
        { cot: "A", ngay: ngay1 },
        { cot: "B", ngay: ngay1 },
        { cot: "C", ngay: ngay1 },
        { cot: "D", ngay: ngay1 },
        { cot: "A", ngay: ngay2 },
        { cot: "B", ngay: ngay2 },
        { cot: "C", ngay: ngay2 },
        { cot: "D", ngay: ngay2 },
        { cot: "A", ngay: ngay3 },
        { cot: "B", ngay: ngay3 },
        { cot: "C", ngay: ngay3 },
        { cot: "D", ngay: ngay3 },
      ].map((item) => {
        const findIndex = (form.dsChuKyBacSi || []).findIndex(
          (x) => x.ngay == item.ngay && x.cot == item.cot
        );
        if (findIndex > -1) {
          item = form.dsChuKyBacSi[findIndex];
        }
        return item;
      });

      const dsChuKyDieuDuong = [
        { cot: "A", ngay: ngay1 },
        { cot: "B", ngay: ngay1 },
        { cot: "C", ngay: ngay1 },
        { cot: "D", ngay: ngay1 },
        { cot: "A", ngay: ngay2 },
        { cot: "B", ngay: ngay2 },
        { cot: "C", ngay: ngay2 },
        { cot: "D", ngay: ngay2 },
        { cot: "A", ngay: ngay3 },
        { cot: "B", ngay: ngay3 },
        { cot: "C", ngay: ngay3 },
        { cot: "D", ngay: ngay3 },
      ].map((item) => {
        const findIndex = (form.dsChuKyDieuDuong || []).findIndex(
          (x) => x.ngay == item.ngay && x.cot == item.cot
        );
        if (findIndex > -1) {
          item = form.dsChuKyDieuDuong[findIndex];
        }
        return item;
      });

      const dsChuKyKiemTra = [
        { cot: "A", ngay: ngay1 },
        { cot: "B", ngay: ngay1 },
        { cot: "C", ngay: ngay1 },
        { cot: "D", ngay: ngay1 },
        { cot: "A", ngay: ngay2 },
        { cot: "B", ngay: ngay2 },
        { cot: "C", ngay: ngay2 },
        { cot: "D", ngay: ngay2 },
        { cot: "A", ngay: ngay3 },
        { cot: "B", ngay: ngay3 },
        { cot: "C", ngay: ngay3 },
        { cot: "D", ngay: ngay3 },
      ].map((item) => {
        const findIndex = (form.dsChuKyKiemTra || []).findIndex(
          (x) => x.ngay == item.ngay && x.cot == item.cot
        );
        if (findIndex > -1) {
          item = form.dsChuKyKiemTra[findIndex];
        }
        return item;
      });

      const dsChuKyNguoiNha = [
        { cot: "A", ngay: ngay1 },
        { cot: "B", ngay: ngay1 },
        { cot: "C", ngay: ngay1 },
        { cot: "D", ngay: ngay1 },
        { cot: "A", ngay: ngay2 },
        { cot: "B", ngay: ngay2 },
        { cot: "C", ngay: ngay2 },
        { cot: "D", ngay: ngay2 },
        { cot: "A", ngay: ngay3 },
        { cot: "B", ngay: ngay3 },
        { cot: "C", ngay: ngay3 },
        { cot: "D", ngay: ngay3 },
      ].map((item) => {
        const findIndex = (form.dsChuKyNguoiNha || []).findIndex(
          (x) => x.ngay == item.ngay && x.cot == item.cot
        );
        if (findIndex > -1) {
          item = form.dsChuKyNguoiNha[findIndex];
          item = {
            ...item,
            dsChuKyNguoiNha: {
              anhKy: item.anhKy,
              thoiGianKy: item.thoiGianKy,
            },
          };
        }
        return item;
      });

      refDsChuKyBacSi.current = dsChuKyBacSi;
      refDsChuKyDieuDuong.current = dsChuKyDieuDuong;
      refChuKyKiemTra.current = dsChuKyKiemTra;
      refDsChuKyNguoiNha.current = dsChuKyNguoiNha;

      setState({
        dsChuKyBacSi: dsChuKyBacSi,
        dsChuKyDieuDuong: dsChuKyDieuDuong,
        dsChuKyKiemTra: dsChuKyKiemTra,
        dsChuKyNguoiNha: dsChuKyNguoiNha,
      });
    }
  }, [Object.keys(form)?.length, state.dsNgayYLenh]);

  const renderThuoc = ({
    data,
    isSub = false,
    thuocKeNgoai = false,
    tongSoLuongHuy,
  }) => {
    const buoiDung = [];
    if (data.slSang) buoiDung.push({ ten: "Sáng", soLuong: data.slSang });
    if (data.slChieu) buoiDung.push({ ten: "Chiều", soLuong: data.slChieu });
    if (data.slToi) buoiDung.push({ ten: "Tối", soLuong: data.slToi });
    if (data.slDem) buoiDung.push({ ten: "Đêm", soLuong: data.slDem });
    const donViTocDoTruyen = (listDonViTocDoTruyen || []).find(
      (el) => el.id === data.donViTocDoTruyen
    );
    let textBuoiDung = buoiDung
      .map((buoi, index) => {
        return `${buoi.ten ? ` ${buoi.ten}` : ""}${
          buoi.soLuong ? ` ${buoi.soLuong}` : ""
        }${data.tocDoTruyen ? `, ${data.tocDoTruyen}` : ""}${
          donViTocDoTruyen ? `(${donViTocDoTruyen?.ten})` : ""
        }`;
      })
      .join(", ");
    if (!data.slSang && !data.slToi && !data.slDem && !data.slChieu) {
      textBuoiDung = `${data.tocDoTruyen ? `${data.tocDoTruyen}` : ""}${
        donViTocDoTruyen ? `(${donViTocDoTruyen?.ten})` : ""
      }`;
    }

    const tenHoatChat_HamLuong = () => {
      let textCustom = "";
      const showHamLuong =
        (itemProps.showHamLuong == null ? true : itemProps.showHamLuong) &&
        data.hamLuong;
      const showHoatChat =
        (itemProps.showHoatChat == null ? true : itemProps.showHoatChat) &&
        data.tenHoatChat;

      if (showHoatChat && !showHamLuong) {
        textCustom = ` (${data?.tenHoatChat})`;
      } else if (!showHoatChat && showHamLuong) {
        textCustom = ` (${data?.hamLuong})`;
      } else if (showHoatChat && showHamLuong) {
        textCustom = ` (${data?.tenHoatChat} - ${data?.hamLuong})`;
      }
      return textCustom;
    };

    const soLuong_Dvt = () => {
      if (!itemProps?.hienSoLuongDvt) return null;

      return (
        <>
          x{" "}
          {data.loaiDonThuoc === 20 ? (
            <span className="so-luong" style={{ margin: `0 5px` }}>
              {numberToString(data.soLuong)}
            </span>
          ) : (
            data.soLuong
          )}
          {data.tenDonViTinh ? ` (${data.tenDonViTinh})` : ""}{" "}
        </>
      );
    };

    return (
      <div className={`item-drug  ${isSub ? "drug" : ""} `}>
        <div className={` ml5 ${isSub ? "child" : ""}`}>
          <div
            className={`${!isSub ? "b" : ""}`}
            style={{ fontWeight: "bold" }}
          >
            {isSub ? "-" : ""}{" "}
            {thuocKeNgoai ? data.tenThuocChiDinhNgoai : data.tenDichVu}
            {tenHoatChat_HamLuong()}
            {data.boSung ? "(bổ sung)" : ""}{" "}
            {thuocKeNgoai
              ? `(TT)`
              : [50, 55].includes(data.loai)
              ? `(NT)`
              : " "}
            {soLuong_Dvt()}
            <i style={{ fontWeight: "normal", marginLeft: 2 }}>
              {tongSoLuongHuy || data.soLuongHuy
                ? `  (Hủy ${tongSoLuongHuy || data.soLuongHuy} ${
                    data.tenDonViTinh || ""
                  }${data.lyDoHuy ? `, ${data.lyDoHuy}` : ""})`
                : ""}
            </i>
          </div>
          <div className="i " style={{ fontStyle: "italic" }}>
            <div>
              {`${data.tenLieuDung || ""} ${
                data.cachDung && itemProps.hienCachDung
                  ? `${data.cachDung}`
                  : ""
              } ${
                textBuoiDung
                  ? `(${textBuoiDung}${
                      data.thoiDiem ? ` ${data.thoiDiem}` : ""
                    })`
                  : ""
              }`}
              <span>{!!data.ghiChu && `.Ghi chú: ${data.ghiChu}`}</span>
            </div>
          </div>
        </div>
        {data?.dsThuocDungKem?.length ? (
          <>
            {data.dsThuocDungKem.map((e) =>
              renderThuoc({ data: e, isSub: true, thuocKeNgoai })
            )}
          </>
        ) : null}
      </div>
    );
  };
  const onChangeForm = ({ thuocId, time, timeId, thucHienThuoc }) => {
    const res = refdsThoiGianSuDung.current
      .find((item) => item.id === thuocId)
      .dsThoiGianSuDung.find((el) => el.id === timeId);
    res.tuThoiGian = time;
    res.thucHienThuoc = thucHienThuoc;
    formChange["dsThuoc"]?.(refdsThoiGianSuDung.current);
  };

  const onSign = (index, key) => (value) => {
    let _dsChuKy = state[key];

    let newItem = _dsChuKy[index];
    if (key === "dsChuKyNguoiNha") {
      if (!value) {
        newItem = { cot: newItem.cot, ngay: newItem.ngay };
      } else {
        newItem = {
          ...newItem,
          ...value,
        };
      }
    } else {
      newItem = {
        ...newItem,
        chuKy: value,
      };
    }

    _dsChuKy[index] = newItem;

    let newData = [];
    if (key === "dsChuKyBacSi") {
      refDsChuKyBacSi.current = _dsChuKy.filter((x) => x.chuKy);

      newData = refDsChuKyBacSi.current;
    } else if (key === "dsChuKyDieuDuong") {
      refDsChuKyDieuDuong.current = _dsChuKy.filter((x) => x.chuKy);

      newData = refDsChuKyDieuDuong.current;
    } else if (key === "dsChuKyKiemTra") {
      refChuKyKiemTra.current = _dsChuKy.filter((x) => x.chuKy);

      newData = refChuKyKiemTra.current;
    } else if (key === "dsChuKyNguoiNha") {
      refDsChuKyNguoiNha.current = _dsChuKy.filter((x) => x.anhKy);

      newData = refDsChuKyNguoiNha.current;
    }

    formChange[key](newData);

    setState({ [key]: _dsChuKy });
  };

  const renderChuKy = (index, key) => {
    const idx = CHUKY_INDEX[key];
    const isDaKy =
      (form.lichSuKy?.dsChuKy || []).find(
        (x) => x.viTri == `${idx}${index + 1}`
      )?.trangThai == TRANG_THAI_KY.DA_KY;
    const isDaKy2 =
      (form.lichSuKy?.dsChuKy || []).find(
        (x) => x.viTri == `2${idx}${index + 1}`
      )?.trangThai == TRANG_THAI_KY.DA_KY;

    return state[key][index]?.ngay ? (
      <>
        <ImageSign
          component={{
            props: {
              fieldName: itemProps[`fieldNameKy${idx}`],
              allowReset: isDaKy2 ? false : itemProps[`allowReset${idx}`],
              fontSize: 12,
              capKy: itemProps[`capKy${idx}`],
              loaiKy: key === "dsChuKyNguoiNha" ? 2 : 1,
              width: 45,
              height: 40,
              showCa: false,
              isMultipleSign: true,
              showPatientSign: false,
              isKyTheoCot: true,
              viTri: `${idx}${index + 1}`,
              customText: "Ký",
              contentAlign: "center",
              dataSign: {
                id: form.id,
                soPhieu: form.soPhieu,
                lichSuKyId: form?.lichSuKy?.id,
              },
            },
          }}
          mode={mode}
          form={combineFields({
            ...state[key][index],
            lichSuKy: form.lichSuKy,
          })}
          formChange={{
            setMultiData: (value) => {
              const _chuKy = convert(value);
              onSign(index, key)(_chuKy[key]);
            },
          }}
        />

        {/* Hiển thị thêm chữ ký cùng 1 vị trí điều dưỡng */}
        {itemProps.kyNhieuChanKyDieuDuong && idx === 3 && isDaKy && (
          <ImageSign
            component={{
              props: {
                fieldName: itemProps[`fieldNameKy${idx}`],
                allowReset: itemProps[`allowReset${idx}`],
                fontSize: 12,
                capKy: itemProps[`capKy${idx}`],
                loaiKy: key === "dsChuKyNguoiNha" ? 2 : 1,
                width: 45,
                height: 40,
                showCa: false,
                isMultipleSign: true,
                showPatientSign: false,
                isKyTheoCot: true,
                viTri: `2${idx}${index + 1}`,
                customText: "Ký",
                contentAlign: "center",
                dataSign: {
                  id: form.id,
                  soPhieu: form.soPhieu,
                  lichSuKyId: form?.lichSuKy?.id,
                },
              },
            }}
            mode={mode}
            form={combineFields({
              ...state[key][index],
              lichSuKy: form.lichSuKy,
            })}
            formChange={{
              setMultiData: (value) => {
                const _chuKy = convert(value);
                onSign(index, key)(_chuKy[key]);
              },
            }}
          />
        )}
      </>
    ) : null;
  };

  const dsTenDieuDuong = useMemo(() => {
    const listDanhSach = form?.dsThuoc?.flatMap((item) =>
      (item.dsThoiGianSuDung || []).map((x) => ({
        thoiGianThucHien: item.thoiGianThucHien,
        type: getTypeTime(x.tuThoiGian, x.tuThoiGian, configThoiGianBuoi),
        ...x,
      }))
    );
    const data = listDanhSach?.map((item) => ({
      ...item,
      thoiGianThucHien2: moment(item.thoiGianThucHien).format("DD/MM/YYYY"),
    }));
    const listThuoc = groupBy(data, "thoiGianThucHien2");
    return listThuoc;
  }, [form]);

  function shortName(fullName) {
    if (!fullName) return null;
    const parts = fullName.trim().split(/\s+/);
    if (parts.length === 1) return parts[0];

    const last = parts[parts.length - 1];
    const initials = parts
      .slice(0, -1)
      .map((p) => p[0].toUpperCase() + ".")
      .join("");
    return `${initials} ${last}`;
  }

  const renderTenDieuDuong = (index, type) => {
    const data = Object.values(dsTenDieuDuong);
    const item = data[index];
    if (item) {
      const tenDieuDuong = item
        .filter((x) => x.type === type)
        .map((item) =>
          shortName(
            listAllNhanVien?.find((x) => x.id === item.dieuDuongId)?.ten
          )
        )
        .filter(Boolean);
      const uniqueNames = [...new Set(tenDieuDuong)];

      return uniqueNames.join(", ");
    }

    return "";
  };
  console.log("itemProps.hienThiTenDieuDuong", itemProps.hienThiTenDieuDuong)
  const renderFooter = () => {
    return (
      <>
        {Array.from(Array(3).keys()).map((key) => (
          <tr>
            {new Array(20).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
          </tr>
        ))}
        {!itemProps[`anChuKy1`] && (
          <tr>
            <td colSpan={2}>Bác sĩ ba tra thuốc</td>
            {new Array(3).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
            {Array.from({ length: 4 }, (_, i) => i).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyBacSi")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 4).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyBacSi")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 8).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyBacSi")}</td>
            ))}
            <td></td>
          </tr>
        )}
        {!itemProps[`anChuKy2`] && (
          <tr>
            <td colSpan={2}>Kiểm tra kép</td>
            {new Array(3).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
            {Array.from({ length: 4 }, (_, i) => i).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyKiemTra")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 4).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyKiemTra")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 8).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyKiemTra")}</td>
            ))}
            <td></td>
          </tr>
        )}
        {!itemProps[`anChuKy3`] && (
          <tr>
            <td colSpan={2}>Tên điều dưỡng thực hiện</td>
            {new Array(3).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
            {Array.from({ length: 4 }, (_, i) => i).map((item, index) =>
              !itemProps.hienThiTenDieuDuong ? (
                <td key={item}>{renderChuKy(item, "dsChuKyDieuDuong")}</td>
              ) : (
                <td key={item}>{renderTenDieuDuong(0, index + 1)}</td>
              )
            )}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 4).map((item, index) =>
              !itemProps.hienThiTenDieuDuong ? (
                <td key={item}>{renderChuKy(item, "dsChuKyDieuDuong")}</td>
              ) : (
                <td key={item}>{renderTenDieuDuong(1, index + 1)}</td>
              )
            )}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 8).map((item, index) =>
              !itemProps.hienThiTenDieuDuong ? (
                <td key={item}>{renderChuKy(item, "dsChuKyDieuDuong")}</td>
              ) : (
                <td key={item}>{renderTenDieuDuong(2, index + 1)}</td>
              )
            )}
            <td></td>
          </tr>
        )}

        {!itemProps[`anChuKy4`] && (
          <tr>
            <td colSpan={2}>Người bệnh / Người nhà ký tên</td>
            {new Array(3).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
            {Array.from({ length: 4 }, (_, i) => i).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyNguoiNha")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 4).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyNguoiNha")}</td>
            ))}
            <td></td>
            {Array.from({ length: 4 }, (_, i) => i + 8).map((item) => (
              <td key={item}>{renderChuKy(item, "dsChuKyNguoiNha")}</td>
            ))}
            <td></td>
          </tr>
        )}
      </>
    );
  };

  const renderBoSung = () => {
    const renderItem = () => {
      return (state.boSung || []).map((item) => (
        <div style={{ marginBottom: 10 }}>
          <span>
            <b>{moment(item.time).format("HH:mm - DD/MM/YYYY")}</b>
          </span>
          <br />
          <span
            dangerouslySetInnerHTML={{
              __html: (item.content || "").replaceAll("\n", "<br/>"),
            }}
          ></span>
        </div>
      ));
    };

    return (
      <tr>
        <td></td>
        <td>{state.showBoSung && renderItem()}</td>
        {[...Array(19).keys()].map(() => (
          <td></td>
        ))}
      </tr>
    );
  };

  const getDonVi = (item) => {
    const { tenDonViTinh, tenDvtSoCap, tenDvtSuDung, loai } = item;

    if (
      loai === 60 ||
      (tenDonViTinh === tenDvtSoCap && tenDonViTinh !== tenDvtSuDung)
    ) {
      return tenDonViTinh;
    }

    return tenDvtSuDung || "";
  };

  const renderTable = useMemo(() => {
    const onAddTime = ({ ngayYLenh, thuoc, thuocId }) => {
      if (refModalAddTime.current) {
        refModalAddTime.current.show({}, (value) => {
          const arr = ngayYLenh.split("/");

          const data = {
            tuThoiGian: value
              ? moment(value)
                  .set({ year: arr[2], month: +arr[1] - 1, date: arr[0] })
                  .format()
              : null,
            dieuDuongId: nhanVienId,
            denThoiGian: null,
            dieuDuong: null,
            type: getTypeTime(value, value, configThoiGianBuoi),
            id: stringUtils.guid(),
          };
          const res = refDataThuoc.current
            .find((item) => item.id === thuoc.id)
            ?.dsNgayYLenh[ngayYLenh]?.find((el) => el.id === thuocId);
          res.dsThoiGianSuDung = [...(res.dsThoiGianSuDung || []), data];
          if (formChange["dsThuoc"]) {
            formChange["dsThuoc"](refDataThuoc.current);
          }
          const thuoc2 = refdsThoiGianSuDung.current.find(
            (item) => item.id === thuocId
          );
          thuoc2.dsThoiGianSuDung = res.dsThoiGianSuDung;
          if (formChange["dsThuoc"]) {
            formChange["dsThuoc"](refdsThoiGianSuDung.current);
          }
          setState({
            dsThuoc: cloneDeep(refDataThuoc.current),
          });
        });
      }
    };
    const onRemoveTime = ({ ngayYLenh, thuocId, timeId, thuoc }) => {
      const res = refDataThuoc.current
        .find((item) => item.id === thuoc.id)
        ?.dsNgayYLenh[ngayYLenh]?.find((el) => el.id === thuocId);

      res.dsThoiGianSuDung = res.dsThoiGianSuDung.filter(
        (x) => x.id !== timeId
      );
      if (formChange["dsThuoc"]) {
        formChange["dsThuoc"](refDataThuoc.current);
      }
      const thuoc2 = refdsThoiGianSuDung.current.find(
        (item) => item.id === thuocId
      );
      thuoc2.dsThoiGianSuDung = res.dsThoiGianSuDung;
      if (formChange["dsThuoc"]) {
        formChange["dsThuoc"](refdsThoiGianSuDung.current);
      }

      setState({
        dsThuoc: cloneDeep(refDataThuoc.current),
      });
    };

    return (
      <ContextPhieuProvider
        value={{
          onChangeForm,
          onAddTime,
          onRemoveTime,
        }}
      >
        {(state?.dsThuoc || []).map((item, index) => {
          const onAdd = ({ time, ngayYLenh, thuocId }) => {
            onAddTime({
              time,
              ngayYLenh,
              thuoc: item,
              thuocId,
            });
          };
          const onRemove = ({ timeId, ngayYLenh, thuocId }) => {
            onRemoveTime({
              timeId,
              ngayYLenh,
              thuoc: item,
              thuocId,
            });
          };

          return (
            <tr>
              <td className="stt">{index + 1}</td>
              <td>{item.tenThuoc}</td>
              <td className="center">
                <div className="flex-td">{item?.donVi} </div>
                {(item.dsThuocDungKem || []).map((x) => {
                  return <div className="flex-td">{x.tenDvtSoCap}</div>;
                })}
              </td>
              <td className="center">
                <div className="flex-td">{item?.lieuDung} </div>
                {(item.dsThuocDungKem || []).map((x) => {
                  return (
                    <div className="flex-td">{`${
                      x.soLuong1Lan ? x.soLuong1Lan : ""
                    } ${x.tenDvtSuDung ? x?.tenDvtSuDung : ""}${
                      x.soLan1Ngay ? ` x ${x?.soLan1Ngay}` : ""
                    }`}</div>
                  );
                })}
              </td>
              <td className="center">
                <div className="flex-td">{item?.tenDuongDung} </div>
                {(item.dsThuocDungKem || []).map((x) => {
                  return <div className="flex-td">{x.tenDuongDung}</div>;
                })}
              </td>

              <GioSuDungs
                thuoc={item}
                dsNgayYLenh={state.dsNgayYLenh}
                onAdd={onAdd}
                onRemove={onRemove}
                itemProps={itemProps}
              />
              <td className={`center mw-60 wrBrW`}>
                <div>{`${item.tongLieu}`}</div>
                <div>{getDonVi(item)}</div>
                {(item.dsThuocDungKem || []).map((x) => {
                  let soLuong = 0;
                  Object.keys(item.dsNgayYLenh).forEach((key) => {
                    (item.dsNgayYLenh[key][0].dsThuocDungKem || []).forEach(
                      (x1) => {
                        if (
                          x.dichVuId === x1.dichVuId &&
                          x.thoiGianThucHien2 === x1.thoiGianThucHien2
                        ) {
                          soLuong += x1.soLuong;
                        }
                      }
                    );
                  });
                  return (
                    <div className="mg-t-30">
                      <div>{round(soLuong, 3)}</div>
                      <div>{getDonVi(x)}</div>
                    </div>
                  );
                })}
              </td>
            </tr>
          );
        })}
      </ContextPhieuProvider>
    );
  }, [state.dsThuoc, formChange, state.dsNgayYLenh, configThoiGianBuoi]);

  const extractTenHienThi = (config, defaultTen) => {
    if (!config) return defaultTen;
    const parts = config.split("/");
    return (parts[2] || defaultTen).trim();
  };

  const tenCotSang = extractTenHienThi(dataTHOI_GIAN_VA_TEN_BUOI_SANG, "A");
  const tenCotChieu = extractTenHienThi(dataTHOI_GIAN_VA_TEN_BUOI_CHIEU, "B");
  const tenCotToi = extractTenHienThi(dataTHOI_GIAN_VA_TEN_BUOI_TOI, "C");
  const tenCotDem = extractTenHienThi(dataTHOI_GIAN_VA_TEN_BUOI_DEM, "D");

  return (
    <Main
      className="phieu-thuc-hien-cong-khai-thuoc-1"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-thuc-hien-cong-khai-thuoc-1"
    >
      <GlobalStyle></GlobalStyle>
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}

      <table className="table-1">
        <thead>
          <tr className="thead">
            <td rowSpan={2} className="stt" width={40}>
              STT
              {!state.showBoSung ? (
                <EyeInvisibleOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: true });
                  }}
                />
              ) : (
                <EyeOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: false });
                  }}
                />
              )}
            </td>
            <td rowSpan={2} width={180} className={"mw-180"}>
              Tên thuốc, hàm lượng
            </td>
            <td rowSpan={2}>Đơn vị</td>
            <td rowSpan={2}>Liều dùng</td>
            <td rowSpan={2}>Đường dùng</td>
            {state.dsNgayYLenh.map((ngay) => (
              <td colSpan={5}>Ngày {ngay || ""}</td>
            ))}

            <td rowSpan={2} width={60}>
              Tổng liều
            </td>
          </tr>
          <tr className="thead">
            <td className="col-time">{tenCotSang}</td>
            <td className="col-time">{tenCotChieu}</td>
            <td className="col-time">{tenCotToi}</td>
            <td className="col-time">{tenCotDem}</td>
            <td className="col-time" width={70}>
              TC
            </td>
            <td className="col-time">{tenCotSang}</td>
            <td className="col-time">{tenCotChieu}</td>
            <td className="col-time">{tenCotToi}</td>
            <td className="col-time">{tenCotDem}</td>
            <td className="col-time" width={70}>
              TC
            </td>
            <td className="col-time">{tenCotSang}</td>
            <td className="col-time">{tenCotChieu}</td>
            <td className="col-time">{tenCotToi}</td>
            <td className="col-time">{tenCotDem}</td>
            <td className="col-time" width={70}>
              TC
            </td>
          </tr>
        </thead>
        <tbody>
          {state.showBoSung && renderBoSung()}
          {renderTable}
          {renderFooter()}
        </tbody>
      </table>

      <ModalAddTime ref={refModalAddTime}></ModalAddTime>
    </Main>
  );
});

export default memo(PhieuThucHienVaCongKhaiThuoc);
