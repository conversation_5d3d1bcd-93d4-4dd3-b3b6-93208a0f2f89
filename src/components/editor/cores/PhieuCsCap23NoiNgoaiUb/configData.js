// Generated from "ĐD_CS cấp 2-3_ <PERSON><PERSON><PERSON>i UB_19.8.2025.docx"
// Structure aligned to configData.js style. Values in droplists start from 1.
// Do NOT auto-add or remove fields beyond what the Word file specifies.

export const DATA_TABLE = [
  // --- Thời gian ---
  { label1: "Thời gian", key: "thoiGianThucHien", type: "time", colSpan1: 3 },

  // --- <PERSON><PERSON> cấp chăm sóc ---
  {
    label1: "Phân cấp chăm sóc",
    key: "phanCapChamSoc",
    type: "droplist",
    hint: ["(1) III", "(2) IIB", "(3) IIA"],
    data: [
      { label: "III", value: 1 },
      { label: "IIB", value: 2 },
      { label: "IIA", value: 3 },
    ],
    colSpan1: 1,
    colSpan3: 2,
  },

  // --- Chỉ số sinh tồn ---
  {
    label1: "Chỉ số sinh tồn",
    rowSpan: 6,
    label2: "<PERSON><PERSON><PERSON> (lần/phút)",
    key: "chiSoSong.mach",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "Nhiệt độ (°C)",
    key: "chiSoSong.nhietDo",
    type: "number",
    colSpan2: 2,
  },
  { label2: "HA (mmHg)", key: "chiSoSong.huyetAp", type: "text", colSpan2: 2 },
  {
    label2: "Nhịp thở (lần/phút)",
    key: "chiSoSong.nhipTho",
    type: "number",
    colSpan2: 2,
  },
  { label2: "SpO₂", key: "chiSoSong.spo2", type: "number", colSpan2: 2 },
  { label2: "Nước tiểu (ml)", key: "nuocTieu", type: "number", colSpan2: 2 },

  // --- Sinh trắc ---
  {
    label1: "Sinh trắc",
    rowSpan: 3,
    label2: "Cân nặng (kg)",
    key: "chiSoSong.canNang",
    type: "number",
    colSpan2: 2,
  },
  {
    label2: "Chiều cao (m)",
    key: "chiSoSong.chieuCao",
    type: "number",
    colSpan2: 2,
  },
  { label2: "BMI", key: "chiSoSong.bmi", type: "calc", colSpan2: 2 },

  // --- Toàn trạng ---
  {
    label1: "Toàn trạng",
    rowSpan: 4,
    label2: "Trí giác",
    key: "toanTrang.triGiac",
    type: "droplist",
    hint: ["(1) Tỉnh", "(2) Lơ mơ", "(3) Vật vã, kích thích"],
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Lơ mơ", value: 2 },
      { label: "Vật vã, kích thích", value: 3 },
    ],
  },
  {
    label2: "Da, niêm mạc",
    key: "toanTrang.daNiemMac",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hồng",
      "(2) Vàng",
      "(3) Nhợt nhạt",
      "(4) Nổi mẩn đỏ",
      "(5) Xuất huyết, tụ máu",
    ],
    data: [
      { label: "Hồng", value: 1 },
      { label: "Vàng", value: 2 },
      { label: "Nhợt nhạt", value: 3 },
      { label: "Nổi mẩn đỏ", value: 4 },
      { label: "Xuất huyết, tụ máu", value: 5 },
    ],
  },
  {
    label2: "Phù",
    key: "toanTrang.phu",
    type: "droplist",
    hint: ["(1) Không", "(2) Có"],
    data: [
      { label: "Không", value: 1 },
      { label: "Có", value: 2 },
    ],
  },
  {
    label2: "Vị trí phù",
    key: "toanTrang.viTriPhu",
    type: "text",
    colSpan2: 2,
  },

  // --- Hô hấp ---
  {
    label1: "Hô hấp",
    rowSpan: 4,
    label2: "Mức độ",
    key: "hoHap.mucDo",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Khó thở"],
    data: [
      { label: "Bình thường", value: 1 },
      { label: "Khó thở", value: 2 },
    ],
  },
  {
    label2: "Phương pháp",
    key: "hoHap.phuongPhap",
    type: "droplist",
    hint: ["(1) Tự thở", "(2) Thở oxy"],
    data: [
      { label: "Tự thở", value: 1 },
      { label: "Thở oxy", value: 2 },
    ],
  },
  {
    label2: "Ho",
    key: "hoHap.ho",
    type: "droplist",
    hint: ["(1) Không ho", "(2) Ho khan", "(3) Ho đờm/máu"],
    data: [
      { label: "Không ho", value: 1 },
      { label: "Ho khan", value: 2 },
      { label: "Ho đờm/máu", value: 3 },
    ],
  },
  {
    label2: "Đờm",
    key: "hoHap.dom",
    type: "droplist",
    hint: ["(1) Đờm loãng", "(2) Đờm đặc", "(3) Đuôi khái huyết"],
    data: [
      { label: "Đờm loãng", value: 1 },
      { label: "Đờm đặc", value: 2 },
      { label: "Đuôi khái huyết", value: 3 },
    ],
  },

  // --- Tuần hoàn ---
  {
    label1: "Tuần hoàn",
    rowSpan: 1,
    label2: "Tính chất mạch",
    key: "tuanHoan.tinhChatMach",
    type: "droplist",
    hint: ["(1) Đều, rõ", "(2) Không đều"],
    data: [
      { label: "Đều, rõ", value: 1 },
      { label: "Không đều", value: 2 },
    ],
  },

  // --- Tiêu hóa ---
  {
    label1: "Tiêu hóa",
    rowSpan: 2,
    label2: "Tình trạng",
    key: "tieuHoa.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bụng mềm", "(2) Bụng chướng", "(3) Buồn nôn/nôn", "(4) Ăn kém"],
    data: [
      { label: "Bụng mềm", value: 1 },
      { label: "Bụng chướng", value: 2 },
      { label: "Buồn nôn/nôn", value: 3 },
      { label: "Ăn kém", value: 4 },
    ],
  },
  {
    label2: "Đường nuôi ăn",
    key: "tieuHoa.duongNuoiAn",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Ăn qua miệng", "(2) Ăn qua thông", "(3) Tĩnh mạch"],
    data: [
      { label: "Ăn qua miệng", value: 1 },
      { label: "Ăn qua thông", value: 2 },
      { label: "Tĩnh mạch", value: 3 },
    ],
  },

  // --- Theo dõi khác ---
  {
    label1: "Theo dõi khác",
    rowSpan: 5,
    label2: "Vị trí đau",
    key: "theoDoiKhac.viTriDau",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Điểm VAS",
    key: "theoDoiKhac.diemVAS",
    type: "droplist",
    hint: [
      "(1) 1",
      "(2) 2",
      "(3) 3",
      "(4) 4",
      "(5) 5",
      "(6) 6",
      "(7) 7",
      "(8) 8",
      "(9) 9",
      "(10) 10",
    ],
    data: [
      { label: "1", value: 1 },
      { label: "2", value: 2 },
      { label: "3", value: 3 },
      { label: "4", value: 4 },
      { label: "5", value: 5 },
      { label: "6", value: 6 },
      { label: "7", value: 7 },
      { label: "8", value: 8 },
      { label: "9", value: 9 },
      { label: "10", value: 10 },
    ],
  },
  { label2: "Dẫn lưu", key: "theoDoiKhac.danLuu", type: "text", colSpan2: 2 },
  {
    label2: "Độ viêm tĩnh mạch (VIP)",
    key: "theoDoiKhac.vip",
    type: "droplist",
    hint: ["(1) Độ 1", "(2) Độ 2", "(3) Độ 3", "(4) Độ 4", "(5) Độ 5"],
    data: [
      { label: "Độ 1", value: 1 },
      { label: "Độ 2", value: 2 },
      { label: "Độ 3", value: 3 },
      { label: "Độ 4", value: 4 },
      { label: "Độ 5", value: 5 },
    ],
  },
  {
    label2: "Nguy cơ ngã (thang Morse)",
    key: "theoDoiKhac.nguyCoNgaMorse",
    type: "droplist",
    hint: ["(1) Nguy cơ thấp", "(2) Nguy cơ trung bình", "(3) Nguy cơ cao"],
    data: [
      { label: "Nguy cơ thấp", value: 1 },
      { label: "Nguy cơ trung bình", value: 2 },
      { label: "Nguy cơ cao", value: 3 },
    ],
  },

  // --- Xương khớp ---
  {
    label1: "Xương khớp",
    rowSpan: 1,
    label2: "Vận động",
    key: "xuongKhop.vanDong",
    type: "droplist",
    hint: ["(1) Bình thường", "(2) Hạn chế"],
    data: [
      { label: "Bình thường", value: 1 },
      { label: "Hạn chế", value: 2 },
    ],
  },

  // --- Vết mổ/vết thương ---
  {
    label1: "Vết mổ/vết thương",
    rowSpan: 1,
    label2: "Tình trạng",
    key: "vetMo.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bình thường", "(2) Dịch thấm băng", "(3) Sưng nề"],
    data: [
      { label: "Bình thường", value: 1 },
      { label: "Dịch thấm băng", value: 2 },
      { label: "Sưng nề", value: 3 },
    ],
  },

  // --- Dẫn lưu (mục riêng) ---
  {
    label1: "Dẫn lưu",
    rowSpan: 3,
    label2: "Tình trạng",
    key: "danLuu.tinhTrang",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) DL thông", "(2) Sủi khí", "(3) DL tắc"],
    data: [
      { label: "DL thông", value: 1 },
      { label: "Sủi khí", value: 2 },
      { label: "DL tắc", value: 3 },
    ],
  },
  {
    label2: "Màu sắc dịch",
    key: "danLuu.mauSacDich",
    type: "text",
    colSpan2: 2,
  },
  {
    label2: "Chân dẫn lưu",
    key: "danLuu.chanDanLuu",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Khô, sạch", "(2) Sưng nề", "(3) Dịch thấm băng"],
    data: [
      { label: "Khô, sạch", value: 1 },
      { label: "Sưng nề", value: 2 },
      { label: "Dịch thấm băng", value: 3 },
    ],
  },

  // --- Nhận định khác ---
  { label1: "Nhận định khác", key: "nhanDinhKhac", type: "text", colSpan1: 3 },

  // --- CAN THIỆP ĐIỀU DƯỠNG ---
  {
    label1: "CAN THIỆP ĐIỀU DƯỠNG",
    key: "canThiepDieuDuong",
    type: "text",
    disable: true,
    colSpan1: 3,
  },

  {
    label1: "Thực hiện y lệnh",
    key: "thucHienYLenh",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Thở oxy",
      "(2) Thực hiện y lệnh thuốc",
      "(3) Truyền dịch",
      "(4) Truyền máu",
      "(5) Chăm sóc canuyn MKQ",
      "(6) Hướng dẫn NB tập thở",
      "(7) Vỗ rung lồng ngực",
      "(8) XN/CĐHA/   PHCN",
      "(9) Hạn chế vận động",
      "(10) HD NB ho hiệu quả",
      "(11) HD NB uống nhiều nước",
    ],
    data: [
      { label: "Thở oxy", value: 1 },
      { label: "Thực hiện y lệnh thuốc", value: 2 },
      { label: "Truyền dịch", value: 3 },
      { label: "Truyền máu", value: 4 },
      { label: "Chăm sóc canuyn MKQ", value: 5 },
      { label: "Hướng dẫn NB tập thở", value: 6 },
      { label: "Vỗ rung lồng ngực", value: 7 },
      { label: "XN/CĐHA/   PHCN", value: 8 },
      { label: "Hạn chế vận động", value: 9 },
      { label: "HD NB ho hiệu quả", value: 10 },
      { label: "HD NB uống nhiều nước", value: 11 },
    ],
    colSpan3: 2,
  },

  {
    label1: "Thực hiện kỹ thuật/thủ thuật",
    key: "kyThuatThuThuat",
    type: "droplist",
    mode: "multiple",
    hint: [
      "(1) Hút đờm",
      "(2) Thay băng",
      "(3) Khí dung",
      "(4) Đặt thông dạ dày",
      "(5) Đặt thông tiểu",
      "(6) Chọc dịch MP",
      "(7) Chọc dịch não tủy",
      "(8) Đặt Catheter",
      "(9) Mở màng phổi",
      "(10) Chọc hút khí MP",
      "(11) Rút dẫn lưu",
    ],
    data: [
      { label: "Hút đờm", value: 1 },
      { label: "Thay băng", value: 2 },
      { label: "Khí dung", value: 3 },
      { label: "Đặt thông dạ dày", value: 4 },
      { label: "Đặt thông tiểu", value: 5 },
      { label: "Chọc dịch MP", value: 6 },
      { label: "Chọc dịch não tủy", value: 7 },
      { label: "Đặt Catheter", value: 8 },
      { label: "Mở màng phổi", value: 9 },
      { label: "Chọc hút khí MP", value: 10 },
      { label: "Rút dẫn lưu", value: 11 },
    ],
    colSpan3: 2,
  },

  // --- Tư thế ---
  {
    label1: "Tư thế",
    key: "tuThe",
    type: "droplist",
    hint: [
      "(1) Thích hợp",
      "(2) Fowler",
      "(3) Đầu thấp",
      "(4) Nghiêng phải",
      "(5) Nghiêng trái",
    ],
    data: [
      { label: "Thích hợp", value: 1 },
      { label: "Fowler", value: 2 },
      { label: "Đầu thấp", value: 3 },
      { label: "Nghiêng phải", value: 4 },
      { label: "Nghiêng trái", value: 5 },
    ],
    colSpan3: 2,
  },

  // --- Chăm sóc đau ---
  {
    label1: "Chăm sóc đau",
    key: "chamSocDau",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) HD/Chườm ấm vùng đau", "(2) HD/Xoa vùng đau"],
    data: [
      { label: "HD/Chườm ấm vùng đau", value: 1 },
      { label: "HD/Xoa vùng đau", value: 2 },
    ],
    colSpan3: 2,
  },

  // --- Chăm sóc tâm lý ---
  {
    label1: "Chăm sóc tâm lý",
    key: "chamSocTamLy",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Động viên tinh thần", "(2) Giải đáp thắc mắc"],
    data: [
      { label: "Động viên tinh thần", value: 1 },
      { label: "Giải đáp thắc mắc", value: 2 },
    ],
    colSpan3: 2,
  },

  // --- Dinh dưỡng ---
  {
    label1: "Dinh dưỡng",
    rowSpan: 2,
    label2: "Hình thức",
    key: "dinhDuong.hinhThuc",
    type: "droplist",
    hint: [
      "(1) Bơm ăn qua thông dạ dày",
      "(2) Truyền nhỏ giọt qua thông dạ dày",
    ],
    data: [
      { label: "Bơm ăn qua thông dạ dày", value: 1 },
      { label: "Truyền nhỏ giọt qua thông dạ dày", value: 2 },
    ],
  },
  {
    label2: "Số lượng (ml)",
    key: "dinhDuong.soLuong",
    type: "number",
    colSpan2: 2,
  },

  // --- GDSK ---
  {
    label1: "GDSK",
    key: "gdsk",
    type: "droplist",
    mode: "multiple",
    hint: ["(1) Bệnh", "(2) Dinh dưỡng", "(3) Vệ sinh", "(4) Vận động"],
    data: [
      { label: "Bệnh", value: 1 },
      { label: "Dinh dưỡng", value: 2 },
      { label: "Vệ sinh", value: 3 },
      { label: "Vận động", value: 4 },
    ],
    colSpan3: 2,
  },

  // --- Tình trạng NB ---
  {
    label1: "Tình trạng NB",
    key: "tinhTrangNB",
    type: "droplist",
    hint: [
      "(1) Ổn định ra viện",
      "(2) Chuyển khoa",
      "(3) Chuyển viện",
      "(4) Xin về",
      "(5) Tử vong",
    ],
    data: [
      { label: "Ổn định ra viện", value: 1 },
      { label: "Chuyển khoa", value: 2 },
      { label: "Chuyển viện", value: 3 },
      { label: "Xin về", value: 4 },
      { label: "Tử vong", value: 5 },
    ],
    colSpan3: 2,
  },

  // --- Chăm sóc khác ---
  { label1: "Chăm sóc khác", key: "chamSocKhac", type: "text", colSpan1: 3 },

  // --- Điều dưỡng thực hiện ---
  {
    label1: "Điều dưỡng thực hiện",
    key: "ddThucHien",
    type: "sign",
    colSpan1: 3,
  },
];
