import React, {
  useState,
  useEffect,
  useImperative<PERSON>andle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row, Col } from "antd";
import { Select } from "components";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useListAll } from "hooks";
import { PropertiesSign } from "../../ImageSign/Properties";

const PhieuCsCap23NoiNgoaiUbProperties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [listAllKhoa] = useListAll("khoa");

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <PropertiesSign
          state={state}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

PhieuCsCap23NoiNgoaiUbProperties.defaultProps = {
  state: {},
};

PhieuCsCap23NoiNgoaiUbProperties.propTypes = {
  state: T.shape({}),
};

export default PhieuCsCap23NoiNgoaiUbProperties;
