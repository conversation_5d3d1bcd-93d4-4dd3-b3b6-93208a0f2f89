import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>and<PERSON>,
  useState,
} from "react";
import { cloneDeep, get } from "lodash";

const RenderBmi = ({ value, index, arr }, ref) => {
  const [data, setData] = useState({});
  useImperativeHandle(
    ref,
    () => ({
      setValue: (item) => {
        setData(cloneDeep(item));
      },
    }),
    []
  );

  useEffect(() => {
    setData(value);
  }, [value]);

  return arr.map((el, idx) => {
    const value = get(data, `dsChiTiet[${idx}].chiSoSong.bmi`, "");
    return (
      <td key={idx} className="center">
        {value ? (+value).toFixed(2) : ""}
      </td>
    );
  });
};
export default forwardRef(RenderBmi);
