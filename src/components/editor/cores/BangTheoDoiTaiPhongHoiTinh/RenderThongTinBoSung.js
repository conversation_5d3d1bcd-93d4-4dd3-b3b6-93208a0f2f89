import React, { memo, useEffect, useMemo, useState } from "react";
import { SVG } from "assets";
import { cloneDeep, get } from "lodash";
import { DeboundInput } from "module_html_editor/Components";

const styleIcon = {
  position: "absolute",
  top: 2,
  right: 2,
  width: 15,
  height: 15,
  cursor: "pointer",
};

const RenderThongTinBoSung = ({
  formChange,
  form,
  dataTable,
  indexTable,
  countTable,
}) => {
  const [state, _setState] = useState({
    dsBoSung: [],
  });
  const setState = (data) => {
    _setState((prev) => ({
      ...prev,
      ...data,
    }));
  };

  useEffect(() => {
    const dsBoSung = form?.dsBoSung || [
      {
        khac: "",
        slTheoGio: new Array(5).fill(""),
      },
    ];
    dsBoSung.forEach((e) => {
      if (!e.slTheoGio) {
        e.slTheoGio = new Array(countTable * 5)
          .fill("")
          .map((el, idx) => get(e, `slTheoGio[${indexTable * 5 + idx}]`, ""));
      }
    });

    setState({
      dsBoSung,
    });
  }, [form, countTable, countTable]);

  const onChangeInput = (index, indexCot) => (e) => {
    let dsBoSung = state[`dsBoSung`] || [];
    dsBoSung[index].slTheoGio[indexCot] = e;
    formChange[`dsBoSung`](dsBoSung);
  };

  const onAdd = () => {
    let dsBoSung = state[`dsBoSung`] || [];
    dsBoSung.push({
      khac: "",
      slTheoGio: new Array((indexTable + 1) * 5).fill(""),
    });
    setState({
      dsBoSung: cloneDeep(dsBoSung),
    });
    formChange[`dsBoSung`](dsBoSung);
  };

  const onDelete = (index) => {
    let dsBoSung = state[`dsBoSung`] || [];
    dsBoSung.splice(index, 1);
    setState({
      dsBoSung: cloneDeep(dsBoSung),
    });
    formChange[`dsBoSung`](dsBoSung);
  };

  const onChange = (index) => (value) => {
    const dsBoSung = state[`dsBoSung`] || [];
    dsBoSung[index].khac = value;

    formChange[`dsBoSung`](dsBoSung);
  };

  return (
    <>
      {(state.dsBoSung || []).map((item, index) => {
        let isShowIconDelete = true;
        //check nếu line có dữ liệu và đã ký thì ko được phép xóa
        if (
          new Array(5).fill(0).some((el, idx) => {
            const indexCot = indexTable * 5 + idx;
            const value = get(item, `slTheoGio[${indexCot}]`);
            const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
            const disable = dsChuKy.some((chuKy) => {
              const stt = get(dataTable, `[${idx}].stt`);
              return (
                chuKy.viTri === stt &&
                chuKy.trangThai >= 50 &&
                chuKy.chuKySo == 1
              );
            });

            return disable && value;
          })
        ) {
          isShowIconDelete = false;
        }

        return (
          <tr key={index}>
            <td colSpan={2}>
              <div className="flex items-center gap-2 relative">
                <DeboundInput
                  value={item.khac}
                  onChange={onChange(index)}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={9}
                  minHeight={9 + 6}
                  markSpanRow={false}
                  styleMain={{ minWidth: "100px" }}
                  readOnly={!isShowIconDelete}
                />
                {isShowIconDelete && (
                  <SVG.IcDelete
                    style={{ ...styleIcon, right: 0 }}
                    onClick={() => onDelete(index)}
                    className="hide-print"
                  />
                )}
                {index === state.dsBoSung?.length - 1 ? (
                  <SVG.IcAdd
                    style={{ ...styleIcon, right: 20 }}
                    onClick={onAdd}
                    className="hide-print"
                  />
                ) : null}
              </div>
            </td>
            {new Array(5).fill(0).map((el, idx) => {
              const indexCot = indexTable * 5 + idx;
              const value = get(item, `slTheoGio[${indexCot}]`);
              const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
              const disable = dsChuKy.some((chuKy) => {
                const stt = get(dataTable, `[${idx}].stt`);
                return (
                  chuKy.viTri === stt &&
                  chuKy.trangThai >= 50 &&
                  chuKy.chuKySo == 1
                );
              });
              return (
                <RenderTd
                  key={indexCot}
                  value={value}
                  onChangeInput={onChangeInput(index, indexCot)}
                  disable={disable}
                />
              );
            })}
          </tr>
        );
      })}
    </>
  );
};

RenderThongTinBoSung.propTypes = {};

export const RenderTd = memo(({ value, onChangeInput, disable }) => {
  return (
    <td style={{ width: 70, maxWidth: 70 }}>
      <DeboundInput
        isHideLineMark={disable}
        readOnly={disable}
        label=""
        value={value}
        onChange={onChangeInput}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        markSpanRow={false}
        contentAlign="center"
      />
    </td>
  );
});

export default RenderThongTinBoSung;
