import React, { memo, useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import { SVG } from "assets";
import { cloneDeep, get } from "lodash";
import { useStore } from "hooks";
import DropDownList from "./DropDownList";
import { DeboundInput } from "module_html_editor/Components";

const LOAI_THUOC = {
  THUONG: "dsThuoc",
  DICH_TRUYEN: "dsThuocTruyenDich",
  CHE_PHAM_MAU: "dsDvChePhamMau",
};

const styleIcon = {
  position: "absolute",
  top: 2,
  right: 2,
  width: 15,
  height: 15,
  cursor: "pointer",
};

const RenderThuoc = ({
  formChange,
  form,
  itemProps,
  indexTable = 1,
  dataTable,
  countTable,
}) => {
  const [rowSpan, setRowSpan] = useState(3);
  const [state, _setState] = useState({
    dsDvChePhamMau: [],
    dsThuoc: [],
    dsThuocTruyenDich: [],
  });
  const setState = (data) => {
    _setState((prev) => ({
      ...prev,
      ...data,
    }));
  };

  const listAllThuoc = useStore("danhMucThuoc.listAllData", []);

  const { dsThuoc, dsDvChePhamMau } = useMemo(() => {
    const dsThuoc = listAllThuoc.filter((el) => el.loaiDichVu === 90);
    const dsDvChePhamMau = listAllThuoc.filter((el) => el.loaiDichVu === 120);
    return {
      dsThuoc,
      dsDvChePhamMau,
    };
  }, [listAllThuoc?.length]);

  useEffect(() => {
    Object.values(LOAI_THUOC).forEach((item) => {
      const data = form[item];
      if (data?.length) {
        data.forEach((el) => {
          if (!el.slTheoGio) {
            el.slTheoGio = new Array(countTable * 5).fill("");
          }
          if (!el.donViSlTheoGio) {
            el.donViSlTheoGio = new Array(countTable * 5).fill("");
          }
        });
      }
    });
    const dsDvChePhamMau = (
      form?.dsDvChePhamMau || [
        {
          tenDichVu: "",
          id: null,
          slTheoGio: new Array(5).fill(""),
          donViSlTheoGio: new Array(5).fill(""),
        },
      ]
    ).filter((el) =>
      el.khoaChiDinhId
        ? (itemProps?.dsKhoaChiDinhThuocId || []).includes(el.khoaChiDinhId)
        : true
    );

    const dsThuoc = (
      form?.dsThuoc || [
        {
          tenDichVu: "",
          id: null,
          slTheoGio: new Array(5).fill(""),
          donViSlTheoGio: new Array(5).fill(""),
        },
      ]
    ).filter((el) =>
      el.khoaChiDinhId
        ? (itemProps?.dsKhoaChiDinhThuocId || []).includes(el.khoaChiDinhId)
        : true
    );
    const dsThuocTruyenDich = (
      form?.dsThuocTruyenDich || [
        {
          tenDichVu: "",
          id: null,
          slTheoGio: new Array(5).fill(""),
          donViSlTheoGio: new Array(5).fill(""),
        },
      ]
    ).filter((el) =>
      el.khoaChiDinhId
        ? (itemProps?.dsKhoaChiDinhThuocId || []).includes(el.khoaChiDinhId)
        : true
    );

    setState({
      dsDvChePhamMau,
      dsThuoc,
      dsThuocTruyenDich,
    });
  }, [form, itemProps, countTable, indexTable]);

  useEffect(() => {
    const { dsThuoc, dsDvChePhamMau, dsThuocTruyenDich } = state;
    const numberRowSpan =
      dsThuoc?.length + dsDvChePhamMau?.length + dsThuocTruyenDich?.length + 3;
    setRowSpan(numberRowSpan);
  }, [
    state.dsThuoc?.length,
    state?.dsThuocTruyenDich?.length,
    state.dsDvChePhamMau?.length,
  ]);

  const onChangeInput =
    (loaiThuoc, indexThuoc, indexCot, key = "slTheoGio") =>
    (e) => {
      let dsThuoc = state[`${loaiThuoc}`] || [];
      dsThuoc[indexThuoc][key][indexCot] = e;
      formChange[`${loaiThuoc}`](dsThuoc);
    };

  const onAddThuoc = (loaiThuoc) => {
    let dsThuoc = state[`${loaiThuoc}`] || [];
    dsThuoc.push({
      tenDichVu: "",
      slTheoGio: new Array((indexTable + 1) * 5).fill(""),
      donViSlTheoGio: new Array((indexTable + 1) * 5).fill(""),
      id: null,
    });
    setState({
      [loaiThuoc]: cloneDeep(dsThuoc),
    });
    formChange[`${loaiThuoc}`](dsThuoc);
  };

  const onDeleteThuoc = (loaiThuoc, index) => {
    let dsThuoc = state[`${loaiThuoc}`] || [];
    dsThuoc.splice(index, 1);
    setState({
      [loaiThuoc]: cloneDeep(dsThuoc),
    });
    formChange[`${loaiThuoc}`](dsThuoc);
  };

  const onChangeThuocKhac = (loaiThuoc, index) => (value) => {
    const dsThuoc = state[`${loaiThuoc}`] || [];
    dsThuoc[index] = {
      ...dsThuoc[index],
      tenDichVu: value.ten,
      id: value.id,
    };
    setState({
      [loaiThuoc]: cloneDeep(dsThuoc),
    });
    formChange[`${loaiThuoc}`](dsThuoc);
  };

  return (
    <>
      <tr>
        <td rowSpan={rowSpan}>Thuốc và dịch truyền</td>
        <td>
          <div className="flex items-center gap-2 relative">
            Thuốc
            <SVG.IcAdd
              style={styleIcon}
              onClick={() => onAddThuoc(LOAI_THUOC.THUONG)}
              className="hide-print"
            />
          </div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {(state.dsThuoc || []).map((item, index) => {
        let isShowIconDelete = true;
        //check nếu line có dữ liệu và đã ký thì ko được phép xóa
        if (
          new Array(5).fill(0).some((el, idx) => {
            const indexCot = indexTable * 5 + idx;
            const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
            const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);
            const dsChuKy = get(form, "lichSuKy.dsChuKy", []);
            const disable = dsChuKy.some((chuKy) => {
              const stt = get(dataTable, `[${idx}].stt`);
              return (
                chuKy.viTri === stt &&
                chuKy.trangThai >= 50 &&
                chuKy.chuKySo == 1
              );
            });

            return disable && (slTheoGio || donViSlTheoGio);
          })
        ) {
          isShowIconDelete = false;
        }
        return (
          <tr key={index}>
            <td>
              <div className="flex items-center gap-2 relative">
                <DropDownList
                  disabled={!isShowIconDelete}
                  title="Chọn Thuốc"
                  className={"f1 drop-list"}
                  dataSource={dsThuoc}
                  value={item}
                  onChange={onChangeThuocKhac(LOAI_THUOC.THUONG, index)}
                />
                {isShowIconDelete && (
                  <SVG.IcDelete
                    style={styleIcon}
                    onClick={() => onDeleteThuoc(LOAI_THUOC.THUONG, index)}
                    className="hide-print"
                  />
                )}
              </div>
            </td>
            {new Array(5).fill(0).map((el, idx) => {
              const indexCot = indexTable * 5 + idx;
              const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
              const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);

              const dsChuKy = get(form, "lichSuKy.dsChuKy", []);
              const disable = dsChuKy.some((chuKy) => {
                const stt = get(dataTable, `[${idx}].stt`);
                return (
                  chuKy.viTri === stt &&
                  chuKy.trangThai >= 50 &&
                  chuKy.chuKySo == 1
                );
              });

              return (
                <RenderMultiTd
                  data={[
                    {
                      key: `thuoc_slTheoGio_${indexCot}`,
                      value: slTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.THUONG,
                        index,
                        indexCot,
                        "slTheoGio"
                      ),
                      disable,
                      inputNumber: true,
                    },
                    {
                      key: `thuoc_donViSlTheoGio_${indexCot}`,
                      value: donViSlTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.THUONG,
                        index,
                        indexCot,
                        "donViSlTheoGio"
                      ),
                      disable,
                    },
                  ]}
                />
              );
            })}
          </tr>
        );
      })}
      <tr>
        <td>
          <div className="flex items-center gap-2 relative">
            Dịch truyền
            <SVG.IcAdd
              style={styleIcon}
              onClick={() => onAddThuoc(LOAI_THUOC.DICH_TRUYEN)}
              className="hide-print"
            />
          </div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {(state.dsThuocTruyenDich || []).map((item, index) => {
        const key = 1000 + index;

        let isShowIconDelete = true;
        //check nếu line có dữ liệu và đã ký thì ko được phép xóa
        if (
          new Array(5).fill(0).some((el, idx) => {
            const indexCot = indexTable * 5 + idx;
            const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
            const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);
            const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
            const disable = dsChuKy.some((chuKy) => {
              const stt = get(dataTable, `[${idx}].stt`);
              return (
                chuKy.viTri === stt &&
                chuKy.trangThai >= 50 &&
                chuKy.chuKySo == 1
              );
            });

            return disable && (slTheoGio || donViSlTheoGio);
          })
        ) {
          isShowIconDelete = false;
        }

        return (
          <tr key={key}>
            <td>
              <div className="flex items-center gap-2 relative">
                <DropDownList
                  disabled={!isShowIconDelete}
                  title="Chọn Thuốc"
                  className={"f1 drop-list"}
                  dataSource={dsThuoc}
                  value={item}
                  onChange={onChangeThuocKhac(LOAI_THUOC.DICH_TRUYEN, index)}
                />
                {isShowIconDelete && (
                  <SVG.IcDelete
                    style={styleIcon}
                    onClick={() => onDeleteThuoc(LOAI_THUOC.DICH_TRUYEN, index)}
                    className="hide-print"
                  />
                )}
              </div>
            </td>
            {new Array(5).fill(0).map((el, idx) => {
              const indexCot = indexTable * 5 + idx;
              const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
              const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);
              const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
              const disable = dsChuKy.some((chuKy) => {
                const stt = get(dataTable, `[${idx}].stt`);
                return (
                  chuKy.viTri === stt &&
                  chuKy.trangThai >= 50 &&
                  chuKy.chuKySo == 1
                );
              });

              return (
                <RenderMultiTd
                  data={[
                    {
                      value: slTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.DICH_TRUYEN,
                        index,
                        indexCot,
                        "slTheoGio"
                      ),
                      disable,
                      inputNumber: true,
                    },
                    {
                      value: donViSlTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.DICH_TRUYEN,
                        index,
                        indexCot,
                        "donViSlTheoGio"
                      ),
                      disable,
                    },
                  ]}
                />
              );
            })}
          </tr>
        );
      })}
      <tr>
        <td>
          <div className="flex items-center gap-2 relative">
            Máu và chế phẩm máu
            <SVG.IcAdd
              style={styleIcon}
              onClick={() => onAddThuoc(LOAI_THUOC.CHE_PHAM_MAU)}
              className="hide-print"
            />
          </div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      {(state.dsDvChePhamMau || []).map((item, index) => {
        const key = 2000 + index;
        let isShowIconDelete = true;
        //check nếu line có dữ liệu và đã ký thì ko được phép xóa
        if (
          new Array(5).fill(0).some((el, idx) => {
            const indexCot = indexTable * 5 + idx;
            const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
            const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);
            const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
            const disable = dsChuKy.some((chuKy) => {
              const stt = get(dataTable, `[${idx}].stt`);
              return (
                chuKy.viTri === stt &&
                chuKy.trangThai >= 50 &&
                chuKy.chuKySo == 1
              );
            });

            return disable && (slTheoGio || donViSlTheoGio);
          })
        ) {
          isShowIconDelete = false;
        }

        return (
          <tr key={key}>
            <td>
              <div className="flex items-center gap-2 relative">
                <DropDownList
                  disabled={!isShowIconDelete}
                  title="Chọn Thuốc"
                  className={"f1 drop-list"}
                  dataSource={dsDvChePhamMau}
                  value={item}
                  onChange={onChangeThuocKhac(LOAI_THUOC.CHE_PHAM_MAU, index)}
                />
                {isShowIconDelete && (
                  <SVG.IcDelete
                    style={styleIcon}
                    onClick={() =>
                      onDeleteThuoc(LOAI_THUOC.CHE_PHAM_MAU, index)
                    }
                    className="hide-print"
                  />
                )}
              </div>
            </td>
            {new Array(5).fill(0).map((el, idx) => {
              const indexCot = indexTable * 5 + idx;
              const slTheoGio = get(item, `slTheoGio[${indexCot}]`);
              const donViSlTheoGio = get(item, `donViSlTheoGio[${indexCot}]`);
              const dsChuKy = get(form, "lichSuKy.dsChuKy") || [];
              const disable = dsChuKy.some((chuKy) => {
                const stt = get(dataTable, `[${idx}].stt`);
                return (
                  chuKy.viTri === stt &&
                  chuKy.trangThai >= 50 &&
                  chuKy.chuKySo == 1
                );
              });

              return (
                <RenderMultiTd
                  data={[
                    {
                      key: `chePhamMau_slTheoGio_${key}_${indexCot}`,
                      value: slTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.CHE_PHAM_MAU,
                        index,
                        indexCot,
                        "slTheoGio"
                      ),
                      disable,
                      inputNumber: true,
                    },
                    {
                      key: `chePhamMau_donViSlTheoGio_${key}_${indexCot}`,
                      value: donViSlTheoGio,
                      onChangeInput: onChangeInput(
                        LOAI_THUOC.CHE_PHAM_MAU,
                        index,
                        indexCot,
                        "donViSlTheoGio"
                      ),
                      disable,
                    },
                  ]}
                />
              );
            })}
          </tr>
        );
      })}
    </>
  );
};

RenderThuoc.propTypes = {};

export const RenderTd = memo(({ value, onChangeInput, disable }) => {
  return (
    <td style={{ width: 70, maxWidth: 70 }}>
      <DeboundInput
        isHideLineMark={disable}
        readOnly={disable}
        label=""
        value={value}
        onChange={onChangeInput}
        type="multipleline"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        markSpanRow={false}
        contentAlign="center"
      />
    </td>
  );
});

export const RenderMultiTd = memo(({ data = [] } = {}) => {
  return (
    <td style={{ width: 70, maxWidth: 70 }}>
      <div className="flex">
        {data.map((item, index) => {
          const { value, onChangeInput, disable, inputNumber } = item || {};
          return (
            <div key={index} className="flex1">
              <DeboundInput
                isHideLineMark={disable}
                readOnly={disable}
                label=""
                value={value}
                onChange={onChangeInput}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={9}
                minHeight={9 + 6}
                markSpanRow={false}
                contentAlign="center"
                inputNumber={inputNumber}
              />
            </div>
          );
        })}
      </div>
    </td>
  );
});

export default RenderThuoc;
