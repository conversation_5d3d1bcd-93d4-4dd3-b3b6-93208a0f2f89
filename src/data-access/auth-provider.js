import { client, dataPath } from "client/request";
import {
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AUTH_REFRESH,
  DM_NHAN_VIEN,
  DM_TAI_KHOAN,
} from "client/api";
import { combineUrlParams } from "utils";
import isofhTool<PERSON>rovider from "./isofh-tool-provider";

export default {
  login: ({ taiKhoan, matKhau, coSoKcbId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${AUTH_LOGIN}`, {
          taiKhoan,
          matKhau,
          coSoKcbId,
        }, { headers: { "Authorization": null } })
        .then((s) => {
          if (s?.data?.code === 0 && s?.data?.data) {
            resolve(s?.data);
          } else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  logout: () => {
    return new Promise((resolve, reject) => {
      client.post(`${dataPath}${AUTH_LOGOUT}`).finally(() => {
        resolve();
      });
    });
  },
  refreshToken: ({ refreshToken, coSoKcbId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${AUTH_REFRESH}`, {
          refreshToken,
          coSoKcbId,
        })
        .then((s) => {
          if (s?.data?.code === 0 && s?.data?.data) {
            resolve(s?.data);
          } else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  onChangePassword: ({ matKhauCu, matKhauMoi, taiKhoan }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${DM_TAI_KHOAN}/doi-mat-khau`, {
          matKhauCu,
          matKhauMoi,
          taiKhoan,
        })
        .then((s) => {
          if (s?.data?.code === 0 && s?.data?.data) {
            resolve(s?.data);
          } else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getUserCsKcb: ({ access_token, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}/auth/co-so-kcb`, payload), access_token !== undefined ? {
          headers: {
            "authorization": null
          }
        } : {})
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsVanTay: ({ username, taiKhoanId, auth }) => {
    return new Promise(async (resolve, reject) => {
      if (auth) {
        client
          .get(`${dataPath}/dm-tai-khoan/van-tay/${taiKhoanId}`)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      } else {
        const thoiGianThucHien = new Date().format("yyyy-MM-ddTHH:mm:ss");
        const tenThietBi = "isofhtools";
        const { signatureData } = await isofhToolProvider.signText(`${username}|${thoiGianThucHien}|${tenThietBi}`, 1);
        client
          .post(`${dataPath}/auth/van-tay`, { signature: signatureData, taiKhoan: username, thoiGianThucHien, tenThietBi: tenThietBi })
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      }
    });
  },
  onChangePasswordKySo: ({ matKhauKyCu, matKhauKyMoi }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${DM_NHAN_VIEN}/doi-mat-khau-ky`, {
          matKhauKyCu,
          matKhauKyMoi,
        })
        .then((s) => {
          if (s?.data?.code === 0) {
            resolve(s?.data);
          } else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
