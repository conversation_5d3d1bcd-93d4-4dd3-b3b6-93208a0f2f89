import { NB_PHA_CHE_THUOC } from "client/api";
import apiBase from "../api-base";
import { client, dataPath } from "client/request";
import { combineUrlParams } from "utils/index";

export default {
  ...apiBase.init({ API: NB_PHA_CHE_THUOC }),
  getPhieuPhaCheThuoc: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_PHA_CHE_THUOC}/phieu-pha-che-thuoc/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  duyetPhaChe: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/pha-che`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDuyetPhaChe: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/huy-pha-che`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  giaoPhaChe: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/giao-pha-che`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyGiaoPhaChe: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/huy-giao-pha-che`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  duyetDuyetDuoc: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/duyet-duoc`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDuyetDuoc: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/huy-duyet-duoc`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  xacNhanNb: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/xac-nhan-nb`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyXacNhanNb: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/huy-xac-nhan-nb`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getNhanPhaCheThuoc: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_PHA_CHE_THUOC}/nhan-pha-che`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuCongKhaiPhaCheThuoc: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_PHA_CHE_THUOC}/phieu-cong-khai-pha-che-thuoc/${id}`
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  taoPhieuLinhThuocPhaChe: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC}/phieu-linh`, payload)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  xoaThuocPhieuLinh: (id, nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .delete(`${dataPath}${NB_PHA_CHE_THUOC}/phieu-linh/${id}`, {
          data: [nbDotDieuTriId],
        })
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuBanGiaoDichTruyen: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_PHA_CHE_THUOC}/phieu-ban-giao-dich-truyen/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
