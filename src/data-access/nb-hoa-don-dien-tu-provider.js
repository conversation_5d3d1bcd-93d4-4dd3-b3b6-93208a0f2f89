import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import {
  NB_DV_PHAT_HANH_HOA_DON,
  NB_HOA_DON_TONG_HOP,
  NB_HOA_DON,
  NB_HOA_DON_CHI_TIET_TONG_HOP,
  PHAT_HANH_HOA_DON,
  BANG_KE_KEM_HDDT_XUAT_GOP,
  BIEN_BAN_DIEU_CHINH,
} from "../client/api";

export default {
  getChiTietHoaDonDienTu: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_HOA_DON_TONG_HOP}/${id}`))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  getDsDichVuChiTiet: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_HOA_DON_CHI_TIET_TONG_HOP}`, params)
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  xuatHoaDon: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}${PHAT_HANH_HOA_DON}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  luuNhapHoaDon: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getFileHoaDon: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_HOA_DON}/${id}`, payload), {
          responseType: payload.dinhDang == 20 ? "arraybuffer" : "text",
        })
        .then((s) => {
          let obj = {};
          function IsJsonString(str) {
            try {
              obj = JSON.parse(new TextDecoder().decode(str));
            } catch (e) {
              return false;
            }
            return true;
          }
          IsJsonString(s.data);

          if (obj.message) {
            reject(obj);
          } else {
            if (payload.dinhDang == 20) {
              if (s.data?.byteLength != 0) {
                resolve(s.data);
              } else {
                reject(false);
              }
            } else {
              resolve(s.data);
            }
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  deleteHoaDon: ({ id, lyDo = "" }) => {
    return new Promise((resolve, reject) => {
      client
        .delete(`${dataPath}${NB_HOA_DON}/${id}`, { data: { lyDo } })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getChiTietHoaDon: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_HOA_DON_TONG_HOP}/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xuatHoaDonNhap: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}${PHAT_HANH_HOA_DON}/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xuatHoaDonNhapMisa: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/xuat-hoa-don-nhap`, payload, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          let obj = {};
          function IsJsonString(str) {
            try {
              obj = JSON.parse(new TextDecoder().decode(str));
            } catch (e) {
              return false;
            }
            return true;
          }
          IsJsonString(s.data);

          if (obj.message) {
            reject(s);
          } else {
            resolve(s.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getBangKeKemHDDT: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_HOA_DON}${BANG_KE_KEM_HDDT_XUAT_GOP}/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getBienBanDieuChinh: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_HOA_DON}${BIEN_BAN_DIEU_CHINH}/${id}`, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          try {
            const text = new TextDecoder("utf-8").decode(s.data); // Chuyển thành string
            const jsonData = JSON.parse(text); // Thử parse JSON
            reject(jsonData);
          } catch (error) {}
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dieuChinhHoaDon: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/dieu-chinh-hoa-don/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  capNhatHoaDon: (id, payload) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_HOA_DON}/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  taiTro: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/tai-tro`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  suaTaiTro: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_HOA_DON}/tai-tro/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phatHanhHoaDon: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/phat-hanh-hoa-don/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phatHanhHoaDonLoi: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/phat-hanh-hoa-don-loi`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  searchTaiTro: ({ page = 0, active, sort, size = 1000, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_HOA_DON}/tai-tro`, {
            page: page + "",
            active,
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getHoaDonNhap: (hoaDonId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_HOA_DON}/hoa-don-nhap`, {
            hoaDonId,
          })
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xemHoaDonNhap: (hoaDonId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_HOA_DON}/xem-hoa-don-nhap/${hoaDonId}`
          ),
          { responseType: "arraybuffer" }
        )
        .then((s) => {
          try {
            const text = new TextDecoder("utf-8").decode(s.data); // Chuyển thành string
            const jsonData = JSON.parse(text); // Thử parse JSON
            reject(jsonData);
          } catch (error) {}
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  search: ({ page = 0, active, sort, size = 1000, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_HOA_DON_TONG_HOP}`, {
            page: page + "",
            active,
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDsDichVuDefault: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_PHAT_HANH_HOA_DON}`, params))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  phatHanhHoaDonHangLoat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/phat-hanh-hoa-don`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  getTrangThaiDaPhatHanhHDDVBH: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_HOA_DON}/hoa-don-bao-hiem`, {
            nbDotDieuTriId,
          })
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phatHanhHddtTaoMoiHangLoat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/phat-hanh-hang-loat`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  taoHoaDonThayThe: ({ id, ...payload } = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/thay-the-hoa-don/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chuyenDoiHoaDon: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_HOA_DON}/chuyen-doi/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
