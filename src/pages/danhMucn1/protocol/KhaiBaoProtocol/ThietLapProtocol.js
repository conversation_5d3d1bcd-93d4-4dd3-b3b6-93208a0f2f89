import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import {
  TableWrapper,
  HeaderSearch,
  Select,
  Checkbox,
  Button,
  Popover,
} from "components";
import { cloneDeep } from "lodash";
import { message } from "antd";
import { checkRole } from "lib-utils/role-utils";
import { useEnum, useLoading, useQueryAll } from "hooks";
import classNames from "classnames";
import { ENUM, HIEU_LUC, YES_NO } from "constants/index";
import { SVG } from "assets";
import { query } from "redux-store/stores";
import QuickAddForm from "./QuickAddForm";
import { useQuery } from "@tanstack/react-query";
import dmProtocolChiTietProvider from "data-access/categories/dm-protocol-chi-tiet-provider";
import { Main } from "../styled";
import { toSafePromise } from "lib-utils";

const ThietLapProtocol = (props) => {
  const { protocolId, roleSave, roleEdit, editStatus } = props;
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    active: false,
    expandedRowKeys: [],
    currentItem: null,
    currentIndex: -1,
    isAddingChild: false,
    parentForNewChild: null,
    hideAddIcons: false,
    previousAction: null,
    previousState: null,
    showQuickAddPopover: false,
    quickAddForm: null,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    protocolChiTiet: { createOrEdit },
    protocolTenTruong: { createOrEdit: createOrEditProtocolTenTruong },
  } = useDispatch();

  const [dataSearchProtocolChiTiet, setDataSearchProtocolChiTiet] = useState(
    {}
  );

  const { data: listData, refetch: refetchProtocolChiTiet } = useQuery({
    queryKey: [
      "protocolChiTiet",
      "listData",
      protocolId,
      dataSearchProtocolChiTiet,
    ],
    queryFn: () => {
      return dmProtocolChiTietProvider.search({
        protocolId,
        page: 0,
        size: 2000,
        ...dataSearchProtocolChiTiet,
      });
    },
    enabled: !!protocolId,
    select: (res) => res.data,
  });

  const {
    data: listAllProtocolTenTruong,
    refetch: refetchListAllProtocolTenTruong,
  } = useQueryAll(
    query.protocolTenTruong.queryAllProtocolTenTruong({
      persist: false,
      cacheTime: 0,
      staleTime: 0,
    })
  );

  const {
    data: listProtocolTenTruong,
    isFetching: isFetchingListProtocolTenTruong,
    refetch: refetchListProtocolTenTruong,
  } = useQueryAll(
    query.protocolTenTruong.queryAllProtocolTenTruong({
      params: {
        tenTruongChaId:
          state.currentItem?.protocolTenTruong?.tenTruongChaId || "",
      },
      persist: false,
      cacheTime: 0,
      staleTime: 0,
      enabled: !!state.currentItem?.protocolTenTruong?.tenTruongChaId,
    })
  );

  const listProtocolTenTruongCha = useMemo(() => {
    return listAllProtocolTenTruong.filter(
      (item) => item.ma.indexOf(".") === -1
    );
  }, [listAllProtocolTenTruong]);

  const [listLoaiHienThi] = useEnum(ENUM.LOAI_HIEN_THI);

  const buildHierarchicalData = (data) => {
    if (!data || !Array.isArray(data)) return [];

    const sortedData = [...data].sort((a, b) => {
      const maA = a.protocolTenTruong?.ma || "";
      const maB = b.protocolTenTruong?.ma || "";
      return maA.localeCompare(maB);
    });

    const flatItems = sortedData.map((item) => {
      const ma = item.protocolTenTruong?.ma || "";
      const level = ma.split(".").length;

      return {
        ...item,
        key: item.id,
        level,
        children: null,
        parentKey: null,
        parentMa: null,
        ma,
      };
    });

    const rootItems = [];
    const itemMap = new Map();

    flatItems.forEach((item) => {
      if (!itemMap.has(item.ma)) {
        itemMap.set(item.ma, []);
      }
      itemMap.get(item.ma).push(item);
    });

    flatItems.forEach((item) => {
      if (item.isNewChild && item.parentId) {
        const parent = flatItems.find((p) => p.id === item.parentId);
        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }
          item.parentKey = parent.key;
          item.parentMa = parent.ma;
          parent.children.push(item);
          item.level = (parent.level || 1) + 1;
          return;
        }
      }

      const maParts = item.ma.split(".");
      if (maParts.length > 1) {
        const parentMa = maParts.slice(0, -1).join(".");
        const parentItems = itemMap.get(parentMa);

        if (parentItems && parentItems.length > 0) {
          const parent = parentItems[0];
          if (!parent.children) {
            parent.children = [];
          }
          item.parentKey = parent.key;
          item.parentMa = parent.ma;
          parent.children.push(item);
        } else {
          rootItems.push(item);
        }
      } else {
        rootItems.push(item);
      }
    });

    return rootItems;
  };

  const hierarchicalData = useMemo(() => {
    let dataToProcess = [...(listData || [])];

    if (state.isAddingChild && state.currentItem) {
      dataToProcess.push(state.currentItem);
    }

    if (state.currentItem?.isNewRow && !state.isAddingChild) {
      dataToProcess.unshift(state.currentItem);
    }

    return buildHierarchicalData(dataToProcess);
  }, [listData, state.isAddingChild, state.currentItem]);

  const maxNestingLevel = useMemo(() => {
    const calculateMaxLevel = (items, currentLevel = 1) => {
      let maxLevel = currentLevel;
      items.forEach((item) => {
        if (item.children && item.children.length > 0) {
          const childMaxLevel = calculateMaxLevel(
            item.children,
            currentLevel + 1
          );
          maxLevel = Math.max(maxLevel, childMaxLevel);
        }
      });
      return maxLevel;
    };
    return calculateMaxLevel(hierarchicalData);
  }, [hierarchicalData]);

  const getTenTruongColumnWidth = () => {
    const baseWidth = 300;
    const prefixWidth = maxNestingLevel * 80;
    const calculatedWidth = baseWidth + prefixWidth;
    return Math.max(baseWidth, calculatedWidth);
  };

  const getLevelPrefix = (level) => {
    if (level === 1) return "";
    if (level === 2) return "├─ ";
    if (level === 3) return "│  ├─ ";
    if (level === 4) return "│  │  ├─ ";
    if (level === 5) return "│  │  │  ├─ ";
    return "│  ".repeat(level - 2) + "├─ ";
  };

  const getAllExpandableKeys = (data) => {
    let keys = [];
    const collectKeys = (items) => {
      items.forEach((item) => {
        if (item.children && item.children.length > 0) {
          keys.push(item.key);
          collectKeys(item.children);
        }
      });
    };
    collectKeys(data);
    return keys;
  };

  useEffect(() => {
    if (hierarchicalData.length > 0) {
      const expandableKeys = getAllExpandableKeys(hierarchicalData);
      setState({ expandedRowKeys: expandableKeys });
    }
  }, [hierarchicalData]);

  useEffect(() => {
    setState({
      currentIndex: -1,
      currentItem: null,
    });
  }, [protocolId]);

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;
    setDataSearchProtocolChiTiet({
      ...dataSearchProtocolChiTiet,
      [key]: value,
    });
  };

  const onChange = (key) => (e, option) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e._d;
    else value = e;

    if (state.currentItem) {
      const updatedItem = { ...state.currentItem, [key]: value };

      if (key === "protocolTenTruongId" && value) {
        const selectedProtocolTenTruong = option;
        if (selectedProtocolTenTruong) {
          updatedItem.protocolTenTruong = {
            id: selectedProtocolTenTruong.id,
            ma: selectedProtocolTenTruong.ma,
            ten: selectedProtocolTenTruong.ten,
            tenTruongChaId: selectedProtocolTenTruong.tenTruongChaId,
          };
        }
      }

      setState({ currentItem: updatedItem });
    }
  };

  const onChangeCheckbox = (key, item, record) => (e) => {
    e.stopPropagation();
    const flatIndex = flatData.findIndex((data) => data.id === record.id);

    if (state.currentIndex !== flatIndex) return;
    const value = e.target.checked;
    const _currentItem =
      state.currentIndex === flatIndex && state.currentItem
        ? state.currentItem
        : item;
    const data = {
      currentItem: {
        ..._currentItem,
        [key]: value,
      },
      ...(state.currentIndex !== flatIndex && {
        currentIndex: flatIndex,
      }),
    };
    setState(data);
  };

  const handleQuickAddSave = async (promise) => {
    const [err, values] = await toSafePromise(promise);
    if (err) {
      return;
    }
    showLoading();
    const result = await createOrEditProtocolTenTruong(values)
      .then(() => {
        refetchListProtocolTenTruong();
        refetchListAllProtocolTenTruong();
        message.success(t("common.themMoiThanhCongDuLieu"));
      })
      .catch((err) => {
        message.error(t("common.themMoiKhongThanhCong"));
      })
      .finally(() => {
        hideLoading();
      });

    if (result && result.id) {
      const updatedItem = {
        ...state.currentItem,
        protocolTenTruongId: result.id,
        protocolTenTruong: {
          id: result.id,
          ma: result.ma,
          ten: result.ten,
        },
      };

      setState({
        currentItem: updatedItem,
        showQuickAddPopover: false,
        quickAddForm: null,
      });
    }
  };

  const handleQuickAddCancel = () => {
    setState({
      showQuickAddPopover: false,
      quickAddForm: null,
    });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("danhMuc.tenTruong")}
              <span style={{ color: "red" }}>*</span>
            </>
          }
          searchSelect={
            <Select
              data={listAllProtocolTenTruong}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("protocolTenTruongId")}
            />
          }
        />
      ),
      width: getTenTruongColumnWidth(),
      dataIndex: "protocolTenTruong",
      key: "protocolTenTruong",
      render: (item, record) => {
        const level = record.level || 1;

        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        const containerStyle = {
          display: "flex",
          alignItems: "center",
          width: "100%",
          minWidth: 0,
        };

        if (flatIndex === state.currentIndex) {
          return (
            <div style={containerStyle}>
              <Popover
                content={
                  <QuickAddForm
                    onSave={handleQuickAddSave}
                    onCancel={handleQuickAddCancel}
                    tenTruongChaId={record.protocolTenTruong?.tenTruongChaId}
                    showLoading={showLoading}
                    hideLoading={hideLoading}
                    listAllProtocolTenTruong={listAllProtocolTenTruong}
                  />
                }
                destroyTooltipOnHide={true}
                title={t("danhMuc.themMoiProtocolChung")}
                trigger="click"
                open={state.showQuickAddPopover}
                onOpenChange={(open) => {
                  if (!open) {
                    setState({
                      showQuickAddPopover: false,
                      quickAddForm: null,
                    });
                  }
                }}
                placement="bottomLeft"
              >
                <Select
                  placeholder={t("danhMuc.chonTenTruong")}
                  data={
                    state.currentItem?.protocolTenTruong?.tenTruongChaId
                      ? listProtocolTenTruong
                      : listProtocolTenTruongCha
                  }
                  onChange={onChange("protocolTenTruongId")}
                  value={state.currentItem?.protocolTenTruongId}
                  style={{
                    width: "100%",
                    minWidth: "200px",
                  }}
                  dropdownMatchSelectWidth={Math.max(
                    300,
                    getTenTruongColumnWidth()
                  )}
                  loading={isFetchingListProtocolTenTruong}
                  {...(state.showQuickAddPopover && {
                    open: false,
                  })}
                  dropdownRender={(menu) => (
                    <div>
                      {menu}
                      <div
                        style={{
                          padding: "8px 12px",
                          borderTop: "1px solid #f0f0f0",
                          cursor: "pointer",
                          display: "flex",
                          alignItems: "center",
                          gap: 8,
                        }}
                        onClick={() => {
                          setState({ showQuickAddPopover: true });
                        }}
                      >
                        <SVG.IcAdd style={{ fontSize: 14 }} />
                        <span>{t("danhMuc.themMoiProtocolChung")}</span>
                      </div>
                    </div>
                  )}
                />
              </Popover>
            </div>
          );
        } else {
          return (
            <div style={containerStyle}>
              <span
                style={{
                  fontWeight: level === 1 ? "bold" : "normal",
                  fontSize: level === 1 ? "14px" : "13px",
                  color: level === 1 ? "#262626" : "#595959",
                  lineHeight: "1.4",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "block",
                  width: "100%",
                }}
              >
                <span
                  style={{
                    color: "#8c8c8c",
                    fontFamily: "monospace",
                    fontSize: "12px",
                    marginRight: "4px",
                  }}
                >
                  {getLevelPrefix(level)}
                </span>
                {item?.ma && `[${item.ma}] `}
                {item?.ten || ""}
              </span>
            </div>
          );
        }
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("danhMuc.dangHienThi")}
              <span style={{ color: "red" }}>*</span>
            </>
          }
          searchSelect={
            <Select
              data={listLoaiHienThi}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("loaiHienThi")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "loaiHienThi",
      key: "loaiHienThi",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        if (flatIndex === state.currentIndex) {
          return (
            <Select
              placeholder={t("danhMuc.chonDangHienThi")}
              data={listLoaiHienThi}
              onChange={onChange("loaiHienThi")}
              value={state.currentItem?.loaiHienThi}
            />
          );
        } else return listLoaiHienThi?.find((i) => i.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.choPhepBoSungThemTextGhiChu")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("ghiChu")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex ? state.currentItem?.ghiChu : item
            }
            onChange={onChangeCheckbox("ghiChu", record, record)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.choPhepThucHienThatBai")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("thucHienThatBai")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "thucHienThatBai",
      key: "thucHienThatBai",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex
                ? state.currentItem?.thucHienThatBai
                : item
            }
            onChange={onChangeCheckbox("thucHienThatBai", record, record)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.macDinhChon")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("macDinh")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "macDinh",
      key: "macDinh",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex
                ? state.currentItem?.macDinh
                : item
            }
            onChange={onChangeCheckbox("macDinh", record, record)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.layLenBaoCao")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("baoCao")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "baoCao",
      key: "baoCao",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex ? state.currentItem?.baoCao : item
            }
            onChange={onChangeCheckbox("baoCao", record, record)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.batBuocKhaiBao")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("batBuoc")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "batBuoc",
      key: "batBuoc",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex
                ? state.currentItem?.batBuoc
                : item
            }
            onChange={onChangeCheckbox("batBuoc", record, record)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.coHieuLuc")}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item, record) => {
        const flatIndex = flatData.findIndex((data) => data.id === record.id);
        return (
          <Checkbox
            checked={
              flatIndex == state.currentIndex ? state.currentItem?.active : item
            }
            onChange={onChangeCheckbox("active", record, record)}
          />
        );
      },
    },
    {
      title: t("common.thaoTac"),
      width: 100,
      key: "actions",
      align: "center",
      fixed: "right",
      render: (_, record) => {
        const isNewChild = record.isNewChild;
        const isNewRow = record.isNewRow;

        return (
          <div
            style={{ display: "flex", gap: "4px", justifyContent: "center" }}
          >
            {isNewChild && checkRole(roleEdit) && (
              <Button
                type="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onCancelAddChild();
                }}
                style={{
                  color: "#ff4d4f",
                  padding: "0 4px",
                  minWidth: "auto",
                  height: "auto",
                }}
                title={t("common.huy")}
              >
                ✕
              </Button>
            )}

            {isNewRow && checkRole(roleEdit) && (
              <Button
                type="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onCancel();
                }}
                style={{
                  color: "#ff4d4f",
                  padding: "0 4px",
                  minWidth: "auto",
                  height: "auto",
                }}
                title={t("common.huy")}
              >
                ✕
              </Button>
            )}

            {!isNewChild &&
              !isNewRow &&
              checkRole(roleEdit) &&
              !state.hideAddIcons && (
                <SVG.IcAdd
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild(record);
                  }}
                />
              )}
          </div>
        );
      },
    },
  ];

  const flattenData = (data, level = 0) => {
    let result = [];
    data.forEach((item) => {
      result.push({ ...item, flatIndex: result.length });
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children, level + 1));
      }
    });
    return result;
  };

  const flatData = useMemo(() => {
    const result = flattenData(hierarchicalData);
    return result;
  }, [hierarchicalData]);

  useEffect(() => {
    if (
      state.isAddingChild &&
      state.currentItem &&
      hierarchicalData.length > 0
    ) {
      const flatData = flattenData(hierarchicalData);
      const childIndex = flatData.findIndex(
        (item) => item.id === state.currentItem.id
      );
      if (childIndex !== -1) {
        setState({ currentIndex: childIndex });
      }
    }
  }, [hierarchicalData, state.isAddingChild, state.currentItem]);

  const getRowClassName = (record) => {
    const flatIndex = flatData.findIndex((item) => item.id === record.id);
    const isActive = flatIndex === state.currentIndex;
    const isInactive = !record.active;

    return classNames({
      "row-gray": isInactive,
      "row-actived": isActive,
    });
  };

  const onExpand = (expanded, record) => {
    const key = record.key;
    let newExpandedKeys = [...state.expandedRowKeys];

    if (expanded) {
      if (!newExpandedKeys.includes(key)) {
        newExpandedKeys.push(key);
      }
    } else {
      newExpandedKeys = newExpandedKeys.filter((k) => k !== key);
    }

    setState({ expandedRowKeys: newExpandedKeys });
  };

  const onRow = (record = {}) => {
    return {
      onClick: (event) => {
        if (state.isAddingChild || state.previousAction === "onRow") {
          return;
        }

        const flatIndex = flatData.findIndex((item) => item.id === record.id);

        if (flatIndex !== -1 && state?.currentIndex !== flatIndex) {
          const previousState = {
            currentIndex: state.currentIndex,
            currentItem: state.currentItem,
            isAddingChild: state.isAddingChild,
            parentForNewChild: state.parentForNewChild,
            hideAddIcons: state.hideAddIcons,
          };

          setState({
            currentItem: cloneDeep(record),
            currentIndex: flatIndex,
            previousAction: "onRow",
            previousState: previousState,
          });
        }
      },
    };
  };

  const onAddNewRow = () => {
    const newItem = {
      id: `temp-${Date.now()}`,
      protocolId,
      protocolTenTruongId: null,
      protocolTenTruong: {
        ma: "",
        ten: "",
      },
      loaiHienThi: null,
      ghiChu: false,
      thucHienThatBai: false,
      macDinh: false,
      baoCao: false,
      batBuoc: false,
      active: true,
      isNewRow: true,
    };

    const previousState = {
      currentIndex: state.currentIndex,
      currentItem: state.currentItem,
      isAddingChild: state.isAddingChild,
      parentForNewChild: state.parentForNewChild,
      hideAddIcons: state.hideAddIcons,
      expandedRowKeys: state.expandedRowKeys,
    };

    setState({
      currentItem: newItem,
      currentIndex: 0,
      isAddingChild: false,
      parentForNewChild: null,
      hideAddIcons: true,
      previousAction: "addNewRow",
      previousState: previousState,
    });
  };

  const onAddChild = (parentRecord) => {
    const previousState = {
      currentIndex: state.currentIndex,
      currentItem: state.currentItem,
      isAddingChild: state.isAddingChild,
      parentForNewChild: state.parentForNewChild,
      hideAddIcons: state.hideAddIcons,
      expandedRowKeys: state.expandedRowKeys,
    };
    const newChild = {
      id: `temp-${Date.now()}`,
      protocolId,
      protocolTenTruongId: null,
      protocolTenTruong: {
        ma: "",
        ten: "",
        tenTruongChaId: parentRecord.protocolTenTruongId || "",
      },
      loaiHienThi: null,
      ghiChu: false,
      thucHienThatBai: false,
      macDinh: false,
      baoCao: false,
      batBuoc: false,
      active: true,
      isNewChild: true,
      parentId: parentRecord.id,
      parentMa: parentRecord.protocolTenTruong?.ma || "",
    };

    const newExpandedKeys = [...state.expandedRowKeys];
    if (!newExpandedKeys.includes(parentRecord.key)) {
      newExpandedKeys.push(parentRecord.key);
    }

    setState({
      isAddingChild: true,
      parentForNewChild: parentRecord,
      currentItem: newChild,
      currentIndex: -1,
      expandedRowKeys: newExpandedKeys,
      hideAddIcons: true,
      previousAction: "addChild",
      previousState: previousState,
    });
  };

  const onCancelAddChild = () => {
    setState({
      isAddingChild: false,
      parentForNewChild: null,
      currentItem: null,
      currentIndex: -1,
      hideAddIcons: false,
      previousAction: null,
      previousState: null,
    });
  };

  const onCancel = () => {
    if (state.isAddingChild) {
      onCancelAddChild();
    } else if (state.previousAction && state.previousState) {
      setState({
        ...state.previousState,
        previousAction: null,
        previousState: null,
      });
    } else {
      setState({
        currentIndex: -1,
        currentItem: null,
        previousAction: null,
        previousState: null,
      });
    }
  };

  const onSave = async () => {
    try {
      showLoading();

      const {
        id,
        protocolId,
        protocolTenTruongId,
        loaiHienThi,
        ghiChu,
        thucHienThatBai,
        macDinh,
        baoCao,
        batBuoc,
        active = true,
        isNewChild,
        isNewRow,
      } = state.currentItem || {};

      if (!protocolTenTruongId) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("danhMuc.tenTruong").toLowerCase(),
          })
        );
        return;
      }

      if (!loaiHienThi) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("danhMuc.dangHienThi").toLowerCase(),
          })
        );
        return;
      }

      const saveData = {
        id: isNewChild || isNewRow ? undefined : id,
        active,
        protocolId,
        protocolTenTruongId,
        loaiHienThi,
        ghiChu,
        thucHienThatBai,
        macDinh,
        baoCao,
        batBuoc,
      };

      await createOrEdit(saveData);
      refetchProtocolChiTiet();

      message.success(t("common.themMoiThanhCongDuLieu"));
    } catch (error) {
      console.error(error);
      message.error(t("common.themMoiKhongThanhCong"));
    } finally {
      hideLoading();
    }

    setState({
      currentIndex: -1,
      currentItem: null,
      isAddingChild: false,
      parentForNewChild: null,
      hideAddIcons: false,
      previousAction: null,
      previousState: null,
    });
  };

  return (
    <Main>
      <EditWrapper
        title={t("danhMuc.thietLapProtocol")}
        showAdded={true}
        roleSave={roleSave}
        roleEdit={roleEdit}
        onAddNewRow={onAddNewRow}
        onCancel={onCancel}
        onSave={onSave}
        editStatus={editStatus}
        isShowSaveButton={state.currentItem}
        isShowCancelButton={state.currentItem}
        forceShowButtonSave={checkRole(props.roleEdit)}
        forceShowButtonCancel={checkRole(props.roleEdit)}
        // isEditAndPressRow={protocolId && checkRole(roleEdit)}
      >
        <fieldset disabled={editStatus}>
          <TableWrapper
            columns={columns}
            dataSource={protocolId ? hierarchicalData : []}
            onRow={onRow}
            rowClassName={getRowClassName}
            scroll={{ x: Math.max(1200, getTenTruongColumnWidth() + 800) }}
            expandable={{
              childrenColumnName: "children",
              expandedRowKeys: state.expandedRowKeys,
              onExpand: onExpand,
              expandRowByClick: false,
              rowExpandable: (record) =>
                record.children && record.children.length > 0,
            }}
          />
        </fieldset>
      </EditWrapper>
    </Main>
  );
};

export default ThietLapProtocol;
