import React, { useEffect } from "react";
import moment from "moment";
import { Col, Row } from "antd";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { useListAll, useStore } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { openInNewTab } from "utils/index";
/**
 * PTTT10. Sổ bàn giao người bệnh vào phòng mổ
 *
 */

const PTTT10 = () => {
  const {
    baoCaoDaIn: { getPttt10 },
    khoa: { getListAllKhoa },
    phong: { getListPhongTongHop },
  } = useDispatch();

  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );
  const listPhong = useStore("phong.listPhong", []);

  useEffect(() => {
    getListAllKhoa({ page: "", size: "", active: true });
    getListPhongTongHop({ page: "", size: "", active: true })
  }, []);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.thoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("baoCao.chonNgay")}
              value={_state.thoiGian}
              onChange={onChange("thoiGian")}
              format="DD/MM/YYYY"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.thoiGian && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/khoa")}
            >
              {t("baoCao.khoaChiDinh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={(e) => {
                onChange("dsKhoaChiDinhId")(e);
              }}
              value={_state.dsKhoaChiDinhId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/khoa")}
            >
              {t("baoCao.khoaThucHien")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              // onChange={(e) => {
              //   onChange("dsKhoaThucHienId")(e);
              // }}
              onChange={(e) => {
                const value = onChange("dsKhoaThucHienId", true)(e);
                getListPhongTongHop({
                  active: true,
                  page: "",
                  size: "",
                  dsKhoaId: value,
                });
                onChange("dsPhongThucHienId", true)();
              }}
              value={_state.dsKhoaThucHienId}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/phong")}
            >
              {t("baoCao.phongThucHien")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongThucHien")}
              onChange={onChange("dsPhongThucHienId")}
              value={_state.dsPhongThucHienId}
              data={listPhong}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    thoiGian: moment(_state.thoiGian).format("DD-MM-YYYY"),
    dsKhoaThucHienId: _state.dsKhoaThucHienId,
    dsPhongThucHienId: _state.dsPhongThucHienId,
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
  });

  return (
    <BaseBaoCao
      title={t("baoCao.pttt10")}
      renderFilter={renderFilter}
      getBc={getPttt10}
      handleDataSearch={handleDataSearch}
      breadcrumb={[{ title: "PTTT10", link: "/bao-cao/pttt-10" }]}
      initState={{
        dsKhoaThucHienId: [""],
        dsPhongThucHienId: [""],
        dsKhoaChiDinhId: listAllKhoaTheoTaiKhoan?.map((item) => item.id),
        thoiGian: moment().startOf("day")
      }}
    />
  );
};

export default PTTT10;