import { Col, message, Row } from "antd";
import { Checkbox, Select, SelectLoadMore } from "components";
import moment from "moment";
import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { useTranslation } from "react-i18next";
import { useListAll, useStore, useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";
import FilterThoiGian from "pages/baocao/BaseBaoCao/components/FilterThoiGian";

/**
 * Báo cáo kho: K04. Báo cáo thẻ kho
 *
 */
const K04 = () => {
  const {
    baoCaoDaIn: { getK04 },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
  } = useDispatch();

  const listKhoUser = useStore("kho.listKhoUser");
  const [MAC_DINH_TEN_THEO_THAU_BC_KHO] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TEN_THEO_THAU_BC_KHO
  );
  const [dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KHO_HIEN_THI_THEM_HAM_LUONG
  );

  useEffect(() => {
    getKhoTheoTaiKhoan({ page: "", size: "", active: true });
  }, []);

  const [listAllKhoa] = useListAll("khoa", {}, true);
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const customChange = (name, onChange) => (e, list) => {
    if (name === "khoId") {
      setState({
        paramHangHoa: { khoId: e },
      });
      onChange("dichVuId")();
    }
    onChange(name)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <FilterThoiGian
          t={t}
          onChange={onChange}
          _state={_state}
          onKeyDownDate={onKeyDownDate}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listKhoUser || []}
              onChange={customChange("khoId", onChange)}
              value={_state.khoId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i, idx) => ({
                value: `${i.dichVuId}-${i.khoId}-${i.ten}-${idx}`,
                label: `${i.ma} - ${i.ten}${
                  dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG?.eval()
                    ? i.hamLuong
                      ? " - " + i.hamLuong
                      : ""
                    : ""
                }`,
              })}
              onChange={onChange("dichVuId")}
              keySearch={"timKiem"}
              value={_state.dichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
              // hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoNgayHoaDon}
              onChange={onChange("theoNgayHoaDon")}
            >
              {t("baoCao.theoNgayHoaDon")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.khoTaiKhoa}
              onChange={onChange("khoTaiKhoa")}
            >
              {t("baoCao.khoTaiKhoa")}
            </Checkbox>
          </div>
        </Col>
        {_state.khoTaiKhoa && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.tenKhoTaiKhoa")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKhoa")}
                data={(listAllKhoa || []).map((item) => ({
                  ...item,
                  ten: `${item.ma} - ${item.ten}`,
                }))}
                onChange={onChange("dsKhoTaiKhoaId")}
                value={_state.dsKhoTaiKhoaId}
                mode="multiple"
                defaultValue={[]}
              />
            </div>
          </Col>
        )}
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiTenTrungThau}
              onChange={onChange("hienThiTenTrungThau")}
            >
              {t("baoCao.hienThiTenHangHoaTrungThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoSlThuCap}
              onChange={onChange("theoSlThuCap")}
            >
              {t("baoCao.theoSlThuCap")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    let dichVuId = _state?.dichVuId?.split("-")[0] || null;
    return {
      loaiThoiGian: _state.loaiThoiGian,
      tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      khoId: _state.khoId,
      dichVuId: dichVuId,
      theoNgayHoaDon: _state.theoNgayHoaDon,
      khoTaiKhoa: _state.khoTaiKhoa,
      dsKhoTaiKhoaId: _state.khoTaiKhoa ? _state.dsKhoTaiKhoaId : [],
      hienThiTenTrungThau: _state.hienThiTenTrungThau,
      theoSlThuCap: _state.theoSlThuCap,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.khoId) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      if (!_state.dichVuId) {
        message.error(t("baoCao.vuiLongchonHangHoa"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k04")}
        renderFilter={renderFilter}
        beforeOk={beforeOk}
        getBc={getK04}
        handleDataSearch={handleDataSearch}
        initState={{
          hienThiTenTrungThau:
            MAC_DINH_TEN_THEO_THAU_BC_KHO?.toLowerCase() === "true",
        }}
        breadcrumb={[{ title: "K04", link: "/bao-cao/k04" }]}
      />
    </Main>
  );
};

export default K04;
