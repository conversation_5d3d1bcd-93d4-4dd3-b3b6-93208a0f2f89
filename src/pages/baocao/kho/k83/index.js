import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { Col, Row, message } from "antd";
import moment from "moment";
import { useStore } from "hooks";
import { DateTimePicker, Select, SelectLoadMore } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { useQueryAll, useListAll } from "hooks";
import { query } from "redux-store/stores";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";

const K83 = () => {
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  const {
    baoCaoDaIn: { getK83 },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
  } = useDispatch();

  const { data: listKhoTheoTaiKhoan } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan
  );
  const [listAllKho] = useListAll("kho", {}, true);
  const isAllKho = checkRole([ROLES["DANH_MUC"].XEM_DS_BAO_CAO_TAT_CA_KHO])

  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );

  useEffect(() => {
    getAllTongHopDichVuCap1({ page: "", size: "", active: true });
    getAllTongHopDichVuCap2({ page: "", size: "", active: true });
    getAllTongHopDichVuCap3({ page: "", size: "", active: true });
  }, []);

  const listKhoMemo = useMemo(() => {
    return isAllKho ? listAllKho : listKhoTheoTaiKhoan
  }, [isAllKho, listAllKho, listKhoTheoTaiKhoan])

  const onHandleChange = (key, onChange) => (value) => {
    if (key === "dsKhoId") {
      let paramHangHoa = { active: true };
      if (Array.isArray(value) && value.length > 0 && value[0] != "") {
        paramHangHoa = { ...paramHangHoa, dsKhoId: value };
      }
      setState({
        paramHangHoa,
      });
      onChange("dsDichVuId")();
    }

    if (key === "dsNhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        page: "",
        size: "",
        active: true,
        dsNhomDichVuCap1Id: value,
      });
      onChange("dsNhomDichVuCap2Id")();
    } else if (key === "dsNhomDichVuCap2Id") {
      getAllTongHopDichVuCap3({
        page: "",
        size: "",
        active: true,
        dsNhomDichVuCap2Id: value,
      });
      onChange("dsNhomDichVuCap3Id")();
    }
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("denNgay")}
              value={_state.denNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              onChange={onHandleChange("dsKhoId", onChange)}
              value={_state.dsKhoId}
              data={listKhoMemo}
              mode="multiple"
            />
            {!_state.isValidData && !_state.dsKhoId?.length && (
              <div className="error">{t("baoCao.vuiLongChonKho")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              data={listAllNhomDichVuCap1}
              onChange={onHandleChange("dsNhomDichVuCap1Id", onChange)}
              value={_state.dsNhomDichVuCap1Id}
              mode="multiple"
            />
            {!_state.isValidData && !_state.dsNhomDichVuCap1Id?.length && (
              <div className="error">
                {t("danhMuc.vuiLongChonNhomDichVuCap1")}
              </div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              data={listAllNhomDichVuCap2}
              onChange={onHandleChange("dsNhomDichVuCap2Id", onChange)}
              value={_state.dsNhomDichVuCap2Id}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap3")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap3")}
              data={listAllNhomDichVuCap3}
              onChange={onChange("dsNhomDichVuCap3Id")}
              value={_state.dsNhomDichVuCap3Id}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i) => ({
                value: `${i.dichVuId}-${i.khoId}-${i.ten}`,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsDichVuId", true)}
              keySearch={"timKiem"}
              value={_state.dsDichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    const filterList = (list, fullList) =>
      list?.length !== fullList?.length ? list : null;

    const dsDichVuId = _state.dsDichVuId?.map((item) => {
      return item?.split("-")[0];
    });
    return {
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoId: _state.dsKhoId,
      dsNhomDichVuCap1Id: filterList(
        _state.dsNhomDichVuCap1Id,
        listAllNhomDichVuCap1
      ),
      dsNhomDichVuCap2Id: filterList(
        _state.dsNhomDichVuCap2Id,
        listAllNhomDichVuCap2
      ),
      dsNhomDichVuCap3Id: filterList(
        _state.dsNhomDichVuCap3Id,
        listAllNhomDichVuCap3
      ),
      dsDichVuId,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.denNgay) {
          message.error(t("baoCao.chonDenNgay"));
          return false;
        }
        if (!_state.dsKhoId?.length) {
          message.error(t("baoCao.vuiLongChonKho"));
          return false;
        }
        if (!_state.dsNhomDichVuCap1Id?.length) {
          message.error(t("danhMuc.vuiLongChonNhomDichVuCap1"));
          return false;
        }
        return _beforeOk();
      };

  return (
    <BaseBaoCao
      title={t("baoCao.k83")}
      breadcrumb={[{ title: "K83", link: "/bao-cao/k83" }]}
      renderFilter={renderFilter}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      getBc={getK83}
    />
  );
};

export default K83;
