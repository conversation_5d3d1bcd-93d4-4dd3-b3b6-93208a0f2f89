import { Col, message, Row } from "antd";
import { DateTimePicker, Select, SelectLoadMore } from "components";
import moment from "moment";
import React, { useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { useTranslation } from "react-i18next";
import { useQueryAll, useListAll } from "hooks";
import { query } from "redux-store/stores";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";

/**
 * Báo cáo kho: K04. Báo cáo thẻ kho
 *
 */
const K04 = () => {
  const {
    baoCaoDaIn: { getK04_4 },
  } = useDispatch();

  const { data: listKhoTheoTaiKhoan } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan
  );
  const [listAllKho] = useListAll("kho", {}, true);
  const isAllKho = checkRole([ROLES["DANH_MUC"].XEM_DS_BAO_CAO_TAT_CA_KHO])

  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const listKhoMemo = useMemo(() => {
    return isAllKho ? listAllKho : listKhoTheoTaiKhoan
  }, [isAllKho, listAllKho, listKhoTheoTaiKhoan])

  const customChange = (name, onChange) => (e) => {
    if (name === "dsKhoId") {
      setState({
        paramHangHoa: { dsKhoId: e },
      });
      onChange("dichVuId")();
    }
    onChange(name)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("denNgay")}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listKhoMemo || []}
              onChange={customChange("dsKhoId", onChange)}
              value={_state.dsKhoId}
              mode="multiple"
              hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i) => ({
                value: `${i.dichVuId}-${i.khoId}-${i.ten}`,
                label: `${i.ma} - ${i.ten}`,
              })}
              onChange={onChange("dsDichVuId")}
              keySearch={"timKiem"}
              value={_state.dsDichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    return {
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoId: _state.dsKhoId,
      dsDichVuId: _state.dsDichVuId?.map((item) => item?.split("-")[0]),
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.dsKhoId) {
          message.error(t("baoCao.vuiLongChonKho"));
          return false;
        }
        return _beforeOk();
      };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k04_4")}
        renderFilter={renderFilter}
        beforeOk={beforeOk}
        getBc={getK04_4}
        handleDataSearch={handleDataSearch}
        breadcrumb={[{ title: "K04.4", link: "/bao-cao/k04_4" }]}
        initState={{
          dsKhoId: listKhoMemo.map((item) => item.id),
        }}
      />
    </Main>
  );
};

export default K04;
