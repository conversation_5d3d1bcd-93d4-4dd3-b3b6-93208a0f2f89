import React, { useMemo } from "react";
import { Col, Row } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import { selectMaTen } from "redux-store/selectors";
import { useStore, useEnum, useQueryAll } from "hooks";
import { ROLES, ENUM } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { query } from "redux-store/stores";

const Index = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc64_1 },
  } = useDispatch();

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllLoaiGiuong } = useQueryAll(
    query.loaiGiuong.queryAllLoaiGiuong
  );
  const [dataPHAN_LOAI_GIUONG] = useEnum(ENUM.PHAN_LOAI_GIUONG);
  const auth = useStore("auth.auth", {});
  const isMacDinhKhoaChiDinh = checkRole([
    ROLES["BAO_CAO"].MAC_DINH_KHOA_CHI_DINH,
  ]);

  const listLayDuLieuGiuong = useMemo(() => {
    return [
      {
        id: 1,
        ten: t("baoCao.theoPhanLoaiGiuong"),
      },
      {
        id: 2,
        ten: t("baoCao.theoLoaiGiuong"),
      },
    ];
  }, []);

  const listLoaiThoiGian = useMemo(() => {
    return [
      {
        id: 70,
        ten: t("baoCao.raVien"),
      },
      {
        id: 40,
        ten: t("baoCao.thanhToan"),
      },
      {
        id: 30,
        ten: t("baoCao.theoThoiGianThucHien"),
      },
    ];
  }, []);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
              disabledDate={(current) =>
                current && _state.denNgay && current > _state.denNgay
              }
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
              disabledDate={(current) =>
                current && _state.tuNgay && current < _state.tuNgay
              }
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("dsKhoaChiDinhId")}
              value={_state.dsKhoaChiDinhId}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
              disabled={isMacDinhKhoaChiDinh}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoKhoaChiDinh}
              onChange={onChange("theoKhoaChiDinh")}
            >
              {t("baoCao.nhomTheoKhoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.layDuLieuGiuong")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhanLoaiGiuong")}
              onChange={onChange("layDuLieuGiuong")}
              value={_state.layDuLieuGiuong}
              data={listLayDuLieuGiuong}
            />
          </div>
        </Col>
        {_state.layDuLieuGiuong === 1 && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.phanLoaiGiuong")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonPhanLoaiGiuong")}
                onChange={onChange("phanLoai")}
                value={_state.phanLoai}
                data={dataPHAN_LOAI_GIUONG}
              />
            </div>
          </Col>
        )}
        {_state.layDuLieuGiuong === 2 && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiGiuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiGiuong")}
                onChange={onChange("dsLoaiGiuongId")}
                value={_state.dsLoaiGiuongId}
                data={listAllLoaiGiuong}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
        )}
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiThoiGian")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              data={listLoaiThoiGian}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNgayThucHien}
              onChange={onChange("hienThiNgayThucHien")}
            >
              {t("baoCao.hienThiNgayThucHien")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
    theoKhoaChiDinh: _state.theoKhoaChiDinh,
    phanLoai: _state.layDuLieuGiuong === 1 ? _state.phanLoai : null,
    dsLoaiGiuongId: _state.layDuLieuGiuong === 2 ? _state.dsLoaiGiuongId : null,
    loaiThoiGian: _state.loaiThoiGian,
    hienThiNgayThucHien: _state.hienThiNgayThucHien,
  });

  return (
    <BaseBaoCao
      title={t("baoCao.tc64_1")}
      breadcrumb={[{ title: "TC64.1", link: "/bao-cao/tc64_1" }]}
      renderFilter={renderFilter}
      handleDataSearch={handleDataSearch}
      getBc={getTc64_1}
      initState={{
        dsKhoaChiDinhId: isMacDinhKhoaChiDinh ? [auth?.khoaId] : [""],
        theoKhoaChiDinh: false,
        layDuLieuGiuong: 1,
        hienThiNgayThucHien: false,
      }}
    />
  );
};

export default Index;
