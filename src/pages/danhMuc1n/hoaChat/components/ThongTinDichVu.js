import React, { forwardRef, useState, useEffect, useMemo, useRef } from "react";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { Select, Checkbox, SelectLoadMore } from "components";
import { Form, Input, InputNumber } from "antd";
import { useDispatch } from "react-redux";
import {
  openInNewTab,
  parseFloatNumberFromString,
  formatNumberInput,
} from "utils/index";
import { checkRole } from "lib-utils/role-utils";
import { useEnum, useListAll, useStore, useThietLap } from "hooks";
import { ENUM, LOAI_DICH_VU, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { useTranslation } from "react-i18next";
import { InputNumberFormat } from "components/common";
import { orderBy } from "lodash";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";

function FormServiceInfo(props, ref) {
  const { layerId, refCallbackSave = {}, roleSave, roleEdit } = props;
  const { t } = useTranslation();
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);
  const [listAllNhomDichVuCap3] = useListAll("nhomDichVuCap3", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllPhanLoaiVatTu] = useListAll("phanLoaiVatTu", { loaiDichVu: LOAI_DICH_VU.HOA_CHAT }, true);
  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const listAllXuatXu = useStore("xuatXu.listAllXuatXu", []);
  const listAllMaPhieuLinh = useStore("maPhieuLinh.listAllMaPhieuLinh", []);
  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);
  const listMeGrLv1 = useStore("nhomDichVuKho.listMeGrLv1", []);
  const listMeGrLv2 = useStore("nhomDichVuKho.listMeGrLv2", []);
  const currentItem = useStore("danhMucHoaChat.currentItem");

  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [listLoaiLamTron] = useEnum(ENUM.LOAI_LAM_TRON);
  const [listNhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listGoiThau] = useEnum(ENUM.GOI_THAU);

  const [dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT
  );

  const listnguonKhacChiTra = useMemo(() => {
    return orderBy(listAllNguonKhacChiTra || [], (a) => a.ma, "asc").map(
      (o) => ({
        ...o,
        ten: `${o.ma} - ${o.ten}`,
      })
    );
  }, [listAllNguonKhacChiTra]);

  const {
    danhMucHoaChat: { createOrEdit },
    maPhieuLinh: { getListAllMaPhieuLinh },
    nhomDichVuKho: { getListMeGrLv2TongHop },
  } = useDispatch();

  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  let editStatus = useMemo(() => props?.editStatus, [props?.editStatus]);

  useEffect(() => {
    form.resetFields();
    loadCurrentItem(currentItem);
  }, [currentItem]);

  useEffect(() => {
    getListAllMaPhieuLinh({
      page: "",
      size: "",
      active: true,
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
  }, []);

  const [form] = Form.useForm();

  const onChange = (key1, key2) => (e, list) => {
    setState({ data: { ...state.data, [key1]: e, [key2]: list } });
  };

  const loadCurrentItem = (item) => {
    if (item && Object.keys(item).length) {
      const {
        dichVu: {
          donViTinhId,
          nguonKhacId,
          giaBaoHiem,
          giaKhongBaoHiem,
          giaPhuThu,
          khongTinhTien,
          ma,
          maTuongDuong,
          nhomChiPhiBh,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          ten,
          tenTuongDuong,
          tyLeBhTt,
          tyLeTtDv,
          chiDinhSlLe,
          mienPhiGiamDocDuyet,
          maTckt,
          tenTckt,
        } = {},
        id,
        dsDoiTuongSuDung,
        active,
        nhomDvKhoCap1Id,
        nhomDvKhoCap2Id,
        nhaCungCapId,
        nhaSanXuatId,
        quyCach,
        giaNhapSauVat,
        giaTran,
        dsMucDichSuDung,
        xuatXuId,
        tranBaoHiem,
        soNgayCanhBaoHsd,
        quyetDinhThau,
        phieuLinhId,
        tenTrungThau,
        maSinhPham,
        maSinhHieu,
        loaiLamTron,
        dvtSoCapId,
        heSoDinhMuc,
        nhomThau,
        goiThau,
        thongTinThau,
        tenMoiThau,
        tenHoatChat,
        thongSoKyThuat,
        maKyHieu,
        soVisa,
        dsDichVuThayTheId,
        dsDichVuThayThe,
        phanLoaiDvKhoId
      } = item || {};
      const data = {
        id,
        donViTinhId,
        giaBaoHiem,
        giaKhongBaoHiem,
        giaPhuThu,
        khongTinhTien,
        ma,
        maTuongDuong,
        nhomChiPhiBh,
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        ten,
        tenTuongDuong,
        tyLeBhTt,
        tyLeTtDv,
        nguonKhacId: nguonKhacId || null,
        dsDoiTuongSuDung: dsDoiTuongSuDung || [],
        active: active !== undefined ? active : true,
        nhomDvKhoCap1Id,
        nhomDvKhoCap2Id,
        nhaCungCapId,
        nhaSanXuatId,
        quyCach,
        giaNhapSauVat,
        giaTran,
        dsMucDichSuDung,
        xuatXuId,
        tranBaoHiem,
        soNgayCanhBaoHsd,
        quyetDinhThau,
        phieuLinhId,
        chiDinhSlLe,
        tenTrungThau,
        maSinhPham,
        maSinhHieu,
        mienPhiGiamDocDuyet,
        loaiLamTron,
        maTckt,
        tenTckt,
        soVisa,
        dsDichVuThayTheId: dsDichVuThayTheId || [],
        dvtSoCapId,
        heSoDinhMuc,
        nhomThau,
        goiThau,
        thongTinThau,
        tenMoiThau,
        tenHoatChat,
        thongSoKyThuat,
        maKyHieu,
        dsDichVuThayThe,
        phanLoaiDvKhoId
      };

      form.setFieldsValue(data);
      setState({
        data: data,
      });
    } else {
      form.resetFields();
      setState({
        data: null,
      });
    }
  };

  const onAddNewRow = () => {
    loadCurrentItem({});
  };

  const onCancel = () => {
    if (currentItem?.id) {
      loadCurrentItem({ ...currentItem });
    } else {
      loadCurrentItem({});
      form.resetFields();
    }
  };
  const onSave = (e) => {
    e.preventDefault();
    form.submit();
  };
  refCallbackSave.current = onSave;

  const onUpdateData = (item, type) => {
    if (type === "tyLeBhTt" || type === "tyLeTtDv") {
      form.setFieldsValue({ [type]: formatNumberInput(item).slice(0, 3) });
    } else {
      form.setFieldsValue({ [type]: item });
    }
  };

  const onHandleSubmit = (values) => {
    const {
      donViTinhId,
      khongTinhTien,
      ma,
      maTuongDuong,
      nhomChiPhiBh,
      nhomDichVuCap1Id,
      nhomDichVuCap2Id,
      nhomDichVuCap3Id,
      ten,
      tenTuongDuong,
      tyLeBhTt,
      tyLeTtDv,
      nguonKhacId,
      giaNhapSauVat,
      giaTran,
      nhomDvKhoCap1Id,
      nhomDvKhoCap2Id,
      nhaCungCapId,
      nhaSanXuatId,
      quyCach,
      tenHoatChat,
      tranBaoHiem,
      xuatXuId,
      dsMucDichSuDung,
      soNgayCanhBaoHsd,
      quyetDinhThau,
      phieuLinhId,
      chiDinhSlLe,
      tenTrungThau,
      active,
      maSinhHieu,
      maSinhPham,
      mienPhiGiamDocDuyet,
      loaiLamTron,
      maTckt,
      tenTckt,
      soVisa,
      dsDichVuThayTheId,
      dvtSoCapId,
      heSoDinhMuc,
      nhomThau,
      goiThau,
      thongTinThau,
      tenMoiThau,
      thongSoKyThuat,
      maKyHieu,
      phanLoaiDvKhoId
    } = values;
    values = {
      dichVu: {
        donViTinhId,
        khongTinhTien,
        ma,
        maTuongDuong,
        nhomChiPhiBh,
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        ten,
        tenTuongDuong,
        tyLeBhTt,
        tyLeTtDv: parseFloatNumberFromString(tyLeTtDv),
        loaiDichVu: 110,
        nguonKhacId: nguonKhacId || null,
        chiDinhSlLe,
        mienPhiGiamDocDuyet,
        maTckt,
        tenTckt,
      },
      dvtSoCapId: donViTinhId,
      active,
      giaNhapSauVat: parseFloatNumberFromString(giaNhapSauVat),
      giaTran: parseFloatNumberFromString(giaTran),
      nhomDvKhoCap1Id,
      nhomDvKhoCap2Id,
      nhaCungCapId,
      nhaSanXuatId,
      xuatXuId,
      quyCach,
      tenHoatChat,
      tranBaoHiem: parseFloatNumberFromString(tranBaoHiem),
      dsMucDichSuDung: dsMucDichSuDung?.length > 0 ? dsMucDichSuDung : [10, 20],
      soNgayCanhBaoHsd,
      quyetDinhThau,
      phieuLinhId,
      tenTrungThau,
      id: state.data?.id,
      maSinhHieu,
      maSinhPham,
      loaiLamTron,
      dvtSoCapId,
      heSoDinhMuc,
      nhomThau,
      goiThau,
      thongTinThau,
      tenMoiThau,
      thongSoKyThuat,
      maKyHieu,
      soVisa,
      dsDichVuThayTheId,
      phanLoaiDvKhoId
    };
    createOrEdit(values).then((res) => {
      form.resetFields();
    });
  };

  const validatorPrice = (rule, value, callback) => {
    if (value && String(value).length > 15) {
      callback(
        new Error(
          `${t("danhMuc.vuiLongNhapKhongQuaNumKyTu", {
            num: 15,
          })}!`
        )
      );
    } else {
      callback();
    }
  };

  return (
    <EditWrapper
      title={t("common.thongTinDichVu")}
      onCancel={onCancel}
      onSave={onSave}
      onAddNewRow={onAddNewRow}
      roleSave={roleSave}
      roleEdit={roleEdit}
      editStatus={editStatus}
      forceShowButtonSave={checkRole(roleEdit) && true}
      forceShowButtonCancel={checkRole(roleEdit) && true}
      isHiddenButtonAdd={true}
      layerId={layerId}
    >
      <fieldset disabled={editStatus}>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
          initialValues={{
            tyLeTtDv: 100,
            heSoDinhMuc: 1,
          }}
        >
          <Form.Item
            label={t("danhMuc.maHoaChat")}
            name="ma"
            rules={
              dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()
                ? []
                : [
                  {
                    required: true,
                    message: `${t("danhMuc.vuiLongNhapMaHoaChat")}`,
                  },
                  {
                    max: 200,
                    message: t("danhMuc.vuiLongNhapMaHoaChatKhongQua200KyTu"),
                  },
                  {
                    whitespace: true,
                    message: `${t("danhMuc.vuiLongNhapMaHoaChat")}`,
                  },
                ]
            }
          >
            <Input
              autoFocus={true}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaHoaChat")}
              disabled={!!dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()}
            />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.toDieuTri.tenHoaChat")}
            name="ten"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongNhapTenHoaChat")}`,
              },
              {
                max: 1000,
                message: t("danhMuc.vuiLongNhapTenHoaChatKhongQua1000KyTu"),
              },
              {
                whitespace: true,
                message: `${t("danhMuc.vuiLongNhapTenHoaChat")}`,
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenHoaChat")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-hoa-chat")}
              >
                {t("danhMuc.nhomHoaChatCap1")}
              </div>
            }
            name="nhomDvKhoCap1Id"
          >
            <Select
              data={listMeGrLv1}
              placeholder={t("danhMuc.chonNhomHoaChatCap1")}
              onChange={(e) => {
                form.setFieldsValue({
                  nhomDvKhoCap2Id: null,
                });
                const params = {
                  page: "",
                  size: "",
                  active: true,
                  sort: "ten,asc",
                  loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
                };
                if (e) {
                  getListMeGrLv2TongHop({
                    ...params,
                    nhomDvKhoCap1Id: e,
                  });
                } else {
                  getListMeGrLv2TongHop({ ...params });
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.nhomHoaChatCap2")}
            name="nhomDvKhoCap2Id"
          >
            <Select
              data={listMeGrLv2}
              placeholder={t("danhMuc.chonNhomHoaChatCap2")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.donViSoCap")} name="dvtSoCapId">
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViSoCap")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViThuCap")}
              </div>
            }
            name="donViTinhId"
          >
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViThuCap")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.heSoDinhMuc")} name="heSoDinhMuc">
            <InputNumber
              className="input-option"
              placeholder={t("danhMuc.nhapHeSoDinhMuc")}
              min={1}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/xuat-xu")}
              >
                {t("danhMuc.nuocSanXuat")}
              </div>
            }
            name="xuatXuId"
          >
            <Select
              data={listAllXuatXu}
              placeholder={t("danhMuc.chonNuocSanXuat")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("common.nhaSanXuat")}
              </div>
            }
            name="nhaSanXuatId"
          >
            <Select
              data={listAllNhaSanXuat}
              placeholder={t("danhMuc.chonNhaSanXuat")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("danhMuc.nhaCungCap")}
              </div>
            }
            name="nhaCungCapId"
          >
            <Select
              data={listAllNhaCungCap}
              placeholder={t("danhMuc.chonNhaCungCap")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.quyCach")} name="quyCach">
            <Input
              className="input-option"
              placeholder={t("kho.quyetDinhThau.vuiLongNhapQuyCach")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.giaNhap")}
            name="giaNhapSauVat"
            rules={[{ validator: validatorPrice }]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapGiaNhap")}
              allowNegative={false}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.giaTran")}
            name="giaTran"
            rules={[{ validator: validatorPrice }]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapGiaTran")}
              allowNegative={false}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.tranBaoHiem")}
            name="tranBaoHiem"
            rules={[{ validator: validatorPrice }]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTranBaoHiem")}
              allowNegative={false}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.tyLeBhThanhToan")} name="tyLeBhTt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTyLeBhThanhToan")}
              onChange={(e) => {
                onUpdateData(e.target.value, "tyLeBhTt");
              }}
              disabled={
                currentItem?.id &&
                !checkRole([
                  ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_BAO_HIEM,
                ])
              }
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.tyLeThanhToanDV")}
            name="tyLeTtDv"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongNhapTyLeThanhToanDV")}`,
              },
            ]}
          >
            <InputNumberFormat
              allowNegative={false}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTyLeThanhToanDV")}
              disabled={
                currentItem?.id &&
                !checkRole([ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_DICH_VU])
              }
              decimalScale={0}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=1")}
              >
                {t("danhMuc.nhomDVCap1")}
              </div>
            }
            name="nhomDichVuCap1Id"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonNhomDvCap1"),
              },
            ]}
          >
            <Select
              data={listAllNhomDichVuCap1}
              placeholder={t("danhMuc.chonNhomDvCap1")}
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongChonNhomDvCap1"),
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=2")}
              >
                {t("danhMuc.nhomDVCap2")}
              </div>
            }
            name="nhomDichVuCap2Id"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonNhomDvCap2"),
              },
            ]}
          >
            <Select
              data={listAllNhomDichVuCap2}
              placeholder={t("danhMuc.chonNhomDVCap2")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.nhomDVCap3")}
              </div>
            }
            name="nhomDichVuCap3Id"
          >
            <Select
              data={listAllNhomDichVuCap3}
              placeholder={t("danhMuc.chonNhomDVCap3")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.maTuongDuong")} name="maTuongDuong">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaTuongDuong")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.quyetDinhThau")}
            name="quyetDinhThau"
            rules={[
              {
                max: 300,
                message: t("danhMuc.khongDuocNhapQuaNumKyTu", {
                  num: 300,
                }),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapQuyetDinhThau")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.tenTuongDuong")} name="tenTuongDuong">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenTuongDuong")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div className="color">
                {t("kho.quyetDinhThau.tenHangHoaTrungThau")}{" "}
              </div>
            }
            name="tenTrungThau"
          >
            <Input
              className="input-option"
              placeholder={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.mucDichSuDung")} name="dsMucDichSuDung">
            <Select
              mode="multiple"
              data={listDsMucDichSuDung}
              placeholder={t("danhMuc.chonMucDichSuDung")}
            />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.nhomThau")}</div>}
            name="nhomThau"
          >
            <Select
              placeholder={t("danhMuc.chonNhomThau")}
              data={listNhomThau}
            />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.goiThau")}</div>}
            name="goiThau"
          >
            <Select data={listGoiThau} placeholder={t("danhMuc.chonGoiThau")} />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.thongTinThau")}</div>}
            name="thongTinThau"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.thongTinThau")}
            />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.tenMoiThau")}</div>}
            name="tenMoiThau"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.tenMoiThau")}
            />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.tenHoatChat")}</div>}
            name="tenHoatChat"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.tenHoatChat")}
            />
          </Form.Item>
          <Form.Item
            label={<div className="color">{t("danhMuc.thongSoKyThuat")}</div>}
            name="thongSoKyThuat"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.thongSoKyThuat")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div className="color">{t("danhMuc.maKyHieuTenThuongMai")}</div>
            }
            name="maKyHieu"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maKyHieuTenThuongMai")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.nguonChiTraKhac")} name="nguonKhacId">
            <Select
              data={listnguonKhacChiTra}
              placeholder={t("danhMuc.chonNguonChiTraKhac")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.soNgayCanhBaoHsd")}
            name="soNgayCanhBaoHsd"
          >
            <InputNumber
              min={0}
              className="input-option"
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.loaiLamTron")} name="loaiLamTron">
            <Select
              placeholder={t("danhMuc.loaiLamTron")}
              data={listLoaiLamTron}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.maPhieuLinh")} name="phieuLinhId">
            <Select
              placeholder={t("danhMuc.maPhieuLinh")}
              data={listAllMaPhieuLinh}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.maSinhPham")}
            name="maSinhPham"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maSinhPham")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.maHieuSinhPham")}
            name="maSinhHieu"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maHieuSinhPham")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.maTckt")} name="maTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaTckt")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.tenTckt")} name="tenTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenTckt")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.soVisa")} name="soVisa">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapSoVisa")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.maHoaChatThayThe")}
            name="dsDichVuThayTheId"
          >
            <SelectLoadMore
              placeholder={t("danhMuc.maHoaChatThayThe")}
              api={dmDichVuKhoProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma} - ${i.ten}`,
                ten: `${i.ma} - ${i.ten}`,
              })}
              onChange={onChange("dsDichVuThayTheId", "dsDichVuThayThe")}
              uniqByKey="value"
              addValue={
                state.data?.dsDichVuThayThe?.length > 0
                  ? state.data?.dsDichVuThayThe.map((i) => ({
                    value: i.id,
                    label: `${i.ma} - ${i.ten}`,
                  }))
                  : undefined
              }
              value={state.data?.dsDichVuThayThe?.map((i) => i.id)}
              addParam={{ dsLoaiDichVu: [LOAI_DICH_VU.HOA_CHAT] }}
              keySearch={"ma"}
              mode="multiple"
            ></SelectLoadMore>
          </Form.Item>
          <Form.Item label={t("danhMuc.phanLoaiHoaChat")} name="phanLoaiDvKhoId">
            <Select
              placeholder={t("danhMuc.chonPhanLoaiVTYT")}
              data={listAllPhanLoaiVatTu}
            />
          </Form.Item>
          <Form.Item label=" " name="khongTinhTien" valuePropName="checked">
            <Checkbox>{t("danhMuc.khongTinhTien")}</Checkbox>
          </Form.Item>
          <Form.Item label=" " name="chiDinhSlLe" valuePropName="checked">
            <Checkbox>{t("danhMuc.choPhepKeSlLe")}</Checkbox>
          </Form.Item>
          <Form.Item
            label=" "
            name="mienPhiGiamDocDuyet"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.mienPhiGiamDocDuyet")}</Checkbox>
          </Form.Item>
          {state.data?.id && (
            <Form.Item label=" " name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </Form>
      </fieldset>
    </EditWrapper>
  );
}

export default forwardRef(FormServiceInfo);
