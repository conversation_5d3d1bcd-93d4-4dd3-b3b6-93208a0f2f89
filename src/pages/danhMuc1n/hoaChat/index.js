import React, { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import { formatNumber } from "utils";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  HomeWrapper,
  Select,
  Button,
} from "components";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  HIEU_LUC,
  KHONG_TINH_TIEN,
  ENUM,
  YES_NO,
  ROLES,
  HOTKEY,
  LOAI_DICH_VU,
} from "constants/index";
import { Col, Input, Form, InputNumber } from "antd";
import { checkRole } from "lib-utils/role-utils";
import MultiLevelTab from "components/MultiLevelTab";
import ThongTinDichVu from "./components/ThongTinDichVu";
import NhomChiPhi from "components/DanhMuc/NhomChiPhi";
import { useEnum, useGuid, useListAll, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import isNil from "lodash/isNil";

const { Setting } = TableWrapper;

const HoaChat = (props) => {
  const { t } = useTranslation();
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const refSelectRow = useRef();
  const refSettings = useRef(null);

  const {
    danhMucHoaChat: {
      onSearch,
      updateData,
      onChangeInputSearch,
      onSortChange,
      onSizeChange,
      onImport,
      onExport,
    },
    donViTinh: { getListAllDonViTinh },
    nhomVatTu: { getListSuppliesGroupTongHop },
    doiTac: { getListAllNhaSanXuat, getListAllNhaCungCap },
    xuatXu: { getListAllXuatXu },
    nhomDichVuKho: { getListMeGrLv1TongHop, getListMeGrLv2TongHop },
  } = useDispatch();

  const {
    listData,
    totalElements,
    page,
    size,
    dataEditDefault,
    dataSearch,
    dataSortColumn,
    currentItem,
  } = useStore(
    "danhMucHoaChat",
    {},
    {
      fields:
        "listData,totalElements,page,size,dataEditDefault,dataSearch,dataSortColumn,currentItem",
    }
  );
  const [listNhomChiPhiBh] = useEnum(ENUM.NHOM_CHI_PHI);
  const [listNguonKhacChiTra] = useEnum(ENUM.NGUON_KHAC_CHI_TRA);
  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [listLoaiLamTron] = useEnum(ENUM.LOAI_LAM_TRON);
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const [listAllPhanLoaiVatTu] = useListAll("phanLoaiVatTu", { loaiDichVu: LOAI_DICH_VU.HOA_CHAT }, true);
  const listMeGrLv1 = useStore("nhomDichVuKho.listMeGrLv1", []);
  const listMeGrLv2 = useStore("nhomDichVuKho.listMeGrLv2", []);
  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);

  const [editStatus, setEditStatus] = useState(false);
  const [collapseStatus, setCollapseStatus] = useState(false);
  const [state, _setState] = useState({
    showFullTable: false,
  });
  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useEffect(() => {
    const params = {
      page,
      size,
      sort: dataSortColumn,
      ...dataSearch,
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    };
    onSearch(params);
    getListSuppliesGroupTongHop({
      page: "",
      size: "",
      active: true,
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
    getListAllDonViTinh({ page: "", size: "", active: true });
    getListAllNhaSanXuat({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [10],
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
    getListAllNhaCungCap({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [20],
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
    getListMeGrLv1TongHop({
      active: true,
      page: "",
      size: "",
      sort: "ten,asc",
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
    getListMeGrLv2TongHop({
      active: true,
      page: "",
      size: "",
      sort: "ten,asc",
      loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
    });
    getListAllXuatXu({ page: "", size: "", active: true });
  }, []);

  // register layerId
  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            if (refSelectRow.current && e.target.nodeName != "INPUT")
              refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN, //down
          onEvent: (e) => {
            if (refSelectRow.current && e.target.nodeName != "INPUT")
              refSelectRow.current(1);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
      updateData({ currentItem: {} });
    };
  }, []);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (listData?.findIndex((item) => item.id === state.currentItem?.id) || 0) +
      index;
    if (-1 < indexNextItem && indexNextItem < listData.length) {
      onShowAndHandleUpdate(listData[indexNextItem]);
      setState({ currentItem: listData[indexNextItem] });
      document
        .getElementsByClassName("row-id-" + listData[indexNextItem]?.id)[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const onClickSort = (key, value) => {
    onSortChange(
      {
        [key]: value,
      },
      110
    );
  };

  const getNhomChiPhi = (item) => {
    let res = listNhomChiPhiBh.filter((el) => el.id === item);
    return (res.length && res[0]) || {};
  };

  const refTimeOut = useRef(null);
  const onSearchInput = (key) => (e) => {
    if (key === "nhomDvKhoCap1Id") {
      const params = {
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
      };
      if (e) {
        getListMeGrLv2TongHop({
          ...params,
          nhomDvKhoCap1Id: e,
        });
      } else {
        getListMeGrLv2TongHop({ ...params });
      }
    }
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    refTimeOut.current = setTimeout(
      (key, s) => {
        let value = "";
        if (s) {
          if (s?.hasOwnProperty("checked")) value = s?.checked;
          else value = s?.value;
        } else value = e;
        onChangeInputSearch(
          {
            [key]: value,
          },
          110
        );
      },
      500,
      key,
      e?.target
    );
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.stt")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 60,
      dataIndex: "index",
      key: "index",
      fixed: "left",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maHoaChat")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ma"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaHoaChat")}
              onChange={onSearchInput("dichVu.ma")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.ma",
      fixed: "left",
      i18Name: "danhMuc.maHoaChat",
      show: true,
      render: (item) => {
        return item?.ma;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.tenHoaChat")}
          sort_key="dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenHoaChat")}
              onChange={onSearchInput("dichVu.ten")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.ten",
      fixed: "left",
      i18Name: "quanLyNoiTru.toDieuTri.tenHoaChat",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donViSoCap")}
          sort_key="dvtSoCapId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dvtSoCapId"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.timDonVi")}
              onChange={onSearchInput("dvtSoCapId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dvtSoCap",
      key: "dvtSoCapId",
      i18Name: "danhMuc.donViSoCap",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donViThuCap")}
          sort_key="dichVu.donViTinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.donViTinhId"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.timDonVi")}
              onChange={onSearchInput("dichVu.donViTinhId")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.donViTinhId",
      i18Name: "danhMuc.donViThuCap",
      show: true,
      render: (item) => {
        return item?.donViTinh?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.heSoDinhMuc")}
          sort_key="heSoDinhMuc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.heSoDinhMuc || 0}
          search={
            <InputNumber
              placeholder={t("danhMuc.timHeSoDinhMuc")}
              onChange={onSearchInput("heSoDinhMuc")}
              min={1}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "heSoDinhMuc",
      key: "heSoDinhMuc",
      i18Name: "danhMuc.heSoDinhMuc",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomHoaChatCap1")}
          sort_key="nhomDvKhoCap1.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap1.ten"] || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listMeGrLv1}
              placeholder={t("danhMuc.chonNhomHoaChatCap1")}
              onChange={onSearchInput("nhomDvKhoCap1Id")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "nhomDvKhoCap1",
      key: "nhomDvKhoCap1",
      i18Name: "danhMuc.nhomHoaChatCap1",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomHoaChatCap2")}
          sort_key="nhomDvKhoCap2.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap2.ten"] || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listMeGrLv2}
              placeholder={t("danhMuc.chonNhomHoaChatCap2")}
              onChange={onSearchInput("nhomDvKhoCap2Id")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "nhomDvKhoCap2",
      key: "nhomDvKhoCap2",
      i18Name: "danhMuc.nhomHoaChatCap2",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nuocSanXuat")}
          sort_key="xuatXu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["xuatXu.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timNuocSanXuat")}
              onChange={onSearchInput("tenXuatXu")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "xuatXu",
      key: "xuatXu",
      i18Name: "danhMuc.nuocSanXuat",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.nhaSanXuat")}
          sort_key="nhaSanXuat.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaSanXuat.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timNhaSanXuat")}
              onChange={onSearchInput("nuocSanXuat.ten")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "nhaSanXuat",
      key: "nhaSanXuat",
      i18Name: "common.nhaSanXuat",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhaCungCap")}
          sort_key="nhaCungCap.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaCungCap.ten"] || 0}
          search={
            <InputNumber
              placeholder={t("danhMuc.timNhaCungCap")}
              onChange={onSearchInput("nhaCungCap.ten")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "nhaCungCap",
      key: "nhaCungCap",
      i18Name: "danhMuc.nhaCungCap",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.quyCach")}
          sort_key="quyCach"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.quyCach || 0}
          search={
            <Input
              placeholder={t("danhMuc.timQuyCach")}
              onChange={onSearchInput("quyCach")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "quyCach",
      key: "quyCach",
      i18Name: "danhMuc.quyCach",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.giaNhap")}
          sort_key="giaNhapSauVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.giaNhapSauVat || 0}
          search={
            <InputNumber
              placeholder={t("danhMuc.timGiaNhap")}
              onChange={onSearchInput("giaNhapSauVat")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "giaNhapSauVat",
      key: "giaNhapSauVat",
      align: "right",
      i18Name: "danhMuc.giaNhap",
      show: true,
      render: (field) => (isNil(field) ? "" : formatNumber(field)),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.giaTran")}
          sort_key="giaTran"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.giaTran || 0}
          search={
            <InputNumber
              placeholder={t("danhMuc.timGiaTran")}
              onChange={onSearchInput("giaTran")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "giaTran",
      align: "right",
      key: "giaTran",
      i18Name: "danhMuc.giaTran",
      show: true,
      render: (field) => (isNil(field) ? "" : formatNumber(field)),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tranBaoHiem")}
          sort_key="tranBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tranBaoHiem || 0}
          search={
            <Input
              placeholder={t("kho.quyetDinhThau.timTranBaoHiem")}
              onChange={onSearchInput("tranBaoHiem")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "tranBaoHiem",
      key: "tranBaoHiem",
      align: "right",
      i18Name: "danhMuc.tranBaoHiem",
      show: true,
      render: (field) => (isNil(field) ? "" : formatNumber(field)),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomChiPhi")}
          sort_key="dichVu.nhomChiPhiBh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomChiPhiBh"] || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listNhomChiPhiBh}
              onChange={onSearchInput("dichVu.nhomChiPhiBh")}
              placeholder={t("danhMuc.chonNhomChiPhi")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.nhomChiPhiBh",
      i18Name: "danhMuc.nhomChiPhi",
      show: true,
      render: (item) => {
        return getNhomChiPhi(item?.nhomChiPhiBh).ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tyLeBhThanhToan")}
          sort_key="dichVu.tyLeBhTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeBhTt"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTyLeBhThanhToan")}
              onChange={onSearchInput("dichVu.tyLeBhTt")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.tyLeBhTt",
      align: "right",
      i18Name: "danhMuc.tyLeBhThanhToan",
      show: true,
      render: (item) => {
        return item?.tyLeBhTt;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tyLeThanhToanDV")}
          sort_key="dichVu.tyLeTtDv"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeTtDv"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTyLeThanhToanDV")}
              onChange={onSearchInput("dichVu.tyLeTtDv")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.tyLeTtDv",
      align: "right",
      i18Name: "danhMuc.tyLeThanhToanDV",
      show: true,
      render: (item) => {
        return item?.tyLeTtDv;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap1")}
          sort_key="dichVu.nhomDichVuCap1.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap1.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timNhomDvCap1")}
              onChange={onSearchInput("tenNhomDichVuCap1")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "tenNhomDichVuCap1",
      i18Name: "danhMuc.nhomDVCap1",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap1?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.dvNoiTru.nhomDvCap2")}
          sort_key="dichVu.nhomDichVuCap2.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap2.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timNhomDvCap2")}
              onChange={onSearchInput("tenNhomDichVuCap2")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "tenNhomDichVuCap2",
      i18Name: "quanLyNoiTru.dvNoiTru.nhomDvCap2",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap2?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap3")}
          sort_key="dichVu.nhomDichVuCap3.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap3.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timNhomDvCap3")}
              onChange={onSearchInput("tenNhomDichVuCap3")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "tenNhomDichVuCap3",
      i18Name: "danhMuc.nhomDVCap3",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap3?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maTuongDuong")}
          sort_key="dichVu.maTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaTuongDuong")}
              onChange={onSearchInput("maTuongDuong")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "maTuongDuong",
      i18Name: "danhMuc.maTuongDuong",
      show: true,
      render: (item) => {
        return item?.maTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapQuyetDinhThau")}
              onChange={onSearchInput("quyetDinhThau")}
            />
          }
          title={t("danhMuc.quyetDinhThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      i18Name: "danhMuc.quyetDinhThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenTuongDuong")}
          sort_key="dichVu.tenTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenTuongDuong")}
              onChange={onSearchInput("tenTuongDuong")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "tenTuongDuong",
      i18Name: "danhMuc.tenTuongDuong",
      show: true,
      render: (item) => {
        return item?.tenTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="tenTrungThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenTrungThau"] || 0}
          search={
            <Input
              placeholder={t("kho.quyetDinhThau.nhapTenTrungThau")}
              onChange={onSearchInput("tenTrungThau")}
            />
          }
          title={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "tenTrungThau",
      key: "tenTrungThau",
      i18Name: "kho.quyetDinhThau.tenHangHoaTrungThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.mucDichSuDung")}
          sort_key="dsMucDichSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsMucDichSuDung"] || 0}
          searchSelect={
            <Select
              data={listDsMucDichSuDung}
              placeholder={t("danhMuc.chonMucDichSuDung")}
              onChange={onSearchInput("dsMucDichSuDung")}
              hasAllOption={true}
              mode="multiple"
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "dsMucDichSuDung",
      key: "dsMucDichSuDung",
      i18Name: "danhMuc.mucDichSuDung",
      show: true,
      render: (item) => {
        let arr = [];
        if (item?.length && listDsMucDichSuDung) {
          item.forEach((mucDichSuDungItem) => {
            const tenMucDIchSuDung = listDsMucDichSuDung.find(
              (item) => item.id === mucDichSuDungItem
            );
            if (tenMucDIchSuDung) arr.push(tenMucDIchSuDung.ten);
          });
        }
        return arr.join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenHoatChat")}
          sort_key="tenHoatChat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenHoatChat"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenHoatChat")}
              onChange={onSearchInput("tenHoatChat")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "tenHoatChat",
      key: "tenHoatChat",
      i18Name: "danhMuc.tenHoatChat",
      show: true,
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nguonChiTraKhac")}
          sort_key="dichVu.dsNguonKhacChiTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.dsNguonKhacChiTra"] || 0}
          searchSelect={
            <Select
              data={listNguonKhacChiTra}
              placeholder={t("danhMuc.chonNguonChiTraKhac")}
              onChange={onSearchInput("dichVu.dsNguonKhacChiTra")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "dichVu",
      key: "dichVu.dsNguonKhacChiTra",
      i18Name: "danhMuc.nguonChiTraKhac",
      show: true,
      render: (item) => {
        if ((listNguonKhacChiTra || [])?.length) {
          let list =
            (item?.dsNguonKhacChiTra || [])
              ?.map((el, index) => {
                let x = (listNguonKhacChiTra || []).find((dv) => dv.id === el);
                return x?.ten || "";
              })
              .filter((item) => item) ?? [];

          return list.join(", ");
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          searchSelect={
            <Select
              data={KHONG_TINH_TIEN}
              placeholder={t("baoCao.chonKhongTinhTien")}
              defaultValue=""
              onChange={onSearchInput("dichVu.khongTinhTien")}
              hasAllOption={true}
            />
          }
          sort_key="dichVu.khongTinhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
          title={t("danhMuc.khongTinhTien")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.khongTinhTien",
      align: "center",
      i18Name: "danhMuc.khongTinhTien",
      show: true,
      render: (item) => {
        return <Checkbox checked={item?.khongTinhTien} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="loaiLamTron"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiLamTron || 0}
          title={t("danhMuc.loaiLamTron")}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonLoaiLamTron")}
              data={listLoaiLamTron}
              onChange={onSearchInput("loaiLamTron")}
            />
          }
        />
      ),
      width: 130,
      dataIndex: "loaiLamTron",
      key: "loaiLamTron",
      i18Name: "danhMuc.loaiLamTron",
      show: true,
      render: (item) => {
        return (listLoaiLamTron || []).find((i) => i.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="soNgayCanhBaoHsd"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soNgayCanhBaoHsd || 0}
          search={
            <Input
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
              onChange={onSearchInput("soNgayCanhBaoHsd")}
            />
          }
          title={t("danhMuc.soNgayCanhBaoHsd")}
        />
      ),
      width: 170,
      dataIndex: "soNgayCanhBaoHsd",
      key: "soNgayCanhBaoHsd",
      align: "center",
      i18Name: "danhMuc.soNgayCanhBaoHsd",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maPhieuLinh")}
          sort_key="phieuLinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phieuLinhId"] || 0}
          searchSelect={
            <Select
              data={listAllMaPhieuLinh}
              placeholder={t("danhMuc.maPhieuLinh")}
              onChange={onSearchInput("phieuLinhId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "phieuLinh",
      key: "phieuLinh",
      i18Name: "danhMuc.maPhieuLinh",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.maTckt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTckt"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaTckt")}
              onChange={onSearchInput("dichVu.maTckt")}
            />
          }
          title={t("danhMuc.maTckt")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.maTckt",
      i18Name: "danhMuc.maTckt",
      show: true,
      align: "center",
      render: (item) => item?.maTckt,
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tenTckt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTckt"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenTckt")}
              onChange={onSearchInput("dichVu.tenTckt")}
            />
          }
          title={t("danhMuc.tenTckt")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.tenTckt",
      i18Name: "danhMuc.tenTckt",
      show: true,
      align: "center",
      render: (item) => item?.tenTckt,
    },
    {
      title: (
        <HeaderSearch
          sort_key="soVisa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soVisa || 0}
          search={
            <Input
              placeholder={t("danhMuc.timSoVisa")}
              onChange={onSearchInput("soVisa")}
            />
          }
          title={t("danhMuc.soVisa")}
        />
      ),
      width: 150,
      dataIndex: "soVisa",
      key: "soVisa",
      i18Name: "danhMuc.soVisa",
      show: true,
      render: (item) => {
        return item && <div className="break-word">{item}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maHoaChatThayThe")}
          sort_key="dsDichVuThayTheId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsDichVuThayTheId"] || 0}
          searchSelect={
            <Select
              data={listAllMaPhieuLinh}
              placeholder={t("danhMuc.maHoaChatThayThe")}
              onChange={onSearchInput("dsDichVuThayTheId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "dsDichVuThayTheId",
      key: "dsDichVuThayTheId",
      i18Name: "danhMuc.maThuocThayThe",
      show: true,
      render: (item) => {
        return item?.map((el) => el.ten).join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.chiDinhSlLe"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.chiDinhSlLe"] || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonKeSlLe")}
              onChange={onSearchInput("dichVu.chiDinhSlLe")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.choPhepKeSlLe")}
        />
      ),
      width: 130,
      dataIndex: "dichVu",
      key: "dichVu.chiDinhSlLe",
      align: "center",
      i18Name: "danhMuc.choPhepKeSlLe",
      show: true,
      render: (item) => {
        return <Checkbox checked={item?.chiDinhSlLe} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhPham"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhPham || 0}
          search={
            <Input
              placeholder={t("danhMuc.maSinhPham")}
              onChange={onSearchInput("maSinhPham")}
            />
          }
          title={t("danhMuc.maSinhPham")}
        />
      ),
      width: 120,
      dataIndex: "maSinhPham",
      key: "maSinhPham",
      i18Name: "danhMuc.maSinhPham",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhHieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhHieu || 0}
          search={
            <Input
              placeholder={t("danhMuc.maHieuSinhPham")}
              onChange={onSearchInput("maSinhHieu")}
            />
          }
          title={t("danhMuc.maHieuSinhPham")}
        />
      ),
      width: 150,
      dataIndex: "maSinhHieu",
      key: "maSinhHieu",
      i18Name: "danhMuc.maHieuSinhPham",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phanLoaiHoaChat")}
          sort_key="phanLoaiDvKhoId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phanLoaiDvKhoId"] || 0}
          searchSelect={
            <Select
              data={listAllPhanLoaiVatTu}
              placeholder={t("danhMuc.phanLoaiHoaChat")}
              onChange={onSearchInput("phanLoaiDvKhoId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "phanLoaiDvKhoId",
      key: "phanLoaiDvKhoId",
      i18Name: "danhMuc.phanLoaiHoaChat",
      show: true,
      render: (item) => {
        return listAllPhanLoaiVatTu?.find(i => i.id === item)?.ten || ""
      },
    },
    {
      title: (
        <HeaderSearch
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              defaultValue=""
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      ignore: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
  ];
  const onChangePage = (page) => {
    onSearch({ page: page - 1, loaiDichVu: 110 });
  };

  const onHandleSizeChange = (size) => {
    onSizeChange({ size: size, loaiDichVu: 110 });
  };

  const onShowAndHandleUpdate = (data = {}) => {
    setEditStatus(true);
    updateData({
      currentItem: { ...data },
    });
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        getListMeGrLv2TongHop({
          page: "",
          size: "",
          active: true,
          sort: "ten,asc",
          loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
        });
        onShowAndHandleUpdate(record);
        setState({ currentItem: record });
      },
    };
  };

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({
      currentItem: {},
    });
  };
  refClickBtnAdd.current = handleClickedBtnAdded;

  const setRowClassName = (record) => {
    let idDiff = currentItem?.id;

    return record.id === idDiff
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };
  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };
  const listPanel = [
    {
      title: t("common.thongTinDichVu"),
      key: 1,
      render: () => {
        return <ThongTinDichVu updateData={updateData} />;
      },
    },
    {
      title: t("danhMuc.nhomChiPhi"),
      key: 2,
      render: () => <NhomChiPhi dichVuId={currentItem?.id} />,
    },
  ];
  return (
    <Main>
      <HomeWrapper
        title={t("danhMuc.title")}
        listLink={[
          {
            title: t("danhMuc.title"),
            link: "/danh-muc",
          },
          {
            title: t("danhMuc.hoaChat"),
            link: "/danh-muc/hoa-chat",
          },
        ]}
      >
        <Col
          {...(!state.showFullTable
            ? collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          span={state.showFullTable ? 24 : null}
          className={`pr-3 ${state.changeShowFullTbale ? "" : "transition-ease"
            }`}
        >
          <TableWrapper
            title={t("danhMuc.danhMucHoaChat")}
            scroll={{ x: 1000 }}
            styleMain={{ marginTop: 0 }}
            classNameRow={"custom-header"}
            styleContainerButtonHeader={{
              display: "flex",
              width: "100%",
              justifyContent: "flex-end",
              alignItems: "center",
              paddingRight: 35,
            }}
            buttonHeader={
              checkRole([ROLES["DANH_MUC"].HOA_CHAT_THEM])
                ? [
                  {
                    content: (
                      <Button
                        type="success"
                        onClick={handleClickedBtnAdded}
                        rightIcon={<SVG.IcAdd />}
                      >
                        {t("common.themMoiF1")}
                      </Button>
                    ),
                  },
                  {
                    className: `btn-change-full-table ${state.showFullTable ? "small" : "large"
                      }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },
                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
                : [
                  {
                    className: `btn-change-full-table ${state.showFullTable ? "small" : "large"
                      }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },
                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
            }
            columns={columns}
            dataSource={listData}
            onRow={onRow}
            rowClassName={setRowClassName}
            onImport={onImport}
            onExport={() =>
              onExport({
                id: LOAI_DICH_VU.HOA_CHAT,
                ten: t("danhMuc.danhMucHoaChat"),
              })
            }
            ref={refSettings}
            tableName="table_DANHMUC_DichVuHoaChat"
          ></TableWrapper>
          {totalElements > 0 && (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              listData={listData}
              total={totalElements}
              onShowSizeChange={onHandleSizeChange}
              style={{ flex: 1, justifyContent: "flex-end" }}
            />
          )}
        </Col>
        {!state.showFullTable && (
          <Col
            {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3 ${state.changeShowFullTbale ? "" : "transition-ease"
              }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            <MultiLevelTab
              listPanel={listPanel}
              isBoxTabs={true}
              activeKey={state.activeKeyTab}
              onChange={(activeKeyTab) => setState({ activeKeyTab })}
            ></MultiLevelTab>
          </Col>
        )}
      </HomeWrapper>
    </Main>
  );
};

export default HoaChat;
