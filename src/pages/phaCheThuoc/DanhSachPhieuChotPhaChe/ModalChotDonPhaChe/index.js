import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { ModalTemplate, Button, Select, DatePicker } from "components";
import { useListAll, useLoading, useThietLap } from "hooks";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { Main } from "./styled";
import { Col, Form, Row } from "antd";
import { centralizedErrorHandling } from "lib-utils";
import { SVG } from "assets";
import { LOAI_NHAP_XUAT, LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import ModalSuaSoLuongLe from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/ModalSuaSoLuongLe";

const ModalChotDonPhaChe = ({}, ref) => {
  const { t } = useTranslation();
  const history = useHistory();
  const [form] = Form.useForm();
  const { showLoading, hideLoading } = useLoading();

  const refModal = useRef(null);
  const refCallback = useRef(null);
  const refSuaSoLuongLe = useRef(null);
  const [dataLAM_TRON_SL_THUOC_PHA_CHE] = useThietLap(
    THIET_LAP_CHUNG.LAM_TRON_SL_THUOC_PHA_CHE
  );
  const isShowSLDung = dataLAM_TRON_SL_THUOC_PHA_CHE?.eval();

  const [listAllKhoa] = useListAll("khoa", { active: true }, true);
  const [listAllKho] = useListAll("kho", { active: true }, true);

  const {
    danhSachNguoiBenhNoiTru: { clearData },
    nbChotPhaCheThuoc: { themMoiChotPhaCheThuoc },
  } = useDispatch();

  const [state, _setState] = useState({ show: false, slTra: 1 });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  useImperativeHandle(ref, () => ({
    show: (data, callback) => {
      form.setFieldsValue({
        tuThoiGian: moment().startOf("day"),
        denThoiGian: moment().endOf("day"),
      });
      setState({ show: true });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      clearData();
    }
  }, [state.show]);

  const onHandleSubmit = async () => {
    let values = await centralizedErrorHandling(
      form.validateFields().catch(() => null)
    );
    if (!values) return;

    const { tuThoiGian, denThoiGian, khoId, khoaChiDinhId } = values || {};

    try {
      showLoading();

      const payload = {
        tuThoiGian:
          tuThoiGian instanceof moment
            ? tuThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : null,
        denThoiGian:
          denThoiGian instanceof moment
            ? denThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : null,
        khoId,
        khoaChiDinhId,
        loaiNhapXuat: LOAI_NHAP_XUAT.LINH_NOI_TRU,
        loaiDichVu: LOAI_DICH_VU.THUOC,
      };
      const res = await themMoiChotPhaCheThuoc(payload);

      if (res?.code == 1032) {
        refSuaSoLuongLe.current &&
          refSuaSoLuongLe.current.show(
            {
              dsDichVu: res.data,
              code: res.code,
              payload,
              isShowSLDung,
            },
            () => {
              refCallback.current && refCallback.current();
            }
          );
      } else {
        refCallback.current && refCallback.current();
      }

      onOk(false)();
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onOk = (isOk) => () => {
    if (isOk) {
      onHandleSubmit();
    } else {
      setState({ show: false });
      form.resetFields();
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={600}
      title={t("phaCheThuoc.chotDonPhaChe")}
      onCancel={onOk(false)}
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave />}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          actionRight={
            <Button
              type="primary"
              minWidth={100}
              iconHeight={15}
              onClick={onOk(true)}
            >
              {t("common.xuatFile")}
            </Button>
          }
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                label={t("cdha.khoaChiDinh")}
                name="khoaChiDinhId"
                rules={[
                  {
                    required: true,
                    message: t("danhMuc.vuiLongChonKhoaChiDinh"),
                  },
                ]}
              >
                <Select data={listAllKhoa} placeholder={t("common.chonKhoa")} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label={t("cdha.denkho")} name="khoId">
                <Select data={listAllKho} placeholder={t("kho.chonKho")} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tuThoiGian"
                label={t("phaCheThuoc.linhTuNgay")}
                rules={[
                  {
                    required: true,
                    message: t("phaCheThuoc.vuiLongChonLinhTuNgay"),
                  },
                ]}
              >
                <DatePicker showTime={true} format="DD/MM/YYYY HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="denThoiGian"
                label={t("phaCheThuoc.linhDenNgay")}
                rules={[
                  {
                    required: true,
                    message: t("phaCheThuoc.vuiLongChonLinhDenNgay"),
                  },
                ]}
              >
                <DatePicker showTime={true} format="DD/MM/YYYY HH:mm:ss" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <ModalSuaSoLuongLe ref={refSuaSoLuongLe} isPhaCheThuoc={true} />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChotDonPhaChe);
