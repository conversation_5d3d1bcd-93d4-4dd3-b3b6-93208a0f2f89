import React, { useEffect, useRef, useState } from "react";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { BaseSearch } from "components";
import { ENUM } from "constants/index";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useListAll, useStore } from "hooks";

const BoLocTimKiem = () => {
  const { t } = useTranslation();
  const refShowDate1 = useRef(null);
  const refShowDate2 = useRef(null);

  const [listLoaiPhieuXuatPhaChe] = useEnum(ENUM.TRANG_THAI_PHIEU_XUAT_PHA_CHE);
  const listKhoUser = useStore("kho.listKhoUser", []);
  const [listAllKhoa] = useListAll("khoa");

  const {
    nbChotPhaCheThuoc: { onChangeInputSearch },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
  } = useDispatch();

  const [state, _setState] = useState({
    tuThoiGianTaoPhieu: moment()
      .set("hour", 0)
      .set("minute", 0)
      .set("second", 0),
    denThoiGianTaoPhieu: moment()
      .set("hour", 23)
      .set("minute", 59)
      .set("second", 59),
    tenKho: "",
    dsTrangThai: [10, 30],
    trangThaiMacDinh: [10, 30],
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    onChangeInputSearch({
      tuThoiGianTaoPhieu: state.tuThoiGianTaoPhieu?.format(
        "YYYY-MM-DD 00:00:00"
      ),
      denThoiGianTaoPhieu: state.denThoiGianTaoPhieu?.format(
        "YYYY-MM-DD 23:59:59"
      ),
      tenKho: state.tenKho,
    });

    getKhoTheoTaiKhoan({ page: "", page: "", active: true });
  }, []);

  const onSearchInput = (type) => (data) => {
    if (type == "thoiGianTaoPhieu") {
      onChangeInputSearch({
        tuThoiGianTaoPhieu: data.tuThoiGianTaoPhieu?.format(
          "YYYY-MM-DD 00:00:00"
        ),
        denThoiGianTaoPhieu: data.denThoiGianTaoPhieu?.format(
          "YYYY-MM-DD 23:59:59"
        ),
      });
    }
    if (type == "thoiGian") {
      onChangeInputSearch({
        tuThoiGian: data.tuThoiGian?.format("YYYY-MM-DD 00:00:00"),
        denThoiGian: data.denThoiGian?.format("YYYY-MM-DD 23:59:59"),
      });
    }
    if (type === "dsTrangThai") {
      setState({ dsTrangThai: data.dsTrangThai });
      onChangeInputSearch({ dsTrangThai: data.dsTrangThai });
    }
    if (
      ["soPhieu", "khoId", "khoaChiDinhId", "soPhieuXuatKho"].includes(type)
    ) {
      onChangeInputSearch({ [type]: data[type] });
    }
  };

  return (
    <Main>
      <BaseSearch
        cacheData={{
          tuThoiGianTaoPhieu: state.tuThoiGianTaoPhieu,
          denThoiGianTaoPhieu: state.denThoiGianTaoPhieu,
          tenKho: state.tenKho,
          dsTrangThai: state.dsTrangThai,
        }}
        dataInput={[
          {
            widthInput: "180px",
            type: "dateOptions",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGianTaoPhieu", "denThoiGianTaoPhieu"],
            functionChangeInput: onSearchInput("thoiGianTaoPhieu"),
            title: t("phaCheThuoc.thoiGianLapPhieu"),
            placeholder: t("phaCheThuoc.thoiGianLapPhieu"),
            format: "DD/MM/YYYY",
            ref: refShowDate1,
          },
          {
            widthInput: "180px",
            type: "dateOptions",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGian", "denThoiGian"],
            functionChangeInput: onSearchInput("thoiGian"),
            title: t("phaCheThuoc.thoiGianLinh"),
            placeholder: t("phaCheThuoc.thoiGianLinh"),
            format: "DD/MM/YYYY",
            ref: refShowDate2,
          },
          {
            widthInput: "180px",
            placeholder: t("phaCheThuoc.soPhieuXuatPhaChe"),
            keyValueInput: "soPhieu",
            functionChangeInput: onSearchInput("soPhieu"),
          },
          {
            widthInput: "180px",
            placeholder: t("phaCheThuoc.soPhieuXuatKho"),
            keyValueInput: "soPhieuXuatKho",
            functionChangeInput: onSearchInput("soPhieuXuatKho"),
          },
          {
            widthInput: "180px",
            placeholder: t("common.khoa"),
            keyValueInput: "khoaChiDinhId",
            functionChangeInput: onSearchInput("khoaChiDinhId"),
            type: "select",
            value: state.khoaChiDinhId,
            defaultValue: "",
            listSelect: listAllKhoa,
            hasAllOption: true,
          },
          {
            widthInput: "180px",
            placeholder: t("phaCheThuoc.khoXuat"),
            keyValueInput: "khoId",
            functionChangeInput: onSearchInput("khoId"),
            type: "select",
            value: state.khoId,
            defaultValue: "",
            listSelect: listKhoUser,
            hasAllOption: true,
          },
          {
            widthInput: "180px",
            title: t("phaCheThuoc.trangThai"),
            keyValueInput: "dsTrangThai",
            functionChangeInput: onSearchInput("dsTrangThai"),
            type: "selectCheckbox",
            defaultValue: state.trangThaiMacDinh,
            hasCheckAll: true,
            virtual: true,
            listSelect: listLoaiPhieuXuatPhaChe,
          },
        ]}
      />
    </Main>
  );
};
export default BoLocTimKiem;
