import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage } from "./styled";
import { useTranslation } from "react-i18next";
import { Col, Menu, Row } from "antd";
import PhieuXuatPhaChe from "./containers/PhieuXuatPhaChe";
import { Button, Dropdown, ModalSignPrint } from "components";
import { useLoading, useStore } from "hooks";
import { useParams } from "react-router-dom";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { useDispatch } from "react-redux";
import { SVG } from "assets";

const PhieuChotPhaCheThuoc = () => {
  const { t } = useTranslation();
  const refModalNhapLyDo = useRef(null);
  const { id } = useParams();
  const { showLoading, hideLoading } = useLoading();
  const refSelectRow = useRef(null);
  const refModalSignPrint = useRef(null);
  const refPhieuXuatPhaChe = useRef(null);
  const chiTietPhieuPhaCheTheoChot = useStore(
    "nbChotPhaCheThuoc.chiTietPhieuPhaCheTheoChot",
    {}
  );
  const phieuNhapXuatId = useStore("nbChotPhaCheThuoc.phieuNhapXuatId", null);
  const {
    nbChotPhaCheThuoc: { phaCheThuoc, inNhanPhaCheChai, inBanGiaoDichTruyen },
    phieuNhapXuat: { inPhieuDsNbLinhThuoc, inPhieuLinh },
  } = useDispatch();

  const [state, _setState] = useState({
    baoCao: "1",
  });
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  refSelectRow.current = (index) => {
    const indexNextItem = +state.baoCao + index;
    if (0 < indexNextItem && indexNextItem < 5) {
      setState({
        baoCao: "" + indexNextItem,
      });
    }
  };

  const handlePhaChe = async () => {
    try {
      showLoading();

      await phaCheThuoc([{ id: id }]);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintNhanPhaChe = async () => {
    try {
      showLoading();
      await inNhanPhaCheChai({ dsId: id });
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const onPrintBanGiaoDichTruyen = async () => {
    try {
      showLoading();
      await inBanGiaoDichTruyen(id);
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const onPrintDonThuoc = async () => {
    try {
      showLoading();
      await inPhieuDsNbLinhThuoc({
        phieuLinhId: phieuNhapXuatId,
        tachTheoNb: true,
      });
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuLinh = async () => {
    try {
      showLoading();
      await inPhieuLinh({ id: phieuNhapXuatId });
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={[
          {
            key: "inNhanPhaChe",
            label: (
              <a href={() => false} onClick={onPrintNhanPhaChe}>
                {t("phaCheThuoc.inNhanPhaCheChai")}
              </a>
            ),
          },
          {
            key: "inPhieuLinh",
            label: (
              <a href={() => false} onClick={onPrintPhieuLinh}>
                {t("quanLyNoiTru.inPhieuLinh")}
              </a>
            ),
          },
          {
            key: "inDonThuoc",
            label: (
              <a href={() => false} onClick={onPrintDonThuoc}>
                {t("common.inDonThuoc")}
              </a>
            ),
          },
          {
            key: "inBanGiaoDichTruyen",
            label: (
              <a href={() => false} onClick={onPrintBanGiaoDichTruyen}>
                {t("phaCheThuoc.inPhieuBanGiaoDichTruyenPhaThuoc")}
              </a>
            ),
          },
        ]}
      />
    );
  }, [id, phieuNhapXuatId]);

  const renderActionPhaChe = () => {
    return (
      <>
        {chiTietPhieuPhaCheTheoChot?.trangThai !== 20 && (
          <Button
            type="primary"
            minWidth={100}
            iconHeight={15}
            onClick={handlePhaChe}
          >
            {t("phaCheThuoc.phaChe")}
          </Button>
        )}
        <Dropdown overlay={menu} trigger="click">
          <Button type="primary" rightIcon={<SVG.IcPrint />} iconHeight={15}>
            <span>{t("common.inGiayTo")}</span>
          </Button>
        </Dropdown>
      </>
    );
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("phaCheThuoc.phaCheThuoc"), link: "/pha-che-thuoc" },
        {
          title: t("phaCheThuoc.danhSachPhieuChotPhaChe"),
          link: "/pha-che-thuoc/danh-sach-phieu-chot-pha-che",
        },
        {
          title: t("phaCheThuoc.phieuChotPhaCheThuoc"),
          link: `/pha-che-thuoc/phieu-chot-pha-che-thuoc/chi-tiet/${id}`,
        },
      ]}
      style={{ pageBodyPadding: "0" }}
      actionRight={renderActionPhaChe()}
    >
      <div className="wrapper">
        <Row gutter={0} className="wrapper-inner">
          <Col span={24} className="main">
            <PhieuXuatPhaChe ref={refPhieuXuatPhaChe} />
          </Col>
        </Row>
      </div>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
      <ModalSignPrint ref={refModalSignPrint} />
    </MainPage>
  );
};

export default PhieuChotPhaCheThuoc;
