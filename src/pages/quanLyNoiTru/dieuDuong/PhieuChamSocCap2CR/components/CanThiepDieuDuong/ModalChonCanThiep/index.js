import React, {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { <PERSON><PERSON>, DateTimePicker, ModalTemplate } from "components";
import { useEffect } from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import { useStore } from "hooks";
import moment from "moment";
import { SVG } from "assets";
import { showError } from "utils/message-utils";
import { cloneDeep, isArray, isNumber } from "lodash";

const ModalChonCanThiep = (props, ref) => {
  const { t } = useTranslation();
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const fullname = useStore("auth.auth.full_name", null);
  const refModal = useRef(null);
  const refCallback = useRef(null);

  const [state, _setState] = useState({
    show: false,
  });
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  useImperativeHandle(ref, () => ({
    show: ({ dataSource, dataItem: _dataItem }, callback) => {
      const dataItem = cloneDeep(_dataItem);
      if (dataItem != null) {
        if (!isArray(dataItem.congViec)) {
          dataItem.congViec = dataItem.congViec?.split(",");
        }
        dataItem.congViec = dataItem.congViec.filter(item => item.trim()).map(congViec => (dataSource || []).find(item => item.ten == congViec.trim())?.id);
        dataItem.nguoiThucHienId = dataItem.nguoiThucHienId || nhanVienId;
        if (dataItem.thoiGianThucHien && !(dataItem.thoiGianThucHien instanceof moment)) {
          dataItem.thoiGianThucHien = moment(dataItem.thoiGianThucHien);
        }
      }
      refCallback.current = callback;
      setState({ show: true, dataSource, dataItem });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onChange = type => value => {
    const dataItem = state.dataItem || {};
    dataItem[type] = value;
    setState({ dataItem: { ...dataItem } })
  }

  const onOk = isOk => () => {
    if (isOk) {
      if (!state.dataItem?.congViec || !state.dataItem.thoiGianThucHien) {
        showError("Vui lòng chọn thời gian và công việc")
        return;
      }
      refCallback.current?.({
        thoiGianThucHien: state.dataItem.thoiGianThucHien._d.format("yyyy-MM-dd HH:mm:ss"),
        congViec: state.dataItem.congViec?.map(congViec => state.dataSource?.find(item => item.id == congViec)?.ten).join(", "),
        nguoiThucHienId: state.dataItem?.nguoiThucHienId || nhanVienId,
        tenNguoiThucHien: state.dataItem?.tenNguoiThucHien || fullname
      });
      onOk(false)();
    }
    else {
      setState({ show: false });
    }
  }

  return (
    <ModalTemplate
      ref={refModal}
      width={"500px"}
      onCancel={() => {
        setState({ show: false });
      }}
      title={"Chọn can thiệp"}
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      actionRight={<Button onClick={onOk(true)} type="primary" leftIcon={<SVG.IcSuccess />}>{t("common.luu")}</Button>}
    >
      <Main>
        <FieldItem title="Thời gian">
          <DateTimePicker
            format="DD/MM/YYYY HH:mm"
            onChange={onChange("thoiGianThucHien")} value={state.dataItem?.thoiGianThucHien ? moment(state.dataItem.thoiGianThucHien) : null}>
          </DateTimePicker>
        </FieldItem>
        <FieldItem title="Chọn can thiệp">
          <MultipleChoice
            type="checkbox"
            checkboxWidth={200}
            data={state.dataSource} value={state.dataItem?.congViec?.filter(item => isNumber(item))}
            onChange={onChange("congViec")}>
          </MultipleChoice>
        </FieldItem>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalChonCanThiep);
