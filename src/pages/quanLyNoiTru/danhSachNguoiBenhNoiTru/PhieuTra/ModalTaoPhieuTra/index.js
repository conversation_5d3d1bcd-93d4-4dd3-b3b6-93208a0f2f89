import { Col, message, Row } from "antd";
import { Select, SelectLoadMore, DatePicker } from "components";
import moment from "moment";
import React, { forwardRef, useEffect, useMemo, useState, useRef } from "react";
import { useDispatch } from "react-redux";
import { useEnum, useStore } from "hooks";
import { ENUM } from "constants/index";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { useTranslation } from "react-i18next";
import ScreenPhieuTra from "pages/quanLyNoiTru/components/ScreenPhieuTra";
import { useHistory } from "react-router-dom";
import cacheUtils from "lib-utils/cache-utils";

const ModalTaoPhieuTra = (
  {
    khoaLamViec,
    refModalTaoPhieuTraSuatAn,
    isPttt = false,
    isCDHA = false,
    initState: _initState,
  },
  ref
) => {
  const { t } = useTranslation();
  const history = useHistory();

  const [listLoaiDichVuKho] = useEnum(ENUM.LOAI_DICH_VU_KHO);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);

  const listPhong = useStore("phong.listPhong", []);

  const listThietLapChonKhoTongHop = useStore(
    "thietLapChonKho.listThietLapChonKhoTongHop",
    []
  );
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const auth = useStore("auth.auth", {});
  const userId = auth?.id;
  const isQLNoiTru = window.location.pathname.indexOf("/quan-ly-noi-tru") >= 0;

  const {
    phong: { getListPhongTongHop },
    nhapKho: { taoPhieuTraBu },
    thietLapChonKho: { getListThietLapChonKhoTongHop },
    phieuNhapXuat: { updateData },
  } = useDispatch();

  const listLoaiNhapXuatMemo = useMemo(() => {
    return listLoaiNhapXuat.filter((i) => [70, 71, 72].includes(i.id));
  }, [listLoaiNhapXuat]);

  const initState = useMemo(() => {
    return _initState
      ? {
          ..._initState,
          tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
          denThoiGian: moment()
            .set("hour", 23)
            .set("minute", 59)
            .set("second", 59),
          dsKhoaChiDinhId: khoaLamViec?.id,
        }
      : {
          loaiDichVu: 90,
          tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
          denThoiGian: moment()
            .set("hour", 23)
            .set("minute", 59)
            .set("second", 59),
          dsKhoaChiDinhId: khoaLamViec?.id,
          khoId:
            listThietLapChonKhoTongHop.length === 1
              ? listThietLapChonKhoTongHop[0]?.id
              : null,
        };
  }, [khoaLamViec?.id, _initState]);

  useEffect(() => {
    if (khoaLamViec?.id) {
      getListPhongTongHop({ khoaId: khoaLamViec.id });
      getListDenKho({ ...initState, khoaChiDinhId: khoaLamViec?.id });
    }
  }, [khoaLamViec?.id]);

  const getListDenKho = (_state, onChange = () => {}) => {
    getListThietLapChonKhoTongHop({
      khoaChiDinhId: khoaLamViec?.id,
      loaiDichVu: _state.loaiDichVu,
      noiTru: true,
      tuTruc: false,
      active: true,
      page: "",
      size: "",
      ...(_state.dsLoaiKho && {
        dsLoaiKho: _state.dsLoaiKho,
      }),
      ...(isQLNoiTru
        ? {
            canLamSang: false,
          }
        : null),
    }).then((response) =>
      onChange(response.length === 1 ? response[0]?.id : null)
    );
  };

  const onCustomChange = (key, onChange, _state) => (value) => {
    if (key === "loaiDichVu") {
      if (value === 50) {
        ref.current && ref.current.close();

        refModalTaoPhieuTraSuatAn.current &&
          refModalTaoPhieuTraSuatAn.current.show();
        return;
      }

      getListDenKho({ ..._state, [key]: value }, onChange("khoId"));
      onChange("loaiDichVu")(value);
    }

    if (key === "dsKhoaChiDinhId") {
      onChange("dsKhoaChiDinhId")(value);
      getListPhongTongHop({ khoaId: value });
    }
    if (key === "loaiTra") {
      onChange("loaiTra")(value);
      getListDenKho({ ..._state, dsLoaiKho: 60 }, onChange("khoId"));
    }
  };

  const renderFilter = ({ _state, onChange }) => {
    return (
      <Row>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("quanLyNoiTru.loaiHangHoa")}
              <span className="icon-required">*</span>
            </label>
            <Select
              // onChange={onChange("loaiDichVu")}
              onChange={onCustomChange("loaiDichVu", onChange, _state)}
              value={_state.loaiDichVu}
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonLoaiHangHoa")}
              data={[...listLoaiDichVuKho, { ten: t("common.suatAn"), id: 50 }]}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("quanLyNoiTru.loaiTra")}</label>
            <Select
              onChange={onCustomChange("loaiTra", onChange, _state)}
              value={_state.loaiTra}
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonLoaiTra")}
              data={listLoaiNhapXuatMemo}
              allowClear={true}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("quanLyNoiTru.khoaTra")}
              <span className="icon-required">*</span>
            </label>
            <Select
              // mode="multiple"
              onChange={onCustomChange("dsKhoaChiDinhId", onChange)}
              value={_state.dsKhoaChiDinhId}
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonKhoa")}
              data={listKhoaTheoTaiKhoan}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">
              {t("quanLyNoiTru.denkho")}
              <span className="icon-required">*</span>
            </label>
            <Select
              onChange={onChange("khoId")}
              value={_state.khoId}
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonKho")}
              data={listThietLapChonKhoTongHop}
            />
          </div>
        </Col>

        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("common.phong")}</label>
            <Select
              onChange={onChange("dsPhongId")}
              value={_state.dsPhongId}
              className="input-filter"
              mode="multiple"
              placeholder={t("common.chonPhong")}
              data={listPhong}
              disabled={!_state.dsKhoaChiDinhId}
            />
          </div>
        </Col>

        <Col md={12} xl={12} xxl={12}>
          <div className="item-date">
            <label className="label-filter">
              {t("quanLyNoiTru.traTuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DatePicker
              showTime
              value={_state.tuThoiGian}
              onChange={onChange("tuThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              disabledDate={(date) => date > _state.denThoiGian}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-date">
            <label className="label-filter">
              {t("quanLyNoiTru.traDenNgay")}{" "}
              <span className="icon-required">*</span>
            </label>
            <DatePicker
              showTime
              value={_state.denThoiGian}
              onChange={onChange("denThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              disabledDate={(date) => date < _state.tuThoiGian}
            />
          </div>
        </Col>
        <Col md={12} xl={12} xxl={12}>
          <div className="item-select">
            <label className="label-filter">{t("quanLyNoiTru.maBenhAn")}</label>
            <SelectLoadMore
              api={(params) => {
                const { value, ...searchParams } = params || {};
                if (!value) {
                  return nbDotDieuTriProvider.getNbNoiTru({
                    ...searchParams,
                    maBenhAn: "",
                    tenNb: "",
                  });
                }
                let _value = value.trim();
                switch (true) {
                  case /^[0-9]{7}$/.test(_value):
                    Object.assign(searchParams, { maBenhAn: _value });
                    break;
                  case !/^[0-9]+$/.test(_value) &&
                    !/^[0-9A-F]{8}$/.test(_value) &&
                    !/^[a-zA-Z]+[0-9]+$/.test(_value):
                    Object.assign(searchParams, { tenNb: _value });
                    break;
                  default:
                    return Promise.reject("Invalid search value");
                }
                return nbDotDieuTriProvider.getNbNoiTru(searchParams);
              }}
              mapData={(i) => ({
                value: i.id,
                label: `${i.maBenhAn} - ${i.tenNb}`,
              })}
              onChange={onChange("nbDotDieuTriId")}
              value={_state.nbDotDieuTriId}
              keySearch={"value"}
              placeholder={t("quanLyNoiTru.chonMa")}
              className="input-filter"
              hasAll={true}
              blurReset={true}
              haveLoading
            />
          </div>
        </Col>
      </Row>
    );
  };

  const onOk = (data) => {
    if (!data.loaiDichVu) {
      message.error(t("cdha.vuiLongChonLoaiHangHoa"));
      return;
    }
    const body = {
      loaiDichVu: data.loaiDichVu,
      ...([71, 72].includes(data.loaiTra) ? { khoDoiUngId: data.khoId } : {}),
      khoId: data.khoId,
      tuThoiGian: data.tuThoiGian,
      denThoiGian: data.denThoiGian,
      khoaChiDinhId: data.dsKhoaChiDinhId ? data.dsKhoaChiDinhId : undefined,
      dsPhongId: data.dsPhongId?.length > 0 ? data.dsPhongId : undefined,
      nbDotDieuTriId: data.nbDotDieuTriId,
    };

    return taoPhieuTraBu(body);
  };

  const afterSubmit = (data) => {
    const khoaTra = data && data.length > 0 ? data[0].khoaChiDinh : null;

    if (khoaTra) {
      cacheUtils.save(userId, "DATA_KHOA_LAM_VIEC_PHIEU_TRA", khoaTra, false);
      updateData({ khoaLamViec: khoaTra });
    }

    setTimeout(() => {
      if (
        window.location.pathname.indexOf("danh-sach-phieu-tra") >= 0 &&
        data?.length > 1
      ) {
        window.location.reload();
      } else {
        let chiTiet = "danh-sach-phieu-tra";
        if (data?.length == 1) {
          chiTiet = "chi-tiet-phieu-tra/" + data[0].id;
        }
        history.push(
          `/${
            isCDHA
              ? "chan-doan-hinh-anh"
              : isPttt
              ? "phau-thuat-thu-thuat"
              : "quan-ly-noi-tru"
          }/${chiTiet}`
        );
      }
    }, 500);
  };

  return (
    <ScreenPhieuTra.ModalCreate
      width={600}
      title={t("quanLyNoiTru.taoPhieuTra")}
      renderFilter={renderFilter}
      initState={initState}
      // initState={{
      //   ...initState,
      //   tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
      //   denThoiGian: moment()
      //     .set("hour", 23)
      //     .set("minute", 59)
      //     .set("second", 59),
      // }}
      ref={ref}
      onSubmit={onOk}
      afterSubmit={afterSubmit}
    ></ScreenPhieuTra.ModalCreate>
  );
};

export default forwardRef(ModalTaoPhieuTra);
