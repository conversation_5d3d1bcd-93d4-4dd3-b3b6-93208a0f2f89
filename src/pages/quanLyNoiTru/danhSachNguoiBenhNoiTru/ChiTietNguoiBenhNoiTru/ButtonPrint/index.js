import React, { memo, useMemo, useRef, useState } from "react";
import { <PERSON><PERSON>, ModalSignPrint, Popover } from "components";
import { GlobalStyle } from "./styled";
import moment from "moment";
import {
  LIST_PHIEU_CHON_TIEU_CHI,
  LOAI_BIEU_MAU,
  LOAI_DICH_VU,
  LOAI_IN,
  MA_BIEU_MAU_EDITOR,
  THIET_LAP_CHUNG,
  LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU,
  LOAI_PHIEU_THU,
  ENUM,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
  DOI_TUONG,
} from "constants/index";
import printProvider, { printJS } from "data-access/print-provider";
import { openInNewTab, isArray } from "utils/index";
import { flatten, sortBy } from "lodash";
import { useEnum, useListAll, useLoading, useStore, useThietLap } from "hooks";
import { useDispatch } from "react-redux";
import { SVG } from "assets";
import { useTranslation } from "react-i18next";
import { createUniqueText } from "lib-utils";
import { message } from "antd";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { parseKiemTraCongKhiInBangKeBHYT } from "utils/thiet-lap-chung-utils";
import ContentPrint from "./ContentPrint";
import nhomDichVuCap2Provider from "data-access/categories/dm-nhom-dich-vu-cap2-provider";
import nbBienBanHoiChanProvider from "data-access/nb-bien-ban-hoi-chan-provider";

import ModalChonPhieuTruyenDich from "../ToDieuTri/modals/ModalChonPhieuTruyenDich";
import ModalChonPhieuCongKhaiThuoc from "../ToDieuTri/modals/ModalChonPhieuCongKhaiThuoc";
import ModalChonPhieuCongKhaiThuoc2 from "../ToDieuTri/modals/ModalChonPhieuCongKhaiThuoc2";
import ModalChonPhieuCongKhaiVtyt from "../ToDieuTri/modals/ModalChonPhieuCongKhaiVtyt";
import ModalChonTieuChiBangKe from "components/ModalChonTieuChiBangKe";
import ModalChonTieuChi from "../ModalChonTieuChi";
import ModalInPhieuCongKhai from "../Modal/ModalInPhieuCongKhai";
import ModalChonKetQuaXetNghiem from "../ToDieuTri/modals/ModalChonKetQuaXetNghiem";
import ModalInToDieuTriNhieuNgay from "../ToDieuTri/modals/ModalInToDieuTriNhieuNgay";
import ModalCheckBaoHiem from "pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem";
import ModalInPhieuTra from "../Modal/ModalInPhieuTra";
import ModalInPhieuLinh from "../Modal/ModalInPhieuLinh";
import ModalGiayChuyenTuyen from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalGiayChuyenTuyen";
import ModalDanhSachPhieuSoKet from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalDanhSachPhieuSoKet";
import ModalInPhieuHuyThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuHuyThuoc";
import ModalPhieuPHCN from "../ToDieuTri/modals/ModalPhieuPHCN";
import ModalInPhieuSaoBA from "../Modal/ModalInPhieuSaoBA";
import ModalInPhieuTomTatBenhAn from "../Modal/ModalInPhieuTomTatBenhAn";
import ModalInPhieuChamSoc from "../Modal/ModalInPhieuChamSoc";
import ModalInPhieuCamDoanChapNhanPTTT from "../Modal/ModalInPhieuCamDoanChapNhanPTTT";
import ModalChonPhieuCanThiepDuoc from "pages/kho/DuyetDuocLamSang/ChiTietDuyetDuocLamSang/modal/ModalChonPhieuCanThiepDuoc";
import ModalPhieuHenKhamDieuTriNgoaiTru from "../ToDieuTri/modals/ModalPhieuHenKhamDieuTriNgoaiTru";
import ModalPhieuBanGiaoNguoiBenh from "../Modal/ModalPhieuBanGiaoNguoiBenh";
import ModalChonPhieuThuPhanUngThuoc from "../Modal/ModalChonPhieuThuPhanUngThuoc";
import ModalInPhieuTheoDoiDieuTriThan from "../Modal/ModalInPhieuTheoDoiDieuTriThan";
import ModalChonKhoaPhieuTruyenDich from "../ToDieuTri/modals/ModalChonKhoaPhieuTruyenDich";
import ModalInPhieuTuVanGDSK from "../Modal/ModalInPhieuTuVanGDSK";
import ModalInPhieuDeNghiSDDVYeuCau from "../Modal/ModalInPhieuDeNghiSDDVYeuCau";
import ModalInGiayChungSinh from "../ThongTinCon/ModalInGiayChungSinh";
import ModalInPhieuTienMe from "../Modal/ModalInPhieuTienMe";
import ModalInPhieuTuVanDangKyDVTuNguyen from "../Modal/ModalInPhieuTuVanDangKyDVTuNguyen";
import ModalInDonXinNamGiuongDVTheoYC from "../Modal/ModalInDonXinNamGiuongDVTheoYC";
import ModalPhieuBieuDoNongDoBilirubin from "../Modal/ModalPhieuBieuDoNongDoBilirubin";
import ModalInBangKiemNbTruocPhauThuat from "../Modal/ModalInBangKiemNbTruocPhauThuat";
import ModalPhieuBanGiaoNguoiBenhSauPhauThuat from "../Modal/ModalPhieuBanGiaoNguoiBenhSauPhauThuat";
import ModalInBieuDoChuyenDa from "../Modal/ModalInBieuDoChuyenDa";
import ModalInGiayNghiHuongBHXH from "../Modal/ModalInGiayNghiHuongBHXH";
import ModalInPhieuTuVanHuongDanGiaoDucSucKhoe from "../Modal/ModalInPhieuTuVanHuongDanGiaoDucSucKhoe";
import ModalInPhieuPC_CAMKET_PTTT_TYCNT from "../Modal/ModalInPhieuPC_CAMKET_PTTT_TYCNT";
import ModalInPhieuChamSocTreSSKhongThoMay from "../Modal/ModalInPhieuChamSocTreSSKhongThoMay";

const LIST_FILE_SHOW_MODAL = {
  P062: 20,
  P032: 10,
  P107: 30,
  P178: 40,
  P554: 20,
  P141: 10,
  P678: 22,
  P677: 12,
  P727: 70,
  P747: 33,
};

const ButtonPrint = ({ khoaLamViec, nbDotDieuTriId }) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    listPhieuBacSi: [],
    listPhieuDieuDuong: [],
  });
  const setState = (payload) => {
    _setState((prev) => ({ ...prev, ...payload }));
  };

  const refModalChonPhieuTruyenDich = useRef(null);
  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refModalChonPhieuCongKhaiThuoc2 = useRef(null);
  const refModalSignPrint = useRef(null);
  const refModalChonPhieuCongKhaiVtyt = useRef(null);
  const refModalChonTieuChiBangKe = useRef(null);
  const refModalChonTieuChi = useRef(null);
  const refModalInPhieuCongKhai = useRef(null);
  const refModalChonKetQuaXetNghiem = useRef(null);
  const refModalInToDieuTri = useRef(null);
  const refModalCheckBaoHiem = useRef();
  const refModalInPhieuTra = useRef(null);
  const refModalGiayChuyenTuyen = useRef(null);
  const refModalInPhieuLinh = useRef(null);
  const refModalPhieuSoKet = useRef(null);
  const refModalInPhieuHuyThuoc = useRef(null);
  const refModalPhieuPHCN = useRef(null);
  const refModalInPhieuSaoBA = useRef(null);
  const refModalInPhieuTomTatBA = useRef(null);
  const refModalInPhieuChamSoc = useRef(null);
  const refModalInPhieuCamDoanChapNhanPTTT = useRef(null);
  const refModalChonPhieuCanThiepDuoc = useRef(null);
  const refModalPhieuHenKhamDieuTriNgoaiTru = useRef(null);
  const refModalPhieuBanGiaoNguoiBenh = useRef(null);
  const refModalPhieuThuPhanUngThuoc = useRef(null);
  const refModalInPhieuTheoDoiDieuTriThan = useRef(null);
  const refModalChonKhoaPhieuTruyenDich = useRef(null);
  const refModalInPhieuTuVanGDSK = useRef(null);
  const refModalInPhieuDeNghiSDDVYeuCau = useRef(null);
  const refModalInGiayChungSinh = useRef(null);
  const refModalInPhieuTienMe = useRef(null);
  const refModalInPhieuTuVanDangKyDVTuNguyen = useRef(null);
  const refModalInDonXinNamGiuongDVTheoYC = useRef(null);
  const refModalBieuDoNongDoBilirubin = useRef(null);
  const refModalInBangKiemNbTruocPhauThuat = useRef(null);
  const refModalPhieuBanGiaoNguoiBenhSauPhauThuat = useRef(null);
  const refModalInBieuDoChuyenDa = useRef(null);
  const refModalInGiayNghiHuongBHXH = useRef(null);
  const refModalInPhieuTuVanHuongDanGiaoDucSucKhoe = useRef(null);
  const refModalInPhieuPC_CAMKET_PTTT_TYCNT = useRef(null);
  const refModalInPhieuChamSocTreSSKhongThoMay = useRef(null);

  const nhanVienId = useStore("auth.auth.nhanVienId");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const { khoaNbId } = chiTietNguoiBenhNoiTru || {};
  const listToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const [BANG_KE_IN_THEO_KHOA_CHI_DINH] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_IN_THEO_KHOA_CHI_DINH
  );
  const [listAllQuyenKy] = useListAll(
    "quyenKy",
    {
      active: true,
      size: "",
      page: "",
    },
    true
  );
  const [listLoaiHoiChan] = useEnum(ENUM.LOAI_HOI_CHAN);

  const [dataNHOM_GIAI_PHAU_BENH] = useThietLap(
    THIET_LAP_CHUNG.NHOM_GIAI_PHAU_BENH
  );

  const [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
  );
  const [dataCHAN_KY_TRUONG_KHOA] = useThietLap(
    THIET_LAP_CHUNG.CHAN_KY_TRUONG_KHOA
  );
  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );
  const [dataTRANG_THAI_NB_CHO_PHEP_IN_PHIEU_HEN_KHAM_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.TRANG_THAI_NB_CHO_PHEP_IN_PHIEU_HEN_KHAM_NOI_TRU
  );
  const [dataMA_THIET_LAP_CHECK_CONG] = useThietLap(
    THIET_LAP_CHUNG.MA_THIET_LAP_CHECK_CONG,
    "P032"
  );
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const {
    phieuIn: { getFilePhieuIn, showFileEditor },
    quanLyNoiTru: {
      getTieuChi,
      updateTruongKhoa,
      getDsPhieuThuNbNoiTru,
      getChiTietPhieuThu,
    },
    tiepDon: { giamDinhThe, onUpdate },
    nbChuyenVien: { getDsGiayChuyenTuyen },
    nbDieuTriSoKet: { onSearch },
    phieuIn: { getListPhieu },
    dsHoaDonDienTu: { inHoaDon },
    nbThongTinSanPhu: { getById: getThongTinSanPhu },
  } = useDispatch();

  const refreshPhieu = () => {
    return getListPhieu({
      nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
      maViTri: VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
    }).then((s) => {
      setState({
        listPhieuBacSi: s,
      });
    });
  };

  const refreshPhieuDieuDuong = () => {
    return getListPhieu({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
      maViTri: VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
      khoaId: khoaLamViec?.id,
    }).then((data) => {
      setState({
        listPhieuDieuDuong: [
          { key: "phieu-tra", ten: t("quanLyNoiTru.inPhieuTra") },
          { key: "phieu-linh", ten: t("quanLyNoiTru.inPhieuLinh") },
          ...data,
        ],
      });
    });
  };

  const { kiemTra, dsManHinh } = useMemo(() => {
    if (dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT) {
      return parseKiemTraCongKhiInBangKeBHYT(
        dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
      );
    }

    return {};
  }, [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT]);

  const onPrint = (type) => () => {
    refModalSignPrint.current.show({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
      maViTri:
        type == 0
          ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
          : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
    });
  };

  const getFileAndPrint = async (payload) => {
    try {
      showLoading();
      const { finalFile, dsPhieu } = await getFilePhieuIn(payload);
      if (isArray(dsPhieu, true)) {
        const inMoTab = dsPhieu.every((item) => {
          let valid = false;
          if (isArray(item.data, true)) {
            valid = item.data.every((el) => el?.loaiIn === LOAI_IN.MO_TAB);
          } else {
            valid = item?.loaiIn === LOAI_IN.MO_TAB;
          }
          return valid;
        });

        if (inMoTab) openInNewTab(finalFile);
        else printProvider.printPdf(dsPhieu, { mergePdfFile: finalFile });
      } else {
        printJS({
          printable: finalFile,
          type: "pdf",
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const capNhatThongTinTruongKhoa = (item) => {
    const quyenKyId = item?.dsSoPhieu[0]?.quyenKyTiepTheoId;
    if (!quyenKyId) return;

    const qKTruongKhoa = listAllQuyenKy.find(
      (x) => x.ma === dataCHAN_KY_TRUONG_KHOA
    );
    //Nếu quyenKyTiepTheoId = quyền ký của trưởng khoa => gọi thêm api nb-dot-dieu-tri/tong-ket-ra-vien/truong-khoa/{id}
    if (qKTruongKhoa?.id === quyenKyId) {
      updateTruongKhoa({
        nbDotDieuTriId: nbDotDieuTriId,
        truongKhoaId: nhanVienId,
      });
    }
  };

  const onPrintPhieu = (type, item, boQuaCheckCong) => async () => {
    if (
      !boQuaCheckCong &&
      dataMA_THIET_LAP_CHECK_CONG?.split(",")?.includes(item.ma) &&
      kiemTra &&
      (!dsManHinh?.length || dsManHinh?.includes("006")) &&
      thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      let data = {
        hoTen: thongTinCoBan.tenNb,
        maThe: thongTinCoBan?.maTheBhyt,
        ngaySinh: moment(thongTinCoBan?.ngaySinh).format("YYYY-MM-DD"),
      };
      const showModalCheckBH = (_data) =>
        refModalCheckBaoHiem.current.show(
          {
            show: true,
            data: _data,
            hoTen: thongTinCoBan.tenNb,
            diaChi: thongTinCoBan.diaChi,
          },
          (res) => {
            if (res.boQuaTheLoi) {
              onUpdate({
                id: nbDotDieuTriId,
                nbTheBaoHiem: { boQuaTheLoi: true },
              });

              onPrintPhieu(type, item, true)();
            }
          }
        );
      giamDinhThe({ ...data, keepCode: true })
        .then((res) => {
          if (res?.code === 0) {
            onPrintPhieu(type, item, true)();
          } else {
            showModalCheckBH(res);
          }
        })
        .catch((e) => {
          showModalCheckBH(e);
        });
      return;
    }
    const listTrangThaiNbDuocPhepInPhieuHenKham =
      dataTRANG_THAI_NB_CHO_PHEP_IN_PHIEU_HEN_KHAM_NOI_TRU
        ?.split(",")
        .map((i) => i.trim());
    if (item.value == -1) {
      onPrint(type)();
    } else if (item.key === "phieu-tra") {
      refModalInPhieuTra.current &&
        refModalInPhieuTra.current.show({
          khoaLamViec,
        });
    } else if (item.key === "phieu-linh") {
      refModalInPhieuLinh.current &&
        refModalInPhieuLinh.current.show({
          khoaLamViec,
        });
    } else if (item.ma == "P291") {
      try {
        showLoading();

        //Hóa đơn điện tử
        const resDsPhieuThu = await getDsPhieuThuNbNoiTru({
          dsLoaiPhieuThu: LOAI_PHIEU_THU.PHIEU_THU_TONG,
          nbDotDieuTriId: nbDotDieuTriId,
          page: 0,
          size: 50,
          active: true,
          sort: "thanhToan,asc",
        });

        //Get ds hóa đơn
        const resHoaDon = await Promise.all(
          resDsPhieuThu.map(async (item) => {
            const data = await getChiTietPhieuThu(item.id);

            return (data.dsHoaDon || []).map((x) => x.hoaDonId);
          })
        );

        const _dsHoaDonId = flatten(resHoaDon);

        if (_dsHoaDonId.length) {
          await inHoaDon({
            hoaDonId: _dsHoaDonId,
            dinhDang: dataDINH_DANG_XEM_HOA_DON,
          });
        } else {
          message.error(t("thuNgan.khongTonTaiHoaDonTrongPhieuThu"));
        }
      } finally {
        hideLoading();
      }
    } else if (
      item.ma === "P055" &&
      isArray(listTrangThaiNbDuocPhepInPhieuHenKham, true) &&
      !listTrangThaiNbDuocPhepInPhieuHenKham.some(
        (i) => i == chiTietNguoiBenhNoiTru?.trangThai
      )
    ) {
      message.error(
        t("quanLyNoiTru.nbCoTrangThaiKhongChoPhepInPhieuHenKhamNoiTru", {
          trangThaiNb: listTrangThaiNb.find(
            (i) => i.id === chiTietNguoiBenhNoiTru?.trangThai
          )?.ten,
        })
      );
      return;
    } else {
      if (item.type == "editor") {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
            maViTri:
              type == 0
                ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
            kySo: true,
            maPhieuKy: item.ma,
            nbDotDieuTriId,
          };
        }

        const _listPhieuIn =
          type == 0 ? state.listPhieuBacSi : state.listPhieuDieuDuong;
        //get ds các phiếu kèm theo => mở ra thêm tab mới
        const _dsPhieuInKemTheo = _listPhieuIn.filter(
          (x) =>
            (item.dsPhieuInKemTheoId || []).includes(x.id) &&
            x.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA
        );

        if (item.ma == "PC_CAMKET_PTTT_TYCNT") {
          //Phiếu cam kết Phẫu thuật - Thủ thuật Theo yêu cầu (NT)
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );

          refModalInPhieuPC_CAMKET_PTTT_TYCNT.current &&
            refModalInPhieuPC_CAMKET_PTTT_TYCNT.current.show(
              {
                dsSoPhieu: _dsSoPhieu,
                tenPhieu: item.ten,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: item.maBaoCao,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P988") {
          //Bảng kiểm chuẩn bị và bàn giao NB trước phẫu thuật không theo DV PTTT
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );

          refModalInBangKiemNbTruocPhauThuat.current &&
            refModalInBangKiemNbTruocPhauThuat.current.show(
              {
                defaultValue: khoaLamViec?.id,
                tenPhieu: item.ten,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  denKhoaId: filterData?.denKhoaId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P143") {
          //Phiếu tư vấn giáo dục sức khoẻ cho người bệnh tại khoa lâm sàng
          let dsTieuChi = await getTieuChi({
            maBaoCao: item.ma,
            dataSearch: { nbDotDieuTriId },
          });

          refModalInPhieuTuVanGDSK.current &&
            refModalInPhieuTuVanGDSK.current.show(
              {
                dsTieuChi: dsTieuChi.map((x) => ({
                  id: x.khoaId,
                  ten: x.tenKhoa,
                  value: item.khoaId,
                })),
                defaultValue: khoaLamViec?.id,
                tenPhieu: item.ten,
                dsSoPhieu: item.dsSoPhieu || [],
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma === "P819") {
          //Phiếu khám tiền mê (không theo DV PTTT)
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );

          refModalInPhieuTienMe.current &&
            refModalInPhieuTienMe.current.show(
              {
                defaultValue: khoaLamViec?.id,
                tenPhieu: item.ten,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma === "P833") {
          //Phiếu tư vấn và đăng ký các dịch vụ tự nguyện
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );

          refModalInPhieuTuVanDangKyDVTuNguyen.current &&
            refModalInPhieuTuVanDangKyDVTuNguyen.current.show(
              {
                defaultValue: khoaLamViec?.id,
                tenPhieu: item.ten,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma === "P846") {
          //Đơn xin nằm giường dịch vụ theo yêu cầu
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );

          refModalInDonXinNamGiuongDVTheoYC.current &&
            refModalInDonXinNamGiuongDVTheoYC.current.show(
              {
                defaultValue: khoaLamViec?.id,
                tenPhieu: item.ten,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (["P217", "P940"].includes(item.ma)) {
          let resSanPhu = await getThongTinSanPhu(nbDotDieuTriId);

          refModalInGiayChungSinh.current &&
            refModalInGiayChungSinh.current.show(
              { phieu: item, data: resSanPhu?.data?.dsThongTinCon },
              ({ values } = {}) => {
                showFileEditor({
                  phieu: item,
                  ma: item.ma,
                  conThu: values.conThu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  mhParams: {
                    ...mhParams,
                    nbDotDieuTriId: nbDotDieuTriId,
                  },
                });
              }
            );

          return;
        } else if (item.ma == "P792") {
          //Phiếu đề nghị sử dụng dịch vụ yêu cầu
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalInPhieuDeNghiSDDVYeuCau.current &&
            refModalInPhieuDeNghiSDDVYeuCau.current.show(
              {
                defaultValue: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
              },
              (filterData = {}) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (["P688"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalChonPhieuTruyenDich.current &&
            refModalChonPhieuTruyenDich.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                });
              }
            );
        } else if (["P086"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalChonKhoaPhieuTruyenDich.current &&
            refModalChonKhoaPhieuTruyenDich.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P620") {
          //Phiếu theo dõi và điều trị thận nhân tạo (cấp cứu/ chu kỳ)
          refModalInPhieuTheoDoiDieuTriThan.current &&
            refModalInPhieuTheoDoiDieuTriThan.current.show(
              {
                khoaId: khoaLamViec?.id,
                dsSoPhieu: (item.dsSoPhieu || []).filter(
                  (x) => x.soPhieu != nbDotDieuTriId
                ),
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaId: filterData?.khoaId,
                  ngayTheoDoi: filterData?.ngayTheoDoi,
                  loaiTheoDoi: filterData?.loaiTheoDoi,
                  mhParams,
                });
              }
            );
        } else if (
          ["P537", "P941", "P963", "P366", "P651", "P1073"].includes(item.ma)
        ) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          //Giấy cam đoan chấp nhận phẫu thuật, thủ thuật và gây mô hồi sức (Mẫu dùng cho bệnh viện phổi)
          refModalInPhieuCamDoanChapNhanPTTT.current &&
            refModalInPhieuCamDoanChapNhanPTTT.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu || [],
                ten: item.ten,
                ma: item.ma,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                  inKemParams: _dsPhieuInKemTheo,
                });
              }
            );
        } else if (["P1090"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          // Phiếu tư vấn - hướng dẫn - giáo dục sức khỏe
          refModalInPhieuTuVanHuongDanGiaoDucSucKhoe.current &&
            refModalInPhieuTuVanHuongDanGiaoDucSucKhoe.current.show(
              {
                nbDotDieuTriId,
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu || [],
                ten: item.ten,
                ma: item.ma,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                  inKemParams: _dsPhieuInKemTheo,
                });
              }
            );
        } else if (["P044", "P151"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalInGiayNghiHuongBHXH.current &&
            refModalInGiayNghiHuongBHXH.current.show(
              {
                dsSoPhieu: _dsSoPhieu || [],
                ten: item.ten,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  thoiGianThucHien: filterData?.thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (
          [
            "P136",
            "P185",
            "P514",
            "P557",
            "P834",
            "P829",
            "P956",
            "P361",
            "P774",
            "P784",
            "P781",
            "P1132",
            "P911",
            "P1185",
            "P933",
            "P11129",
            "P11126",
            "P11124",
            "P11127",
          ].includes(item.ma)
        ) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          //Phiếu chăm sóc nặng và chăm sóc nhẹ
          refModalInPhieuChamSoc.current &&
            refModalInPhieuChamSoc.current.show(
              {
                khoaLamViec: khoaLamViec,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
                tenPhieu: item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;

                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (["P1237", "P1238"]?.includes(item.ma)) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          //Phiếu theo dõi chăm sóc trẻ sơ sinh không thở máy || thở máy
          refModalInPhieuChamSocTreSSKhongThoMay.current &&
            refModalInPhieuChamSocTreSSKhongThoMay.current.show(
              {
                khoaLamViec: khoaLamViec,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
                tenPhieu: item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;

                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P922") {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          //Biểu đồ chuyển dạ
          refModalInBieuDoChuyenDa.current &&
            refModalInBieuDoChuyenDa.current.show(
              {
                khoaLamViec: khoaLamViec,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
                tenPhieu: item.ten,
              },
              (data) => {
                const {
                  thoiGianThucHien,
                  khoaChiDinhId,
                  id: idPhieu,
                  boSung,
                } = data;

                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  boSung,
                  mhParams,
                });
              }
            );
        } else if (["P1111", "P088"]?.includes(item.ma)) {
          refModalInToDieuTri.current &&
            refModalInToDieuTri.current.show({
              nbDotDieuTriId,
              khoaId: khoaLamViec?.id,
              maBaoCao: item.ma,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              mhParams,
            });
        } else if (["P191", "P181", "P182"].includes(item.ma)) {
          //P181: Phiếu bàn giao thuốc
          //P182: Phiếu sử dụng thuốc
          refModalChonPhieuCongKhaiThuoc.current &&
            refModalChonPhieuCongKhaiThuoc.current.show(
              {
                thoiGianYLenh: listToDieuTri[0]?.thoiGianYLenh,
                khoaChiDinhId: khoaLamViec?.id,
                listDanhSachKhoa: item.ma == "P181" ? listKhoaTheoTaiKhoan : [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: nbDotDieuTriId,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  thoiGianYLenh: filterData?.thoiGianYLenh,
                  mhParams,
                });
              }
            );
        } else if (
          ["P406", "P718", "P998", "P1187", "P1188", "P1190", "P1189"].includes(
            item.ma
          )
        ) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalPhieuHenKhamDieuTriNgoaiTru.current &&
            refModalPhieuHenKhamDieuTriNgoaiTru.current.show(
              {
                khoaLamViec: khoaLamViec,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
                ten: item.tenBaoCao || item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;

                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (
          item.ma == "P183" ||
          item.ma == "P531" ||
          item.ma == "P532" ||
          item.ma == "P533" ||
          item.ma == "P534" ||
          item.ma == "P535" ||
          item.ma == "P536"
        ) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) =>
              x.soPhieu != nbDotDieuTriId &&
              x.soPhieu != "null" &&
              x.baoCaoId == item.baoCaoId
          );
          //Phiếu thực hiện và công khai thuốc
          refModalChonPhieuCongKhaiThuoc2.current &&
            refModalChonPhieuCongKhaiThuoc2.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  dsLoaiChiDinh: filterData?.dsLoaiChiDinh,
                  mhParams: {
                    ...mhParams,
                    ...(item.maBaoCao === "EMR_HSDD015.4" && {
                      dsChiDinhTuLoaiDichVu: [
                        LOAI_DICH_VU.NOI_TRU,
                        LOAI_DICH_VU.NHA_THUOC,
                      ],
                      chiDinhTuLoaiDichVu: null,
                    }),
                  },
                });
              }
            );
        } else if (["P197", "P389", "P666", "P669"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) =>
              x.soPhieu != nbDotDieuTriId &&
              x.soPhieu != "null" &&
              x.baoCaoId == item.baoCaoId
          );
          //Phiếu thực hiện và công khai vật tư y tế tiêu hao
          refModalChonPhieuCongKhaiVtyt.current &&
            refModalChonPhieuCongKhaiVtyt.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
              },
              (filterData) => {
                const payload = {
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                };
                showFileEditor(payload);
              }
            );
        } else if (["P637"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) =>
              x.soPhieu != nbDotDieuTriId &&
              x.soPhieu != "null" &&
              x.baoCaoId == item.baoCaoId
          );
          //Phiếu thực hiện và công khai vật tư y tế tiêu hao
          refModalChonPhieuCongKhaiVtyt.current &&
            refModalChonPhieuCongKhaiVtyt.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
                ma: item.ma,
              },
              (filterData) => {
                const payload = {
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaId: filterData?.khoaChiDinhId,
                  tuNgay: filterData?.denThoiGian
                    ? moment(filterData.denThoiGian).format("YYYY-MM-DD")
                    : "",
                  denNgay: filterData?.denThoiGian
                    ? moment(filterData.denThoiGian).format("YYYY-MM-DD")
                    : "",
                  mhParams,
                };

                showFileEditor(payload);
              }
            );
        } else if (item.ma == "P149") {
          refModalInPhieuHuyThuoc.current &&
            refModalInPhieuHuyThuoc.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: item.dsSoPhieu || [],
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  dsPhieuLinhId: (filterData?.dsPhieuLinhId || []).join(","),
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                });
                return;
              }
            );
          return;
        } else if (
          item.ma === "P141" ||
          item.ma === "P563" ||
          item.ma === "P564"
        ) {
          refModalPhieuPHCN.current &&
            refModalPhieuPHCN.current.show(item, (values) => {
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: nbDotDieuTriId,
                id: nbDotDieuTriId,
                ma: item.ma,
                loai: LIST_FILE_SHOW_MODAL[item.ma],
                loaiIn:
                  item.dsBaoCao && item.dsBaoCao.length > 0
                    ? item.dsBaoCao[0]?.loaiIn
                    : null,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                tuThoiGian: values.tuThoiGianThucHien,
                denThoiGian: values.denThoiGianThucHien,
                mhParams,
              });
            });
        } else if (["P370", "P438", "P467"].includes(item.ma)) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          //Sao bệnh án
          refModalInPhieuSaoBA.current &&
            refModalInPhieuSaoBA.current.show(
              { khoaLamViec: khoaLamViec, dsSoPhieu: _dsSoPhieu, ma: item.ma },
              (data) => {
                const {
                  thoiGianThucHien,
                  khoaChiDinhId,
                  id: idPhieu,
                  denThoiGian,
                } = data;
                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  ...(item.ma === "P467"
                    ? { tuThoiGian: thoiGianThucHien, denThoiGian }
                    : { thoiGianThucHien }),
                  mhParams,
                });
              }
            );
        } else if (
          ["P396", "P422", "P439", "P395", "P489", "P632", "P712"].includes(
            item.ma
          )
        ) {
          //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) =>
              x.soPhieu != nbDotDieuTriId && x.soPhieu && x.soPhieu !== "null"
          );
          //Tóm tắt bệnh án
          refModalInPhieuTomTatBA.current &&
            refModalInPhieuTomTatBA.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
                ten: item.ten,
              },
              (data) => {
                const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  thoiGianThucHien,
                  mhParams,
                });
              }
            );
        } else if (["P603", "P607", "P953"].includes(item.ma)) {
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalPhieuBanGiaoNguoiBenh.current &&
            refModalPhieuBanGiaoNguoiBenh.current.show(
              {
                khoaChuyenDiId: khoaNbId,
                dsSoPhieu: _dsSoPhieu || [],
                ma: item.ma,
              },
              ({
                khoaChuyenDiId,
                idPhieu,
                khoaChuyenDenId,
                thoiGianBanGiao,
              }) => {
                try {
                  showFileEditor({
                    phieu: item,
                    id: idPhieu,
                    nbDotDieuTriId: nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: nbDotDieuTriId,
                    thoiGianBanGiao,
                    khoaChuyenDenId,
                    khoaChuyenDiId,
                    mhParams,
                  });
                } catch (error) {
                  console.log(error);
                }
              }
            );
        } else if (["P992"].includes(item.ma)) {
          refModalPhieuBanGiaoNguoiBenhSauPhauThuat.current &&
            refModalPhieuBanGiaoNguoiBenhSauPhauThuat.current.show(
              {},
              ({ khoaChiDinhId, denKhoaId, thoiGianBanGiao }) => {
                try {
                  showFileEditor({
                    phieu: item,
                    nbDotDieuTriId: nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: nbDotDieuTriId,
                    thoiGianThucHien: thoiGianBanGiao,
                    khoaChiDinhId,
                    denKhoaId,
                    mhParams,
                  });
                } catch (error) {
                  console.log(error);
                }
              }
            );
        } else if (["P616"].includes(item.ma)) {
          //Phiếu thử phản ứng thuốc
          const _dsSoPhieu = (item.dsSoPhieu || []).filter(
            (x) => x.soPhieu != nbDotDieuTriId
          );
          refModalPhieuThuPhanUngThuoc.current &&
            refModalPhieuThuPhanUngThuoc.current.show(
              {
                khoaId: khoaLamViec?.id,
                dsSoPhieu: _dsSoPhieu,
                ten: item.ten,
                ma: item.ma,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  khoaId: filterData?.khoaId,
                  tuThoiGian: filterData?.tuThoiGian,
                  denThoiGian: filterData?.denThoiGian,
                  mhParams,
                  khoaChiDinhId: filterData?.khoaId,
                });
              }
            );
        } else if (["P041"].includes(item.ma)) {
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: nbDotDieuTriId,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            khoaChiDinhId: khoaNbId,
            notPrint: true,
            nbThongTinId: thongTinCoBan.nbThongTinId,
            mhParams,
          });
        } else if (LIST_FILE_SHOW_MODAL[item.ma]) {
          //các mã bảng kê, luôn truyền thông tin sang để lấy phiếu in kèm
          mhParams = {
            maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
            maViTri:
              type == 0
                ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
            nbDotDieuTriId,
          };
          if (
            BANG_KE_IN_THEO_KHOA_CHI_DINH?.toLowerCase() === "true" &&
            ["P178", "P107", "P727"].includes(item.ma)
            //P178: Bảng kê chi tiết KCB nội trú
            //P107: Bảng kê chi phí nội trú
            //P727: Bảng kê chi tiết chi phí phẫu thuật (Viện phí, Tiện ích, Hao phí)
          ) {
            refModalChonTieuChiBangKe.current.show(
              {
                khoaNbId: [chiTietNguoiBenhNoiTru.khoaNbId],
                khoaChiDinhId:
                  item.ma === "P727" && chiTietNguoiBenhNoiTru?.khoaNbId,
              },
              (values) => {
                const data = {
                  id: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  loai: LIST_FILE_SHOW_MODAL[item.ma],
                  loaiIn: item.loaiIn,
                  tuThoiGianThucHien: values.tuThoiGianThucHien
                    ? moment(values.tuThoiGianThucHien).valueOf()
                    : null,
                  denThoiGianThucHien: values.denThoiGianThucHien
                    ? moment(values.denThoiGianThucHien).valueOf()
                    : null,
                  dsKhoaChiDinhId: values.dsKhoaChiDinhId,
                  ...mhParams,
                };

                window.refModalViewPhieu.current &&
                  window.refModalViewPhieu.current.show(data);
              }
            );
          } else {
            window.refModalViewPhieu.current &&
              window.refModalViewPhieu.current.show({
                id: nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                loai: LIST_FILE_SHOW_MODAL[item.ma],
                loaiIn: item.loaiIn,
                hinhThucIn: item.hinhThucIn,
                ...mhParams,
              });
          }
        } else if (["P864"].includes(item.ma)) {
          const dataSearch = {
            nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id || nbDotDieuTriId,
            loaiHoiChan: 40,
          };
          const s = await nbBienBanHoiChanProvider.search({
            page: 0,
            size: 500,
            ...dataSearch,
          });
          let dsTieuChi = (s?.data || []).map((item) => {
            const loaiHoiChan = listLoaiHoiChan?.find(
              (x) => x.id === item.loaiHoiChan
            );
            return {
              id: item.id,
              ten: (
                <span
                  title={`${item.khoaHoiChan?.ten}-${loaiHoiChan?.ten}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>{`${item.khoaHoiChan?.ten} - ${loaiHoiChan?.ten} `}</b>-{" "}
                  {moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              value: item.id,
              uniqueText: createUniqueText(
                `${item.maDichVu}-${item.tenDichVu}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                if (checkIsPhieuKySo(item)) {
                  //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
                  mhParams.id = idTieuChi;
                }
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: nbDotDieuTriId,
                  id: idTieuChi,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  notPrint: true,
                  mhParams,
                });
              }
            );
        } else if (["P973"].includes(item.ma)) {
          const dsSoPhieu = item.dsSoPhieu.filter(
            (el) => el.soPhieu !== nbDotDieuTriId
          );
          refModalBieuDoNongDoBilirubin.current.show(
            {
              dsSoPhieu,
            },
            ({ tuanThai }) => {
              debugger;
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                tuanThai,
                mhParams,
              });
            }
          );
        } else {
          if (LIST_PHIEU_CHON_TIEU_CHI.includes(item.ma)) {
            let dsNhomDichVuCap2Id;
            if (item.ma === "P168") {
              const response = await nhomDichVuCap2Provider.searchAll({
                size: "",
                page: "",
                active: true,
                dsMaThietLap: ["MA_NHOM_DICH_VU_CAP2_CT"],
              });
              dsNhomDichVuCap2Id = (response?.data || []).map(
                (item) => item.id
              );
            }
            const dataSearch = {
              nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id || nbDotDieuTriId,
              dataNHOM_GIAI_PHAU_BENH,
              dsNhomDichVuCap2Id,
            };
            let dsTieuChi = await getTieuChi({
              maBaoCao: item.ma,
              dataSearch:
                item.ma === "P137"
                  ? {
                      nbDotDieuTriId,
                    }
                  : dataSearch,
            });
            dsTieuChi = sortBy(dsTieuChi, "thoiGianThucHien", "asc").map(
              (item) => {
                return {
                  id: item.id,
                  ten: (
                    <span
                      title={`${item.maDichVu}-${item.tenDichVu}-${moment(
                        item.thoiGianThucHien
                      ).format("DD/MM/YYYY")}`}
                    >
                      <b>{`${item.maDichVu} - ${item.tenDichVu} `}</b>-{" "}
                      {moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                    </span>
                  ),
                  value: item.id,
                  uniqueText: createUniqueText(
                    `${item.maDichVu}-${item.tenDichVu}-${moment(
                      item.thoiGianThucHien
                    ).format("DD/MM/YYYY")}`
                  ),
                };
              }
            );
            refModalChonTieuChi &&
              refModalChonTieuChi.current.show(
                { data: dsTieuChi },
                (idTieuChi) => {
                  if (checkIsPhieuKySo(item)) {
                    //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
                    mhParams.id = idTieuChi;
                  }
                  showFileEditor({
                    phieu: item,
                    nbDotDieuTriId: nbDotDieuTriId,
                    nbDvXetNgiemId: idTieuChi,
                    id: idTieuChi,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    khoaChiDinhId: khoaNbId,
                    notPrint: true,
                    mhParams,
                  });
                }
              );
          } else if (
            LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU.includes(item.ma)
          ) {
            let dsTieuChi = item.dsSoPhieu;
            dsTieuChi = sortBy(dsTieuChi, "thoiGianThucHien", "asc").map(
              (item) => {
                return {
                  id: item.soPhieu,
                  ten: (
                    <span
                      title={`${item.ten1}-${item.ten2}-${moment(
                        item.thoiGianThucHien
                      ).format("DD/MM/YYYY")}`}
                    >
                      <b>
                        {item.ten1}-{item.ten2}
                      </b>
                      -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                    </span>
                  ),
                  value: item.soPhieu,
                  uniqueText: createUniqueText(
                    `${item.ten1}-${item.ten2}-${moment(
                      item.thoiGianThucHien
                    ).format("DD/MM/YYYY")}`
                  ),
                };
              }
            );
            if (dsTieuChi?.length === 1) {
              if (checkIsPhieuKySo(item)) {
                mhParams.id = dsTieuChi[0]?.id;
              }
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: nbDotDieuTriId,
                nbDvXetNgiemId: dsTieuChi[0]?.id,
                id: dsTieuChi[0]?.id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: khoaNbId,
                notPrint: true,
                mhParams,
              });
            } else {
              refModalChonTieuChi &&
                refModalChonTieuChi.current.show(
                  { data: dsTieuChi },
                  (idTieuChi) => {
                    if (checkIsPhieuKySo(item)) {
                      //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
                      mhParams.id = idTieuChi;
                    }
                    showFileEditor({
                      phieu: item,
                      nbDotDieuTriId: nbDotDieuTriId,
                      nbDvXetNgiemId: idTieuChi,
                      id: idTieuChi,
                      ma: item.ma,
                      maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                        ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                        : "",
                      khoaChiDinhId: khoaNbId,
                      notPrint: true,
                      mhParams,
                    });
                  }
                );
            }
          } else {
            showFileEditor({
              phieu: item,
              nbDotDieuTriId: nbDotDieuTriId,
              id: nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              khoaChiDinhId: khoaNbId,
              khoaLamViecId: khoaLamViec?.id,
              notPrint: true,
              chiDinhTuLoaiDichVu: 201,
              mhParams: {
                ...mhParams,
                baoCaoId: item.baoCaoId,
              },
              ngonNguParams: item?.ngonNguParams,
              inKemParams: _dsPhieuInKemTheo,
            });
          }
        }
      } else if (item.ma == "P092") {
        //Phiếu công khai dịch vụ khám, chữa bệnh nội trú
        refModalInPhieuCongKhai.current &&
          refModalInPhieuCongKhai.current.show(
            { khoaLamViec: khoaLamViec, dsSoPhieu: item.dsSoPhieu || [] },
            async (data) => {
              const {
                tuThoiGian,
                denThoiGian,
                dsNhomDichVuCap1Id,
                khoaChiDinhId,
                dsLoaiDichVu,
                dsThoiGian,
                id: idPhieu,
                hienThiNgayCoChiDinh,
              } = data;
              if (item.loaiBieuMau === LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM) {
                if (checkIsPhieuKySo(item)) {
                  refModalSignPrint.current &&
                    refModalSignPrint.current.showToSign({
                      phieuKy: item,
                      payload: {
                        nbDotDieuTriId: nbDotDieuTriId,
                        maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                        maViTri:
                          type == 0
                            ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                            : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
                        chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
                        khoaChiDinhId: khoaNbId || khoaLamViec?.id,
                        tuThoiGian,
                        denThoiGian,
                        dsNhomDichVuCap1Id,
                        dsLoaiDichVu,
                        dsThoiGian,
                        hienThiNgayCoChiDinh,
                      },
                    });
                } else {
                  await getFileAndPrint({
                    listPhieus: [item],
                    showError: true,
                    nbDotDieuTriId: nbDotDieuTriId,
                    tuThoiGian,
                    denThoiGian,
                    dsNhomDichVuCap1Id,
                    khoaChiDinhId,
                    dsLoaiDichVu,
                    dsThoiGian,
                    hienThiNgayCoChiDinh,
                  });
                }
              } else {
                let mhParams = {};
                //kiểm tra phiếu ký số
                if (checkIsPhieuKySo(item)) {
                  //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
                  mhParams = {
                    maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                    maViTri:
                      type == 0
                        ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                        : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
                    kySo: true,
                    maPhieuKy: item.ma,
                    nbDotDieuTriId,
                    hienThiNgayCoChiDinh,
                  };
                }
                showFileEditor({
                  phieu: item,
                  id: idPhieu,
                  nbDotDieuTriId: nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: nbDotDieuTriId,
                  khoaChiDinhId,
                  tuThoiGian,
                  denThoiGian,
                  dsLoaiDichVu,
                  dsNhomDichVuCap1Id,
                  mhParams,
                  dsThoiGian,
                });
              }
            }
          );
      } else if (item.ma == "P050") {
        refModalChonKetQuaXetNghiem.current &&
          refModalChonKetQuaXetNghiem.current.show({
            nbDotDieuTriId,
            baoCaoId: item.baoCaoId,
          });
      } else if (item.ma == "P054") {
        const res = await getDsGiayChuyenTuyen(nbDotDieuTriId);
        if (res?.length) {
          if (res.length === 1) {
            if (checkIsPhieuKySo(item)) {
              refModalSignPrint.current &&
                refModalSignPrint.current.showToSign({
                  phieuKy: item,
                  payload: {
                    id: res[0].id,
                    maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                    maViTri:
                      type == 0
                        ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                        : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
                    nbDotDieuTriId: nbDotDieuTriId,
                    khoaChiDinhId: khoaNbId || khoaLamViec?.id,
                  },
                });
            } else {
              await getFileAndPrint({
                listPhieus: [item],
                id: res[0].id,
              });
            }
          } else {
            refModalGiayChuyenTuyen.current &&
              refModalGiayChuyenTuyen.current.show();
          }
        }
      } else if (item.ma == "P214") {
        const res = await onSearch({ dataSearch: { nbDotDieuTriId } });
        if (res?.length) {
          if (res.length === 1) {
            if (checkIsPhieuKySo(item)) {
              refModalSignPrint.current &&
                refModalSignPrint.current.showToSign({
                  phieuKy: item,
                  payload: {
                    id: res[0].id,
                    maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                    maViTri:
                      type == 0
                        ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                        : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
                    nbDotDieuTriId: nbDotDieuTriId,
                  },
                });
            } else {
              await getFileAndPrint({
                listPhieus: [item],
                id: res[0].id,
              });
            }
          } else {
            refModalPhieuSoKet.current && refModalPhieuSoKet.current.show();
          }
        }
      } else {
        let listItems = [];
        //nếu là Phiếu phẫu thuật (114) or Phiếu thủ thuật (024) thì in tất cả phiếu
        //nếu là Phiếu chứng nhận phẫu thuật (060) or Phiếu chứng nhận thủ thuật (065) thì in tất cả phiếu
        if (["P114", "P024", "P060", "P065"].includes(item.ma)) {
          (item.dsSoPhieu || []).forEach((element) => {
            listItems.push({ ...item, dsSoPhieu: [element] });
          });
        } else {
          listItems = [item];
        }

        if (item.ma === "P036") {
          //Phiếu ra viện
          capNhatThongTinTruongKhoa(item);
        }
        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                nbDotDieuTriId: nbDotDieuTriId,
                maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                maViTri:
                  type == 0
                    ? VI_TRI_PHIEU_IN.NOI_TRU.IN_BAC_SI
                    : VI_TRI_PHIEU_IN.NOI_TRU.IN_DIEU_DUONG,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
                khoaChiDinhId: khoaNbId || khoaLamViec?.id,
                ngonNguParams: item?.ngonNguParams,
              },
              kyTheoTungPhieu: ["P114", "P024", "P060", "P065"].includes(
                item.ma
              ),
            });
        } else {
          const _listPhieuIn =
            type == 0 ? state.listPhieuBacSi : state.listPhieuDieuDuong;
          //get ds các phiếu kèm theo
          const _dsPhieuInKemTheo = _listPhieuIn.filter(
            (x) =>
              (item.dsPhieuInKemTheoId || []).includes(x.id) &&
              x.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM
          );

          await getFileAndPrint({
            listPhieus: [...listItems, ..._dsPhieuInKemTheo],
            nbDotDieuTriId: nbDotDieuTriId,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
            showError: true,
            ngonNguParams: item?.ngonNguParams,
          });
        }
      }
    }
  };

  return (
    <>
      <GlobalStyle />
      <Popover
        overlayClassName="dieuTriNoiTru__button-print"
        showArrow={false}
        destroyTooltipOnHide
        placement={"topLeft"}
        content={
          <ContentPrint
            onChange={(_, phieu) => {
              onPrintPhieu(0, phieu)();
            }}
            getListPhieu={refreshPhieu}
            data={state.listPhieuBacSi}
          />
        }
        trigger="click"
        allowContentInherit
      >
        <Button rightIcon={<SVG.IcPrint />} iconHeight={15}>
          {t("quanLyNoiTru.inBacSi")}
        </Button>
      </Popover>
      <Popover
        overlayClassName="dieuTriNoiTru__button-print"
        showArrow={false}
        destroyTooltipOnHide
        placement={"topLeft"}
        content={
          <ContentPrint
            getListPhieu={refreshPhieuDieuDuong}
            onChange={(_, phieu) => {
              onPrintPhieu(1, phieu)();
            }}
            data={state.listPhieuDieuDuong}
          />
        }
        trigger="click"
        allowContentInherit
      >
        <Button rightIcon={<SVG.IcPrint />} iconHeight={15}>
          {t("quanLyNoiTru.inDieuDuong")}
        </Button>
      </Popover>

      <ModalChonPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalChonPhieuCongKhaiThuoc2 ref={refModalChonPhieuCongKhaiThuoc2} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalChonPhieuCongKhaiVtyt ref={refModalChonPhieuCongKhaiVtyt} />
      <ModalChonTieuChiBangKe ref={refModalChonTieuChiBangKe} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
      <ModalInPhieuCongKhai ref={refModalInPhieuCongKhai} />
      <ModalChonKetQuaXetNghiem ref={refModalChonKetQuaXetNghiem} />
      <ModalInToDieuTriNhieuNgay ref={refModalInToDieuTri} />
      <ModalCheckBaoHiem ref={refModalCheckBaoHiem} isShowButtonOk={true} />
      <ModalInPhieuTra ref={refModalInPhieuTra} />
      <ModalGiayChuyenTuyen ref={refModalGiayChuyenTuyen} />
      <ModalInPhieuLinh ref={refModalInPhieuLinh} />
      <ModalDanhSachPhieuSoKet ref={refModalPhieuSoKet} />
      <ModalPhieuPHCN ref={refModalPhieuPHCN} />
      <ModalInPhieuHuyThuoc ref={refModalInPhieuHuyThuoc} />
      <ModalInPhieuSaoBA ref={refModalInPhieuSaoBA} />
      <ModalInPhieuTomTatBenhAn ref={refModalInPhieuTomTatBA} />
      <ModalInPhieuChamSoc ref={refModalInPhieuChamSoc} />
      <ModalInPhieuCamDoanChapNhanPTTT
        ref={refModalInPhieuCamDoanChapNhanPTTT}
      />
      <ModalChonPhieuCanThiepDuoc ref={refModalChonPhieuCanThiepDuoc} />
      <ModalPhieuHenKhamDieuTriNgoaiTru
        ref={refModalPhieuHenKhamDieuTriNgoaiTru}
      />
      <ModalPhieuBanGiaoNguoiBenh ref={refModalPhieuBanGiaoNguoiBenh} />
      <ModalChonPhieuThuPhanUngThuoc ref={refModalPhieuThuPhanUngThuoc} />
      <ModalInPhieuTheoDoiDieuTriThan ref={refModalInPhieuTheoDoiDieuTriThan} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonKhoaPhieuTruyenDich} />
      <ModalInPhieuTuVanGDSK ref={refModalInPhieuTuVanGDSK} />
      <ModalInPhieuDeNghiSDDVYeuCau ref={refModalInPhieuDeNghiSDDVYeuCau} />
      <ModalInGiayChungSinh ref={refModalInGiayChungSinh} />
      <ModalInPhieuTienMe ref={refModalInPhieuTienMe} />
      <ModalInPhieuTuVanDangKyDVTuNguyen
        ref={refModalInPhieuTuVanDangKyDVTuNguyen}
      />
      <ModalInDonXinNamGiuongDVTheoYC ref={refModalInDonXinNamGiuongDVTheoYC} />
      <ModalPhieuBieuDoNongDoBilirubin ref={refModalBieuDoNongDoBilirubin} />
      <ModalInBangKiemNbTruocPhauThuat
        ref={refModalInBangKiemNbTruocPhauThuat}
      />
      <ModalPhieuBanGiaoNguoiBenhSauPhauThuat
        ref={refModalPhieuBanGiaoNguoiBenhSauPhauThuat}
      />
      <ModalInBieuDoChuyenDa ref={refModalInBieuDoChuyenDa} />
      <ModalInGiayNghiHuongBHXH ref={refModalInGiayNghiHuongBHXH} />
      <ModalInPhieuTuVanHuongDanGiaoDucSucKhoe
        ref={refModalInPhieuTuVanHuongDanGiaoDucSucKhoe}
      />
      <ModalInPhieuPC_CAMKET_PTTT_TYCNT
        ref={refModalInPhieuPC_CAMKET_PTTT_TYCNT}
      />
      <ModalInPhieuChamSocTreSSKhongThoMay
        ref={refModalInPhieuChamSocTreSSKhongThoMay}
      />
    </>
  );
};

export default memo(ButtonPrint);
