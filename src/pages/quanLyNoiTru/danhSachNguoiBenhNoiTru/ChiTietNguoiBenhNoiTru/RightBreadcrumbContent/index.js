import React, { useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from "components";
import { ROLES, THIET_LAP_CHUNG } from "constants/index";
import ButtonTaoPhieuLinhTra from "pages/quanLyNoiTru/components/ButtonTaoPhieuLinhTra";
import ModalQLDieuDuongPhuTrachPhongDuong from "../../ModalQLDieuDuongPhuTrachPhongDuong";
import { useTranslation } from "react-i18next";
import ModalCapPhatSdThuoc from "../../ModalCapPhatSdThuoc";
import { DS_TINH_CHAT_KHOA } from "constants/index";
import { POPUP_TYPE } from "../../ModalCapPhatSdThuoc/config";
import { useHistory } from "react-router-dom";
import { useStore, useThietLap, useConfirm } from "hooks";
import moment from "moment";
import { Main } from "./styled";

export default function RightBreadcrumbContent({ khoaLamViec }) {
  const { t } = useTranslation();
  const history = useHistory();
  const refModalQuanLyDieuDuong = useRef(null);
  const refModalCapPhatSdThuoc = useRef(null);

  const { showConfirm } = useConfirm();

  const [dataHIEN_THI_TINH_NANG_CHUYEN_MO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TINH_NANG_CHUYEN_MO
  );

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );

  const onClickQuanLyDieuDuong = () => {
    refModalQuanLyDieuDuong.current && refModalQuanLyDieuDuong.current.show();
  };

  const onCapPhatThuoc = () => {
    if (!chiTietNguoiBenhNoiTru?.maHoSo) {
      showConfirm({
        title: t("common.thongBao"),
        content: t(
          "quanLyNoiTru.duLieuNguoiBenhChuaDuocTaiXongXinVuiLongThuLaiSau"
        ),
        cancelText: t("common.dong"),
        showBtnOk: false,
        typeModal: "warning",
      });

      return;
    }
    refModalCapPhatSdThuoc.current &&
      refModalCapPhatSdThuoc.current.show(
        {
          dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
          maHoSo: chiTietNguoiBenhNoiTru?.maHoSo,
        },
        {},
        {
          popupType: POPUP_TYPE.DS_CHI_TIET_BAN_GIAO_VA_SD_THUOC,
        }
      );
  };

  const onClickChuyenMo = () => {
    const params = {
      khongThucHienDvkt: false,
      dsTrangThai: [25, 43, 63],
      maHoSo: chiTietNguoiBenhNoiTru?.maHoSo,
      khoaThucHienId: khoaLamViec?.id,
      // phongThucHienId: phongChoMoId,
      tuThoiGianThucHien: moment(
        chiTietNguoiBenhNoiTru?.thoiGianVaoVien
      ).valueOf(),
      denThoiGianThucHien: moment.now(),
    };
    let url = "";

    Object.keys(params).forEach((item, index) => {
      if (params[item]) {
        url += `${index === 0 ? "" : "&"}${item}=${params[item]}`;
      }
    });

    history.push({
      pathname: `/phau-thuat-thu-thuat/danh-sach-nguoi-benh`,
      search: `?${url}`,
    });
  };
  const onClickDanhSachNbSuDungDVKT = () => {
    history.push(
      `/quan-ly-noi-tru/danh-sach-nguoi-benh-su-dung-dvkt?maHoSo=${
        chiTietNguoiBenhNoiTru?.maHoSo
      }&tuThoiGianThucHien=-&denThoiGianThucHien=-&fromUrl=${encodeURIComponent(
        history.location.pathname
      )}`
    );
  };
  return (
    <Main>
      {dataHIEN_THI_TINH_NANG_CHUYEN_MO?.eval() &&
        (khoaLamViec?.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.PHAU_THUAT
        ) && (
          <Button onClick={onClickChuyenMo}>
            {t("quanLyNoiTru.chuyenMo")}
          </Button>
        )}

      <Button onClick={onClickDanhSachNbSuDungDVKT}>
        {t("quanLyNoiTru.danhSachNbSuDungDVKT")}
      </Button>
      <AuthWrapper
        accessRoles={[
          ROLES["QUAN_LY_NOI_TRU"]
            .HIEN_THI_POPUP_QL_DIEU_DUONG_PHU_TRACH_GIUONG,
        ]}
      >
        <Button onClick={onClickQuanLyDieuDuong}>
          {t("quanLyNoiTru.bangPhanCongDieuDuong")}
        </Button>
      </AuthWrapper>
      <AuthWrapper
        accessRoles={[ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_QUAN_LY_CAP_PHAT_THUOC]}
      >
        <Button onClick={onCapPhatThuoc}>
          {t("quanLyNoiTru.capPhatThuoc.capPhatVaSuDungThuoc")}
        </Button>
      </AuthWrapper>

      <ButtonTaoPhieuLinhTra khoaLamViec={khoaLamViec} />
      <ModalQLDieuDuongPhuTrachPhongDuong
        khoaLamViec={khoaLamViec}
        ref={refModalQuanLyDieuDuong}
      />
      <ModalCapPhatSdThuoc ref={refModalCapPhatSdThuoc} />
    </Main>
  );
}
