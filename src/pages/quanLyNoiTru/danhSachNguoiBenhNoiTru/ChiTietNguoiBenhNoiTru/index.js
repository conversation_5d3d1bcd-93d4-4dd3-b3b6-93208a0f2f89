import React, {
  useEffect,
  memo,
  useCallback,
  useState,
  useRef,
  useMemo,
  Fragment,
} from "react";
import { Menu } from "antd";
import ModalChuyenKhoa from "./Modal/ModalChuyenKhoa";
import ModalNghiDieuTri from "./Modal/ModalNghiDieuTri";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, useLocation, useParams } from "react-router-dom";
import {
  Dropdown,
  Button,
  Card,
  KhoaThucHien,
  ModalSignPrint,
  ThongTinBenh<PERSON>han,
  Tooltip,
  TableWrapper,
  HeaderSearch,
} from "components";
import {
  CACHE_KEY,
  DATA_MODE_DS_NB,
  DOI_TUONG_KCB,
  DS_TINH_CHAT_KHOA,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DUYET_BH,
  TRANG_THAI_NB,
  LOAI_DANG_KY,
  ENUM,
  DOI_TUONG,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import {
  useCache,
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import ModalDongHoSo from "./Modal/ModalKetThucDieuTri";
import ModalChuyenVien from "./ThongTinVaoVien/ModalChuyenVien";
import ModalDuKienRaVien from "./Modal/ModalDuKienRaVien";
import ModalYeuCauMuonNb from "./Modal/ModalYeuCauMuonNb";
import ModalKiemTraHoSo from "./Modal/ModalKiemTraHoSo";
import ModalDuyetYeuCauMuonNb from "./Modal/ModalDuyetYeuCauMuonNb";
import ModalNgatDieuTri from "./Modal/ModalNgatDieuTri";
import ModalSoDoPhongGiuong from "../../ModalSoDoPhongGiuong";
import ModalGiayChuyenTuyen from "./Modal/ModalGiayChuyenTuyen";
import ModalHoanThanhBA from "./Modal/ModalHoanThanhBA";
import { isArray } from "utils/index";
import { orderBy } from "lodash";
import ModalChonLoaiPHCN from "./Modal/ModalChonLoaiPHCN";
import TabsList from "./TabsList";
import ButtonPrint from "./ButtonPrint";
import DanhSachBenhNhanSidePanel from "./DanhSachBenhNhanSidePanel";
import DropdownSangLocDD from "pages/quanLyDinhDuong/chiTietNbQuanLyDinhDuong/components/DropdownSangLocDD";
import ModalTuVanThuoc from "./Modal/ModalTuVanThuoc";
import ModalTaoPhieuTuVanThuoc from "./Modal/ModalTaoPhieuTuVanThuoc";
import ModalChiTietTuVanThuoc from "./Modal/ModalChiTietTuVanThuoc";
import RightBreadcrumbContent from "./RightBreadcrumbContent";
import DanhSachBenhNhan from "./DanhSachBenhNhan";
import ModalHenKhamCMU from "./Modal/ModalHenKhamCMU";
import ModalHenDieuTriNgoaiTru from "./Modal/ModalHenDieuTriNgoaiTru";
import ModalChinhSuaThongTin from "pages/tiepDon/TiepDon/ModalChinhSuaThongTin";
import ModalChonLoaiDangKyKetHopDieuTri from "./Modal/ModalChonLoaiDangKyKetHopDieuTri";
import ModalNhapLyDoDuyetChiPhi from "./Modal/ModalNhapLyDoDuyetChiPhi";
import { MainPage, GlobalStyle, Main, InfoMeCon } from "./styled";
import ModalChotDotDieuTri from "./Modal/ModalChotDotDieuTri";
import { SVG } from "assets";
import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";
import { onCheckThongTinTheBh } from "utils/index";
import moment from "moment";
import ModalChinhSuaMocThoiGian from "./Modal/ModalChinhSuaMocThoiGian";
import { useKiemTraChongChiDinh } from "./hooks/useKiemTraChongChiDinh";

const ChiTietNguoiBenhNoiTru = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const history = useHistory();
  const { state: { searchQueryString = "" } = {} } = useLocation();
  const { id: nbDotDieuTriId } = useParams();
  const { showLoading, hideLoading } = useLoading();

  const refModalChuyenKhoa = useRef(null);
  const refModalSoDoPhongGiuong = useRef(null);
  const refModalNghiDieuTri = useRef(null);
  const refModalSignPrint = useRef(null);
  const refModalDongHoSo = useRef(null);
  const refModalChuyenVien = useRef(null);
  const refModalDuKienRaVien = useRef(null);
  const refModalYeuCauMuonNb = useRef(null);
  const refModalKiemTraHoSo = useRef(null);
  const refModalDuyetYeuCauMuonNb = useRef(null);
  const refModalNgatDieuTri = useRef(null);
  const refModalGiayChuyenTuyen = useRef(null);
  const refModalHoanThanhBA = useRef(null);
  const refModalChonLoaiPHCN = useRef(null);
  const refDanhSachBenhNhanSidePanel = useRef(null);
  const refModalTuVanThuoc = useRef(null);
  const refModalTaoPhieuTuVanThuoc = useRef(null);
  const refModalChiTietTuVanThuoc = useRef(null);
  const refModalHenKhamCMU = useRef(null);
  const refModalHenDieuTriNgoaiTru = useRef(null);
  const refModalChinhSuaThongTin = useRef(null);
  const refModalDangKyKetHop = useRef(null);
  const refModalNhapLyDoDuyetChiPhi = useRef(null);
  const refModalChotDotDieuTri = useRef(null);
  const refModalChinhSuaMocThoiGian = useRef(null);

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );

  const { checkChongChiDinh } = useKiemTraChongChiDinh(chiTietNguoiBenhNoiTru);

  const trangThaiDuyetBh = useStore(
    "dsDuyetBH.chiTietDuyetBH.trangThaiDuyetBh",
    null
  );
  const dsTrangThaiPhcn = useStore("dieuTriPHCN.dsTrangThaiPhcn", []);
  const activeKey = useStore("toDieuTri.activeKey", null);
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const dsPhongGiuong = useStore("noiTruPhongGiuong.dsPhongGiuong", []);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");

  const [BAT_BUOC_PHAN_PHONG_GIUONG_NB_NOI_TRU, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_PHAN_PHONG_GIUONG_NB_NOI_TRU,
    ""
  );
  const [dataTIEP_DON_HEN_DIEU_TRI_KHONG_YEU_CAU_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_HEN_DIEU_TRI_KHONG_YEU_CAU_THANH_TOAN,
    "FALSE"
  );
  const [dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN] = useThietLap(
    THIET_LAP_CHUNG.BO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN,
    ""
  );
  const [dataMA_QUAY_MAC_DINH_NOI_TRU_QRCODE] = useThietLap(
    THIET_LAP_CHUNG.MA_QUAY_MAC_DINH_NOI_TRU_QRCODE,
    null
  );

  const [dataNHAP_LY_DO_GUI_DUYET_CHI_PHI] = useThietLap(
    THIET_LAP_CHUNG.NHAP_LY_DO_GUI_DUYET_CHI_PHI
  );
  const [dataHIEN_THI_GIO_SINH] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_GIO_SINH
  );
  const [dataTU_DONG_TICH_CO_LICH_HEN_KHAM_DOI_TUONG_BH_HEN_DIEU_TRI] =
    useThietLap(
      THIET_LAP_CHUNG.TU_DONG_TICH_CO_LICH_HEN_KHAM_DOI_TUONG_BH_HEN_DIEU_TRI
    );
  const [dataCANH_BAO_RA_VIEN_THIEU_MA_SO_GIAY_TO_TUY_THAN_HOAC_SO_BHXH] =
    useThietLap(
      THIET_LAP_CHUNG.CANH_BAO_RA_VIEN_THIEU_MA_SO_GIAY_TO_TUY_THAN_HOAC_SO_BHXH
    );

  const {
    trangThai,
    khoaNbId,
    doiTuongKcb,
    maHoSo,
    maBenhAn,
    trangThaiBenhAn,
  } = chiTietNguoiBenhNoiTru || {};

  const {
    danhSachNguoiBenhNoiTru: {
      getNbNoiTruById,
      tiepNhanVaoKhoa,
      tuChoiVaoKhoa,
      huyTiepNhanVaoKhoa,
      choVaoVienLai,
      getDanhSachMuonNb,
      clearData: clearDataNoiTru,
      tiepDonHenDieuTri,
      getDsPhieuThuByMaHS,
      guiDuyetChiPhi,
      huyTiepDonTheoHen,
    },
    maBenh: { getListAllMaBenh },
    toDieuTri: { getToDieuTri },
    nhanVien: { getListNhanVienTongHop },
    phieuIn: { getListPhieu },
    nbDotDieuTri: {
      getThongTinCoBan,
      getThongTinRaVien,
      clearData: clearDataNb,
      getByTongHopId,
      getById,
    },
    quanLyNoiTru: { getDsGiayChuyenVien },
    soDoPhongGiuong: { getDsKhoaNb },
    nbDvKho: { getNbTonTaiTraKho },
    dieuTriPHCN: { getTrangThaiPhcn, huyDangKyDotPHCN, dangKyDotPHCN },
    nbChuyenKhoa: { getDsPhongGiuong },
    dsDuyetBH: { getChiTietDuyetBH, duyetBaoHiem, huyDuyetBaoHiem },
    nbTuVanThuoc: { searchTuVanThuocByNb },
    danhSachLichHen: { onChangeInputSearch },
    soHieuGiuong: { getListAllSoHieuGiuong },
    noiTruPhongGiuong: { onSearch },
    quanLyDieuTriLao: { getThuocLaoById, capNhatDangKy },
    nbMaBenhAn: { getDsMaBADaiHan },
    dsPhieuThuChotDotDieuTri: {
      getListAllPhieuThuChotDotDieuTri,
      huyChotDotDieuTri,
    },
    nbThongTinSanPhu: { getById: getThongTinSanPhu },
    tiepDon: { giamDinhThe },
  } = useDispatch();

  const authId = useStore("auth.auth.id");
  const { nbTonTaiVatTu } = useSelector((state) => state.nbDvKho);

  const listDsMuonNb = useStore("danhSachNguoiBenhNoiTru.listDsMuonNb", []);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listAllSoHieuGiuong = useStore("soHieuGiuong.listAllSoHieuGiuong", []);
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );
  const chiTietDuyetBH = useStore("dsDuyetBH.chiTietDuyetBH", {});
  const [modeDsNb, setModeDsNb] = useCache(
    authId,
    CACHE_KEY.DATA_MODE_DS_NB,
    DATA_MODE_DS_NB.DRAWER,
    false
  );

  const [listAllQuayTiepDon] = useListAll("quayTiepDon", true);

  const [state, _setState] = useState({
    isDrawerDsnb: false,
    dsPhieuThuNb: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { listAllDieuTriKetHopTheoLoai, listAllDieuTriKetHopTheoKhoa } =
    useListDieuTriKetHop({
      manHinh: "NOI_TRU",
      khoaId: state.khoaLamViec?.id,
      options: DIEU_TRI_KET_HOP_OPTIONS.CHI_TIET_TO_DIEU_TRI,
    });

  const quayMacDinhNoiTruQRCode = useMemo(() => {
    const _quay = (listAllQuayTiepDon || []).find(
      (x) => x.ma === dataMA_QUAY_MAC_DINH_NOI_TRU_QRCODE
    );

    return _quay;
  }, [listAllQuayTiepDon, dataMA_QUAY_MAC_DINH_NOI_TRU_QRCODE]);

  const { listDataPhcn, listDataYhct } = useMemo(() => {
    let listDataPhcn = [];
    let listDataYhct = [];
    if (dsTrangThaiPhcn.length > 0) {
      listDataPhcn = dsTrangThaiPhcn.filter((x) => x.loai === 10);
      listDataYhct = dsTrangThaiPhcn.filter((x) => x.loai === 20);
    }
    return {
      listDataPhcn,
      listDataYhct,
    };
  }, [dsTrangThaiPhcn]);

  useEffect(() => {
    return () => {
      clearDataNb();
      clearDataNoiTru();
    };
  }, []);

  useEffect(() => {
    getListAllMaBenh({ active: true, page: "", size: "" });
  }, []);

  const isTonTaiLoaiDieuTriKetHopChuaDangKy = useMemo(() => {
    const listLoaiPhcnNb = new Set(
      dsTrangThaiPhcn
        .filter((x) => [10, 30].includes(x.trangThai))
        .map((x) => x.loaiPhcnId)
    );
    return !listAllDieuTriKetHopTheoLoai.every((x) => listLoaiPhcnNb.has(x.id));
  }, [dsTrangThaiPhcn, listAllDieuTriKetHopTheoLoai]);

  useEffect(() => {
    if (maHoSo) {
      getTrangThaiPhcn({
        nbDotDieuTriId,
      });

      getDsPhieuThuByMaHS({
        maHoSo: maHoSo,
        doiTuongKcb: DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
      }).then((data) => {
        setState({ dsPhieuThuNb: data });
      });
    }
  }, [maHoSo]);

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru.id && state.khoaLamViec?.id) {
      getDanhSachMuonNb({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        tuKhoaId: state.khoaLamViec?.id,
      });
      getNbTonTaiTraKho({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        khoaChiDinhId: state.khoaLamViec?.id,
      });
      getToDieuTri({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        dsKhoaChiDinhId: state.khoaLamViec?.id,
      });
      getListAllSoHieuGiuong({
        active: true,
        page: "",
        size: "",
        khoaId: state.khoaLamViec?.id,
      });
      onSearch({ nbDotDieuTriId: nbDotDieuTriId, sort: "tuThoiGian,asc" });
      if (checkRole([ROLES["QUAN_LY_NOI_TRU"].DS_PHIEU_THU_CHOT_DOT_DIEU_TRI]))
        getListAllPhieuThuChotDotDieuTri({
          page: "",
          size: "",
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          chotDotDieuTri: true,
        });
      getThongTinSanPhu(chiTietNguoiBenhNoiTru.id);
    }
  }, [chiTietNguoiBenhNoiTru.id, state.khoaLamViec?.id]);
  useEffect(() => {
    if (khoaNbId) {
      getListNhanVienTongHop({
        khoaId: khoaNbId,
        size: "",
        page: "",
      });
    }
  }, [khoaNbId]);

  useEffect(() => {
    if (nbDotDieuTriId) {
      getNbNoiTruById(nbDotDieuTriId);
      getThongTinRaVien(nbDotDieuTriId);
      getDsKhoaNb({ nbDotDieuTriId: nbDotDieuTriId });
      getById(nbDotDieuTriId); // get để lấy maDoiTuongKcb?.ma

      Promise.all([
        getListPhieu({
          nbDotDieuTriId: nbDotDieuTriId,
          maManHinh: "006",
          maViTri: "00601",
          chiDinhTuLoaiDichVu: 200,
        }),
      ])
        .then((listPhieu) => {
          setState({
            listPhieuBacSi: listPhieu[0] || [],
          });
        })
        .catch((e) => {});
      getListPhieu({
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId: nbDotDieuTriId,
        maManHinh: "003",
        maViTri: "00303",
      }).then((res) => {
        if (isArray(res, true)) {
          const phieDonThuoc = res.find((i) => i.ma === "P051");
          if (phieDonThuoc) setState({ phieDonThuoc });
        }
      });
      getChiTietDuyetBH(nbDotDieuTriId, true);
    }
  }, [nbDotDieuTriId]);

  useEffect(() => {
    if (nbDotDieuTriId && state.khoaLamViec?.id) {
      getListPhieu({
        nbDotDieuTriId: nbDotDieuTriId,
        maManHinh: "006",
        maViTri: "00602",
        chiDinhTuLoaiDichVu: 200,
        khoaId: state.khoaLamViec?.id,
      }).then((res) => {
        setState({
          listPhieuDieuDuong: res || [],
        });
      });
    }
  }, [nbDotDieuTriId, state.khoaLamViec?.id]);

  const showGioSinh = useMemo(() => {
    const data = dataHIEN_THI_GIO_SINH?.split("/");
    if (isArray(data, 2)) {
      const listMaKhoa = data[1]?.split(",").map((i) => i.trim());
      return (
        data[0]?.toLowerCase() === "true" &&
        (listMaKhoa || []).includes(chiTietNguoiBenhNoiTru?.maKhoaNb)
      );
    } else if (isArray(data, 1)) {
      return data[0]?.toLowerCase() === "true";
    }
  }, [chiTietNguoiBenhNoiTru, dataHIEN_THI_GIO_SINH]);

  const isBatBuocPhanPhongGiuong = useMemo(() => {
    const listData = dsPhongGiuong?.filter(
      (x) => x.khoaNbId === chiTietNguoiBenhNoiTru.khoaNbId
    );
    const phongGiuong = listData?.length
      ? orderBy(listData, "tuThoiGian", "desc")[0]
      : [];
    if (phongGiuong?.soHieuGiuong) {
      return { isError: false, isMessage: false };
    }

    if (isLoadFinish) {
      let arr = BAT_BUOC_PHAN_PHONG_GIUONG_NB_NOI_TRU.split("/");
      let isTreSoSinh =
        thongTinCoBan?.theTam && thongTinBenhNhan?.maDoiTuongKcb?.ma === "1.7";
      if (
        arr.length === 1 &&
        arr[0]?.toLowerCase() === "true" &&
        !isTreSoSinh &&
        thongTinCoBan?.doiTuongKcb !== DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY
      ) {
        return { isError: true, isMessage: true };
      } else if (arr.length === 2) {
        if (
          [
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
          ].includes(thongTinCoBan?.doiTuongKcb) &&
          listAllSoHieuGiuong.length &&
          !isTreSoSinh
        ) {
          if (
            arr[0]?.toLowerCase() === "true" &&
            !arr[1]?.includes(phongGiuong?.maKhoa)
          ) {
            return { isError: true, isMessage: true };
          } else {
            return { isError: false, isMessage: true };
          }
        }
      }
      return { isError: false, isMessage: !isTreSoSinh };
    }
    return { isError: false, isMessage: false };
  }, [
    isLoadFinish,
    dsPhongGiuong,
    thongTinCoBan,
    listAllSoHieuGiuong,
    BAT_BUOC_PHAN_PHONG_GIUONG_NB_NOI_TRU,
    thongTinBenhNhan,
  ]);

  const onChuyenKhoa = () => {
    getNbNoiTruById(nbDotDieuTriId);
    getDsPhongGiuong({
      nbDotDieuTriId: nbDotDieuTriId,
      sort: "tuThoiGian,desc",
    }).then((s) => {
      if (s[0]?.loai === 30) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t("quanLyNoiTru.nguoiBenhChuyenKhoaMaChuaPhanPhongGiuong"),
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            classNameOkText: "button-confirm",
            showImg: true,
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            refModalChuyenKhoa.current && refModalChuyenKhoa.current.show();
          }
        );
      } else {
        refModalChuyenKhoa.current && refModalChuyenKhoa.current.show();
      }
    });
  };
  const onSubmit = useCallback(
    async (e, key) => {
      showLoading();
      let isNeedReloadPage = false;

      if (key === "huyTiepNhanNb")
        await huyTiepNhanVaoKhoa({ id: chiTietNguoiBenhNoiTru.id });
      else {
        let name = e?.currentTarget?.name;
        if (name === "huyTiepNhanNb") {
          await huyTiepNhanVaoKhoa({ id: chiTietNguoiBenhNoiTru.id });
        } else if (name === "tiepNhanVaoKhoa") {
          await tiepNhanVaoKhoa({ id: chiTietNguoiBenhNoiTru.id });
          await getThongTinCoBan(nbDotDieuTriId);

          isNeedReloadPage = true;
        } else if (name === "tuChoiTiepNhanVaoKhoa") {
          await tuChoiVaoKhoa({ id: chiTietNguoiBenhNoiTru.id });
          onSearch({ nbDotDieuTriId: nbDotDieuTriId, sort: "tuThoiGian,asc" });
        }
      }

      getNbNoiTruById(nbDotDieuTriId);
      hideLoading();

      if (isNeedReloadPage) {
        setTimeout(() => {
          history.go();
        }, 1000);
      }
    },
    [chiTietNguoiBenhNoiTru.id, nbDotDieuTriId]
  );

  const onDongHoSo = () => {
    if (thongTinCoBan.theTam) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("quanLyNoiTru.nguoiBenhDungTheTam"),
          cancelText: t("common.quayLai"),
          okText: t("quanLyNoiTru.tiepTucChoRaVien"),
          classNameOkText: "button-confirm",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refModalDongHoSo.current && refModalDongHoSo.current.show();
        }
      );
    } else if (
      dataCANH_BAO_RA_VIEN_THIEU_MA_SO_GIAY_TO_TUY_THAN_HOAC_SO_BHXH?.eval()
    ) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("quanLyNoiTru.vuiLongBoSungSoGiayToTuyThanTruocKhiRaVien"),
          cancelText: t("common.quayLai"),
          okText: t("quanLyNoiTru.tiepTucChoRaVien"),
          classNameOkText: "button-confirm",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refModalDongHoSo.current && refModalDongHoSo.current.show();
        }
      );
    } else {
      refModalDongHoSo.current && refModalDongHoSo.current.show();
    }
  };

  const onChoVaoVienLai = () => {
    showLoading();
    choVaoVienLai({ id: nbDotDieuTriId, trangThai })
      .then(() => {
        getNbNoiTruById(nbDotDieuTriId);
      })
      .finally(() => hideLoading());
  };

  const onChotDotDieuTri = () => {
    refModalChotDotDieuTri.current &&
      refModalChotDotDieuTri.current.show({}, () => {
        getListAllPhieuThuChotDotDieuTri({
          page: "",
          size: "",
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          chotDotDieuTri: true,
        });
      });
  };

  const onHuyChotDotDieuTri = async () => {
    if (!listAllPhieuThuChotDot.length) return;

    try {
      showLoading();
      await Promise.all(
        listAllPhieuThuChotDot.map(async (item) => {
          await huyChotDotDieuTri(item?.chotDotDieuTriId);
        })
      );

      getListAllPhieuThuChotDotDieuTri({
        page: "",
        size: "",
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        chotDotDieuTri: true,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onDuKienRaVien = () => {
    refModalDuKienRaVien.current && refModalDuKienRaVien.current.show();
  };

  const onYeuCauMuonNb = () => {
    refModalYeuCauMuonNb.current &&
      refModalYeuCauMuonNb.current.show({ khoaLamViec: state.khoaLamViec });
  };

  const onDuyetYeuCauMuonNb = () => {
    refModalDuyetYeuCauMuonNb.current &&
      refModalDuyetYeuCauMuonNb.current.show();
  };

  const onNgatDieuTri = () => {
    if (thongTinCoBan.theTam) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("quanLyNoiTru.nguoiBenhDungTheTam"),
          cancelText: t("common.quayLai"),
          okText: t("quanLyNoiTru.tiepTucChoRaVien"),
          classNameOkText: "button-confirm",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refModalNgatDieuTri.current && refModalNgatDieuTri.current.show();
        }
      );
    } else {
      refModalNgatDieuTri.current && refModalNgatDieuTri.current.show();
    }
  };

  const onShowDsGiayChuyenTuyen = () => {
    refModalGiayChuyenTuyen.current && refModalGiayChuyenTuyen.current.show();
  };

  const onDangKyPHCN = async () => {
    if (
      dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN.toLowerCase() === "true"
    ) {
      await dangKyDotPHCN({
        nbDotDieuTriId,
        dsPhanLoaiPhcnId: null,
        loai: LOAI_DANG_KY.PHUC_HOI_CHUC_NANG,
      });
      await getTrangThaiPhcn({ nbDotDieuTriId });
    } else {
      refModalChonLoaiPHCN.current &&
        refModalChonLoaiPHCN.current.show({
          maHoSo,
          nbDotDieuTriId,
          maBenhAn,
        });
    }
  };
  const onDangKyYHCT = async () => {
    await dangKyDotPHCN({
      nbDotDieuTriId,
      dsPhanLoaiPhcnId: null,
      loai: LOAI_DANG_KY.Y_HOC_CO_TRUYEN,
    });
    await getTrangThaiPhcn({ nbDotDieuTriId });
  };

  const { dieuKienPHCN, dieuKienYHCT } = useMemo(() => {
    const checkRole = (listData) => {
      let dangKy = true,
        huyDangKy = false;
      let idHuyDangKy = null;

      //đã có đợt phcn => ẩn nút đăng ký
      if (listData.length > 0) {
        //update 5/4/2023: NB chưa có bản ghi PHCN nào hoặc tồn tại các bản ghi PHCN và trạng thái tất cả bản ghi = Đã hủy (20) (Ngoài các TH trên thì ko hiển thị button Đăng ký)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        if (listData.some((x) => x.trangThai != 20 && x.trangThai != 40)) {
          dangKy = false;
        }

        //update 5/4/2023: Truyền id bản ghi PHCN có stt nhỏ nhất , trạng thái # Đã hủy (20)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        const _listDangKy = orderBy(
          listData.filter((x) => x.trangThai === 10) || [],
          "stt",
          "asc"
        );
        if (_listDangKy.length > 0) {
          huyDangKy = true;
          idHuyDangKy = _listDangKy[0].id;
        }
      }

      if (
        chiTietNguoiBenhNoiTru.trangThai < 5 ||
        chiTietNguoiBenhNoiTru.trangThai > 50
      ) {
        dangKy = false;
        huyDangKy = false;
        idHuyDangKy = null;
      }
      return {
        dangKy,
        huyDangKy,
        idHuyDangKy,
      };
    };

    const dieuKienPHCN = checkRole(listDataPhcn);
    const dieuKienYHCT = checkRole(listDataYhct);
    return {
      dieuKienPHCN,
      dieuKienYHCT,
    };
  }, [listDataPhcn, listDataYhct, chiTietNguoiBenhNoiTru.trangThai]);

  const onHuyDangKy = (loai) => () => {
    const id =
      loai === 10 ? dieuKienPHCN.idHuyDangKy : dieuKienYHCT.idHuyDangKy;
    showLoading();
    huyDangKyDotPHCN({
      nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
      id,
      loai,
    })
      .then(() => {
        getTrangThaiPhcn({
          nbDotDieuTriId,
        });
      })
      .finally(() => hideLoading());
  };

  const onNghiDieuTri = () => {
    refModalNghiDieuTri.current &&
      refModalNghiDieuTri.current.show({
        nbDotDieuTri: nbDotDieuTriId,
      });
  };

  const onShowModalChuyenVien = (
    { dsCdRaVienId, vienChuyenDenId, loai, huongDieuTri },
    callFirstFunc = null,
    callBackFunc = null
  ) => {
    if (refModalChuyenVien.current) {
      showLoading();
      getDsGiayChuyenVien(chiTietNguoiBenhNoiTru.id)
        .then((s) => {
          //check nb có tồn tại giấy chuyển tuyến chuyên khoa = false (loại = 20)
          const giayChuyenVien = (s || []).find((x) => x.loai != 20);

          setState({
            nbChanDoan: {
              dsCdChinhId: dsCdRaVienId
                ? dsCdRaVienId
                : chiTietNguoiBenhNoiTru.dsCdRaVienId || null,
            },
            nbChuyenVien: giayChuyenVien
              ? {
                  ...giayChuyenVien,
                }
              : {
                  nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
                  loai,
                },
            vienChuyenDenId: vienChuyenDenId,
            huongDieuTri,
          });
          refModalChuyenVien.current.show({}, callFirstFunc, callBackFunc);
        })
        .finally(() => hideLoading());
    }
  };

  const onDangKyKetHop = (isDangKy) => (e) => {
    refModalDangKyKetHop.current &&
      refModalDangKyKetHop.current.show({
        nbDotDieuTriId,
        isDangKy: isDangKy,
        khoaChiDinhId: state.khoaLamViec?.id,
      });
  };

  const onChinhSuaMocThoiGian = () => {
    refModalChinhSuaMocThoiGian.current &&
      refModalChinhSuaMocThoiGian.current.show({ nbDotDieuTriId }, () => {
        getById(nbDotDieuTriId);
        getNbNoiTruById(nbDotDieuTriId);
      });
  };

  const dsKhoaId = useMemo(() => {
    if (state.khoaLamViec?.id) {
      return [state.khoaLamViec?.id];
    } else {
      return listKhoaTheoTaiKhoan.map((item) => item?.id);
    }
  }, [state.khoaLamViec, listKhoaTheoTaiKhoan]);

  const isKhoaNbNgatDieuTriNoiTru = useMemo(() => {
    if (khoaNbId) {
      const _khoaNb = listKhoaTheoTaiKhoan.find((x) => x.id === khoaNbId);
      if (_khoaNb) {
        return (_khoaNb?.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.NGAT_DIEU_TRI_NOI_TRU
        );
      }
    }

    return false;
  }, [listKhoaTheoTaiKhoan, khoaNbId]);

  const hienThiBtnTiepDonHenDieuTri = useMemo(() => {
    //Với Trạng thái NB = Hẹn điều trị + không tồn tại phiếu thu nội trú
    //hoặc Với Trạng thái NB = đã thanh toán
    //& Khoa nb = Khoa gán với user (trong danh mục nhân viên)
    //& Đối tượng KCB: Điều trị Ngoại trú
    //bổ sung: đối tượng kcb = DIEU_TRI_NOI_TRU và có tính chất khoa của người bệnh = Ngắt điều trị nội trú
    return (
      (((dataTIEP_DON_HEN_DIEU_TRI_KHONG_YEU_CAU_THANH_TOAN.eval() ||
        state.dsPhieuThuNb?.length === 0) &&
        trangThai === TRANG_THAI_NB.HEN_DIEU_TRI) ||
        trangThai === TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI) &&
      (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU ||
        (isKhoaNbNgatDieuTriNoiTru &&
          (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NOI_TRU ||
            doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY))) &&
      (dsKhoaId || []).includes(khoaNbId)
    );
  }, [trangThai, dsKhoaId, khoaNbId, doiTuongKcb, state.dsPhieuThuNb]);

  const onKiemTraHoSo = useRefFunc((payload = {}) => {
    refModalKiemTraHoSo.current?.show({
      khoaLamViec: state.khoaLamViec,
      tuKhoaId: chiTietNguoiBenhNoiTru.khoaNbId,
      nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
      ...payload,
    });
  });

  const onGuiDuyetChiPhi = () => {
    const onDuyetChiPhi = (lyDoDuyetChiPhi) => {
      showLoading();
      guiDuyetChiPhi({ id: nbDotDieuTriId, lyDoDuyetChiPhi })
        .then(() => {
          getNbNoiTruById(nbDotDieuTriId);
        })
        .finally(() => hideLoading());
    };
    if (dataNHAP_LY_DO_GUI_DUYET_CHI_PHI.eval()) {
      refModalNhapLyDoDuyetChiPhi.current.show((lyDoDuyetChiPhi) => {
        onDuyetChiPhi(lyDoDuyetChiPhi);
      });
    } else {
      onDuyetChiPhi();
    }
  };

  const onHoanThanhBa = () => {
    refModalHoanThanhBA.current && refModalHoanThanhBA.current.show();
  };

  const onTiepDonHenDieuTri = () => {
    refModalChinhSuaThongTin.current &&
      refModalChinhSuaThongTin.current.show(
        {
          id: chiTietNguoiBenhNoiTru.id,
          isTiepDonNoiTru: true,
          isTiepDonHenDieuTri: true,
          isTuDongTichCoLichHenKham:
            dataTU_DONG_TICH_CO_LICH_HEN_KHAM_DOI_TUONG_BH_HEN_DIEU_TRI?.eval(),
        },
        (data) => {
          tiepDonHenDieuTri({
            id: chiTietNguoiBenhNoiTru.id,
            ...data.data,
            khoaId: chiTietNguoiBenhNoiTru.khoaNbId,
            doiTuongKcb: chiTietNguoiBenhNoiTru.doiTuongKcb,
            loaiBenhAnId: chiTietNguoiBenhNoiTru.loaiBenhAnId,
          }).then((res) => {
            setTimeout(() => {
              history.push(
                "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/" + res?.data?.id
              );
            }, 500);
          });
        }
      );
  };

  const onActionBaoHiem = (isDuyet) => async () => {
    if (thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM) {
      showLoading();
      giamDinhThe({
        ngaySinh: moment(thongTinCoBan?.ngaySinh).format("YYYY-MM-DD"),
        maThe: thongTinCoBan?.maTheBhyt,
        hoTen: thongTinCoBan?.tenNb,
      })
        .then(async (res) => {
          const errors = onCheckThongTinTheBh({
            thongTinCoBan,
            dataBaoHiem: res.data,
            listGioiTinh,
          });
          if (errors?.length) {
            hideLoading();
            const content = (
              <div>
                <div style={{ color: "red", paddingBottom: "10px" }}>
                  {t("common.thongTinBaoHiemCuaNguoiBenhDangKhacTrenCong")}{" "}
                </div>
                <TableWrapper
                  columns={[
                    {
                      title: (
                        <HeaderSearch title={t("thuNgan.truongThongTin")} />
                      ),
                      width: 100,
                      dataIndex: "title",
                      key: "title",
                    },
                    {
                      title: <HeaderSearch title={t("baoCao.nguoiBenh")} />,
                      width: 150,
                      dataIndex: "nguoiBenh",
                      key: "nguoiBenh",
                    },
                    {
                      title: <HeaderSearch title={t("thuNgan.congBaoHiem")} />,
                      width: 150,
                      dataIndex: "congBh",
                      key: "congBh",
                    },
                  ]}
                  dataSource={errors || []}
                />
              </div>
            );
            showConfirm({
              title: t("common.thongBao"),
              content: content,
              cancelText: t("common.huy"),
              classNameOkText: "button-warning",
              typeModal: "warning",
              isContentElement: true,
              width: 768,
            });
          } else {
            try {
              isDuyet
                ? await duyetBaoHiem(nbDotDieuTriId)
                : await huyDuyetBaoHiem(nbDotDieuTriId);
              getChiTietDuyetBH(nbDotDieuTriId, true);
              getThongTinCoBan(nbDotDieuTriId);
            } finally {
              hideLoading();
            }
          }
        })
        .catch((err) => {
          hideLoading();
        });
    } else {
      showLoading();
      try {
        isDuyet
          ? await duyetBaoHiem(nbDotDieuTriId)
          : await huyDuyetBaoHiem(nbDotDieuTriId);
        getChiTietDuyetBH(nbDotDieuTriId, true);
        getThongTinCoBan(nbDotDieuTriId);
      } finally {
        hideLoading();
      }
    }
  };

  const onTuVanThuoc = async () => {
    const res = await searchTuVanThuocByNb(chiTietNguoiBenhNoiTru.id);

    if (res.length > 0) {
      refModalTuVanThuoc.current &&
        refModalTuVanThuoc.current.show({
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        });
    } else {
      refModalTaoPhieuTuVanThuoc.current &&
        refModalTaoPhieuTuVanThuoc.current.show(
          {
            nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          },
          (res) => {
            // refModalTuVanThuoc.current &&
            //   refModalTuVanThuoc.current.show({
            //     nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
            //   });
            if (res) {
              refModalChiTietTuVanThuoc.current &&
                refModalChiTietTuVanThuoc.current.show(res);
            }
          }
        );
    }
  };

  const onHenKhamCMU = async () => {
    showLoading();
    try {
      const res = await onChangeInputSearch({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        loai: 60,
      });
      refModalHenKhamCMU.current &&
        refModalHenKhamCMU.current.show({
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          isEdit: res.length > 0,
          data: res[0],
        });
    } finally {
      hideLoading();
    }
  };

  const onDieuTriNgoaiTru = async () => {
    try {
      showLoading();
      const res = await getDsMaBADaiHan(chiTietNguoiBenhNoiTru?.nbThongTinId);
      let dsMaBADangDieuTri = [];
      if (isArray(res, true)) {
        dsMaBADangDieuTri = res.filter(
          (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
        );
      }
      hideLoading();
      if (isArray(dsMaBADangDieuTri, true)) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t(
              "khamBenh.nbCoMaBADaiHanDangDieuTriBanCoMuonTiepTucDangKyDieuTriNgoaiTruKhong",
              {
                maBenhAn: dsMaBADangDieuTri.map((i) => i.maBenhAn).join(", "),
              }
            ),
            cancelText: t("common.quayLai"),
            okText: t("common.dongY"),
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            refModalHenDieuTriNgoaiTru.current &&
              refModalHenDieuTriNgoaiTru.current.show();
          },
          () => {}
        );
      } else {
        refModalHenDieuTriNgoaiTru.current &&
          refModalHenDieuTriNgoaiTru.current.show();
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onCapNhatDangKy = () => {
    capNhatDangKy({ nbDotDieuTriId: nbDotDieuTriId }).then(async (s) => {
      const res = await getByTongHopId(nbDotDieuTriId);
      if (res?.data?.thuocLaoId) getThuocLaoById(res?.data?.thuocLaoId);
    });
  };

  const onHuyTiepDonTheoHen = () => {
    showConfirm(
      {
        title: t("common.canhBao"),
        content: t("quanLyNoiTru.xacNhanHuyTiepDonHenDieuTri"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        try {
          showLoading();
          await huyTiepDonTheoHen(nbDotDieuTriId);
          getNbNoiTruById(nbDotDieuTriId);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const arrTienIchKhac = [
    {
      name: t("quanLyNoiTru.henKhamCmu"),
      action: onHenKhamCMU,
      condition:
        chiTietNguoiBenhNoiTru.id &&
        checkRole([ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_TIEN_ICH_HEN_KHAM_CMU]),
    },
    {
      name: t("quanLyNoiTru.yeuCauMuonNb"),
      action: onYeuCauMuonNb,
      condition:
        trangThai === TRANG_THAI_NB.DANG_DIEU_TRI &&
        state.khoaLamViec &&
        khoaNbId !== state.khoaLamViec?.id,
    },
    {
      name: t("quanLyNoiTru.nghiDieuTri"),
      action: onNghiDieuTri,
      condition:
        trangThai === TRANG_THAI_NB.DANG_DIEU_TRI &&
        (dsKhoaId || []).includes(khoaNbId) &&
        doiTuongKcb !== DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
    },
    {
      name: t("quanLyNoiTru.ngatDieuTri"),
      action: onNgatDieuTri,
      //Điều kiện hiển thị
      //Trạng thái NB: đang điều trị/ chờ hoàn tất thủ tục ra viện
      //đối tượng KCB = 2 (điều trị ngoại trú) hoặc = 3 (điều trị nội trú) và khoa nb có tính chất ngắt điều trị nội trú (95)
      condition:
        [
          TRANG_THAI_NB.DANG_DIEU_TRI,
          TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN,
        ].includes(trangThai) &&
        (dsKhoaId || []).includes(khoaNbId) &&
        (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU ||
          (isKhoaNbNgatDieuTriNoiTru &&
            (doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NOI_TRU ||
              doiTuongKcb === DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY))),
    },

    {
      name: t("quanLyNoiTru.phieuThuChotDot.chotDotDieuTri"),
      action: onChotDotDieuTri,
      condition: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].DS_PHIEU_THU_CHOT_DOT_DIEU_TRI,
      ]),
    },
    {
      name: t("quanLyNoiTru.phieuThuChotDot.huyChotDotDieuTri"),
      action: onHuyChotDotDieuTri,
      condition: listAllPhieuThuChotDot.length > 0,
    },

    {
      name: t("quanLyNoiTru.duKienRaVien"),
      action: onDuKienRaVien,
      condition:
        trangThai === TRANG_THAI_NB.DANG_DIEU_TRI &&
        (dsKhoaId || []).includes(khoaNbId),
    },
    {
      name: t("quanLyNoiTru.huyTiepNhanNb"),
      action: () => onSubmit(undefined, "huyTiepNhanNb"),
      condition:
        trangThai === TRANG_THAI_NB.DANG_DIEU_TRI &&
        (dsKhoaId || []).includes(khoaNbId),
    },
    {
      condition: dieuKienPHCN.dangKy,
      action: onDangKyPHCN,
      name:
        listDataPhcn && listDataPhcn.length > 0
          ? t("quanLyNoiTru.themMoiDotPHCN")
          : t("quanLyNoiTru.dangKyPHCN"),
    },
    {
      condition: dieuKienPHCN.huyDangKy,
      action: onHuyDangKy(10),
      name: t("quanLyNoiTru.huyDangKyPHCN"),
    },
    {
      condition:
        checkRole([ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_NB_DANG_KY_YHCT]) &&
        dieuKienYHCT.dangKy,
      action: onDangKyYHCT,
      name:
        listDataYhct && listDataYhct.length > 0
          ? t("quanLyNoiTru.themMoiDotYHCT")
          : t("quanLyNoiTru.dangKyYHCT"),
    },
    {
      condition:
        checkRole([ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_NB_DANG_KY_YHCT]) &&
        dieuKienYHCT.huyDangKy,
      action: onHuyDangKy(20),
      name: t("quanLyNoiTru.huyDangKyYHCT"),
    },
    {
      condition: checkRole([ROLES["KHO"].XEM_DS_TU_VAN_THUOC]),
      action: onTuVanThuoc,
      name: t("kho.tuVanThuoc.tuVanThuoc"),
    },
    {
      condition:
        chiTietNguoiBenhNoiTru.id &&
        checkRole([ROLES["KHAM_BENH"].DIEU_TRI_NGOAI_TRU]),
      action: onDieuTriNgoaiTru,
      name: t("tiepDon.dieuTriNgoaiTru"),
    },
    {
      condition: chiTietNguoiBenhNoiTru.id,
      action: onCapNhatDangKy,
      name: t("quanLyDieuTriLao.capNhatSoDangKy"),
    },
    {
      condition:
        checkRole([ROLES["QUAN_LY_NOI_TRU"].DIEU_TRI_KET_HOP]) &&
        isTonTaiLoaiDieuTriKetHopChuaDangKy && // không có loại điều trị kết hợp đăng ký nào thì ẩn button
        chiTietNguoiBenhNoiTru?.trangThai <= 50,
      action: onDangKyKetHop(true),
      name: t("phcn.dieuTriKetHop"),
    },
    {
      condition:
        checkRole([ROLES["QUAN_LY_NOI_TRU"].HUY_DIEU_TRI_KET_HOP]) &&
        dsTrangThaiPhcn?.length &&
        dsTrangThaiPhcn?.find((x) => x.trangThai !== 20) &&
        chiTietNguoiBenhNoiTru?.trangThai <= 50,
      action: onDangKyKetHop(false),
      name: t("phcn.huyDieuTriKetHop"),
    },
    {
      condition: checkRole([ROLES["QUAN_LY_NOI_TRU"].CHINH_SUA_MOC_THOI_GIAN]),
      action: onChinhSuaMocThoiGian,
      name: t("quanLyNoiTru.chinhSuaMocThoiGian"),
    },
  ];
  const arrTienIchKhacShow = arrTienIchKhac.filter((item) => item.condition);

  const menuTienIchKhac = useMemo(
    () => (
      <Menu>
        {arrTienIchKhacShow.map((item, key) => {
          return (
            <Menu.Item key={key} onClick={item.action}>
              {item.name}
            </Menu.Item>
          );
        })}
      </Menu>
    ),
    [arrTienIchKhacShow]
  );

  const openModalPhongGiuong = () => {
    refModalSoDoPhongGiuong.current &&
      refModalSoDoPhongGiuong.current.show({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        khoaId: khoaNbId,
      });
  };

  const onChangeKhoaThucHien = (khoaLamViec) => {
    setState({ khoaLamViec });
  };

  const openDrawerDsNb = useCallback(() => {
    refDanhSachBenhNhanSidePanel.current &&
      refDanhSachBenhNhanSidePanel.current.showDrawer({
        khoaLamViec: state.khoaLamViec,
      });
  }, [state.khoaLamViec]);

  const mergeTitle = (...args) => (
    <>
      {args
        .filter((item) => (item === 0 ? false : Boolean(item)))
        .map((arg, index) => (
          <React.Fragment key={index}>
            {index > 0 && " - "}
            {arg}
          </React.Fragment>
        ))}
    </>
  );

  const renderThongTinMeCon = () => {
    const renderIconOpenHSBA = (nbDotDieuTriId) => {
      if (!nbDotDieuTriId) return null;

      return (
        <SVG.IcShowFull
          style={{ position: "absolute", top: 5, right: 5 }}
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();

            history.push(
              `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}`
            );
          }}
        />
      );
    };

    if (chiTietNguoiBenhNoiTru.tenNbMe) {
      return (
        <InfoMeCon>
          <span className="info-title">{t("common.me")}:</span>
          <Tooltip
            color={"#fff"}
            title={
              <span style={{ color: "#000" }}>
                <b>{t("common.tenNb")}: </b>
                {chiTietNguoiBenhNoiTru.tenNbMe}
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                {renderIconOpenHSBA(chiTietNguoiBenhNoiTru.nbDotDieuTriMeId)}
                <br />
                <b>{t("common.maNb")}: </b>
                {chiTietNguoiBenhNoiTru.maNbMe}
                <br />
                <b>{t("baoCao.khoaNB")}: </b>
                {chiTietNguoiBenhNoiTru.tenKhoaNbMe}
                <br />
              </span>
            }
          >
            {mergeTitle(
              chiTietNguoiBenhNoiTru.tenNbMe,
              thongTinCoBan?.sdtNguoiBaoLanh1 || thongTinCoBan?.sdtNguoiBaoLanh2
            )}
          </Tooltip>
        </InfoMeCon>
      );
    }

    if (
      chiTietNguoiBenhNoiTru.dsThongTinCon &&
      chiTietNguoiBenhNoiTru.dsThongTinCon.length
    ) {
      return (
        <InfoMeCon>
          <span className="info-title">{t("common.con")}:</span>
          {chiTietNguoiBenhNoiTru.dsThongTinCon.map((item, index) => (
            <span>
              <Tooltip
                key={index}
                color={"#fff"}
                title={
                  <span style={{ color: "#000" }}>
                    <b>{t("common.tenNb")}: </b>
                    {item.tenNb}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {renderIconOpenHSBA(item.nbDotDieuTriId)}
                    <br />
                    <b>{t("common.maNb")}: </b>
                    {item.maNb}
                    <br />
                    <b>{t("baoCao.khoaNB")}: </b>
                    {item.tenKhoa}
                  </span>
                }
              >
                {item.tenNb}
              </Tooltip>
              {index < chiTietNguoiBenhNoiTru.dsThongTinCon.length - 1 && (
                <span style={{ marginRight: 2 }}>,</span>
              )}
            </span>
          ))}
        </InfoMeCon>
      );
    }
    return null;
  };

  return (
    <MainPage
      breadcrumb={[
        { link: "/quan-ly-noi-tru", title: t("quanLyNoiTru.quanLyNoiTru") },
        {
          title: t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
          to: () => ({
            pathname: "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru",
            search: searchQueryString,
          }),
        },
        {
          link: `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${chiTietNguoiBenhNoiTru.id}`,
          title: t("quanLyNoiTru.dieuTriNoiTru"),
        },
      ]}
      activeKey={activeKey}
      rightBreadcrumbContent={
        <RightBreadcrumbContent khoaLamViec={state.khoaLamViec} />
      }
      actionLeft={
        <>
          {!!arrTienIchKhacShow.length && (
            <Dropdown overlay={menuTienIchKhac} placement={"topRight"}>
              <Button>{t("quanLyNoiTru.tienIchKhac")}</Button>
            </Dropdown>
          )}

          {[
            TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN,
            TRANG_THAI_NB.DA_RA_VIEN,
            TRANG_THAI_NB.HEN_DIEU_TRI,
            TRANG_THAI_NB.CHO_DUYET_CHI_PHI,
            TRANG_THAI_NB.CHO_DUYET_CHI_PHI_HEN_DIEU_TRI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI,
          ].includes(trangThai) &&
            checkRole([ROLES["QUAN_LY_NOI_TRU"].CHO_VAO_VIEN_LAI]) && (
              <Button onClick={() => onChoVaoVienLai()}>
                {t("quanLyNoiTru.choVaoVienLai")}
              </Button>
            )}

          <div style={{ display: "flex" }}>
            {trangThai === TRANG_THAI_NB.DANG_DIEU_TRI &&
              (dsKhoaId || []).includes(khoaNbId) && (
                <>
                  <Button onClick={() => onChuyenKhoa()}>
                    {t("quanLyNoiTru.chuyenKhoan")}
                  </Button>
                  {!!(listDsMuonNb || []).filter((x) => x.trangThai === 10)
                    ?.length && (
                    <Button onClick={() => onDuyetYeuCauMuonNb()}>
                      {t("quanLyNoiTru.duyetYeuCauMuonNb")}
                    </Button>
                  )}
                </>
              )}

            {[
              TRANG_THAI_NB.DANG_DIEU_TRI,
              TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN,
              TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
              TRANG_THAI_NB.HEN_DIEU_TRI,
            ].includes(trangThai) &&
              (dsKhoaId || []).includes(khoaNbId) && (
                <>
                  <Button onClick={onDongHoSo}>
                    {t("quanLyNoiTru.ketThucDieuTri")}
                  </Button>
                </>
              )}
          </div>

          {/* {nbThongTinRaVien?.huongDieuTri === 40 && ( */}
          <Button onClick={() => onShowDsGiayChuyenTuyen()}>
            {t("quanLyNoiTru.giayChuyenCap")}
          </Button>
          {/* )} */}

          {hienThiBtnTiepDonHenDieuTri && (
            <Button onClick={onTiepDonHenDieuTri}>
              {t("quanLyNoiTru.tiepDonHenDieuTri")}
            </Button>
          )}

          {/* Nhóm các nút phục hồi chức năng */}
          {/* <BtnPHCN /> */}

          <Button onClick={() => onKiemTraHoSo()}>
            {t("quanLyNoiTru.kiemTraHoSo")}
          </Button>
          {[
            TRANG_THAI_NB.DA_RA_VIEN,
            TRANG_THAI_NB.HEN_DIEU_TRI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI,
          ].includes(trangThai) &&
            checkRole([ROLES["QUAN_LY_NOI_TRU"].GUI_DUYET_CHI_PHI]) && (
              <Button onClick={() => onGuiDuyetChiPhi()}>
                {t("quanLyNoiTru.guiDuyetChiPhi")}
              </Button>
            )}
          {nbTonTaiVatTu && (
            <Button
              onClick={() => {
                history.push(
                  "/quan-ly-noi-tru/tra-hang-hoa/" + chiTietNguoiBenhNoiTru.id
                );
              }}
              name="traThuocVatTuHoaChat"
            >
              {t("quanLyNoiTru.traThuocVatTuHoaChat")}
            </Button>
          )}

          {[
            TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
            TRANG_THAI_NB.DA_RA_VIEN,
            TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
            TRANG_THAI_NB.CHO_DUYET_CHI_PHI,
            TRANG_THAI_NB.DA_DUYET_CHI_PHI,
            TRANG_THAI_NB.CHO_DUYET_CHI_PHI_HEN_DIEU_TRI,
            TRANG_THAI_NB.DA_DUYET_CHI_PHI_HEN_DIEU_TRI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI,
            TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI,
          ].includes(trangThai) && (
            <>
              {[20, 40].includes(trangThaiBenhAn) && (
                <Button onClick={() => onHoanThanhBa()}>
                  {t("quanLyNoiTru.huyHoanThanhBa")}
                </Button>
              )}

              {[10].includes(trangThaiBenhAn) && (
                <Button onClick={() => onHoanThanhBa()}>
                  {t("quanLyNoiTru.hoanThanhBa")}
                </Button>
              )}
            </>
          )}
          {[
            TRANG_THAI_NB.DA_RA_VIEN,
            TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
            TRANG_THAI_NB.HEN_DIEU_TRI,
            TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
          ].includes(trangThai) &&
            chiTietDuyetBH?.id &&
            checkRole([ROLES["KE_HOACH_TONG_HOP"].DUYET_BAO_HIEM]) && (
              <Fragment>
                {trangThaiDuyetBh === TRANG_THAI_DUYET_BH.DA_DUYET_BH && (
                  <Button name="huyDuyetBH" onClick={onActionBaoHiem(false)}>
                    {t("khth.huyDuyetBH")}
                  </Button>
                )}
                {trangThaiDuyetBh === TRANG_THAI_DUYET_BH.CHUA_DUYET_BH && (
                  <Button name="duyetBH" onClick={onActionBaoHiem(true)}>
                    {t("khth.duyetBH")}
                  </Button>
                )}
              </Fragment>
            )}
          {trangThai === TRANG_THAI_NB.DA_TIEP_DON_HEN_DIEU_TRI && (
            <Button onClick={onHuyTiepDonTheoHen}>
              {t("quanLyNoiTru.huyTiepDonTheoHen")}
            </Button>
          )}
        </>
      }
      actionRight={
        <>
          {activeKey != "12" && <DropdownSangLocDD fromNoiTru={true} />}

          {trangThai === TRANG_THAI_NB.DANG_CHUYEN_KHOA &&
            state.khoaLamViec?.id === chiTietNguoiBenhNoiTru?.denKhoaId && (
              <Button onClick={onSubmit} name="tuChoiTiepNhanVaoKhoa">
                {t("quanLyNoiTru.tuChoiTiepNhanVaoKhoa")}
              </Button>
            )}
          {(trangThai === TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA ||
            (trangThai === TRANG_THAI_NB.DANG_CHUYEN_KHOA &&
              state.khoaLamViec?.id === chiTietNguoiBenhNoiTru?.denKhoaId)) && (
            <Button type="primary" onClick={onSubmit} name="tiepNhanVaoKhoa">
              {t("quanLyNoiTru.tiepNhanVaoKhoa")}
            </Button>
          )}
          <ButtonPrint
            khoaLamViec={state.khoaLamViec}
            listPhieuBacSi={state.listPhieuBacSi}
            listPhieuDieuDuong={state.listPhieuDieuDuong}
            nbDotDieuTriId={nbDotDieuTriId}
          />
        </>
      }
      title={t("quanLyNoiTru.dieuTriNoiTru")}
      titleRight={
        <KhoaThucHien
          dsTinhChatKhoa={DS_TINH_CHAT_KHOA.NOI_TRU}
          onChange={onChangeKhoaThucHien}
          allowReselectKhoa={true}
          type={2}
        />
      }
      contentRight={
        modeDsNb === DATA_MODE_DS_NB.MODULE && (
          <Card className="right-content" noPadding={true} bottom={0} top={10}>
            <DanhSachBenhNhan
              khoaLamViec={state.khoaLamViec}
              modeDsNb={modeDsNb}
              changeModeDsNb={setModeDsNb}
            />
          </Card>
        )
      }
    >
      <GlobalStyle />
      <Main className="wrapper">
        <div className="main">
          <ThongTinBenhNhan
            nbDotDieuTriId={nbDotDieuTriId}
            openDrawerDsNb={openDrawerDsNb}
            modeDsNb={modeDsNb}
            isShowLichSuTiemChung={true}
            isShowDeNghiTamUng={true}
            isShowNgaySinh={true}
            isShowDsPhieuThuTamUng={true}
            quayMacDinhNoiTruQRCode={quayMacDinhNoiTruQRCode}
            renderThongTinMeCon={renderThongTinMeCon}
            isShowGioSinh={showGioSinh}
            nbDotDieuTriMeId={chiTietNguoiBenhNoiTru.nbDotDieuTriMeId}
          />
          <Card className="content">
            <TabsList
              khoaLamViec={state.khoaLamViec}
              nbDotDieuTriId={nbDotDieuTriId}
              onKiemTraHoSo={onKiemTraHoSo}
              isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
              checkChongChiDinh={checkChongChiDinh}
            />
          </Card>
        </div>
        <DanhSachBenhNhanSidePanel
          modeDsNb={modeDsNb}
          ref={refDanhSachBenhNhanSidePanel}
          onChangeMode={setModeDsNb}
        />
      </Main>

      <ModalChuyenKhoa ref={refModalChuyenKhoa} onKiemTraHoSo={onKiemTraHoSo} />
      <ModalNghiDieuTri ref={refModalNghiDieuTri} />
      <ModalKiemTraHoSo
        ref={refModalKiemTraHoSo}
        openModalPhongGiuong={openModalPhongGiuong}
      />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalDongHoSo
        ref={refModalDongHoSo}
        onKiemTraHoSo={onKiemTraHoSo}
        onShowModalChuyenVien={onShowModalChuyenVien}
      />
      <ModalChuyenVien
        ref={refModalChuyenVien}
        infoNb={chiTietNguoiBenhNoiTru}
        thongTinChiTiet={{
          nbChanDoan: state.nbChanDoan,
          nbChuyenVien: {
            ...state.nbChuyenVien,
            vienChuyenDenId: state.vienChuyenDenId,
          },
          huongDieuTri: state.huongDieuTri,
        }}
      />
      <ModalDuKienRaVien ref={refModalDuKienRaVien} />
      <ModalYeuCauMuonNb ref={refModalYeuCauMuonNb} />
      <ModalDuyetYeuCauMuonNb ref={refModalDuyetYeuCauMuonNb} />
      <ModalNgatDieuTri
        ref={refModalNgatDieuTri}
        onKiemTraHoSo={onKiemTraHoSo}
      />
      <ModalSoDoPhongGiuong ref={refModalSoDoPhongGiuong} />
      <ModalGiayChuyenTuyen ref={refModalGiayChuyenTuyen} />
      <ModalHoanThanhBA
        nbDotDieuTriId={nbDotDieuTriId}
        ref={refModalHoanThanhBA}
      />
      <ModalChonLoaiPHCN ref={refModalChonLoaiPHCN} />
      <ModalTuVanThuoc ref={refModalTuVanThuoc} />
      <ModalTaoPhieuTuVanThuoc ref={refModalTaoPhieuTuVanThuoc} />
      <ModalChiTietTuVanThuoc ref={refModalChiTietTuVanThuoc} />
      <ModalHenKhamCMU ref={refModalHenKhamCMU} />
      <ModalHenDieuTriNgoaiTru ref={refModalHenDieuTriNgoaiTru} />
      <ModalChinhSuaThongTin ref={refModalChinhSuaThongTin} />
      <ModalChonLoaiDangKyKetHopDieuTri
        listAllDieuTriKetHopTheoLoai={listAllDieuTriKetHopTheoLoai}
        listAllDieuTriKetHopTheoKhoa={listAllDieuTriKetHopTheoKhoa}
        ref={refModalDangKyKetHop}
      />
      <ModalNhapLyDoDuyetChiPhi
        ref={refModalNhapLyDoDuyetChiPhi}
      ></ModalNhapLyDoDuyetChiPhi>
      <ModalChotDotDieuTri ref={refModalChotDotDieuTri} />
      <ModalChinhSuaMocThoiGian ref={refModalChinhSuaMocThoiGian} />
    </MainPage>
  );
};

export default memo(ChiTietNguoiBenhNoiTru);
