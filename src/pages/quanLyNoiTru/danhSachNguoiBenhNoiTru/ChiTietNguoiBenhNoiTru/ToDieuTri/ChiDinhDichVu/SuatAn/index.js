import React, { useState, useRef, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Main, CollapseWrapper } from "./styled";
import ChiDinhDichVuSuatAn from "pages/chiDinhDichVu/DichVuSuatAn";
import { useDispatch, useSelector } from "react-redux";
import IcArrow from "assets/images/khamBenh/icArrow.svg";
import { Input, Collapse } from "antd";
import { groupBy, uniqBy } from "lodash";
import Header from "./components/Header";
import Table from "./components/Table";
import { ModalTraSuatAn } from "../../modals";
import { checkRole } from "lib-utils/role-utils";
import { LOAI_DICH_VU, ROLES } from "constants/index";
import ThemChiDinhSpan from "pages/chiDinhDichVu/components/ThemChiDinhTxtSpan";
import { SVG } from "assets";
import { HotKeyTrigger } from "components";
import { useStore } from "hooks";

const LOAI_VAT_TU = [
  {
    ten: "Vật tư kho",
    id: 10,
  },
];

const { Panel } = Collapse;
let timer = null;

const SuatAn = (props) => {
  const { t } = useTranslation();
  const {
    isReadonly,
    layerId,
    checkChongChiDinh,
    dataCHI_DINH_LOAI_CHE_DO_AN,
  } = props;
  const modalKeSuatAnRef = useRef(null);
  const refModalTraSuatAn = useRef(null);
  const { currentToDieuTri } = useSelector((state) => state.toDieuTri);
  const { listDvSuatAn } = useSelector((state) => state.chiDinhSuatAn);
  const { listThietLapChonKho } = useSelector((state) => state.thietLapChonKho);
  const nhanVienId = useStore("auth.auth.nhanVienId");

  const {
    chiDinhSuatAn: { getDsSuatAn },
    boChiDinh: { getBoChiDinh },
    toDieuTri: { getToDieuTriById },
  } = useDispatch();

  const [state, _setState] = useState({
    activeKeys: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useEffect(() => {
    if (currentToDieuTri?.nbDotDieuTriId) {
      getDsSuatAn({
        nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
        chiDinhTuLoaiDichVu: 210,
        chiDinhTuDichVuId: currentToDieuTri?.id,
        dsTrangThaiHoan: [0, 10, 20, 40],
      });
    }
  }, [currentToDieuTri?.nbDotDieuTriId]);

  useEffect(() => {
    getBoChiDinh({
      dsLoaiDichVu: LOAI_DICH_VU.SUAT_AN,
      bacSiChiDinhId: nhanVienId,
    });
  }, []);

  const onChiDinhThuoc = (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    modalKeSuatAnRef.current &&
      modalKeSuatAnRef.current.show({
        loaiDonThuoc: state.loaiDonVatTu,
        khoId: state.khoId,
        dataKho: uniqBy(listThietLapChonKho || [], "id"),
        macDinhLoaiChiDinh: listDvSuatAn?.[0]?.loaiChiDinh || 0,
      });
  };

  const onTraSuatAn = (type, loaiTra) => {
    refModalTraSuatAn.current &&
      refModalTraSuatAn.current.show(
        {
          type, //1: trả, 2: hủy trả
          loaiTra,
        },
        refreshList
      );
  };

  const listPanel = useMemo(() => {
    const grouped = groupBy(listDvSuatAn, "nbDotDieuTriId");
    setState({ activeKeys: Object.keys(grouped || []) });

    return Object.keys(grouped || []).map((key) => {
      let groupByIdArr = grouped[key];
      return {
        header: (
          <Header
            title={t("common.suatAn")}
            listDvSuatAn={groupByIdArr}
            nbDotDieuTriId={currentToDieuTri?.nbDotDieuTriId}
            soPhieuId={grouped[key]?.[0]?.soPhieuId}
            phieuNhapXuatId={grouped[key]?.[0]?.phieuNhapXuatId}
            chiDinhTuDichVuId={currentToDieuTri?.id}
            traSuatAn={onTraSuatAn}
            isReadonly={isReadonly}
            dataCHI_DINH_LOAI_CHE_DO_AN={dataCHI_DINH_LOAI_CHE_DO_AN}
          />
        ),
        content: (
          <Table
            listDvSuatAn={groupByIdArr}
            nbDotDieuTriId={currentToDieuTri?.nbDotDieuTriId}
            chiDinhTuDichVuId={currentToDieuTri?.id}
            isReadonly={isReadonly}
          />
        ),
        key,
      };
    });
  }, [listDvSuatAn, currentToDieuTri, isReadonly]);

  const refreshList = async () => {
    const { id, nbDotDieuTriId } = currentToDieuTri || {};
    if (!id || !nbDotDieuTriId) return;

    if (dataCHI_DINH_LOAI_CHE_DO_AN?.eval()) {
      await getToDieuTriById(id);
    }
    await getDsSuatAn({
      nbDotDieuTriId,
      chiDinhTuLoaiDichVu: 210,
      chiDinhTuDichVuId: id,
      dsTrangThaiHoan: [0, 10, 20, 40],
    });
  };

  const onChange = (key) => {
    setState({ activeKeys: key });
  };

  return (
    <Main>
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_SUAT_AN]) &&
        !isReadonly && (
          <div className="selection">
            <ThemChiDinhSpan />

            <HotKeyTrigger
              layerIds={[layerId]}
              hotKey="F2"
              triggerEvent={onChiDinhThuoc}
            >
              <div>
                <div className="input-box">
                  <SVG.IcSearch />
                  <Input
                    placeholder={`${t("common.timKiem")} [F2]`}
                    onClick={onChiDinhThuoc}
                  />
                </div>
              </div>
            </HotKeyTrigger>
          </div>
        )}
      <div className="collapse-content">
        <CollapseWrapper
          bordered={false}
          expandIcon={({ isActive }) => (
            <IcArrow
              style={
                isActive
                  ? { transform: "rotate(90deg)" }
                  : { transform: "unset" }
              }
            />
          )}
          className="site-collapse-custom-collapse"
          activeKey={state.activeKeys}
          onChange={onChange}
        >
          {(listPanel || []).map((panel) => (
            <Panel key={panel.key + ""} header={panel.header}>
              {panel.content}
            </Panel>
          ))}
        </CollapseWrapper>
      </div>

      <ChiDinhDichVuSuatAn
        ref={modalKeSuatAnRef}
        listLoaiVatTu={LOAI_VAT_TU}
        dataNb={currentToDieuTri}
        chiDinhTuLoaiDichVu={210}
        refreshList={refreshList}
      />

      <ModalTraSuatAn ref={refModalTraSuatAn} />
    </Main>
  );
};

export default SuatAn;
