import React from "react";
import { <PERSON><PERSON><PERSON>rap<PERSON> } from "../styled";
import { useDispatch } from "react-redux";
import { Menu, message } from "antd";
import { uniq } from "lodash";
import { useTranslation } from "react-i18next";
import { Dropdown, Tooltip } from "components";
import { SVG } from "assets";
import { useConfirm } from "hooks";
import { LOAI_CHI_DINH } from "constants/index";

function Header(props) {
  const { showConfirm } = useConfirm();
  const {
    chiDinhSuatAn: { deleteAllSuatAn, getDsSuatAn },
    toDieuTri: { getToDieuTriById },
  } = useDispatch();
  const { t } = useTranslation();

  const {
    listDvSuatAn,
    nbDotDieuTriId,
    chiDinhTuDichVuId,
    traSuatAn,
    isReadonly,
    dataCHI_DINH_LOAI_CHE_DO_AN,
  } = props;

  const onDelete = (e) => {
    e.stopPropagation();
    e.preventDefault();

    const payload = listDvSuatAn.map((item) => {
      return item.id;
    });

    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("quanLyNoiTru.banCoChacChanMuonXoaDonSuatAn")}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        const s = await deleteAllSuatAn(payload);

        let data = (s?.data || [])
          .filter((item) => item.code !== 0 && item?.message)
          ?.map((item) => item?.message);

        if (data?.length > 0) {
          message.error(
            `${t("quanLyNoiTru.suatAn.khongTheXoaDichVu")} :  ${uniq(
              data
            )?.join("; ")}`
          );
        } else {
          message.success(t("quanLyNoiTru.suatAn.xoaDonThanhCong"));
        }

        if (dataCHI_DINH_LOAI_CHE_DO_AN?.eval() && chiDinhTuDichVuId) {
          await getToDieuTriById(chiDinhTuDichVuId);
        }
        await getDsSuatAn({
          nbDotDieuTriId,
          chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: 210,
        });
      }
    );
  };

  const onTraSuatAn = (loaiTra) => (e) => {
    e.stopPropagation();
    e.preventDefault();

    traSuatAn(1, loaiTra);
  };

  const onHuyTraSuatAn = (e) => {
    e.stopPropagation();
    e.preventDefault();

    traSuatAn(2);
  };

  const menu = (
    <Menu
      items={[
        {
          key: "1",
          label: (
            <span onClick={onTraSuatAn(LOAI_CHI_DINH.THUONG)}>
              {t("quanLyNoiTru.suatAn.traSuatAn")}
            </span>
          ),
        },
        {
          key: "2",
          label: (
            <span onClick={onTraSuatAn(LOAI_CHI_DINH.DOT_XUAT)}>
              {t("quanLyNoiTru.suatAn.traDotXuat")}
            </span>
          ),
        },
      ]}
    />
  );

  return (
    <HeaderWrapper>
      {props.title && `${props.title}`}
      {!isReadonly && (
        <Tooltip title={t("quanLyNoiTru.suatAn.xoaDon")} placement="bottom">
          <SVG.IcDelete className="ic-action" onClick={onDelete} />
        </Tooltip>
      )}

      <Dropdown overlay={menu} placement="topLeft">
        <SVG.IcHoanDv className="ic-action" />
      </Dropdown>

      <Tooltip title={t("quanLyNoiTru.suatAn.huyTraSuatAn")} placement="bottom">
        <SVG.IcHuyHoanDv className="ic-action" onClick={onHuyTraSuatAn} />
      </Tooltip>
    </HeaderWrapper>
  );
}

export default Header;
