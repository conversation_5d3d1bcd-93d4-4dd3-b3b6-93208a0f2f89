import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../styled";
import { useDispatch } from "react-redux";
import { message } from "antd";
import { uniq } from "lodash";
import { useTranslation } from "react-i18next";
import { useConfirm, useStore } from "hooks";
import { SVG } from "assets";
import { Tooltip } from "components";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";

function Header(props) {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();

  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const {
    chiDinhChePhamDinhDuong: { onDeleteAll, getListDichVuChePhamDD },
    toDieuTri: { getToDieuTriById },
  } = useDispatch();

  const {
    listDvCPDD,
    nbDotDieuTriId,
    chiDinhTuDichVuId,
    isReadonly,
    dataCHI_DINH_LOAI_CHE_DO_AN,
  } = props;

  const onDelete = (e) => {
    e.stopPropagation();
    e.preventDefault();

    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("quanLyNoiTru.cpdd.xacNhanXoaDonCPDD")}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        const payload = listDvCPDD.map((item) => item.id);

        const s = await onDeleteAll(payload);

        const data = (s?.data || [])
          .filter((item) => item.code !== 0 && item?.message)
          .map((item) => item?.message);

        if (data?.length > 0) {
          message.error(
            `${t("khamBenh.donThuoc.khongTheXoaDichVu")} :  ${uniq(data)?.join(
              "; "
            )}`
          );
        } else {
          message.success(t("quanLyNoiTru.cpdd.xoaDonCPDDThanhCong"));
        }

        if (dataCHI_DINH_LOAI_CHE_DO_AN?.eval() && chiDinhTuDichVuId) {
          await getToDieuTriById(chiDinhTuDichVuId);
        }
        await getListDichVuChePhamDD({
          nbDotDieuTriId,
          dsChiDinhTuDichVuId: chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: 210,
        });
      }
    );
  };

  const checkQuyenXoa = () => {
    if (
      listDvCPDD.every((item) => nhanVienId === item?.bacSiChiDinhId) &&
      checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG])
    ) {
      return true;
    }

    if (
      checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
        ROLES["QUAN_LY_NOI_TRU"].XOA_CHI_DINH_DV_VA_THUOC,
      ])
    ) {
      return true;
    }

    return false;
  };

  return (
    <HeaderWrapper>
      {props.title && `${props.title}`}
      {checkQuyenXoa() && !isReadonly && (
        <Tooltip title={t("quanLyNoiTru.cpdd.xoaDonCPDD")} placement="bottom">
          <SVG.IcDelete onClick={onDelete} />
        </Tooltip>
      )}
    </HeaderWrapper>
  );
}

export default Header;
