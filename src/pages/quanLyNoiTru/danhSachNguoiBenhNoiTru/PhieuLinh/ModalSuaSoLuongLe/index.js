import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Input, message, InputNumber } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import {
  Button,
  InputTimeout,
  ModalTemplate,
  TableWrapper,
  Checkbox,
} from "components";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useLoading, useStore, useThietLap } from "hooks";
import {
  FORMAT_DATE_TIME,
  LOAI_NHAP_XUAT,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
} from "constants/index";
import { groupBy, orderBy } from "lodash";
import { useHistory } from "react-router-dom";
import { SVG } from "assets";
import { roundToDigits } from "utils";
const { Column } = TableWrapper;

const ModalSuaSoLuongLe = (props, ref) => {
  const { isPttt = false, isCDHA = false, isPhaCheThuoc = false } = props || {};
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();

  const { nbThongTinRaVien } = useStore("nbDotDieuTri", {});
  const [LINH_BU_TU_TRUC_KB_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.LINH_BU_TU_TRUC_KB_DA_THANH_TOAN,
    ""
  );
  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );
  const listThietLapChonKhoTongHop = useStore(
    "thietLapChonKho.listThietLapChonKhoTongHop",
    []
  );
  const {
    nhapKho: { chinhSuaDichVuLe, taoPhieuLinhBu },
    nbChotPhaCheThuoc: { themMoiChotPhaCheThuoc },
  } = useDispatch();
  const [state, _setState] = useState({
    show: false,
    tuVong: false,
    khongTaoPhieuLinh: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data = {}, callback) => {
      const source = data?.dsDichVu?.map((o) => ({
        ...o,
        soLuongCu: o.soLuong,
        khongTaoPhieuLinh: false,
        thoiGianYLenhOrder: o.thoiGianYLenh
          ? moment(o.thoiGianYLenh)
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .format()
          : null,
      }));
      const orderSource = orderBy(
        source,
        ["tenDichVu", "thoiGianYLenhOrder", "maHoSo"],
        ["asc", "desc", "desc"]
      );
      setState({
        show: true,
        dataSource: orderSource,
        dataSearch: orderSource,
        payloadPhieuLinh: data?.payload,
        isShowSLDung: data?.isShowSLDung || false,
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show, nbThongTinRaVien]);

  const onCancel = () => {
    setState({ show: false, tuVong: false, dsDichVuKhongTaoLinh: null });
  };

  const onChangeInputSearch = (key) => (e) => {
    let data = state?.dataSearch.filter((x) => {
      return x[key].toLowerCase().search(e.toLowerCase()) !== -1;
    });
    setState({ dataSource: data });
  };

  const onChangeInput = (key, index) => (e) => {
    let value;
    if (key === "soLuongHuy") {
      value = e;
    } else if (key === "soLuong") {
      value = +e?.target?.value;
    } else if (key === "khongTaoPhieuLinh") {
      value = e?.target?.checked;
    } else {
      value = e?.target?.value;
    }

    state.dataSource[index][key] = value;
    const obj = { [key]: value };
    if (key === "soLuongHuy") {
      const soLuongMoi = roundToDigits(
        state.dataSource[index]["soLuongCu"] + value,
        3
      );
      state.dataSource[index]["soLuong"] = soLuongMoi;
      obj.soLuong = soLuongMoi;
    }
    setState({ ...obj });
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "40px",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (item, data, index) => index + 1,
    }),
    Column({
      title: t("quanLyNoiTru.tenHangHoa"),
      width: "200px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      renderSearch: (
        <InputTimeout
          placeholder={t("quanLyNoiTru.tenHangHoa")}
          onChange={onChangeInputSearch("tenDichVu")}
        />
      ),
    }),
    Column({
      title: t("common.maHoSo"),
      width: "90px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      renderSearch: (
        <InputTimeout
          placeholder={t("common.maHoSo")}
          onChange={onChangeInputSearch("maHoSo")}
        />
      ),
    }),
    Column({
      title: t("common.tenNb"),
      width: "150px",
      dataIndex: "tenNb",
      key: "tenNb",
      renderSearch: (
        <InputTimeout
          placeholder={t("common.tenNb")}
          onChange={onChangeInputSearch("tenNb")}
        />
      ),
    }),
    Column({
      title: t("quanLyNoiTru.slChiDinh"),
      width: "70px",
      dataIndex: "soLuong",
      key: "soLuong",
      render: (item, data, index) => (
        <Input
          min={0}
          type="number"
          value={item}
          onChange={onChangeInput("soLuong", index)}
        ></Input>
      ),
    }),
    Column({
      title: t("quanLyNoiTru.slHuy"),
      width: "70px",
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      render: (item, data, index) => (
        <InputNumber
          value={item}
          min={0}
          onChange={onChangeInput("soLuongHuy", index)}
          parser={(value) => value.replaceAll(",", ".")}
          formatter={(value) => value.replaceAll(".", ",")}
        />
      ),
    }),
    ...(state.isShowSLDung
      ? [
        Column({
          title: t("quanLyNoiTru.soLuongDung"),
          width: "70px",
          dataIndex: "soLuongHuy",
          key: "soLuongHuy",
          render: (item, data, index) =>
            (data?.soLuong || 0) - (data?.soLuongHuy || 0),
        }),
      ]
      : []),
    Column({
      title: t("quanLyNoiTru.lyDoHuy"),
      width: "150px",
      dataIndex: "lyDoHuy",
      key: "lyDoHuy",
      render: (item, data, index) => (
        <Input value={item} onChange={onChangeInput("lyDoHuy", index)}></Input>
      ),
    }),
    Column({
      title: t("quanLyNoiTru.ghiChuPhieuLinh"),
      width: "100px",
      dataIndex: "ghiChuPhieuLinh",
      key: "ghiChuPhieuLinh",
      render: (item, data, index) => (
        <Input
          value={item}
          onChange={onChangeInput("ghiChuPhieuLinh", index)}
        ></Input>
      ),
    }),
    Column({
      title: t("quanLyNoiTru.toDieuTri.ngayYLenh"),
      width: "100px",
      dataIndex: "thoiGianYLenh",
      key: "thoiGianYLenh",
      render: (item) => item && moment(item).format(FORMAT_DATE_TIME),
    }),
    Column({
      title: t("common.ttThanhToan"),
      width: "80px",
      dataIndex: "thanhToan",
      key: "thanhToan",
      align: "center",
      render: (item, data, index) => (
        <Checkbox
          checked={item === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
        ></Checkbox>
      ),
    }),
    Column({
      title: t("quanLyNoiTru.phieuLinh.khongTaoPhieuLinh"),
      width: "80px",
      dataIndex: "khongTaoPhieuLinh",
      key: "khongTaoPhieuLinh",
      align: "center",
      render: (item, data, index) => (
        <Checkbox
          checked={item}
          onChange={onChangeInput("khongTaoPhieuLinh", index)}
        ></Checkbox>
      ),
    }),
  ];

  const onDelete = () => {
    let renderData = state?.dataSource?.filter(
      (item) => !item.khongTaoPhieuLinh
    );
    let dsDichVuKhongTaoLinh = state?.dataSource?.filter(
      (item) => item.khongTaoPhieuLinh
    );
    setState({
      dataSource: renderData,
      dsDichVuKhongTaoLinh: dsDichVuKhongTaoLinh,
    });
  };
  const onSubmit = async () => {
    try {
      showLoading();

      await chinhSuaDichVuLe({
        payload: state?.dataSource.map((item) => {
          const { id, soLuong, ghiChuPhieuLinh, soLuongHuy, lyDoHuy } = item;
          return {
            id: id,
            nbDvKho: { ghiChuPhieuLinh, soLuongHuy, lyDoHuy },
            nbDichVu: { soLuong },
          };
        }),
        loaiDichVu: state?.payloadPhieuLinh?.loaiDichVu,
      });
      console.log("state.payloadPhieuLinh", state.payloadPhieuLinh);
      let body = {
        ...state.payloadPhieuLinh,
        tuThoiGian:
          state.payloadPhieuLinh.tuThoiGian instanceof moment
            ? state.payloadPhieuLinh.tuThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : "",
        denThoiGian:
          state.payloadPhieuLinh.denThoiGian instanceof moment
            ? state.payloadPhieuLinh.denThoiGian.format("YYYY-MM-DD HH:mm:ss")
            : "",
        khoaChiDinhId: state.payloadPhieuLinh.dsKhoaChiDinhId
          ? state.payloadPhieuLinh.dsKhoaChiDinhId
          : undefined,
        ...(LINH_BU_TU_TRUC_KB_DA_THANH_TOAN?.eval()
          ? { thanhToan: state.payloadPhieuLinh.thanhToan ? 50 : null }
          : {}),
        khoId:
          state.payloadPhieuLinh.loaiNhapXuat === 85
            ? state.payloadPhieuLinh.khoId
            : state.payloadPhieuLinh.khoDoiUngId,
        ...(!state.payloadPhieuLinh.khoId &&
          state.payloadPhieuLinh.loaiNhapXuat === 85 &&
          dataHIEN_THI_CHECKBOX_TU_TRUC?.eval()
          ? {
            dsKhoId: listThietLapChonKhoTongHop.map((x) => x.id),
            khoId: undefined,
          }
          : {}),
        dsDichVuKhongTaoLinhId:
          state?.dsDichVuKhongTaoLinh?.length &&
          state?.dsDichVuKhongTaoLinh?.map((i) => i.dichVuId),
      };
      if (body.hasOwnProperty("dsKhoaChiDinhId")) delete body.dsKhoaChiDinhId;
      if (!body.dsDichVuKhongTaoLinhId?.length)
        delete body.dsDichVuKhongTaoLinhId;
      if (!body.dsPhongId?.length) body.dsPhongId = null;
      if (body.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC)
        body.khoDoiUngId = state.payloadPhieuLinh.khoId;

      let bodyPhaCheThuoc = {
        ...state.payloadPhieuLinh,
        dsDichVuKhongTaoLinhId:
          state?.dsDichVuKhongTaoLinh?.length &&
          state?.dsDichVuKhongTaoLinh?.map((i) => i.dichVuId),
      };

      const s = isPhaCheThuoc
        ? await themMoiChotPhaCheThuoc(bodyPhaCheThuoc)
        : await taoPhieuLinhBu(body);

      if (isPhaCheThuoc) {
        refCallback.current && refCallback.current();
      } else if (s?.data?.length) {
        const data = s.data[0];
        if (data?.id) {
          if (data.loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC) {
            history.push("/kho/xuat-kho/chi-tiet-linh-bu/" + data.id);
          } else {
            history.push(
              `/${isCDHA
                ? "chan-doan-hinh-anh"
                : isPttt
                  ? "phau-thuat-thu-thuat"
                  : "quan-ly-noi-tru"
              }/chi-tiet-phieu-linh/` + data.id
            );
          }
        }
      }
      console.log("body", body);
      onCancel();
    } catch (e) {
      console.error(e);
      message.error(e?.message);
      const source = e?.data?.map((o) => ({
        ...o,
        soLuongCu: o.soLuong,
        thoiGianYLenhOrder: o.thoiGianYLenh
          ? moment(o.thoiGianYLenh)
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .format()
          : null,
      }));
      const orderSource = orderBy(
        source,
        ["tenDichVu", "thoiGianYLenhOrder", "maHoSo"],
        ["asc", "desc", "desc"]
      );
      setState({
        dataSource: orderSource,
        dataSearch: orderSource,
      });
    } finally {
      hideLoading();
    }
  };

  return (
    <ModalTemplate
      width={"95%"}
      ref={refModal}
      title={t("common.thongBao")}
      onCancel={onCancel}
      style={{ height: "calc(100vh - 20px)" }}
      bodyStyle={{ overflowY: "scroll" }}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <>
          <Button
            type="danger"
            minWidth={100}
            onClick={onDelete}
            rightIcon={<SVG.IcDelete />}
          >
            {t("common.xoa")}
          </Button>
          <Button
            type="primary"
            minWidth={100}
            onClick={onSubmit}
            rightIcon={<SVG.IcSave />}
          >
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        <div className="title">
          <label>{t("quanLyNoiTru.tonTaiHangHoaCoTongSoLuongLinhLe")}:</label>
          {Object.values(groupBy(state?.dataSource, "dichVuId") || [])
            .map((item) => {
              const _tongSoLuong = item.reduce(
                (total, x) => (total = total + x.soLuong),
                0
              );

              const tinhSoLuongLe = (hsdm, chiDinh) => {
                const value = (hsdm - (chiDinh % hsdm)) % hsdm;
                return parseFloat(value.toFixed(10));
              };

              return {
                dienGiai: `${item[0]?.tenDichVu} : ${parseFloat(
                  _tongSoLuong.toFixed(10)
                )} ${item[0]?.tenDonViTinh}`,
                dienGiaiHsdm: `Hệ số định mức: ${item[0]?.heSoDinhMuc} ${item[0]?.tenDonViTinh
                  } - Số lượng lẻ: ${tinhSoLuongLe(
                    item[0]?.heSoDinhMuc,
                    parseFloat(_tongSoLuong.toFixed(10))
                  )}`,
              };
            })
            .map((item1) => (
              <div style={{ paddingLeft: "10px" }}>
                <span>{item1.dienGiai}</span>
                {" ("}
                <span style={{ fontStyle: "italic" }}>
                  {item1.dienGiaiHsdm}
                </span>
                {")"}
              </div>
            ))}
        </div>
        <TableWrapper
          dataSource={state?.dataSource}
          columns={columns}
          onRow={(record) => ({
            style: {
              pointerEvents: record.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ? 'none' : 'auto',
            },
          })}
        />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSuaSoLuongLe);
