import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import {
  But<PERSON>,
  HeaderSearch,
  ModalTemplate,
  TableWrapper,
  DateTimePicker,
  Checkbox,
  InputTimeout,
  Select,
} from "components";
import moment from "moment";
import { assign, cloneDeep } from "lodash";
import stringUtils from "mainam-react-native-string-utils";
import { useEnum, useStore, useThietLap } from "hooks";
import { message } from "antd";
import { isArray } from "utils";
import { SVG } from "assets";
import { ENUM, THIET_LAP_CHUNG } from "constants";
import { calculateInfusionTime } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ModalCapPhatSdThuoc/utils";
import { Main, TitleDiv } from "./styled";

const initialState = {
  show: false,
  thoiGianYLenh: null,
  isThuocDichTruyen: false,
  selectedRowKeys: [],
  data: [],
  soLan1Ngay: null,
  thoiGianThucHien: null,
  soLuong1Lan: null,
  disableSave: false,
  tenDonViTinh: null,
  tenDvtSoCap: null,
  tenDvtSuDung: null,
  tocDoTruyen: null,
  donViTocDoTruyen: null,
  soGiot: null,
  cachGio: null,
  dungTich: null,
  originalData: [],
};

const ModalSuaThoiGian = (props, ref) => {
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const timerOutId = useRef(null);
  const [state, _setState] = useState(cloneDeep(initialState));

  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { t } = useTranslation();
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const [dataDON_VI_TOC_DO_TRUYEN] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [dataKHONG_NHAP_THOI_GIAN_SU_DUNG_THUOC_TUONG_LAI] = useThietLap(
    THIET_LAP_CHUNG.KHONG_NHAP_THOI_GIAN_SU_DUNG_THUOC_TUONG_LAI
  );

  const calculateEndTime = (startTime, durationMinutes) => {
    if (!startTime || !durationMinutes) return null;
    return moment(startTime)
      .add(durationMinutes, "minutes")
      .format("YYYY-MM-DD HH:mm:ss");
  };

  const calculateSingleItemEndTime = (item) => {
    if (!state.isThuocDichTruyen) return item;

    if (
      item.soLuong &&
      item.tocDoTruyen &&
      item.donViTocDoTruyen &&
      item.tuThoiGian
    ) {
      const duration = calculateInfusionTime(
        item.soLuong,
        item.tocDoTruyen,
        item.donViTocDoTruyen,
        state.soGiot
      );
      const endTime = calculateEndTime(item.tuThoiGian, duration);

      return {
        ...item,
        denThoiGian: endTime,
      };
    }

    return item;
  };

  const calculateNewItemStartTime = (data) => {
    if (!state.isThuocDichTruyen || data.length === 0) {
      return moment().format("YYYY-MM-DD HH:mm:ss");
    }

    const lastItem = data[data.length - 1];
    const lastEndTime = lastItem.denThoiGian;

    if (lastEndTime) {
      const cachGio = state.cachGio || 0;
      return moment(lastEndTime)
        .add(cachGio, "hours")
        .format("YYYY-MM-DD HH:mm:ss");
    }

    return moment().format("YYYY-MM-DD HH:mm:ss");
  };

  useImperativeHandle(ref, () => ({
    show: (
      {
        data,
        thoiGianYLenh,
        isThuocDichTruyen,
        soLan1Ngay,
        donVi,
        thoiGianThucHien,
        soLuong1Lan,
        tenDonViTinh,
        tenDvtSoCap,
        tenDvtSuDung,
        tocDoTruyen,
        donViTocDoTruyen,
        soGiot,
        cachGio,
        dungTich,
        thoiGianBatDau,
      },
      callback
    ) => {
      const processedData = data.map((item) => ({
        ...item,
        id: stringUtils.guid(),
      }));

      setState({
        show: true,
        data: processedData,
        originalData: processedData,
        thoiGianYLenh,
        isThuocDichTruyen,
        soLan1Ngay,
        donVi,
        thoiGianThucHien,
        soLuong1Lan,
        tenDonViTinh,
        tenDvtSoCap,
        tenDvtSuDung,
        tocDoTruyen,
        donViTocDoTruyen,
        soGiot,
        cachGio,
        dungTich,
        thoiGianBatDau,
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (state.show) {
      timerOutId.current = setTimeout(() => {
        let el = document.querySelector(".error-sua-thoi-gian");
        setState({ disableSave: !!el });
      }, 0);
    }
    return () => clearTimeout(timerOutId.current);
  }, [state.data, state.show]);

  const onDeleteThoiGian = (item) => () => {
    const dsThoiGianSuDung = state.data.filter((x) => x.id != item.id);

    setState({ data: dsThoiGianSuDung });
    if (refCallback.current) {
      refCallback.current(dsThoiGianSuDung);
    }
  };

  const onClose = () => {
    _setState(cloneDeep(initialState));
  };

  const onSaveThoiGian = () => {
    if (refCallback.current) {
      refCallback.current(state.data);
    }

    onClose();
  };

  const onXoaThoiGian = () => {
    const dsThoiGianSuDung = state.data.filter(
      (x) => !(state.selectedRowKeys || []).includes(x.id)
    );

    setState({ data: dsThoiGianSuDung });
    if (refCallback.current) {
      refCallback.current(dsThoiGianSuDung);
    }

    onClose();
  };

  const onChange = (key, index) => (e) => {
    const newData = [...state.data];

    if (key === "tuThoiGian" || key === "denThoiGian") {
      // cho phép xoá đến thời gian khi clear
      newData[index][key] = e
        ? e.format("YYYY-MM-DD HH:mm:ss")
        : key === "tuThoiGian"
        ? state.data[index].tuThoiGian
        : null;
    } else {
      newData[index][key] = e;
    }

    if (state.isThuocDichTruyen) {
      if (
        key === "soLuong" ||
        key === "tuThoiGian" ||
        key === "donViTocDoTruyen" ||
        key === "tocDoTruyen"
      ) {
        newData[index] = calculateSingleItemEndTime(newData[index]);
      }
    }

    setState({ data: newData });
  };

  const onAddThoiGian = () => {
    if (state.soLan1Ngay && state.data.length >= state.soLan1Ngay) {
      return message.error(
        t("quanLyNoiTru.capPhatThuoc.thuocChiDuocNhapSuDungToiDaTitleLanNgay", {
          title: state.soLan1Ngay,
        })
      );
    }
    const newId = stringUtils.guid();
    const newItem = {
      id: newId,
      tuThoiGian: calculateNewItemStartTime(state.data),
      denThoiGian: null,
      dieuDuongId: nhanVienId,
      soLuong: 0,
    };

    let finalNewItem = newItem;
    if (state.isThuocDichTruyen) {
      assign(finalNewItem, {
        tocDoTruyen: state.tocDoTruyen,
        donViTocDoTruyen: state.donViTocDoTruyen,
      });
      finalNewItem = calculateSingleItemEndTime(finalNewItem);
    }

    const dsThoiGianSuDung = [...state.data, finalNewItem];

    setState({
      data: dsThoiGianSuDung,
    });
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            state.isThuocDichTruyen
              ? t("quanLyNoiTru.capPhatThuoc.thoiGianBatDau")
              : t("quanLyNoiTru.capPhatThuoc.thoiGianSuDung")
          }
        />
      ),
      width: 210,
      dataIndex: "tuThoiGian",
      key: "tuThoiGian",
      render: (item, _, index) => {
        const isBeforeImplementationTime =
          item && moment(item) < moment(state.thoiGianThucHien);
        const isFutureTime =
          dataKHONG_NHAP_THOI_GIAN_SU_DUNG_THUOC_TUONG_LAI?.eval() &&
          item &&
          moment(item) > moment();
        return (
          <>
            <DateTimePicker
              placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={true}
              disabledDate={(current) => {
                const isBeforeImplementationTime =
                  current && current < moment(state.thoiGianThucHien);
                const isFutureTime =
                  dataKHONG_NHAP_THOI_GIAN_SU_DUNG_THUOC_TUONG_LAI?.eval() &&
                  current &&
                  current > moment();
                return isBeforeImplementationTime || isFutureTime;
              }}
              value={item && moment(item)}
              onChange={onChange("tuThoiGian", index)}
            />
            {!!isBeforeImplementationTime && (
              <div className="error error-sua-thoi-gian">
                {`${t(
                  "quanLyNoiTru.capPhatThuoc.khongDuocNhapNhoHonThoiGianThucHien"
                )}: ${moment(state.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                )}!`}
              </div>
            )}
            {!!isFutureTime && (
              <div className="error error-sua-thoi-gian">
                {`${t(
                  "quanLyNoiTru.capPhatThuoc.khongDuocNhapLonHonThoiGianHienTai"
                )}: ${moment().format("DD/MM/YYYY HH:mm:ss")}!`}
              </div>
            )}
          </>
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.thoiGianKetThuc")} />
      ),
      width: 210,
      dataIndex: "denThoiGian",
      key: "denThoiGian",
      hidden: !state.isThuocDichTruyen,
      render: (item, _, index) => {
        // Đến thời gian nhỏ hơn thời gian thực hiện
        const isBeforeImplementationTime =
          item && moment(item) < moment(state.thoiGianThucHien);

        return (
          <>
            <DateTimePicker
              placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={true}
              disabledDate={(current) => {
                const isBeforeImplementationTime =
                  current && current < moment(state.thoiGianThucHien);

                return isBeforeImplementationTime;
              }}
              value={item && moment(item)}
              onChange={onChange("denThoiGian", index)}
            />
            {!!isBeforeImplementationTime && (
              <div className="error error-sua-thoi-gian">
                {`${t(
                  "quanLyNoiTru.capPhatThuoc.khongDuocNhapNhoHonThoiGianThucHien"
                )}: ${moment(state.thoiGianThucHien).format(
                  "DD/MM/YYYY HH:mm:ss"
                )}!`}
              </div>
            )}
          </>
        );
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.slSuDung")} />,
      width: 90,
      dataIndex: "soLuong",
      key: "soLuong",
      render: (item, _, index) => {
        let showError = false;

        if (
          state.tenDonViTinh === state.tenDvtSoCap &&
          state.tenDonViTinh !== state.tenDvtSuDung
        ) {
          showError = false;
        } else {
          showError = state.soLuong1Lan && item && item > state.soLuong1Lan;
        }

        return (
          <div>
            <InputTimeout
              onChange={onChange("soLuong", index)}
              value={item || 0}
            />
            {!!showError && (
              <div className="error error-sua-thoi-gian">
                {`${t(
                  "quanLyNoiTru.capPhatThuoc.slSuDungThuocPhaiBeHonHoacBangSLLan"
                )} = ${state.soLuong1Lan}!`}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.donVi")} />,
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      align: "center",
      i18Name: "quanLyNoiTru.donVi",
      render: () => state.donVi,
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.donViTocDoTruyen")} />
      ),
      width: 100,
      dataIndex: "donViTocDoTruyen",
      key: "donViTocDoTruyen",
      align: "center",
      i18Name: "quanLyNoiTru.capPhatThuoc.donViTocDoTruyen",
      hidden: !state.isThuocDichTruyen,
      render: (item, _, index) => {
        return (
          <Select
            data={dataDON_VI_TOC_DO_TRUYEN}
            value={item}
            onChange={onChange("donViTocDoTruyen", index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.capPhatThuoc.tocDoTruyen")} />
      ),
      width: 100,
      dataIndex: "tocDoTruyen",
      key: "tocDoTruyen",
      align: "center",
      i18Name: "quanLyNoiTru.capPhatThuoc.tocDoTruyen",
      hidden: !state.isThuocDichTruyen,
      render: (item, _, index) => {
        return (
          <InputTimeout
            type="number"
            value={item}
            onChange={onChange("tocDoTruyen", index)}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      width: 60,
      dataIndex: "tienIch",
      key: "tienIch",
      align: "center",
      i18Name: "common.tienIch",
      fixed: "right",
      render: (item, record, index) => {
        return (
          <div>
            <SVG.IcDelete onClick={onDeleteThoiGian(record)} />
          </div>
        );
      },
    },
  ];

  const oncheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked ? state.data.map((x) => x.id) : [],
      isCheckedAll: e.target?.checked,
    });
  };

  const onSelectChange = (selectedRowKeys, data) => {
    let updatedSelectedKeys = selectedRowKeys;
    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
    if (state.data?.length === updatedSelectedKeys.length) {
      setState({
        isCheckedAll: true,
        selectedRowKeys: updatedSelectedKeys,
      });
    } else {
      setState({
        isCheckedAll: false,
        selectedRowKeys: updatedSelectedKeys,
      });
    }
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 30,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={
        <TitleDiv>
          <span>{t("quanLyNoiTru.thoiGianDungThuoc")}</span>
          <SVG.IcAdd style={{ cursor: "pointer" }} onClick={onAddThoiGian} />
        </TitleDiv>
      }
      width={"min(95vw,1200px)"}
      onCancel={onClose}
      actionRight={
        <>
          {isArray(state.data, 1) && (
            <Button
              type="error"
              onClick={onXoaThoiGian}
              rightIcon={<SVG.IcDelete />}
            >
              {t("common.xoa")}
            </Button>
          )}

          <Button
            type="primary"
            rightIcon={<SVG.IcSave />}
            onClick={onSaveThoiGian}
            disabled={state.disableSave}
          >
            {t("common.luu")}
          </Button>
        </>
      }
    >
      <Main>
        {state.isThuocDichTruyen && (
          <div
            style={{
              marginBottom: "16px",
              padding: "16px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              backgroundColor: "#f5f5f5",
            }}
          >
            <h4 style={{ marginBottom: "12px", color: "#1890ff" }}>
              {t("quanLyNoiTru.capPhatThuoc.thongSoDichTruyen")}
            </h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
                gap: "12px",
              }}
            >
              <div>
                <label
                  style={{
                    display: "block",
                    marginBottom: "4px",
                    fontWeight: "500",
                    color: "#666",
                  }}
                >
                  {t("quanLyNoiTru.capPhatThuoc.tocDoTruyen")}:
                </label>
                <div
                  style={{
                    padding: "8px 12px",
                    backgroundColor: "#fff",
                    border: "1px solid #d9d9d9",
                    borderRadius: "4px",
                    fontSize: "14px",
                    color: "#333",
                    minHeight: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {state.tocDoTruyen || "-"}
                </div>
              </div>
              <div>
                <label
                  style={{
                    display: "block",
                    marginBottom: "4px",
                    fontWeight: "500",
                    color: "#666",
                  }}
                >
                  {t("quanLyNoiTru.capPhatThuoc.donViTocDoTruyen")}:
                </label>
                <div
                  style={{
                    padding: "8px 12px",
                    backgroundColor: "#fff",
                    border: "1px solid #d9d9d9",
                    borderRadius: "4px",
                    fontSize: "14px",
                    color: "#333",
                    minHeight: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {dataDON_VI_TOC_DO_TRUYEN.find(
                    (x) => x.id === state.donViTocDoTruyen
                  )?.ten || "-"}
                </div>
              </div>
              <div>
                <label
                  style={{
                    display: "block",
                    marginBottom: "4px",
                    fontWeight: "500",
                    color: "#666",
                  }}
                >
                  {t("quanLyNoiTru.capPhatThuoc.soGiotMl")}:
                </label>
                <div
                  style={{
                    padding: "8px 12px",
                    backgroundColor: "#fff",
                    border: "1px solid #d9d9d9",
                    borderRadius: "4px",
                    fontSize: "14px",
                    color: "#333",
                    minHeight: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {state.soGiot || "20"}
                </div>
              </div>
              <div>
                <label
                  style={{
                    display: "block",
                    marginBottom: "4px",
                    fontWeight: "500",
                    color: "#666",
                  }}
                >
                  {t("quanLyNoiTru.capPhatThuoc.cachGio")}:
                </label>
                <div
                  style={{
                    padding: "8px 12px",
                    backgroundColor: "#fff",
                    border: "1px solid #d9d9d9",
                    borderRadius: "4px",
                    fontSize: "14px",
                    color: "#333",
                    minHeight: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  {state.cachGio || "0"}
                </div>
              </div>
            </div>
            <div style={{ marginTop: "8px", fontSize: "12px", color: "#666" }}>
              <p>{t("quanLyNoiTru.capPhatThuoc.luuYDichTruyen")}</p>
            </div>
          </div>
        )}
        <div style={{ maxHeight: "500px", overflowY: "auto", display: "flex" }}>
          <TableWrapper
            columns={columns}
            dataSource={state.data || []}
            rowKey={(record) => `${record.id}`}
            rowSelection={rowSelection}
          />
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSuaThoiGian);
