import { t } from "i18next";

export const listTenTruongNbDotDieuTri = [
  { id: "tenMe", ten: t("tenTruong.tenMe") },
  { id: "tenNbKhongDau", ten: t("tenTruong.tenNbKhongDau") },
  { id: "dinhChiThaiNghen", ten: t("tenTruong.dinhChiThaiNghen") },
  { id: "maHoSo", ten: t("tenTruong.maHoSo") },
  {
    id: "dsCdVaoVienKemTheoId",
    ten: t("tenTruong.dsCdVaoVienKemTheoId"),
    store: "maBenh",
  },
  { id: "soNgayDieuTri", ten: t("tenTruong.soNgayDieuTri") },
  {
    id: "huongDieuTri",
    ten: t("tenTruong.huongDieuTri"),
    enum: "huongDieuTri",
  },
  { id: "phuongPhapDieuTri", ten: t("tenTruong.phuongPhapDieuTri") },
  {
    id: "denThoiGianNghiNgoaiTru",
    ten: t("tenTruong.denThoiGianNghiNgoaiTru"),
  },
  { id: "tenNb", ten: t("tenTruong.tenNb") },
  {
    id: "phongHenKhamId",
    ten: t("tenTruong.phongHenKhamId"),
    store: "phong",
  },
  { id: "quaTrinhBenhLy", ten: t("tenTruong.quaTrinhBenhLy") },
  { id: "active", ten: t("tenTruong.active") },
  {
    id: "diaDiemTuVong",
    ten: t("tenTruong.diaDiemTuVong"),
    enum: "diaDiemTuVong",
  },
  {
    id: "tinhThanhPhoId",
    ten: t("tenTruong.tinhThanhPhoId"),
    store: "tinh",
  },
  { id: "gioiTinh", ten: t("tenTruong.gioiTinh"), enum: "gioiTinh" },
  {
    id: "dsCdKemTheoId",
    ten: t("tenTruong.dsCdKemTheoId"),
    store: "maBenh",
  },
  {
    id: "khoaTiepDonId",
    ten: t("tenTruong.khoaTiepDonId"),
    store: "khoa",
  },
  { id: "soPhoi", ten: t("tenTruong.soPhoi") },
  {
    id: "nguoiLapBenhAnId",
    ten: t("tenTruong.nguoiLapBenhAnId"),
    store: "nhanVien",
  },
  { id: "thoiGianLapBenhAn", ten: t("tenTruong.thoiGianLapBenhAn") },
  { id: "loiDanBacSi", ten: t("tenTruong.loiDanBacSi") },
  {
    id: "dsPhanLoaiNbId",
    ten: t("tenTruong.phanLoaiNbId"),
    store: "phanLoaiNB",
  },
  {
    id: "vienChuyenDenId",
    ten: t("tenTruong.vienChuyenDenId"),
    store: "benhVien",
  },
  { id: "ghiChu", ten: t("tenTruong.ghiChu") },
  {
    id: "coSoYTeId",
    ten: t("tenTruong.coSoYTeId"),
    store: "benhVien",
  },
  {
    id: "dsCdChinhId",
    ten: t("tenTruong.dsCdChinhId"),
    store: "maBenh",
  },
  { id: "ghiChuTuVong", ten: t("tenTruong.ghiChuTuVong") },
  {
    id: "trangThai",
    ten: t("tenTruong.trangThai"),
    enum: "trangThainb",
  },
  { id: "khamSucKhoe", ten: t("tenTruong.khamSucKhoe") },
  { id: "ngaySinh", ten: t("tenTruong.ngaySinh") },
  { id: "maNb", ten: t("tenTruong.maNb") },
  { id: "email", ten: t("tenTruong.email") },
  { id: "canNang", ten: t("tenTruong.canNang") },
  { id: "diaChiNuocNgoai", ten: t("tenTruong.diaChiNuocNgoai") },
  {
    id: "loaiBenhAnId",
    ten: t("tenTruong.loaiBenhAnId"),
    store: "loaiBenhAn",
  },
  {
    id: "khoaNhapVienId",
    ten: t("tenTruong.khoaNhapVienId"),
    store: "khoa",
  },
  { id: "thoiGianRaVien", ten: t("common.thoiGianRaVien") },
  { id: "soNha", ten: t("tenTruong.soNha") },
  { id: "doiTuong", ten: t("tenTruong.doiTuong"), enum: "doiTuong" },
  {
    id: "loaiDoiTuongId",
    ten: t("tenTruong.loaiDoiTuongId"),
    store: "loaiDoiTuong",
  },
  { id: "phanHoi", ten: t("tenTruong.phanHoi") },
  { id: "lyDoDenKham", ten: t("tenTruong.lyDoDenKham") },
  { id: "capCuu", ten: t("tenTruong.capCuu") },
  {
    id: "quanHuyenId",
    ten: t("tenTruong.quanHuyenId"),
    store: "quanHuyen",
  },
  { id: "ngoaiVien", ten: t("tenTruong.ngoaiVien") },
  { id: "thuTruongDonVi", ten: t("tenTruong.thuTruongDonVi") },
  { id: "anhDaiDien", ten: t("tenTruong.anhDaiDien") },
  { id: "tinhTrang", ten: t("tenTruong.tinhTrang") },
  {
    id: "tuThoiGianNghiNgoaiTru",
    ten: t("tenTruong.tuThoiGianNghiNgoaiTru"),
  },
  {
    id: "xaPhuongId",
    ten: t("tenTruong.xaPhuongId"),
    store: "xaPhuong",
  },
  {
    id: "donViYTeId",
    ten: t("tenTruong.donViYTeId"),
    store: "benhVien",
  },
  { id: "maBenhAn", ten: t("tenTruong.maBenhAn") },
  { id: "covid", ten: t("tenTruong.covid") },
  {
    id: "lyDoTuVong",
    ten: t("tenTruong.lyDoTuVong"),
    enum: "lyDoTuVong",
  },
  { id: "soNgayChoDon", ten: t("tenTruong.soNgayChoDon") },
  { id: "thoiGianHenKham", ten: t("tenTruong.thoiGianHenKham") },
  {
    id: "bacSiDieuTriId",
    ten: t("tenTruong.bacSiDieuTriId"),
    store: "nhanVien",
  },
  { id: "tenCha", ten: t("tenTruong.tenCha") },
  { id: "tiemChung", ten: t("tenTruong.tiemChung") },
  {
    id: "ketQuaDieuTri",
    ten: t("tenTruong.ketQuaDieuTri"),
    enum: "ketQuaDieuTri",
  },
  { id: "treEmKhongThe", ten: t("tenTruong.treEmKhongThe") },
  {
    id: "nguonNbId",
    ten: t("tenTruong.nguonNbId"),
    store: "nguonNguoiBenh",
  },
  { id: "moTa", ten: t("tenTruong.moTa") },
  { id: "nhomMau", ten: t("tenTruong.nhomMau"), enum: "nhomMau" },
  { id: "thoiGianTuVong", ten: t("tenTruong.thoiGianTuVong") },
  { id: "uuTien", ten: t("tenTruong.uuTien") },
  {
    id: "doiTuongKcb",
    ten: t("tenTruong.doiTuongKcb"),
    enum: "doiTuongKcb",
  },
  { id: "cdNoiGioiThieu", ten: t("tenTruong.cdNoiGioiThieu") },
  { id: "cheDoTiepTheo", ten: t("tenTruong.cheDoTiepTheo") },
  { id: "thoiGianVaoVien", ten: t("tenTruong.thoiGianVaoVien") },
  {
    id: "dsCdKemTheoTongHopId",
    ten: t("tenTruong.dsCdKemTheoTongHopId"),
  },
  { id: "ketQuaCls", ten: t("tenTruong.ketQuaCls") },
  { id: "nbThongTinId", ten: t("tenTruong.nbThongTinId") },
  { id: "thoiGianChungTu", ten: t("tenTruong.thoiGianChungTu") },
  { id: "truongKhoaId", ten: t("tenTruong.truongKhoaId") },
  { id: "soNhaTamTru", ten: t("tenTruong.soNhaTamTru") },
  { id: "danTocId", ten: t("tenTruong.danTocId"), store: "danToc" },
  {
    id: "dsCdVaoVienId",
    ten: t("tenTruong.dsCdVaoVienId"),
    store: "maBenh",
  },
  { id: "tuoiThai", ten: t("tenTruong.tuoiThai") },
  { id: "soBaoHiemXaHoi", ten: t("tenTruong.soBaoHiemXaHoi") },
  {
    id: "xaPhuongTamTruId",
    ten: t("tenTruong.xaPhuongTamTruId"),
    store: "xaPhuong",
  },
  {
    id: "quayTiepDonId",
    ten: t("tenTruong.quayTiepDonId"),
    store: "quayTiepDon",
  },
  {
    id: "quocTichId",
    ten: t("tenTruong.quocTichId"),
    store: "quocGia",
  },
  {
    id: "quocGiaId",
    ten: t("tenTruong.quocGiaId"),
    store: "quocGia",
  },
  {
    id: "nguoiGioiThieuId",
    ten: t("tenTruong.nguoiGioiThieuId"),
    store: "nhanVien",
  },
  { id: "soDienThoai", ten: t("tenTruong.soDienThoai") },
  { id: "chiNamSinh", ten: t("tenTruong.chiNamSinh") },
  { id: "maDatKham", ten: t("tenTruong.maDatKham") },
  {
    id: "tinhThanhPhoTamTruId",
    ten: t("tenTruong.tinhThanhPhoTamTruId"),
    store: "tinh",
  },
  {
    id: "ngheNghiepId",
    ten: t("tenTruong.ngheNghiepId"),
    store: "ngheNghiep",
  },
  { id: "khoaId", ten: t("tenTruong.khoaId"), store: "khoa" },
  {
    id: "quanHuyenTamTruId",
    ten: t("tenTruong.quanHuyenTamTruId"),
    store: "quanHuyen",
  },
  { id: "noiLamViec", ten: t("tenTruong.noiLamViec") },
  {
    id: "bsChiDinhNhapVienId",
    ten: t("tenTruong.bsChiDinhNhapVienId"),
    store: "nhanVien",
  },
  {
    id: "phongChiDinhNhapVienId",
    ten: t("tenTruong.phongChiDinhNhapVienId"),
    store: "phong",
  },
  {
    id: "trangThaiBenhAn",
    ten: t("tenTruong.trangThaiBenhAn"),
    enum: "trangThaiBenhAn",
  },
  {
    id: "trangThaiDayCong",
    ten: t("tenTruong.trangThaiDayCong"),
    enum: "trangThaiDayCong",
  },
];

export const listTenTruongNbDichVu = [
  {
    id: "nhomDichVuCap1Id",
    ten: t("tenTruong.nhomDichVuCap1Id"),
    store: "nhomDichVuCap1",
  },
  {
    id: "tienMienGiamPhieuThuBh",
    ten: t("tenTruong.tienMienGiamPhieuThuBh"),
  },
  {
    id: "tienGiamGiaBh",
    ten: t("tenTruong.tienGiamGiaBh"),
  },
  {
    id: "giaGoc",
    ten: t("tenTruong.giaGoc"),
  },
  {
    id: "phieuThuId",
    ten: t("tenTruong.phieuThuId"),
  },
  {
    id: "chiDinhTuDichVuId",
    ten: t("tenTruong.chiDinhTuDichVuId"),
  },
  {
    id: "tienNbCungChiTra",
    ten: t("tenTruong.tienNbCungChiTra"),
  },
  {
    id: "phacDoDieuTriDichVuId",
    ten: t("tenTruong.phacDoDieuTriDichVuId"),
    store: "dmPhacDoDieuTriDichVu",
  },
  {
    id: "tienNguonKhac",
    ten: t("tenTruong.tienNguonKhac"),
  },
  {
    id: "nbGoiDvChiTietId",
    ten: t("tenTruong.nbGoiDvChiTietId"),
  },
  {
    id: "mucDichId",
    ten: t("tenTruong.tenMucDichSuDung"),
    store: "mucDichSuDung",
  },
  {
    id: "phieuThuTongId",
    ten: t("tenTruong.phieuThuTongId"),
  },
  {
    id: "thoiGianChiDinh",
    ten: t("tenTruong.thoiGianChiDinh"),
  },
  {
    id: "nbGoiPtTtId",
    ten: t("tenTruong.nbGoiPtTtId"),
  },
  {
    id: "tienNbTraiTuyen",
    ten: t("tenTruong.tienNbTraiTuyen"),
  },
  {
    id: "tienMienGiamDichVuNhapVao",
    ten: t("tenTruong.tienMienGiamDichVuNhapVao"),
  },
  {
    id: "phieuThuKhongBhId",
    ten: t("tenTruong.phieuThuKhongBhId"),
  },
  {
    id: "thanhToan",
    ten: t("tenTruong.thanhToan"),
  },
  {
    id: "doiTuongKcb",
    ten: t("tenTruong.doiTuongKcb"),
    enum: "doiTuongKcb",
  },
  {
    id: "mienCungChiTra",
    ten: t("tenTruong.mienCungChiTra"),
  },
  {
    id: "active",
    ten: t("tenTruong.active"),
  },
  {
    id: "dvGiaId",
    ten: t("tenTruong.tuyChonGiaId"),
  },
  {
    id: "phieuDoiTraId",
    ten: t("tenTruong.phieuDoiTraId"),
  },
  {
    id: "thoiGianThucHien",
    ten: t("tenTruong.thoiGianThucHien"),
  },
  {
    id: "dichVuId",
    ten: t("tenTruong.dichVuId"),
  },
  {
    id: "loaiDichVu",
    ten: t("tenTruong.loaiDichVu"),
  },
  {
    id: "loaiHinhThanhToanId",
    ten: t("tenTruong.loaiHinhThanhToan"),
    store: "loaiHinhThanhToan",
  },
  {
    id: "nbTheBaoHiemId",
    ten: t("tenTruong.nbTheBaoHiemId"),
  },
  {
    id: "tienMienGiamDichVuKhongBh",
    ten: t("tenTruong.tienMienGiamDichVuKhongBh"),
  },
  {
    id: "chiDinhTuLoaiDichVu",
    ten: t("tenTruong.chiDinhTuLoaiDichVu"),
  },
  {
    id: "phieuThuBhId",
    ten: t("tenTruong.phieuThuBhId"),
  },
  {
    id: "phatHanhHoaDon",
    ten: t("tenTruong.phatHanhHoaDon"),
  },
  {
    id: "tienMienGiamPhieuThuKhongBh",
    ten: t("tenTruong.tienMienGiamPhieuThuKhongBh"),
  },
  {
    id: "bacSiChiDinhId",
    ten: t("tenTruong.bacSiChiDinh"),
    store: "nhanVien",
  },
  {
    id: "phanTramMienGiamDichVuBh",
    ten: t("tenTruong.phanTramMienGiamDichVuBh"),
  },
  {
    id: "ghiChu",
    ten: t("tenTruong.ghiChu"),
  },
  {
    id: "tienMienGiamDichVuBh",
    ten: t("tenTruong.tienMienGiamDichVuBh"),
  },
  {
    id: "tienNbPhuThu",
    ten: t("tenTruong.tienNbPhuThu"),
  },
  {
    id: "coSoYTeId",
    ten: t("tenTruong.coSoYTeId"),
    store: "benhVien",
  },
  {
    id: "khoaChiDinhId",
    ten: t("tenTruong.khoaChiDinh"),
    store: "khoa",
  },
  {
    id: "nhomDichVuCap2Id",
    ten: t("tenTruong.nhomDichVuCap2Id"),
    store: "nhomDichVuCap2",
  },
  {
    id: "nbPhacDoDieuTriId",
    ten: t("tenTruong.nbPhacDoDieuTriId"),
  },
  {
    id: "giaBaoHiem",
    ten: t("tenTruong.giaBaoHiem"),
  },
  {
    id: "giaKhongBaoHiem",
    ten: t("tenTruong.giaKhongBaoHiem"),
  },
  { id: "tyLeBhTt", ten: t("tenTruong.tyLeBhTt") },
  {
    id: "nbGoiDvId",
    ten: t("tenTruong.nbGoiDvId"),
  },
  {
    id: "nbChuyenKhoaId",
    ten: t("tenTruong.nbChuyenKhoaId"),
  },
  {
    id: "loaiDoiTuongId",
    ten: t("tenTruong.loaiDoiTuongId"),
    store: "loaiDoiTuong",
  },
  {
    id: "tienNbTuTra",
    ten: t("tenTruong.tienNbTuTra"),
  },
  {
    id: "nbBoChiDinhId",
    ten: t("tenTruong.nbBoChiDinhId"),
  },
  {
    id: "khongTinhTien",
    ten: t("tenTruong.khongTinhTien"),
  },
  {
    id: "ngoaiVien",
    ten: t("tenTruong.ngoaiVien"),
  },
  {
    id: "tyLeTtDv",
    ten: t("tenTruong.tyLeTtDv"),
  },
  {
    id: "tienBhThanhToan",
    ten: t("tenTruong.tienBhThanhToan"),
  },
  {
    id: "mucHuong",
    ten: t("tenTruong.mucHuong"),
  },
  {
    id: "tienGiamGiaKhongBh",
    ten: t("tenTruong.tienGiamGiaKhongBh"),
  },
  {
    id: "giaPhuThu",
    ten: t("tenTruong.giaPhuThu"),
  },
  {
    id: "trangThaiHoan",
    ten: t("tenTruong.trangThaiHoan"),
  },
  {
    id: "phanTramMienGiamDichVuKhongBh",
    ten: t("tenTruong.phanTramMienGiamDichVuKhongBh"),
  },
  {
    id: "dungTuyen",
    ten: t("tenTruong.dungTuyen"),
  },
  {
    id: "tuTra",
    ten: t("tenTruong.tuTra"),
  },
  {
    id: "soLuong",
    ten: t("tenTruong.soLuong"),
  },
  { id: "donViYTeId", ten: t("tenTruong.donViYTeId") },
];
