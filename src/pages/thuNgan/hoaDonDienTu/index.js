import React, { useEffect, useMemo } from "react";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { Main } from "./styled";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>erS<PERSON>ch,
  Page,
  TableWrapper,
  Pagination,
  Card,
  Button,
  Checkbox,
} from "components";
import moment from "moment";
import HeaderSearchHoaDon from "./component/headerSearchHoaDon";
import { useTranslation } from "react-i18next";
import ModalDsNguoiBenh from "./component/modalDsBenhNhan";
import { useRef } from "react";
import ModalXuatHoaDon from "../component/ModalXuatHoaDon";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import {
  ENUM,
  ROLES,
  TRANG_THAI_HOA_DON,
  THIET_LAP_CHUNG,
} from "constants/index";
import { useEnum, useLoading, useThietLap } from "hooks";
import { SVG } from "assets";
import ModalXuatHoaDonGuiLoi from "./component/modalXuatHoaDonGuiLoi";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import ModalXuatHoaDonHangLoat from "./component/modalXuatHoaDonHangLoat";
import ModalTaoHoaDonNhieuNguoi from "../hoaDonDienTu/TaoHoaDonNhieuNguoi";
import ModalXuatHdddTaoMoiHangLoat from "./component/ModalXuatHdddTaoMoiHangLoat";
import { mergeFilters } from "./config";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { getState } from "redux-store/stores";

const DsHoaDonDienTu = (props) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({ selectedRowKeys: [] });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalDsNb = useRef(null);
  const refModalXuatHoaDon = useRef(null);
  const refModalXuatHoaDonGuiLoi = useRef(null);
  const refModalXuatHoaDonHangLoat = useRef(null);
  const refModalTaoHoaDonNhieuNguoi = useRef(null);
  const refModalXuatHdddTaoMoiHangLoat = useRef(null);
  const refNhapLyDo = useRef(null);

  const [dataHIEN_THI_TIEN_AM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TIEN_AM_HOA_DON
  );

  const [listTrangThaiHoaDon] = useEnum(ENUM.TRANG_THAI_HOA_DON);
  const dsCoSoKcbId = getState()?.auth?.auth?.coSoKcbId;
  const { totalElements, listData, page, size, dataSortColumn } = useSelector(
    (state) => state.dsHoaDonDienTu
  );
  const { onSizeChange, onSortChange, onSearch, updateData, taoHoaDonThayThe } =
    dispatch.dsHoaDonDienTu;

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const handleChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ page: 0, size });
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        setState({
          dataSelect: record,
        });
      },
    };
  };

  const handleRedirect = (item) => {
    const { nbDotDieuTriId, maHoSo, id } = item;
    history.push({
      pathname: `/thu-ngan/chi-tiet-hoa-don/${maHoSo}/${id}/${nbDotDieuTriId}`,
      state: getAllQueryString(),
    });
  };

  const setRowClassName = (record) => {
    let idDiff = state.dataSelect?.id;
    return record.id === idDiff ? "row-actived" : null;
  };

  useEffect(() => {
    const { page, size, dataSortColumn, ...queries } = getAllQueryString();
    if (queries.dsTrangThai) {
      queries.dsTrangThai = queries.dsTrangThai
        .split(",")
        .map((i) => parseInt(i));
    }
    if (queries.dsLoaiHoaDon) {
      let _dsLoaiHoaDon = queries.dsLoaiHoaDon.split(",");
      Object.assign(queries, mergeFilters(_dsLoaiHoaDon));
    }
    queries.dsCoSoKcbId = dsCoSoKcbId;
    updateData({ dataSortColumn: { soHoaDon: 2 } });

    onSearch({
      page: parseInt(page || 0),
      size: parseInt(size || 10),
      dataSearch: queries,
      dataSortColumn: { soHoaDon: 2 },
    });
  }, []);

  const handleShowModal = () => {
    if (refModalXuatHoaDon.current) {
      refModalXuatHoaDon.current.show(handleCachXuatHoaDon);
    }
  };

  const handleCachXuatHoaDon = (value) => {
    if (value == 1) {
      if (refModalDsNb.current) {
        refModalDsNb.current.show();
      }
    } else if (value == 2) {
      if (refModalTaoHoaDonNhieuNguoi.current) {
        refModalTaoHoaDonNhieuNguoi.current.show();
      }
      // history.push("/thu-ngan/tao-hoa-don-nhieu-nguoi");
    } else if (value == 4) {
      if (refModalXuatHoaDonGuiLoi.current) {
        refModalXuatHoaDonGuiLoi.current.show({}, () => {
          onSizeChange({ size });
        });
      }
    } else if (value == 5) {
      if (refModalXuatHoaDonHangLoat.current) {
        refModalXuatHoaDonHangLoat.current.show(
          {
            dieuKienXuatHoaDon: true,
          },
          () => {
            onSizeChange({ size });
          }
        );
      }
    } else if (value == 6) {
      if (refModalXuatHdddTaoMoiHangLoat.current) {
        refModalXuatHdddTaoMoiHangLoat.current.show({}, () => {
          onSizeChange({ size });
        });
      }
    }
  };

  const onTaoHoaDonThayThe = (record) => (e) => {
    e.stopPropagation();
    refNhapLyDo.current &&
      refNhapLyDo.current.show(
        {
          title: t("thuNgan.lyDoTaoHoaDonThayThe"),
          message: t("thuNgan.nhapLyDoTaoHoaDonThayThe"),
        },
        async (lyDo) => {
          try {
            showLoading();
            await taoHoaDonThayThe({
              id: record.id,
              lyDo,
            });
            onSizeChange({ size });
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      );
  };

  const tinhTongTienHoaDon = (record) => {
    if (
      dataHIEN_THI_TIEN_AM_HOA_DON?.eval() &&
      record.loaiDieuChinh == null &&
      [45, 50].includes(record.trangThai)
    ) {
      return 0 - record.thanhTien;
    }

    return record.thanhTien;
  };

  const columnsGroup = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 80,
      dataIndex: "index",
      key: "index",
      align: "center",
      fixed: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soHoaDon")}
          sort_key="soHoaDon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.soHoaDon || 0}
        />
      ),
      width: 120,
      dataIndex: "soHoaDon",
      key: "soHoaDon",
      align: "left",
      fixed: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.kyHieuHoaDon")}
          sort_key="kyHieuHoaDon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.kyHieuHoaDon || 0}
        />
      ),
      width: 150,
      dataIndex: "kyHieuHoaDon",
      key: "kyHieuHoaDon",
      align: "left",
      fixed: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tongTienHoaDon")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.tongTien || 0}
        />
      ),
      width: 200,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      render: (item, record) => tinhTongTienHoaDon(record).formatPrice() || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thoiGianXuatHoaDon")}
          sort_key="thoiGianPhatHanhHd"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.thoiGianXuatHoaDon || 0}
        />
      ),
      width: 200,
      dataIndex: "thoiGianPhatHanhHd",
      key: "thoiGianPhatHanhHd",
      align: "left",
      render: (item, list) => {
        if (
          [
            TRANG_THAI_HOA_DON.HD_TAO_MOI,
            TRANG_THAI_HOA_DON.HD_PHAT_HANH_LOI,
            TRANG_THAI_HOA_DON.HD_CHO_DIEU_CHINH,
          ].includes(list.trangThai)
        ) {
          return "";
        }

        return moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.maHoSo || 0}
        />
      ),
      width: 120,
      dataIndex: "maHoSo",
      key: "maHoSo",
      align: "left",
    },

    {
      title: (
        <HeaderSearch
          title={t("thuNgan.hoTenNguoiBenh")}
          sort_key="tenNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.tenNb || 0}
        />
      ),
      width: 250,
      dataIndex: "tenNb",
      key: "tenNb",
      align: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenCongTy")}
          sort_key="tenCongTy"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.tenCongTy || 0}
        />
      ),
      width: 250,
      dataIndex: "tenCongTy",
      key: "tenCongTy",
      align: "left",
    },

    {
      title: (
        <HeaderSearch
          title={t("thuNgan.nguoiXuatHoaDon")}
          sort_key="tenNguoiPhatHanhHd"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.tenNguoiXuatHoaDon || 0}
        />
      ),
      width: 200,
      dataIndex: "tenNguoiPhatHanhHd",
      key: "tenNguoiPhatHanhHd",
      align: "left",
    },

    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soHoaDonGoc")}
          sort_key="soHoaDonGoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.soHoaDonGoc || 0}
        />
      ),
      width: 200,
      dataIndex: "soHoaDonGoc",
      key: "soHoaDonGoc",
      align: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.kyHieuHoaDonGoc")}
          sort_key="kyHieuHoaDonGoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.kyHieuHoaDonGoc || 0}
        />
      ),
      width: 200,
      dataIndex: "kyHieuHoaDonGoc",
      key: "kyHieuHoaDonGoc",
      align: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThai")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.trangThai || 0}
        />
      ),
      width: 140,
      dataIndex: "trangThai",
      key: "trangThai",
      align: "left",
      fixed: "right",
      render: (text) =>
        listTrangThaiHoaDon
          ? listTrangThaiHoaDon.find((item) => item.id == text)?.ten
          : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiChuyenDoi")}
          sort_key="chuyenDoi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.chuyenDoi || 0}
        />
      ),
      width: 140,
      dataIndex: "chuyenDoi",
      key: "chuyenDoi",
      fixed: "right",
      render: (item) =>
        item ? t("thuNgan.daChuyenDoi") : t("thuNgan.chuaChuyenDoi"),
    },
    {
      title: <HeaderSearch title={t("common.thaoTac")} sort_key="soPhieu" />,
      width: 150,
      dataIndex: "soHoaDon",
      key: "soHoaDon",
      align: "center",
      render: (text, item) => {
        return (
          <>
            <Tooltip title={t("thuNgan.xemHoaDon")} placement="bottom">
              <SVG.IcEye
                className="ic-action"
                onClick={() => handleRedirect(item)}
              />
            </Tooltip>
            {checkRole([ROLES["THU_NGAN"].TAO_HOA_DON_THAY_THE]) &&
              item.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH && (
                <Tooltip
                  title={t("thuNgan.taoHoaDonThayThe")}
                  placement="bottom"
                >
                  <SVG.IcAdd
                    className="ic-action"
                    onClick={onTaoHoaDonThayThe(item)}
                  />
                </Tooltip>
              )}
          </>
        );
      },
      fixed: "right",
    },
  ];

  const onSelectChange = (selectedRowKeys, data) => {
    let updatedSelectedKeys = selectedRowKeys;
    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
    setState({
      selectedRowKeys: updatedSelectedKeys,
      dsDichVuId: data.map((item) => item.dichVuId),
      phongId: null,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (listData || []).map((x) => x.id)
        : [],
      dsDichVuId: e.target?.checked
        ? listData?.map((item) => item.dichVuId)
        : [],
    });
  };

  const isCheckedAll = useMemo(() => {
    return (
      (listData || []).length &&
      listData
        .map((item) => item.id)
        .every((i) => state.selectedRowKeys.includes(i))
    );
  }, [listData, state.selectedRowKeys]);

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={onCheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Page
      breadcrumb={[
        { title: t("thuNgan.thuNgan"), link: "/thu-ngan" },
        {
          title: t("thuNgan.hoaDonDienTu"),
          link: "/thu-ngan/ds-hoa-don-dien-tu",
        },
      ]}
      titleRight={
        <>
          {checkRoleOr([
            ROLES["THU_NGAN"].THEM_HOA_DON_DIEN_TU,
            ROLES["THU_NGAN"].THEM_HOA_DON_DIEN_TU_NHIEU_NGUOI,
          ]) && (
            <Button type="primary" onClick={handleShowModal}>
              <span> {t("common.themMoi")} </span>
            </Button>
          )}
        </>
      }
      title={t("thuNgan.hoaDonDienTu")}
    >
      <Main>
        <HeaderSearchHoaDon
          tongTien={listData
            .filter((item) => (state.selectedRowKeys || []).includes(item.id))
            .reduce((prev, current) => prev + tinhTongTienHoaDon(current), 0)}
        />
        <Card noPadding={true}>
          <TableWrapper
            rowKey={(record) => record?.id}
            columns={columnsGroup}
            dataSource={listData || []}
            onRow={onRow}
            scroll={{ x: 200 }}
            rowClassName={setRowClassName}
            rowSelection={rowSelection}
          />
          {!!totalElements ? (
            <Pagination
              listData={listData}
              onChange={handleChangePage}
              current={page + 1 || 1}
              pageSize={size || 10}
              total={totalElements || 15}
              onShowSizeChange={handleSizeChange}
            />
          ) : null}
        </Card>
        <ModalDsNguoiBenh ref={refModalDsNb}></ModalDsNguoiBenh>
        <ModalXuatHoaDon ref={refModalXuatHoaDon}></ModalXuatHoaDon>
        <ModalXuatHoaDonGuiLoi ref={refModalXuatHoaDonGuiLoi} />
        <ModalXuatHoaDonHangLoat ref={refModalXuatHoaDonHangLoat} />
        <ModalTaoHoaDonNhieuNguoi ref={refModalTaoHoaDonNhieuNguoi} />
        <ModalXuatHdddTaoMoiHangLoat ref={refModalXuatHdddTaoMoiHangLoat} />
        <ModalNhapLyDo ref={refNhapLyDo} />
      </Main>
    </Page>
  );
};

export default DsHoaDonDienTu;
