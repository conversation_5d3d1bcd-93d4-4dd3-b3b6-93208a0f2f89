import React, { useEffect, useRef, useState } from "react";
import { Main, MainPage } from "./styled";
import { useDispatch } from "react-redux";
import { useHistory, useLocation, useParams } from "react-router-dom";
import ThongTinHoaDon from "../../component/thongTinHoaDon/ThongTinHoaDon";
import TableDsDichVu from "../../component/TableDsDichVu";
import ModalDeleteHoaDon from "../../component/ModalDeleteHoaDon";
import ModalXuatHoaDon from "../../component/ModalXuatHoaDon";
import ModalXuatHoaDonGuiLoi from "../component/modalXuatHoaDonGuiLoi";
import ModalXuatHoaDonHangLoat from "../component/modalXuatHoaDonHangLoat";
import ModalDsNguoiBenh from "../component/modalDsBenhNhan";
import ModalTaoHoaDonNhieuNguoi from "../../hoaDonDienTu/TaoHoaDonNhieuNguoi";
import { ROLES, TRANG_THAI_HOA_DON, THIET_LAP_CHUNG } from "constants/index";
import { useTranslation } from "react-i18next";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import { Button, Tooltip } from "components";
import { SVG } from "assets";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { useLoading, useThietLap } from "hooks";
import { showError } from "utils/message-utils";
import ModalSapXepThuoc from "pages/khamBenh/DonThuoc/ModalSapXepThuoc";
import { orderBy } from "lodash";

const ChiTietHoaDonDienTu = (props) => {
  const { t } = useTranslation();
  const history = useHistory();
  const { state: locationState } = useLocation();
  const refModalDeleteHoaDon = useRef(null);
  const refModalDsNb = useRef(null);
  const refModalXuatHoaDon = useRef(null);
  const refModalXuatHoaDonGuiLoi = useRef(null);
  const refModalXuatHoaDonHangLoat = useRef(null);
  const refModalTaoHoaDonNhieuNguoi = useRef(null);
  const refMoDalSapXepThuoc = useRef(null);
  const { showLoading, hideLoading } = useLoading();

  const [state, _setState] = useState({
    phatHanhHdCongTy: true,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { maHoSo, hoaDonId, nbDotDieuTriId } = useParams();
  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );
  const {
    dsHoaDonDienTu: {
      getDsDichVuChiTiet,
      deleteHoaDon,
      getChiTietHoaDon,
      xuatHoaDonNhap,
      updateData,
      dieuChinhHoaDon,
      inHoaDon,
    },
  } = useDispatch();
  useEffect(() => {
    fetchData(hoaDonId);
  }, [hoaDonId]);

  const fetchData = (hoaDonId) => {
    getDsDichVuChiTiet({ hoaDonId }).then((s) => {
      setState({ dsDichVu: orderBy(s, "sttHienThi", "asc") });
      let price = 0;
      (s || []).forEach((e) => {
        price += e.thanhTien;
      });

      price = Math.round(price);
      updateData({ price });
    });
    getChiTietHoaDon({
      id: hoaDonId,
    }).then((s) => {
      setState({
        thongTinHoaDon: s,
      });
    });
  };
  const handleRedirect = () => {
    history.push("/thu-ngan/ds-hoa-don-dien-tu");
  };
  const handleXuatHoaDon = async () => {
    try {
      setState({
        isLoadingXuatHd: true,
      });
      let s = await xuatHoaDonNhap({ id: hoaDonId });
      if (s.code === 0) {
        await inHoaDon({
          hoaDonId: hoaDonId,
          dinhDang: dataDINH_DANG_XEM_HOA_DON,
        });
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      setState({
        isLoadingXuatHd: false,
      });
    }
  };

  const onClickHoaDonChuyenDoi = async () => {
    showLoading();
    try {
      await inHoaDon({
        hoaDonId: hoaDonId,
        dinhDang: dataDINH_DANG_XEM_HOA_DON,
        chuyenDoi: true,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const handleDieuChinhHoaDon = () => {
    dieuChinhHoaDon({ id: hoaDonId }).then((s) => {
      setState({
        thongTinHoaDon: { ...state?.thongTinHoaDon, ...s.data },
      });
    });
  };

  const onSuaHoaDon = () => {
    let params = {
      pathname: `/thu-ngan/chinh-sua-hoa-don-dien-tu/${maHoSo}/${hoaDonId}/${nbDotDieuTriId}`,
    };
    if (locationState) params.state = locationState;
    history.push(params);
  };

  const onDelete = () => {
    if (refModalDeleteHoaDon.current) {
      refModalDeleteHoaDon.current.show(state?.thongTinHoaDon, handleDelete);
    }
  };
  const handleDelete = (lyDo) => {
    deleteHoaDon({ id: hoaDonId, lyDo: lyDo }).then((s) => {
      setState({
        thongTinHoaDon: {
          ...state.thongTinHoaDon,
          trangThai: 40,
        },
      });
    });
  };
  const handleShowModal = () => {
    if (refModalXuatHoaDon.current) {
      refModalXuatHoaDon.current.show(handleCachXuatHoaDon);
    }
  };
  const handleCachXuatHoaDon = (value) => {
    if (value == 1) {
      if (refModalDsNb.current) {
        refModalDsNb.current.show();
      }
    } else if (value == 2) {
      if (refModalTaoHoaDonNhieuNguoi.current) {
        refModalTaoHoaDonNhieuNguoi.current.show();
      }
      // history.push("/thu-ngan/tao-hoa-don-nhieu-nguoi");
    } else if (value == 4) {
      if (refModalXuatHoaDonGuiLoi.current) {
        refModalXuatHoaDonGuiLoi.current.show();
      }
    } else if (value == 5) {
      if (refModalXuatHoaDonHangLoat.current) {
        refModalXuatHoaDonHangLoat.current.show();
      }
    }
  };
  const onSaveModalCustomizeColumn = () => {
    fetchData(hoaDonId);
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("thuNgan.thuNgan"), link: "/thu-ngan" },
        {
          title: t("thuNgan.hoaDonDienTu"),
          link:
            "/thu-ngan/ds-hoa-don-dien-tu" +
            transformObjToQueryString(locationState),
        },
        {
          title: t("thuNgan.chiTietHoaDon"),
          link: `/thu-ngan/chi-tiet-hoa-don/${maHoSo}/${hoaDonId}/${nbDotDieuTriId}`,
        },
      ]}
      title={
        <>
          <span>{t("thuNgan.chiTietHoaDon")}</span>{" "}
          <div className="header-action">
            <div className="action-btn">
              <Tooltip title={t("thuNgan.danhSachHDDT")} placement="bottom">
                <SVG.IcList
                  onClick={handleRedirect}
                  style={{ fontSize: 20 }}
                ></SVG.IcList>
              </Tooltip>
            </div>
            {checkRole([ROLES["THU_NGAN"].XOA_HOA_DON]) && (
              <div className="action-btn" style={{ paddingLeft: "5px" }}>
                <Tooltip
                  placement="top"
                  title={
                    state.thongTinHoaDon?.trangThai ===
                    TRANG_THAI_HOA_DON.HD_TAO_MOI
                      ? t("thuNgan.xoaBoHoaDonTaoMoi")
                      : state.thongTinHoaDon?.trangThai ===
                        TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH
                      ? t("thuNgan.xoaBoHoaDonDaPhatHanh")
                      : t("thuNgan.xoaBoHoaDonChoXoa")
                  }
                >
                  <SVG.IcDelete onClick={onDelete} style={{ fontSize: 20 }} />
                </Tooltip>
              </div>
            )}
            <div className="action-btn">
              <Tooltip title={t("common.sapXepThongTinThuoc")}>
                <SVG.IcSetting
                  onClick={() =>
                    refMoDalSapXepThuoc.current &&
                    refMoDalSapXepThuoc.current.show({})
                  }
                />
              </Tooltip>
            </div>
          </div>
          {checkRoleOr([
            ROLES["THU_NGAN"].THEM_HOA_DON_DIEN_TU,
            ROLES["THU_NGAN"].THEM_HOA_DON_DIEN_TU_NHIEU_NGUOI,
          ]) && (
            <Button
              type="primary"
              style={{ marginLeft: "auto" }}
              onClick={handleShowModal}
              rightIcon={<SVG.IcAdd />}
            >
              <span> {t("common.themMoi")} </span>
            </Button>
          )}
          {state?.thongTinHoaDon?.trangThai ===
            TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH &&
            checkRole([ROLES["THU_NGAN"].HOA_DON_CHUYEN_DOI_CHI_TIET_HDDT]) && (
              <div className="bottom-group">
                <Button
                  type="default"
                  onClick={onClickHoaDonChuyenDoi}
                  minWidth={105}
                >
                  {t("thuNgan.hoaDonChuyenDoi")}
                </Button>
              </div>
            )}
        </>
      }
      actionRight={
        <>
          {state?.thongTinHoaDon?.trangThai ===
          TRANG_THAI_HOA_DON.HD_TAO_MOI ? (
            <Button type="default" onClick={onSuaHoaDon}>
              {t("thuNgan.suaHoaDon")}
            </Button>
          ) : null}
          {state?.thongTinHoaDon?.trangThai ===
          TRANG_THAI_HOA_DON.HD_CHO_DIEU_CHINH ? (
            <Button type="primary" onClick={handleDieuChinhHoaDon}>
              {t("thuNgan.dieuChinhHoaDon")}
            </Button>
          ) : null}
          {(state?.thongTinHoaDon?.trangThai ===
            TRANG_THAI_HOA_DON.HD_PHAT_HANH_LOI ||
            state?.thongTinHoaDon?.trangThai ===
              TRANG_THAI_HOA_DON.HD_TAO_MOI) && (
            <Button
              minWidth={100}
              loading={state?.isLoadingXuatHd}
              type="primary"
              onClick={handleXuatHoaDon}
              rightIcon={<SVG.IcXuatHoaDon />}
            >
              {t("thuNgan.xuatHoaDon")}
            </Button>
          )}
        </>
      }
      contentRight={
        <ThongTinHoaDon
          className="right-content"
          thongTinHoaDon={state.thongTinHoaDon}
          isChiTiet={true}
          setStateParent={setState}
          phatHanhHdCongTy={state.phatHanhHdCongTy}
          isNhieuNB={nbDotDieuTriId === "null" ? true : false}
          typeHoaDon={1}
        ></ThongTinHoaDon>
      }
    >
      <Main>
        <TableDsDichVu
          nbDotDieuTriId={nbDotDieuTriId}
          dsDichVu={state.dsDichVu}
          isChiTiet={true}
        />
        <ModalDeleteHoaDon ref={refModalDeleteHoaDon}></ModalDeleteHoaDon>{" "}
        <ModalDsNguoiBenh ref={refModalDsNb} />
        <ModalXuatHoaDon ref={refModalXuatHoaDon} />
        <ModalXuatHoaDonGuiLoi ref={refModalXuatHoaDonGuiLoi} />
        <ModalXuatHoaDonHangLoat ref={refModalXuatHoaDonHangLoat} />
        <ModalTaoHoaDonNhieuNguoi ref={refModalTaoHoaDonNhieuNguoi} />
        <ModalSapXepThuoc
          ref={refMoDalSapXepThuoc}
          listData={state.dsDichVu}
          onSaveModalCustomizeColumn={onSaveModalCustomizeColumn}
        />
      </Main>
    </MainPage>
  );
};

export default ChiTietHoaDonDienTu;
