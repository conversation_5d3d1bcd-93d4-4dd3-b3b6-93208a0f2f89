import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import moment from "moment";
import {
  ENUM,
  HOTKEY,
  ROLES,
  LIST_TRANG_THAI_THANH_TOAN,
  TRANG_THAI_NB,
  THIET_LAP_CHUNG,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_QUAY,
  CACHE_KEY,
  TRANG_THAI_DICH_VU,
  LOAI_PHIEU_THU,
  BIRTHDAY_FORMAT,
} from "constants/index";
import {
  Select,
  TableWrapper,
  Card,
  InputTimeout,
  Pagination,
  HeaderSearch,
  Checkbox,
  Tooltip,
} from "components";
import { checkRole } from "lib-utils/role-utils";
import {
  checkRoleXemDsPhieuThu,
  LIST_TRANG_THAI_TKTT,
  SO_TIEN,
  TIME_FORMAT,
} from "./configs";
import PhieuThuHeader from "./phieuThuHeader";
import <PERSON><PERSON>iemBenhNhan from "../TimKiemBenhNhan";
import { cloneDeep, isEqual, repeat } from "lodash";
import {
  useCache,
  useEnum,
  useGuid,
  useLazyKVMap,
  useListAll,
  useQueryAll,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import {
  setQueryStringValue,
  setQueryStringValues,
  getAllQueryString,
} from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import { isArray, isObject, transformQueryString } from "utils/index";
import classNames from "classnames";
import { query, select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { MainPage, RangePickerStyled } from "./styled";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

const { Setting } = TableWrapper;

const DanhSachPhieuThu = (props) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const refOnChangeSelect = useRef(null);

  const [state, _setState] = useState({
    currentIndex: 0,
    thanhToan: -1,
    ...getAllQueryString(),
    orgSearch: {
      thanhToan: -1,
      ...getAllQueryString(),
    },
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const layerId = useGuid();
  const {
    listData,
    totalElements,
    page,
    size,
    dataSearch,
    dataSortColumn,
    manHinhPath,
    first,
    last,
    isLoading,
  } = useStore(
    "danhSachPhieuThu",
    {},
    {
      fields:
        "listData,totalElements,page,size,dataSearch,dataSortColumn,manHinhPath,first,last,isLoading",
    }
  );
  const userName = useStore("auth.auth.username", "");
  const { listAllKhoa } = useSelector((state) => state.khoa);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listTrangQuyetToan] = useEnum(ENUM.TRANG_THAI_QUYET_TOAN);
  const [listTrangThaiPhatHanhHoaDon] = useEnum(
    ENUM.TRANG_THAI_PHAT_HANH_HOA_DON
  );
  const [listtrangThaiPhieuThu] = useEnum(ENUM.TRANG_THAI_PHIEU_THU);
  const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);
  const [listTrangThaiXacNhanBaoHiem] = useEnum(
    ENUM.TRANG_THAI_XAC_NHAN_BAO_HIEM
  );

  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listTrangThaiTongKetThanhToan] = useEnum(
    ENUM.TRANG_THAI_TONG_KET_THANH_TOAN
  );
  const [listPhanLoaiNBCapCuu] = useEnum(ENUM.PHAN_LOAI_NB_CAP_CUU);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const { data: listAllPhanLoaiNB } = useQueryAll(
    query.phanLoaiNB.queryAllPhanLoaiNB
  );

  const [dataPHIEU_THU_HIEN_THI_LOAI_DICH_VU] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_THU_HIEN_THI_LOAI_DICH_VU
  );
  const [dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA, loadFinishThietLap] =
    useThietLap(THIET_LAP_CHUNG.TACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA);
  const [dataPHIEU_THU_HIEN_THI_TT_KHAM, loadFinishTTKham] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_THU_HIEN_THI_TT_KHAM
  );
  const [dataHIEN_THI_KET_NOI_MEMO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_NOI_MEMO
  );
  const [
    dataTACH_DAI_PHIEU_THU_DV_NGOAI_DIEU_TRI,
    loadFinishThietLapTachDaiPhieuThu,
  ] = useThietLap(THIET_LAP_CHUNG.TACH_DAI_PHIEU_THU_DV_NGOAI_DIEU_TRI);

  const isTachDaiPhieuThuDVNgoaiDieuTri =
    dataTACH_DAI_PHIEU_THU_DV_NGOAI_DIEU_TRI?.eval();

  const authId = useStore("auth.auth.id");
  const [quayTiepDon, setQuayTiepDon, loadFinish] = useCache(
    authId + LOAI_QUAY.THU_NGAN,
    CACHE_KEY.DATA_NHA_TAM_UNG,
    null,
    false,
    true
  );

  const [hienThiPhieuThu, _, loadFinishHienThiPhieuThu] = useCache(
    authId,
    CACHE_KEY.HIEN_THI_PHIEU_THU,
    1,
    false,
    true
  );

  const {
    danhSachPhieuThu: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      clearData: clearDataDSPhieuThu,
    },
    khoa: { getListAllKhoa },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    benhVien: { getListAllBenhVien },
  } = useDispatch();

  const listAllPhuongThucThanhToan = useStore(
    "phuongThucTT.listAllPhuongThucThanhToan",
    []
  );

  const [listAllBenhVien, getBenhVienById] = useStore(
    select.benhVien.listAllBenhVien
  );

  const checkIsOnly = (params) => {
    return Object.keys(params).some(
      (key) =>
        ["maHoSo", "tenNb", "maThe", "maNb", "maBenhAn"].includes(key) &&
        params[key]
    );
  };
  const history = useHistory();

  const listTrangThaiNbCustom = useMemo(() => {
    const listData = listTrangThaiNb.filter(
      (x) =>
        ![
          TRANG_THAI_NB.CHO_DUYET_CHI_PHI_HEN_DIEU_TRI,
          TRANG_THAI_NB.CHO_DUYET_CHI_PHI,
          TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI,
          TRANG_THAI_NB.TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI,
          TRANG_THAI_NB.DA_DUYET_CHI_PHI_HEN_DIEU_TRI,
          TRANG_THAI_NB.DA_DUYET_CHI_PHI,
        ].includes(x.id)
    );

    return [
      ...listData,
      { id: 102, ten: t("common.tuChoiDuyetChiPhi"), listIds: [102, 112] },
      { id: 103, ten: t("common.choDuyetChiPhi"), listIds: [103, 113] },
      { id: 106, ten: t("common.daDuyetChiPhi"), listIds: [106, 116] },
    ];
  }, [listTrangThaiNb]);

  const listTrangThaiDichVuMemo = useMemo(() => {
    return listTrangThaiDichVu.filter((trangThaiDichVu) =>
      [
        TRANG_THAI_DICH_VU.CHO_KHAM,
        TRANG_THAI_DICH_VU.DANG_KHAM,
        TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU,
        TRANG_THAI_DICH_VU.CHO_KET_LUAN,
        TRANG_THAI_DICH_VU.DANG_KET_LUAN,
        TRANG_THAI_DICH_VU.DA_KET_LUAN,
        TRANG_THAI_DICH_VU.BO_QUA,
      ].some((item) => item === trangThaiDichVu.id)
    );
  }, [listTrangThaiDichVu]);

  const isShowColumnTTKham = useMemo(() => {
    return loadFinishTTKham && dataPHIEU_THU_HIEN_THI_TT_KHAM?.eval();
  }, [dataPHIEU_THU_HIEN_THI_TT_KHAM]);

  const [getTrangThaiNb] = useLazyKVMap(listTrangThaiNb);
  const [getTrangThaiNbCustom] = useLazyKVMap(listTrangThaiNbCustom);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  useEffect(() => {
    //Do dùng chung model store giữa màn ds thu dv ngoài điều trị và màn ds phiếu thu
    //=> check nếu chuyển đổi giữa 2 màn hình thì reset các param tìm kiếm
    if (window.location.pathname !== manHinhPath) {
      clearDataDSPhieuThu({ manHinhPath: window.location.pathname });
    }

    if (
      !loadFinish ||
      !loadFinishThietLap ||
      !listTrangThaiNb.length ||
      !loadFinishThietLapTachDaiPhieuThu ||
      !loadFinishHienThiPhieuThu
    )
      return;
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = transformQueryString({
      tuThoiGianThanhToan: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
      },
      denThoiGianThanhToan: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
      },
      tuThoiGianVaoVien: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
        defaultValue: moment().startOf("day").format("YYYY-MM-DDTHH:mm:ss"),
      },
      denThoiGianVaoVien: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
        defaultValue: moment().endOf("day").format("YYYY-MM-DDTHH:mm:ss"),
      },
      tuThoiGianRaVien: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
      },
      denThoiGianRaVien: {
        format: (value) => moment(value).format("YYYY-MM-DDTHH:mm:ss"),
        type: "dateOptions",
      },
      dsPhuongThucTtId: {
        format: (value) => value.split(",").map(Number),
      },
      dsTrangThaiThanhToan: {
        format: (value) => value.split(",").map(Number),
      },
      dsTrangThaiXacNhanBh: {
        format: (value) => value.split(",").map(Number),
      },
      dsDoiTuongKcb: {
        format: (value) => parseInt(value),
      },
      thanhToan: {
        format: (value) => (value ? Number(value) : null),
        defaultValue: -1,
      },
      dsTrangThaiNb: {
        format: (value) => (value ? value.split(",").map(Number) : value),
        defaultValue: listTrangThaiNbCustom.map((i) => i.id),
      },
      thanhTien: {
        defaultValue: "",
      },
      dsTrangThaiKham: {
        format: (value) => (value ? Number(value) : null),
        defaultValue: null,
      },
      dsTrangThaiTktt: {
        format: (value) => (value ? Number(value) : null),
      },
      dsNguoiThucHienId: {
        format: (value) => (value ? Number(value) : null),
      },
    });
    const sort = JSON.parse(dataSortColumn);

    const [tuThanhTien, denThanhTien] = (queries.thanhTien || ",").split(",");
    if (tuThanhTien === "0") {
      queries.tuThanhTien = 0;
      queries.denThanhTien = 0;
    } else {
      queries.tuThanhTien = tuThanhTien;
      queries.denThanhTien = denThanhTien;
    }

    Object.assign(queries, {
      tuThanhTien,
      denThanhTien,
    });
    setState({
      ...queries,
      orgSearch: {
        ...queries,
      },
    });
    delete queries.thanhTien;

    let _dataSearch = {
      ...queries,
      ...checkRoleXemDsPhieuThu(),
      ...(dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA?.eval()
        ? { nhaThuNganId: quayTiepDon?.toaNhaId }
        : {}),
      dsTrangThaiNb: queries.dsTrangThaiNb?.flatMap((i) => {
        const item = getTrangThaiNbCustom(i);
        if (item) {
          return item?.listIds ?? item?.id;
        }
        return i;
      }),
      ...(isTachDaiPhieuThuDVNgoaiDieuTri
        ? {
            dsLoaiPhieuThu: [
              LOAI_PHIEU_THU.PHIEU_THU_TONG,
              LOAI_PHIEU_THU.KHONG_BAO_HIEM,
              LOAI_PHIEU_THU.BAO_HIEM,
              LOAI_PHIEU_THU.PHU_THU,
              LOAI_PHIEU_THU.NHA_THUOC,
              LOAI_PHIEU_THU.KHAM_SUC_KHOE,
              LOAI_PHIEU_THU.GHI_DICH_VU,
            ],
          }
        : {}),
      dsDoiTuongKcb: queries.dsDoiTuongKcb
        ? getPhanLoaiDoiTuongKcb(queries.dsDoiTuongKcb).referIds
        : null,
      isTatCaToaNha: hienThiPhieuThu === 2,
      khuVucId: quayTiepDon?.khuVucId,
    };

    //Nếu tài khoản KHÔNG CÓ QUYỀN: 0400124
    //param chỉ truyền loại phiếu thu 0,1,2,3,10,15 ( bỏ loại =6)
    if (!checkRole([ROLES["THU_NGAN"].XEM_DS_PHIEU_THU_NHA_THUOC])) {
      if (_dataSearch.dsLoaiPhieuThu) {
        _dataSearch.dsLoaiPhieuThu = _dataSearch.dsLoaiPhieuThu.filter(
          (x) => x !== LOAI_PHIEU_THU.NHA_THUOC
        );
      } else {
        _dataSearch.dsLoaiPhieuThu = [
          LOAI_PHIEU_THU.PHIEU_THU_TONG,
          LOAI_PHIEU_THU.KHONG_BAO_HIEM,
          LOAI_PHIEU_THU.BAO_HIEM,
          LOAI_PHIEU_THU.PHU_THU,
          LOAI_PHIEU_THU.NGOAI_DIEU_TRI,
          LOAI_PHIEU_THU.KHAM_SUC_KHOE,
          LOAI_PHIEU_THU.GHI_DICH_VU,
        ];
      }
    }

    if (isArray(quayTiepDon?.dsKhoId, true)) {
      _dataSearch.khoId = quayTiepDon.dsKhoId[0];
    }

    onSizeChange({
      size: parseInt(size || 50),
      page: parseInt(page || 0),
      dataSearch: _dataSearch,
      dataSortColumn: sort,
      isOnly: checkIsOnly(queries),
      getThongKe: true,
    });
  }, [
    loadFinish,
    loadFinishThietLap,
    loadFinishHienThiPhieuThu,
    dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA,
    listTrangThaiNb,
    isTachDaiPhieuThuDVNgoaiDieuTri,
    loadFinishThietLapTachDaiPhieuThu,
  ]);

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  useEffect(() => {
    const params = { active: true, page: "", size: "" };
    getListAllPhuongThucThanhToan(params);
    getListAllKhoa(params);
    getListAllBenhVien(params);
  }, []);

  useEffect(() => {
    if (state.currentIndex > listData.length - 1) {
      setState({ currentIndex: 0 });
    }
  }, [listData]);

  const scrollToRow = (id) => {
    document
      .getElementsByClassName("row-id-" + id)[0]
      .scrollIntoView({ block: "center", behavior: "smooth" });
  };
  useEffect(() => {
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP, //Mũi tên lên
          onEvent: (e) => {
            if (state.currentIndex > 0) {
              setState({
                currentIndex: state.currentIndex - 1,
              });
              scrollToRow(listData[state.currentIndex - 1]?.id);
            }
          },
        },

        {
          keyCode: HOTKEY.DOWN, //Mũi tên xuống
          onEvent: (e) => {
            if (state.currentIndex < listData.length - 1) {
              setState({
                currentIndex: state.currentIndex + 1,
              });
              scrollToRow(listData[state.currentIndex + 1]?.id);
            }
          },
        },
        {
          keyCode: HOTKEY.ENTER, //Enter
          onEvent: (e) => {
            // const record = listData[state.currentIndex];
            // if (record && e.target.nodeName !== "INPUT") {
            //   onRow(record).onClick();
            // }
          },
        },
      ],
    });
  }, [listData, state.currentIndex]);

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value, getThongKe: true });
  };

  const onSearchInput = (key, isTrimStr) => (e, data) => {
    let value = "";
    let queryString = null;
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    if (isTrimStr) value = value.trim();

    if (key === "maHoSo") {
      value = fillMaHoSoSearchValue(value);
    }
    if (key === "maNb") {
      value = fillMaNbSearchValue(value);
    }

    if (
      key === "dsPhuongThucTtId" ||
      key === "dsTrangThaiThanhToan" ||
      key === "dsLoaiDichVu" ||
      key === "dsTrangThaiXacNhanBh"
    ) {
      setState({
        [key]: value,
        orgSearch: { ...state.orgSearch, [key]: value },
      });
    }

    if (key === "dsDoiTuongKcb") {
      queryString = value;
      value = value ? getPhanLoaiDoiTuongKcb(value).referIds : null;
    }

    if (key === "dsTrangThaiNb") {
      queryString = value;
      value = data.flatMap((item) => item?.listIds ?? item.id);
    }

    setQueryStringValues({
      [key]: queryString ?? value,
      page: 0,
    });

    onChangeInputSearch({
      [key]: value,
      isOnly: checkIsOnly({
        ...dataSearch,
        [key]: value,
      }),
      getThongKe: true,
    });
  };

  const onSearchDate = (key) => (e) => {
    let value = null;
    let value1 = null;
    if (e) {
      value = e[0].format("YYYY-MM-DD");
      value1 = e[1].format("YYYY-MM-DD");
    }

    setState({
      [`tu${key}`]: e ? e[0] : null,
      [`den${key}`]: e ? e[1] : null,
      orgSearch: {
        ...state.orgSearch,
        [`tu${key}`]: e ? e[0] : null,
        [`den${key}`]: e ? e[1] : null,
      },
    });

    onChangeInputSearch({
      [`tu${key}`]: value,
      [`den${key}`]: value1,
      isOnly: checkIsOnly({
        ...dataSearch,
        [key]: value,
      }),
      getThongKe: true,
    });
  };

  //function
  function fillMaHoSoSearchValue(initValue) {
    if (!initValue) return null;

    if (initValue.length <= 4) {
      let returnValue = moment().format("YYMMDD");
      returnValue += repeat(0, 4 - initValue.length);
      returnValue += initValue;

      return returnValue;
    } else if (initValue.length === 6) {
      let returnValue = moment().format("YYMM");
      returnValue += initValue;

      return returnValue;
    } else {
      return initValue;
    }
  }

  function fillMaNbSearchValue(initValue) {
    if (!initValue) return null;

    if (initValue.length <= 6) {
      let returnValue = moment().format("YYMM");
      returnValue += repeat(0, 6 - initValue.length);
      returnValue += initValue;

      return returnValue;
    } else {
      return initValue;
    }
  }

  const onChangeTongTien = (value) => {
    setQueryStringValues({
      thanhTien: value,
      page: 0,
    });
    value = value || ",";
    const [tuThanhTien, denThanhTien] = value.split(",");
    if (value == 0) {
      onChangeInputSearch({ tuThanhTien: 0, denThanhTien: 0 });
    } else {
      onChangeInputSearch({ tuThanhTien, denThanhTien });
    }
  };

  const widthColStt = () => {
    const curPage = Number(page || 0) + 1;
    const curSize = Number(size || 0);
    const length = String(curPage * curSize).length;
    return length * 10 + 20;
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: widthColStt(),
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: <HeaderSearch title={t("thuNgan.sttThuNgan")} />,
      width: 80,
      dataIndex: "stt",
      key: "stt",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maBenhAn")}
          sort_key="maBenhAn"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maBenhAn || 0}
          search={
            <InputTimeout
              value={state.maBenhAn}
              placeholder={t("common.nhapMaBenhAn")}
              onChange={onSearchInput("maBenhAn", true)}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHs")}
          sort_key="maHoSo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maHoSo || 0}
          search={
            <InputTimeout
              value={state.maHoSo}
              placeholder={t("common.timMaHoSo")}
              onChange={onSearchInput("maHoSo", true)}
            />
          }
        />
      ),
      width: 110,
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHs",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maNb")}
          sort_key="maNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maNb || 0}
          search={
            <InputTimeout
              value={state.maNb}
              placeholder={t("thuNgan.timMaNb")}
              onChange={onSearchInput("maNb", true)}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.hoTen")}
          sort_key="tenNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNb || 0}
          search={
            <InputTimeout
              value={state.tenNb}
              placeholder={t("thuNgan.timHoTen")}
              onChange={onSearchInput("tenNb")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "thuNgan.hoTen",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ngaySinh")}
          sort_key="ngaySinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ngaySinh || 0}
        />
      ),
      width: 100,
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "common.ngaySinh",
      show: true,
      align: "left",
      render: (field, item, index) => {
        return (
          <div>
            {item?.ngaySinh && moment(item?.ngaySinh).format(BIRTHDAY_FORMAT)}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.diaChi")}
          sort_key="diaChi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.diaChi || 0}
          search={
            <InputTimeout
              value={state.diaChi}
              placeholder={t("tiepDon.timDiaChi")}
              onChange={onSearchInput("diaChi")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "common.diaChi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.soBHYT")}
          sort_key="maTheBhyt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maTheBhyt || 0}
          search={
            <InputTimeout
              value={state.maTheBhyt}
              placeholder={t("tiepDon.soBHYT")}
              onChange={onSearchInput("maTheBhyt")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maTheBhyt",
      key: "maTheBhyt",
      i18Name: "tiepDon.soBHYT",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.giaHanThe.bhTuNgay")}
          sort_key="tuNgayTheBhyt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tuNgayTheBhyt || 0}
        />
      ),
      width: 130,
      dataIndex: "tuNgayTheBhyt",
      key: "tuNgayTheBhyt",
      i18Name: "quanLyNoiTru.giaHanThe.bhTuNgay",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.giaHanThe.bhDenNgay")}
          sort_key="denNgayTheBhyt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.denNgayTheBhyt || 0}
        />
      ),
      width: 130,
      dataIndex: "denNgayTheBhyt",
      key: "denNgayTheBhyt",
      i18Name: "quanLyNoiTru.giaHanThe.bhDenNgay",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soTien")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thanhTien || 0}
          searchSelect={
            <Select
              value={state.thanhTien}
              data={SO_TIEN}
              placeholder={t("thuNgan.chonSoTien")}
              onChange={onChangeTongTien}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      i18Name: "thuNgan.soTien",
      show: true,
      render: (item) => {
        return item?.formatPrice();
      },
    },
    {
      title: <HeaderSearch title={t("thuNgan.tienThuThem")} />,
      width: 100,
      dataIndex: "tienThuThem",
      key: "tienThuThem",
      align: "right",
      i18Name: "thuNgan.tienThuThem",
      show: true,
      render: (item, record) => {
        const tienThuThem = (record.tienTamUng || 0) - (record.thanhTien || 0);
        return tienThuThem <= 0 ? Math.abs(tienThuThem).formatPrice() : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiKham")}
          searchSelect={
            <Select
              value={state.dsTrangThaiKham}
              data={listTrangThaiDichVuMemo}
              placeholder={t("thuNgan.timTrangThaiKham")}
              onChange={onSearchInput("dsTrangThaiKham")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "dsTrangThaiKham",
      key: "dsTrangThaiKham",
      i18Name: isShowColumnTTKham ? "thuNgan.trangThaiKham" : "",
      show: isShowColumnTTKham,
      align: "center",
      render: (item) =>
        !!item?.length &&
        listTrangThaiDichVuMemo
          .filter((o) => item.includes(o.id))
          .map((x) => x.ten)
          .join(", "),
    },
    {
      title: <HeaderSearch title={t("thuNgan.tienTraLai")} />,
      width: 100,
      dataIndex: "tienTraLai",
      key: "tienTraLai",
      align: "right",
      i18Name: "thuNgan.tienTraLai",
      show: true,
      render: (item, record) => {
        const tienTraLai = (record.tienTamUng || 0) - (record.thanhTien || 0);
        return tienTraLai > 0 ? tienTraLai.formatPrice() : "";
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="thanhToan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thanhToan || 0}
          searchSelect={
            <Select
              value={state.thanhToan}
              data={LIST_TRANG_THAI_THANH_TOAN}
              placeholder={t("thuNgan.chonTTPhieuThu")}
              onChange={onSearchInput("thanhToan")}
              hasAllOption={true}
            />
          }
          title={t("thuNgan.trangThaiPt")}
        />
      ),
      width: 150,
      dataIndex: "thanhToan",
      key: "thanhToan",
      i18Name: "thuNgan.trangThaiPt",
      show: true,
      align: "center",
      render: (thanhToan) => {
        return listtrangThaiPhieuThu.find((x) => x.id == thanhToan)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="kyHieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.kyHieu || 0}
          title={t("thuNgan.quanLyTamUng.kyHieu")}
          search={
            <InputTimeout
              value={state.kyHieu}
              placeholder={t("thuNgan.quanLyTamUng.kyHieu")}
              onChange={onSearchInput("kyHieu")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "kyHieu",
      key: "kyHieu ",
      i18Name: "thuNgan.quanLyTamUng.kyHieu",
      show: true,
      align: "center",
      render: (item) => item,
    },
    {
      title: (
        <HeaderSearch
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soPhieu || 0}
          title={t("thuNgan.soPhieuThu")}
          search={
            <InputTimeout
              value={state.soPhieu}
              placeholder={t("thuNgan.soPhieuThu")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "soPhieu",
      key: "soPhieu ",
      i18Name: "thuNgan.soPhieuThu",
      show: true,
      align: "center",
      render: (item) => item,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.ngayThanhToan")}
          sort_key="thoiGianThanhToan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianThanhToan || 0}
        />
      ),
      width: 130,
      dataIndex: "thoiGianThanhToan",
      key: "thoiGianThanhToan",
      i18Name: "thuNgan.ngayThanhToan",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.ngayDangKy")}
          sort_key="thoiGianVaoVien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianVaoVien || 0}
        />
      ),
      width: 150,
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "thuNgan.ngayDangKy",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.noiDangKy")}
          sort_key="noiDangKyId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.noiDangKyId || 0}
          searchSelect={
            <Select
              onChange={onSearchInput("noiDangKyId")}
              value={state.noiDangKyId}
              placeholder={t("baoCao.chonNoiDangKyBD")}
              data={listAllBenhVien}
              getLabel={selectMaTen}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "noiDangKyId",
      key: "noiDangKyId",
      i18Name: "tiepDon.noiDangKy",
      show: true,
      render: (item) =>
        getBenhVienById(item) && selectMaTen(getBenhVienById(item)),
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.noiGioiThieu")}
          sort_key="noiGioiThieuId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.noiGioiThieuId || 0}
          searchSelect={
            <Select
              onChange={onSearchInput("noiGioiThieuId")}
              value={state.noiGioiThieuId}
              placeholder={t("baoCao.chonNoiDangKyBD")}
              data={listAllBenhVien}
              getLabel={selectMaTen}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "noiGioiThieuId",
      key: "noiGioiThieuId",
      i18Name: "tiepDon.noiGioiThieu",
      show: true,
      render: (item) =>
        getBenhVienById(item) && selectMaTen(getBenhVienById(item)),
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenThuNgan")}
          sort_key="tenThuNgan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenThuNgan || 0}
          search={
            <InputTimeout
              value={state.tenThuNgan}
              placeholder={t("thuNgan.timTenThuNgan")}
              onChange={onSearchInput("tenThuNgan")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "tenThuNgan",
      key: "tenThuNgan",
      i18Name: "thuNgan.tenThuNgan",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.daXuatHoaDon")}
          sort_key="trangThaiHoaDon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThaiHoaDon || 0}
          searchSelect={
            <Select
              value={state.trangThaiHoaDon}
              data={listTrangThaiPhatHanhHoaDon}
              placeholder={t("common.chonTrangThai")}
              onChange={onSearchInput("trangThaiHoaDon")}
              valueNumber={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "trangThaiHoaDon",
      key: "trangThaiHoaDon",
      i18Name: "thuNgan.daXuatHoaDon",
      show: true,
      render: (item) => {
        return listTrangThaiPhatHanhHoaDon.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.doiTuongNb")}
          sort_key="doiTuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.doiTuong || 0}
          searchSelect={
            <Select
              value={state.doiTuong}
              data={listDoiTuong}
              defaultValue=""
              valueNumber={true}
              placeholder={t("common.chonDoiTuong")}
              onChange={onSearchInput("doiTuong")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 130,
      dataIndex: "doiTuong",
      key: "doiTuong",
      i18Name: "thuNgan.doiTuongNb",
      show: true,
      render: (item) => {
        const index = listDoiTuong.findIndex((dt) => dt.id === item);
        if (index === -1) return;
        return listDoiTuong[index].ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.doiTuongKCB")}
          sort_key="doiTuongKcb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.doiTuongKcb || 0}
          searchSelect={
            <Select
              value={state.dsDoiTuongKcb}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              defaultValue=""
              placeholder={t("common.chonDoiTuongKCB")}
              onChange={onSearchInput("dsDoiTuongKcb")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 130,
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      i18Name: "thuNgan.doiTuongKCB",
      show: true,
      render: (item) => {
        const doiTuong = PHAN_LOAI_DOI_TUONG_KCB.find((value) =>
          value.referIds.includes(item)
        );
        return doiTuong ? t(doiTuong.i18n) : null;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu")}
          sort_key="phanLoaiNbCapCuu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.phanLoaiNbCapCuu || 0}
          searchSelect={
            <Select
              value={state.phanLoaiNbCapCuu}
              data={listPhanLoaiNBCapCuu}
              defaultValue=""
              valueNumber={true}
              placeholder={t("thuNgan.chonPhanLoaiNbCapCuu")}
              onChange={onSearchInput("phanLoaiNbCapCuu")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 130,
      dataIndex: "phanLoaiNbCapCuu",
      key: "phanLoaiNbCapCuu",
      i18Name: "khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu",
      show: true,
      className: "phanLoaiNbCapCuu",
      render: (item) => {
        const index = listPhanLoaiNBCapCuu.findIndex((dt) => dt.id === item);
        if (index === -1) return;
        return listPhanLoaiNBCapCuu[index].ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiNb")}
          sort_key="trangThaiNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThaiNb || 0}
          searchSelect={
            <Select
              value={state.dsTrangThaiNb}
              data={listTrangThaiNbCustom}
              mode="multiple"
              placeholder={t("thuNgan.chonTrangThaiNb")}
              onChange={onSearchInput("dsTrangThaiNb")}
              dropdownMatchSelectWidth={250}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "trangThaiNb",
      key: "trangThaiNb",
      i18Name: "thuNgan.trangThaiNb",
      show: true,
      render: (item) => {
        return getTrangThaiNb(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="khoaId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.khoaId || 0}
          searchSelect={
            <Select
              value={state.dsKhoaId}
              defaultValue=""
              valueNumber={true}
              data={listAllKhoa}
              placeholder={t("common.chonKhoa")}
              onChange={onSearchInput("dsKhoaId")}
              dropdownMatchSelectWidth={250}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "khoaId",
      key: "khoaId",
      i18Name: "common.khoa",
      show: true,
      render: (item) => {
        const index = listAllKhoa.findIndex((khoa) => khoa.id === item);
        if (index === -1) return;
        return listAllKhoa[index].ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thuNganHuyThanhToan")}
          sort_key="tenThuNganHuyThanhToan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenThuNganHuyThanhToan || 0}
          search={
            <InputTimeout
              value={state.tenThuNganHuyThanhToan}
              placeholder={t("thuNgan.timTenThuNgan")}
              onChange={onSearchInput("tenThuNganHuyThanhToan")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "tenThuNganHuyThanhToan",
      key: "tenThuNganHuyThanhToan",
      i18Name: "thuNgan.thuNganHuyThanhToan",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thoiGianHuyThanhToan")}
          sort_key="thoiGianHuyThanhToan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianHuyThanhToan || 0}
        />
      ),
      width: 180,
      dataIndex: "thoiGianHuyThanhToan",
      key: "thoiGianHuyThanhToan",
      i18Name: "thuNgan.thoiGianHuyThanhToan",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiQuyetToan")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.khoaId || 0}
          searchSelect={
            <Select
              value={state.trangThai}
              defaultValue=""
              valueNumber={true}
              data={listTrangQuyetToan}
              placeholder={t("thuNgan.chonTrangThaiQuyetToan")}
              onChange={onSearchInput("trangThai")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "thuNgan.trangThaiQuyetToan",
      show: true,
      render: (item) =>
        listTrangQuyetToan.find((khoa) => khoa.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phuongThucThanhToan")}
          searchSelect={
            <Select
              data={listAllPhuongThucThanhToan}
              placeholder={t("baoCao.chonPhuongThucThanhToan")}
              onChange={onSearchInput("dsPhuongThucTtId")}
              mode="multiple"
              value={state.dsPhuongThucTtId}
              defaultValue={[""]}
              maxTagCount="responsive"
            />
          }
        />
      ),
      width: 180,
      dataIndex: "dsPhuongThucTt",
      key: "phuongThucThanhToan",
      i18Name: "danhMuc.phuongThucThanhToan",
      show: true,
      render: (item) => item?.map((x) => x.tenPhuongThucTt).join(", "),
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiNganHang")}
          searchSelect={
            <Select
              data={listTrangThaiThanhToan}
              placeholder={t("thuNgan.chonTrangThaiNganHang")}
              onChange={onSearchInput("dsTrangThaiThanhToan")}
              mode="multiple"
              value={state.dsTrangThaiThanhToan}
              defaultValue={[""]}
              maxTagCount="responsive"
              hasAllOption={true}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "dsPhuongThucTt",
      key: "trangThaiNganHang",
      i18Name: "thuNgan.trangThaiNganHang",
      show: true,
      render: (item) =>
        listTrangThaiThanhToan.find(
          (x) =>
            x.id == item?.find((x) => x?.trangThaiThanhToan)?.trangThaiThanhToan
        )?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.trangThaiXacNhanBHYT")}
          searchSelect={
            <Select
              data={listTrangThaiXacNhanBaoHiem}
              placeholder={t("thuNgan.chonTrangThaiXacNhanBHYT")}
              onChange={onSearchInput("dsTrangThaiXacNhanBh")}
              mode="multiple"
              value={state.dsTrangThaiXacNhanBh}
              defaultValue={[""]}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 140,
      dataIndex: "trangThaiXacNhanBh",
      key: "trangThaiXacNhanBh",
      i18Name: "thuNgan.trangThaiXacNhanBHYT",
      show: false,
      render: (item) =>
        listTrangThaiXacNhanBaoHiem.find((x) => x.id === item)?.ten,
    },
    {
      title: <HeaderSearch title={t("danhMuc.loaiDichVu")} />,
      width: 180,
      dataIndex: "dsLoaiDichVu",
      key: "dsLoaiDichVu",
      i18Name: "danhMuc.loaiDichVu",
      show: dataPHIEU_THU_HIEN_THI_LOAI_DICH_VU?.eval(),
      render: (item) =>
        !!item?.length &&
        listLoaiDichVu
          .filter((o) => item.includes(o.id))
          .map((x) => x.ten)
          .join(", "),
    },

    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tongKetThanhToan")}
          searchSelect={
            <Select
              data={LIST_TRANG_THAI_TKTT}
              placeholder={t("thuNgan.tongKetThanhToan")}
              onChange={onSearchInput("dsTrangThaiTktt")}
              hasAllOption={true}
              value={state.dsTrangThaiTktt}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "trangThaiTktt",
      key: "trangThaiTktt",
      i18Name: "thuNgan.tongKetThanhToan",
      show: true,
      render: (item) =>
        listTrangThaiTongKetThanhToan.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.nguoiTongKetThanhToan")}
          searchSelect={
            <Select
              value={state.dsNguoiThucHienId}
              data={listAllNhanVien}
              placeholder={t("thuNgan.nguoiTongKetThanhToan")}
              onChange={onSearchInput("dsNguoiThucHienId")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "tenNguoiThucHien",
      key: "tenNguoiThucHien",
      i18Name: "thuNgan.nguoiTongKetThanhToan",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.nguoiXacNhanBhyt")}
          search={
            <InputTimeout
              value={state.tenNguoiXacNhan}
              placeholder={t("khamBenh.nguoiXacNhanBhyt")}
              onChange={onSearchInput("tenNguoiXacNhan")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "tenNguoiXacNhan",
      key: "tenNguoiXacNhan",
      i18Name: "khamBenh.nguoiXacNhanBhyt",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.thoiGianTongKetThanhToan")}
          sort_key="thoiGianThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianThucHien || 0}
          searchSelect={
            <RangePickerStyled
              placeholder={[t("common.tuNgay"), t("common.denNgay")]}
              format={"DD/MM/YYYY"}
              value={[state.tuThoiGianThucHien, state.denThoiGianThucHien]}
              onChange={onSearchDate("ThoiGianThucHien")}
            />
          }
        />
      ),
      width: 220,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "thuNgan.thoiGianTongKetThanhToan",
      show: true,
      render: (item) => {
        return item && <span>{moment(item).format(TIME_FORMAT)}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.soPhoi")}
          search={
            <InputTimeout
              value={state.soPhoi}
              placeholder={t("thuNgan.soPhoi")}
              onChange={onSearchInput("soPhoi")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "soPhoi",
      key: "soPhoi",
      i18Name: "thuNgan.soPhoi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.quayThu")}
          search={
            <InputTimeout
              value={state.tenQuay}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenQuay")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "tenQuay",
      key: "tenQuay",
      i18Name: "baoCao.quayThu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phanLoaiNb")}
          sort_key="dsPhanLoaiNbId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dsPhanLoaiNbId || 0}
          search={
            <InputTimeout
              value={state.tenPhanLoaiNb}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenPhanLoaiNb")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "dsPhanLoaiNbId",
      key: "dsPhanLoaiNbId",
      i18Name: "common.phanLoaiNb",
      show: true,
      align: "center",
      render: (item) => {
        return (
          <ListPhanLoaiNguoiBenh value={item} listAll={listAllPhanLoaiNB} />
        );
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.noiGioiThieu")} />,
      width: "150px",
      dataIndex: "tenNoiGioiThieu",
      key: "tenNoiGioiThieu",
      i18Name: "tiepDon.tenNoiGioiThieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.ngayChuyenTuyen")}
          sort_key="tuNgayGiayChuyen"
          dataSort={dataSortColumn.tuNgayGiayChuyen || 0}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tuNgayGiayChuyen",
      key: "tuNgayGiayChuyen",
      show: true,
      align: "center",
      i18Name: "tiepDon.ngayChuyenTuyen",
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.giayChuyenVienCoGiaTriTrong01Nam")}
          sort_key="giayChuyen1Nam"
          dataSort={dataSortColumn.giayChuyen1Nam || 0}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "giayChuyen1Nam",
      key: "giayChuyen1Nam",
      align: "center",
      show: true,
      i18Name: "tiepDon.giayChuyenVienCoGiaTriTrong01Nam",
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 100,
      align: "center",
      fixed: "right",
      ignore: true,
      render: (item) => {
        return (
          <>
            <SVG.IcEye className="ic-action" onClick={onOpenDetail(item)} />
            {dataHIEN_THI_KET_NOI_MEMO?.eval() && (
              <Tooltip title="Tool Memo" placement="bottomLeft">
                <SVG.IcMemo
                  onClick={openMemoPopup(item?.maNb)}
                  className="ic-action"
                />
              </Tooltip>
            )}
          </>
        );
      },
    },
    // {
    //   title: (
    //     <HeaderSearch
    //       title={t("thuNgan.soPhoi")}
    //       search={
    //         <InputTimeout
    //           value={state.soPhoi}
    //           placeholder={t("thuNgan.soPhoi")}
    //           onChange={onSearchInput("soPhoi")}
    //         />
    //       }
    //     />
    //   ),
    //   width: 160,
    //   dataIndex: "soPhoi",
    //   key: "soPhoi",
    //   i18Name: "thuNgan.soPhoi",
    //   show: true,
    // },
  ];

  const handleChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1, getThongKe: true });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size, page: 0 });
    onSizeChange({ size, dataSortColumn, dataSearch, getThongKe: true });
  };

  const onOpenDetail = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["THU_NGAN"].CHI_TIET_PHIEU_THU])) return;
    const { maHoSo, id, nbDotDieuTriId } = record;
    history.push({
      pathname: `/thu-ngan/chi-tiet-phieu-thu/${maHoSo}/${id}/${nbDotDieuTriId}`,
      state: getAllQueryString(undefined, {
        filterEmpty: false,
      }),
    });
  };

  const openMemoPopup = (pid) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    const url = `https://note.hpimc.info/Memo?pid=${pid}&username=${userName}`;
    window.open(
      url,
      "_blank",
      "width=800,height=600,resizable=yes,scrollbars=yes"
    );
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onOpenDetail(record)();
        }
      },
    };
  };

  const onChangeHienThiPhieuThu = (isTatCaToaNha) => {
    onSizeChange({
      size: parseInt(size || 50),
      page: parseInt(page || 0),
      dataSearch: { isTatCaToaNha },
    });
  };

  const CustomHeaderThuNgan = useCallback(
    (props) => {
      if (loadFinishThietLap) {
        return (
          <TimKiemBenhNhan
            titleBack={`${t("common.quayLai")} [ESC]`}
            backLink="/thu-ngan"
            layerId={layerId}
            isChiTietPhieuThu={false}
            onChangeSelect={refOnChangeSelect.current}
            onChangeHienThiPhieuThu={onChangeHienThiPhieuThu}
            dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA={
              dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA
            }
            {...props}
          />
        );
      }

      return null;
    },
    [layerId, loadFinishThietLap]
  );

  const onChangeSelect = useRefFunc((key) => (e, data) => {
    if (
      key === "nhaTamUng" &&
      loadFinishThietLap &&
      loadFinish &&
      quayTiepDon?.id !== data?.id
    ) {
      let searchParams = {};

      if (quayTiepDon?.khuVucId != data?.khuVucId) {
        searchParams = {
          ...searchParams,
          khuVucId: data?.khuVucId,
        };
      }
      if (
        quayTiepDon?.toaNhaId != data?.toaNhaId &&
        dataTACH_HIEN_THI_PHIEU_THU_THEO_TOA_NHA?.eval()
      ) {
        searchParams = {
          ...searchParams,
          nhaThuNganId: data?.toaNhaId,
        };
      }
      if (isArray(data?.dsKhoId, true)) {
        searchParams = {
          ...searchParams,
          khoId: data.dsKhoId[0],
        };
      } else if (
        isArray(quayTiepDon?.dsKhoId, true) &&
        !isArray(data.dsKhoId, true)
      ) {
        searchParams = {
          ...searchParams,
          khoId: undefined,
        };
      } else if (!isEqual(quayTiepDon?.dsKhoId, data.dsKhoId)) {
        searchParams = {
          ...searchParams,
          khoId: data.dsKhoId?.[0],
        };
      }

      if (isObject(searchParams, true)) {
        onSizeChange({
          size: parseInt(size || 50),
          page: parseInt(page || 0),
          dataSearch: searchParams,
        });
        setQuayTiepDon(data);
      }
    }
  });
  refOnChangeSelect.current = onChangeSelect;

  return (
    <MainPage showBreadcrumb={false}>
      <PhieuThuHeader
        HeaderThuNgan={CustomHeaderThuNgan}
        layerId={layerId}
        title={t("thuNgan.danhSachPhieuThu")}
        setParentState={setState}
        parentState={state?.orgSearch}
        checkIsOnly={checkIsOnly}
      />
      <Card noPadding={true}>
        <TableWrapper
          scroll={{ y: 500, x: 1500 }}
          columns={columns}
          dataSource={listData}
          onRow={onRow}
          tableName="table_ThuNgan_DanhSachPhieuThu"
          ref={refSettings}
          columnResizable={true}
          rowClassName={(record, index) => {
            return classNames("", {
              "phan-loai-khan-cap": record.phanLoaiNbCapCuu == 10, //Ưu tiên 1 (Khẩn cấp)
              "phan-loai-cap-cuu": record.phanLoaiNbCapCuu == 20, //Ưu tiên 2 (Cấp cứu)
              "phan-loai-cap-cuu-tri-hoan": record.phanLoaiNbCapCuu == 30, //Ưu tiên 3 (Cấp cứu trì hoãn)
              "phan-loai-khong-cap-cuu-45": record.phanLoaiNbCapCuu == 40, //Ưu tiên 4 (Không cấp cứu < 45')
              "phan-loai-khong-cap-cuu-1h": record.phanLoaiNbCapCuu == 60, //Ưu tiên 5 (Không cấp cứu < 1 giờ)
            });
          }}
        />
        <Pagination
          onChange={handleChangePage}
          current={page + 1}
          pageSize={size}
          listData={listData}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          first={first}
          last={last}
          isLoading={isLoading}
          useSearchParamsPageSize={false}
        />
      </Card>
    </MainPage>
  );
};

export default DanhSachPhieuThu;
