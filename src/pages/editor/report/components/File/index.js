import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
  memo,
  useContext,
} from "react";
import { convert, stripHtml } from "utils/editor-utils";
import { useDispatch } from "react-redux";
import File2 from "../File2";
import { message } from "antd";
import { MODE } from "utils/editor-utils";
import { useTranslation } from "react-i18next";
import Prompt from "components/Prompt";
import { cloneDeep, debounce, get, isEqual } from "lodash";
import { useParams } from "react-router-dom";
import { EMR2Context, EMRContext, useEditor } from "components/editor/config";
import moment from "moment";
import nbCapPhatThuocProvider from "data-access/kho/nb-cap-phat-thuoc-provider";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import { checkDataBeforeSave } from "components/editor/cores/TheoDoiVaChamSocNheCap3/contanst";
import { LazyLoad } from "components";
import { useThietLap } from "hooks";
import { validateChiSoSong } from "./utils";

export const refValues = React.createRef({});

const File = ({ mode, isPreview, config, isTemplate, ...props }, ref) => {
  const { tuThoiGian, denThoiGian } = getAllQueryString();
  const { headless } = useContext(EMRContext);
  const { editorId } = useContext(EMR2Context);
  const { maBaoCao, id } = useParams();
  const { chiDinhTuLoaiDichVu } = getAllQueryString();
  const { t } = useTranslation();
  const {
    files: {
      onSaveForm,
      updateEditor,
      createTemplateBieuMau,
      updateTemplateBieuMau,
      onSaveFormPhieuTruyenMau,
    },
    toDieuTri: {
      patchUpdate,
      updateSttHienThiThuoc,
      updateSttHienThiThuocChiDinhNgoai,
    },
    vitalSigns: { onCreateOrEditCategory },
    khamBenh: { themThongTinDV },
  } = useDispatch();
  const [dataMA_DUONG_DUNG_DICH_TRUYEN] = useThietLap(
    THIET_LAP_CHUNG.MA_DUONG_DUNG_DICH_TRUYEN
  );
  const [dataBAT_TRUNG_SINH_HIEU] = useThietLap(
    THIET_LAP_CHUNG.BAT_TRUNG_SINH_HIEU
  );
  if (!refValues.current) refValues.current = {};
  const refSaveFile = useRef(true);
  const refModalName = useRef(null);
  const refPrompt = useRef();
  const apiFields = useEditor(editorId, "apiFields", []);
  const fileTemplate = useEditor(editorId, "fileTemplate", {});
  const fileData = useEditor(editorId, "fileData", {});
  const fileDataHIS = useEditor(editorId, "fileDataHIS", {});
  const file = useEditor(editorId, "file", {});
  const fileDataTemplate = useEditor(editorId, "fileDataTemplate", {});
  const refDefaultData = useRef({});
  const refBaoCaoId = useRef();
  const [state, _setState] = useState({
    formChange: {},
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const handleSubmit = ({ file, id, queries = {}, isSaveTemplate }) => {
    refSaveFile.current = false;
    return new Promise(async (resolve, reject) => {
      if (!Object.keys(refValues.current).length) {
        message.error(
          t(
            isSaveTemplate
              ? "editor.banChuaNhapDuLieu"
              : "editor.formChuaDuocLoadVuiLongThuLaiSau"
          )
        );
        reject();
        return;
      }
      refValues.current.baoCaoId = refBaoCaoId.current;
      const checkDataEmptyBeforSave = () => {
        let errors = [];
        const listKeyRequireData = file?.cauHinh?.listKeyRequireData || [];
        listKeyRequireData.forEach((item) => {
          const value = get(refValues.current, item.key);

          if (!value || !value?.length) {
            errors.push(item.ten || item.key);
          }
        });
        if (errors.length) {
          message.error(
            `Vui lòng điền đủ các thông tin sau: ${errors.join(", ")}`
          );
        }
        return errors;
      };
      if (checkDataEmptyBeforSave().length) {
        resolve();
        return;
      }

      if (maBaoCao === "EMR_BA263") {
        Object.keys(refValues.current).forEach((key) => {
          if (
            ([
              "dienBienBenh",
              "tinhTrangNb",
              "luuYChanDoan",
              "luuYDieuTri",
              "luuYChamSoc",
              "luuYVanChuyen",
            ].includes(key) &&
              refValues.current[key] &&
              typeof refValues.current[key] === "string") ||
            refValues.current[key] instanceof String
          ) {
            let regex = /&nbsp;/gi;
            refValues.current[key] = refValues.current[key].replace(regex, " ");
          }
        });
      }
      if (maBaoCao === "EMR_BA098" || maBaoCao === "EMR_BA256") {
        Object.keys(refValues.current).forEach((key) => {
          if (
            (refValues.current[key] &&
              typeof refValues.current[key] === "string") ||
            refValues.current[key] instanceof String
          ) {
            refValues.current[key] = stripHtml(refValues.current[key]);
          }
        });
      }
      if (maBaoCao === "EMR_BA275") {
        console.log("refValues.current", refValues.current);
        const { mucDo, ketQua, dsThuocGayAdr } = refValues.current || {};
        if (!mucDo) {
          message.error("Vui lòng chọn Mức độ nghiêm trọng của phản ứng!");
          reject();
          return;
        }
        if (!ketQua) {
          message.error("Vui lòng chọn Kết quả sau khi xử trí phản ứng!");
          reject();
          return;
        }

        if (Array.isArray(dsThuocGayAdr)) {
          const invalidRows = dsThuocGayAdr
            .map((row, index) => {
              const hasThuoc = !!row?.ten || !!row?.hamLuong; // cột 13
              const hasKetQua =
                row?.caiThienSauKhiNgung && row.caiThienSauKhiNgung.length > 0; // cột 14
              const hasTaiSuDung =
                row?.tinhTrangSauKhiTaiSuDung &&
                row.tinhTrangSauKhiTaiSuDung.length > 0; // cột 15

              if (hasThuoc && (!hasKetQua || !hasTaiSuDung)) {
                return index + 1; // STT
              }
              return null;
            })
            .filter((x) => x !== null);

          if (invalidRows.length > 0) {
            message.error(
              `Vui lòng nhập mục 14 và 15 khi đã khai báo thuốc ở mục 13 (dòng ${invalidRows.join(
                ", "
              )})`
            );
            reject();
            return;
          }
        }
      }
      if (maBaoCao == "EMR_HSDD015.3") {
        //Tìm component phieu-truyen-dich để check config form
        let phieuTruyenDichComp = (file.components || []).find(
          (x) => x.type == "table" && x.props.type == "phieu-truyen-dich"
        );
        if (phieuTruyenDichComp) {
          if (phieuTruyenDichComp.props?.hienThiThuocKeNgoai) {
            refValues.current.loaiThuoc = 60;
          }
        }
      }
      if (maBaoCao === "EMR_BA111") {
        try {
          const dsPhieusChanged = refValues.current.dsPhieu.filter(
            (item, index) => {
              item.api = file.api;
              item.baoCaoId = file?.id;

              if (item.id) {
                const originalPhieu = refDefaultData.current.dsPhieu.find(
                  (item2) => item2.id == item.id
                );
                if (!isEqual(item, originalPhieu)) {
                  return true;
                }
              }
            }
          );
          if (dsPhieusChanged.length) {
            await onSaveFormPhieuTruyenMau(editorId, dsPhieusChanged);
            resolve();
          } else {
            message.warning("Dữ liệu chưa được chỉnh sửa!");
            reject();
          }
        } catch (error) {
          console.log("error", error);
          reject();
        }
        resolve();
        return;
      }
      if (
        [
          "EMR_BA077",
          "EMR_BA077.1",
          "EMR_BA077.2",
          "EMR_BA503",
          "EMR_BA503.1",
        ].includes(maBaoCao)
      ) {
        if (refValues.current.dsToDieuTri.length > 1) {
          const dsPhieusChanged = refValues.current.dsToDieuTri.filter(
            (item, index) => {
              item.api = file.api;
              if (item.idGuid) {
                const originalPhieu = refDefaultData.current.dsToDieuTri.find(
                  (item2) => item2.idGuid == item.idGuid
                );
                if (!isEqual(item?.sttHienThi, originalPhieu?.sttHienThi)) {
                  return true;
                }
              }
            }
          );
          let dsSttThuoc = [];
          let dsSttThuocChiDinhNgoai = [];
          dsPhieusChanged.forEach((item) => {
            item.sttHienThi.forEach((thuoc) => {
              if (thuoc.thuoChiDinhNgoai) {
                dsSttThuocChiDinhNgoai.push(thuoc);
              } else {
                dsSttThuoc.push(thuoc);
              }
            });
          });
          try {
            if (dsSttThuocChiDinhNgoai.length) {
              await updateSttHienThiThuocChiDinhNgoai(dsSttThuocChiDinhNgoai);
            }
            if (dsSttThuoc.length) {
              await updateSttHienThiThuoc(dsSttThuoc);
            }
            message.success("Cập nhật dữ liệu thành công !");
          } catch (error) {
            message.success(error);
          }
          let listData = [];
          let dataSubmit = cloneDeep(refValues.current.dsToDieuTri) || [];
          await Promise.all([
            ...dataSubmit
              .filter((_toDieuTri) => _toDieuTri.chiSoSong != null)
              .map((_toDieuTri) =>
                onCreateOrEditCategory({
                  ..._toDieuTri.chiSoSong,
                  isSaveToDieuTri: true,
                  ...(maBaoCao === "EMR_BA077.1"
                    ? {
                        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                        chiDinhTuDichVuId: _toDieuTri.id,
                      }
                    : {}),
                })
              ),
            ...dataSubmit.map((_toDieuTri) => {
              const data = {
                ..._toDieuTri,
                thietLap: {
                  thongSo:
                    _toDieuTri.showBangThongSo !== undefined
                      ? _toDieuTri.showBangThongSo
                      : _toDieuTri?.thietLap?.thongSo,
                  chiSoSong:
                    _toDieuTri.showChiSoChiTiet !== undefined
                      ? _toDieuTri.showChiSoChiTiet
                      : _toDieuTri?.thietLap?.chiSoSong,
                  ...(maBaoCao === "EMR_BA077.1"
                    ? {
                        boSung:
                          _toDieuTri.showBoSung !== undefined
                            ? _toDieuTri.showBoSung
                            : _toDieuTri?.thietLap?.boSung,
                      }
                    : {}),
                },
              };
              if (["EMR_BA077", "EMR_BA503.1"].includes(maBaoCao)) {
                listData.push(data);
              } else if (chiDinhTuLoaiDichVu == LOAI_DICH_VU.KHAM) {
                themThongTinDV(data);
              } else if (maBaoCao === "EMR_BA077.2") {
                onSaveForm(editorId, {
                  file,
                  data: { id, ...data },
                  noUpdateRedux: true,
                });
              }
            }),
          ]);
          if (["EMR_BA077", "EMR_BA503.1"].includes(maBaoCao)) {
            await patchUpdate(listData);
          }
          updateEditor(editorId, {
            fileData: cloneDeep(refValues.current),
          });
        } else {
          try {
            let dsSttThuoc = [];
            let dsSttThuocChiDinhNgoai = [];
            const toDieuTri = refValues.current.dsToDieuTri[0];
            (refValues.current.dsToDieuTri[0].sttHienThi || []).forEach(
              (thuoc) => {
                if (thuoc.thuoChiDinhNgoai) {
                  dsSttThuocChiDinhNgoai.push(thuoc);
                } else {
                  dsSttThuoc.push(thuoc);
                }
              }
            );
            if (dsSttThuocChiDinhNgoai.length) {
              updateSttHienThiThuocChiDinhNgoai(dsSttThuocChiDinhNgoai);
            }
            if (dsSttThuoc.length) {
              updateSttHienThiThuoc(dsSttThuoc);
            }
            const payload = {
              id: id,
              ...toDieuTri,
              dsDienBienBenh: refValues.current?.dsToDieuTri[0]?.dsDienBienBenh,
              thietLap: {
                thongSo:
                  toDieuTri.showBangThongSo !== undefined
                    ? toDieuTri.showBangThongSo
                    : toDieuTri?.thietLap?.thongSo,
                chiSoSong:
                  toDieuTri.showChiSoChiTiet !== undefined
                    ? toDieuTri.showChiSoChiTiet
                    : toDieuTri?.thietLap?.chiSoSong,
                ...(maBaoCao === "EMR_BA077.1"
                  ? {
                      boSung:
                        toDieuTri.showBoSung !== undefined
                          ? toDieuTri.showBoSung
                          : toDieuTri?.thietLap?.boSung,
                    }
                  : {}),
              },
            };
            if (chiDinhTuLoaiDichVu == LOAI_DICH_VU.KHAM) {
              await themThongTinDV(payload);
            } else {
              if (maBaoCao === "EMR_BA077.2") {
                onSaveForm(editorId, {
                  file,
                  data: {
                    id,
                    ...payload,
                  },
                  noUpdateRedux: true,
                });
              } else {
                patchUpdate([payload]);
              }
            }
            if (refValues.current.dsToDieuTri[0].chiSoSong) {
              onCreateOrEditCategory({
                ...refValues.current.dsToDieuTri[0].chiSoSong,
                isSaveToDieuTri: true,
                ...(maBaoCao === "EMR_BA077.1"
                  ? {
                      chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
                      chiDinhTuDichVuId: toDieuTri.id,
                    }
                  : {}),
              });
            }
            const indexToDieuTri = fileData.dsToDieuTri.findIndex(
              (item) => item.id === payload.id
            );

            if (indexToDieuTri >= 0) {
              fileData.dsToDieuTri[indexToDieuTri] = payload;
              if (maBaoCao === "EMR_BA077.2") {
                fileData.dsDienBienBenh = refValues.current?.dsDienBienBenh;
              }
              updateEditor(editorId, {
                fileData: cloneDeep(fileData),
              });
            }

            resolve();
          } catch (error) {
            reject();
          }
        }
        resolve();
        return true;
      }

      if (
        maBaoCao === "EMR_HSDD015.5" ||
        maBaoCao === "EMR_HSDD015.4" ||
        maBaoCao === "EMR_HSDD098.1" ||
        maBaoCao === "EMR_HSDD098.2" ||
        maBaoCao === "EMR_HSDD098.3" ||
        maBaoCao === "EMR_HSDD098.4" ||
        maBaoCao === "EMR_HSDD098.5" ||
        maBaoCao === "EMR_HSDD098.6"
      ) {
        const listdsThoiGianSuDung = [];
        const maDuongDung = (dataMA_DUONG_DUNG_DICH_TRUYEN || "").split(",");
        let dsThuoc = (refValues.current.dsThuoc || []).map((thuoc) => {
          const dsThoiGianSuDung = (thuoc.dsThoiGianSuDung || [])
            .filter((item) => item.tuThoiGian)
            .map((item) => {
              if (maDuongDung.includes(thuoc.maDuongDung)) {
                listdsThoiGianSuDung.push(item);
              }
              return {
                ...item,
                tuThoiGian: moment(item.tuThoiGian).format(
                  "YYYY-MM-DD HH:mm:ss"
                ),
              };
            });
          return {
            ...thuoc,
            id: thuoc.id,
            loai: thuoc.loai,
            dsThoiGianSuDung: dsThoiGianSuDung,
          };
        });
        if (
          maBaoCao === "EMR_HSDD015.5" &&
          listdsThoiGianSuDung?.find(
            (x) => ["1", "2", "3"]?.includes(x.thucHienThuoc) && !x.soLuong
          )
        ) {
          message.error(
            "Bắt buộc nhập thông tin Tổng số ml với thuốc đang truyền"
          );
          return;
        }
        if (maBaoCao !== "EMR_HSDD015.5") {
          dsThuoc = dsThuoc.filter((x) => {
            const thoiGianThucHien = new Date(x.thoiGianThucHien).getTime();
            const tuNgay = new Date(tuThoiGian).getTime();
            const denNgay = new Date(denThoiGian).getTime();
            return thoiGianThucHien >= tuNgay && thoiGianThucHien <= denNgay;
          });
        }

        try {
          await nbCapPhatThuocProvider.putThoiGianSuDungThuoc(dsThuoc);
        } catch (e) {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          return;
        }
        refValues.current.dsThuoc = dsThuoc;
      }
      if (maBaoCao === "EMR_BA224" || maBaoCao === "EMR_BA224.1") {
        if (!refValues.current.id) {
          //dùng nbDotDieuTriId làm id phiếu
          refValues.current.id = refValues.current.nbDotDieuTriId;
        }
      }
      /*
      [SAKURA-56087] FE (NDTP.0355) [Form phiếu] Truyền thêm param khi in phiếu hẹn khám điều trị ngoại trú
        nam.mn 15072025 - Xử lý trường hợp trên url không truyền query thoiGianThucHien thì cho người dùng nhập trên form
      */

      if (maBaoCao === "EMR_HSDD015.3") {
        //Bắt điều kiện không để người dùng nhập số lượng= 0 hoặc để trống khi lưu phiếu
        if (
          (refValues.current?.dsThuoc || [])
            .filter((item) => item.hieuLuc)
            .some((item) => !item.soLuongSuDung)
        ) {
          message.error(t("editor.vuiLongDienSoLuongThuoc"));
          reject();
          return;
        }
      }

      if (
        dataBAT_TRUNG_SINH_HIEU &&
        dataBAT_TRUNG_SINH_HIEU.toLowerCase() == "true" &&
        !validateChiSoSong(maBaoCao, refValues.current)
      ) {
        reject();
        return;
      }

      const data = convert(refValues.current);
      if (maBaoCao === "EMR_BA318") {
        if (checkDataBeforeSave(data)) {
          return;
        }
      }
      if (maBaoCao === "EMR_BA440") {
        if (data.dsThongTinCon) {
          delete data.dsThongTinCon;
        }
      }
      if (isSaveTemplate) {
        if (data.id) {
          updateTemplateBieuMau(editorId, {
            ...data,
            api: file?.api,
            baoCaoId: file?.id,
          }).then((s) => {
            refPrompt?.current && refPrompt.current.unCheckChangeForm();
          });
        } else {
          if (refModalName.current.show) {
            refModalName.current.show({}, (text) => {
              createTemplateBieuMau(editorId, {
                ...data,
                api: file?.api,
                baoCaoId: file?.id,
                ten: text,
              }).then((s) => {
                refPrompt?.current && refPrompt.current.unCheckChangeForm();
              });
            });
          }
        }
      } else {
        if (maBaoCao === "EMR_BA250") {
          delete queries.dsPhieuLinhId;
        }
        if (
          [
            "EMR_HSDD015.3",
            "EMR_HSDD092",
            "EMR_HSDD015.4",
            "EMR_BA083",
            "EMR_BA224",
            "EMR_BA284",
            "EMR_BA224.1",
            "EMR_BA250",
            "EMR_BA307",
            "EMR_HSDD098.1",
            "EMR_HSDD098.2",
            "EMR_HSDD098.3",
            "EMR_HSDD098.4",
            "EMR_HSDD098.5",
            "EMR_HSDD098.6",
            "EMR_BA319",
            "EMR_BA346",
            "EMR_BA353",
            "EMR_BA362",
            "EMR_BA376",
            "EMR_BA377",
            "EMR_BA363",
          ].includes(maBaoCao) &&
          queries.tuThoiGian &&
          queries.denThoiGian
        ) {
          queries.tuThoiGian = decodeURIComponent(queries.tuThoiGian);
          queries.denThoiGian = decodeURIComponent(queries.denThoiGian);
        }

        if (["EMR_BA343", "EMR_BA346"].includes(maBaoCao)) {
          queries.thoiGianBanGiao =
            data.thoiGianBanGiao ||
            (queries.thoiGianBanGiao
              ? decodeURIComponent(queries.thoiGianBanGiao)
              : null);
        }

        if (
          [
            "EMR_BA248",
            "EMR_BA283",
            "EMR_BA287",
            "EMR_BA292",
            "EMR_BA300",
            "EMR_BA301",
            "EMR_HSDD067",
            "EMR_HSDD091",
            "EMR_HSDD089",
            "EMR_BA318",
            "EMR_BA317",
            "EMR_HSDD099",
            "EMR_BA348",
            "EMR_BA349",
            "EMR_BA294",
            "EMR_BA360",
            "EMR_BA354",
            "EMR_BA329",
            "EMR_BA393",
            "EMR_BA332.1",
            "EMR_BA434",
            "EMR_BA422",
            "EMR_BA437",
            "EMR_BA459",
            "EMR_HSDD015.1",
            "EMR_HSDD015.2",
            "EMR_BA481",
            "EMR_BA389.1",
            "EMR_BA234.2",
            "EMR_BA098",
            "EMR_BA417",
            "EMR_HSDD094",
            "EMR_BA412",
            "EMR_BA416",
            "EMR_HSDD023.1",
            "EMR_BA231",
            "EMR_BA234.1",
            "EMR_BA492",
            "EMR_HSDD094.1",
            "EMR_HSDD093",
            "EMR_BA562",
            "EMR_BA560",
            "EMR_BA557",
            "EMR_BA558",
            "EMR_BA559",
            "PC_CAMKET_PTTT_TYCNT",
            "EMR_BA410",
            "EMR_BA471",
            "EMR_BA411",
            "EMR_BA588",
            "EMR_BA487",
            "EMR_BA494",
            "EMR_BA606",
            "EMR_BA603",
            "EMR_BA605",
            "EMR_BA604",
          ].includes(maBaoCao) &&
          queries.thoiGianThucHien
        ) {
          queries.thoiGianThucHien = decodeURIComponent(
            queries.thoiGianThucHien
          );
        }

        if (maBaoCao == "EMR_HSDD015.4" && queries.dsLoaiChiDinh) {
          let dsLoaiChiDinh = decodeURIComponent(queries.dsLoaiChiDinh);

          queries.dsLoaiChiDinh = dsLoaiChiDinh
            .split(",")
            .map((x) => Number(x));
        }

        if (maBaoCao == "EMR_BA083" && queries.dsLoaiDichVu) {
          let dsLoaiDichVu = decodeURIComponent(queries.dsLoaiDichVu);

          queries.dsLoaiDichVu = dsLoaiDichVu.split(",").map((x) => Number(x));
        }

        if (maBaoCao === "EMR_BA357") {
          // const error = {};
          // (refValues.current?.dsTheoDoi || []).forEach((el, index) => {
          //   if (!el.thoiGian) {
          //     Object.keys(el).forEach((key) => {
          //       if (
          //         !["nbChiSoSongId", "dsTheoDoiKhac", "nbChiSoSong"].includes(
          //           key
          //         ) &&
          //         el[key]
          //       ) {
          //         error[index + 1] = true;
          //       }
          //       if (["nbChiSoSong"].includes(key)) {
          //         Object.keys(el[key]).forEach((keyCSS) => {
          //           if (
          //             el[key][keyCSS] &&
          //             ![
          //               "active",
          //               "chiDinhTuDichVuId",
          //               "chiDinhTuLoaiDichVu",
          //               "createdAt",
          //               "createdBy",
          //               "id",
          //               "nbDotDieuTriId",
          //               "nguoiThucHienId",
          //               "updatedAt",
          //               "updatedBy",
          //               "phanLoai",
          //               "thoiGianThucHien",
          //             ].includes(keyCSS)
          //           ) {
          //             error[index + 1] = true;
          //           }
          //         });
          //       }
          //     });
          //   }
          // });
          // if (Object.keys(error).length) {
          //   message.error(
          //     t("editor.vuiLongNhapThoiGianCot") +
          //       `${Object.keys(error)
          //         .map((el) => el)
          //         .join(", ")}`
          //   );
          //   return;
          // }
        }

        //xóa biến áp dụng biểu mẫu
        delete data.luotBieuMauApDung;

        onSaveForm(editorId, { file, data: { id, ...data, ...queries } })
          .then((s) => {
            refPrompt?.current && refPrompt.current.unCheckChangeForm();
            if (!(id || data.id) && s?.soPhieu) {
              let _pathname = window.location.pathname;
              if (_pathname.at(-1) != "/") {
                _pathname += "/";
              }
              window.history.pushState(
                {},
                null,
                `${window.location.origin}${_pathname}${s.soPhieu}${window.location.search}`
              );
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
          });
      }
    });
  };
  useImperativeHandle(ref, () => ({
    values: refValues.current,
    handleSubmit,
    setBaoCaoId,
  }));
  useEffect(() => {
    refDefaultData.current = cloneDeep(fileData);
  }, [JSON.stringify(fileData)]);
  const postMessage = debounce(({ key, value }) => {
    window.postMessage(
      {
        TYPE: `CHANGE_VALUE_EDITOR`,
        value: { key, value },
      },
      window.location.origin
    );
  }, 300);
  const setFormKey = (key) => (value) => {
    refPrompt?.current && refPrompt.current.setCheckChangeForm();
    refValues.current = { ...refValues.current, [key]: value };

    //GIẤY CHỨNG NHẬN NGHỈ VIỆC HƯỞNG BẢO HIỂM XÃ HỘI
    if (maBaoCao === "EMR_BA098") {
      let _tuNgay = null;
      let _soNgay = null;

      if (key == "soNgay") {
        _tuNgay = refValues.current?.tuNgay;
        _soNgay = value;
      } else if (key == "tuNgay") {
        _soNgay = refValues.current?.soNgay;
        _tuNgay = value;
      }

      if (_tuNgay && _soNgay) {
        const _denNgay = moment(_tuNgay)
          .add(_soNgay - 1, "days")
          .format("YYYY-MM-DD");

        refValues.current = { ...refValues.current, ["denNgay"]: _denNgay };

        setTimeout(() => {
          postMessage({ key: "denNgayUpdate", value: _denNgay });
        }, 500);
      }
    }
    postMessage({ key, value });
  };
  useEffect(() => {
    if (apiFields) {
      let obj = apiFields.reduce(
        (res, key) => ({
          ...res,
          [key]: setFormKey(key),
        }),
        {}
      );
      obj.setMultiData = (data) => {
        refValues.current = { ...refValues.current, ...data };
      };
      //sử dụng proxy để cho phép trả về giá trị mặc định khi object target không có field prop.
      obj = new Proxy(obj, {
        get(target, prop, receiver) {
          // Nếu field đã tồn tại (dù là undefined) => trả lại giá trị gốc
          if (prop in target) return Reflect.get(target, prop, receiver);

          // Nếu không tồn tại => trả về function mặc định
          return setFormKey(prop);
        },
      });
      setState({ formChange: obj });
    }
  }, [apiFields]);
  refValues.current = useMemo(() => {
    return isTemplate ? fileDataTemplate : fileData;
  }, [fileData, fileDataTemplate]);

  useEffect(() => {
    if (refValues.current) {
      updateEditor(editorId, {
        refValues: refValues.current,
      });
    }
  }, [refValues.current]);
  state.formChange.getAllData = () => {
    return refValues.current;
  };

  const setBaoCaoId = (id) => {
    refBaoCaoId.current = id;
  };

  if (!Object.keys(refValues?.current).length && !isPreview && !isTemplate) {
    return null;
  }

  return (
    <>
      {!isPreview && !headless && <Prompt ref={refPrompt}></Prompt>}
      <File2
        fileTemplate={fileTemplate}
        apiFields={apiFields}
        file={file}
        fileData={isTemplate ? fileDataTemplate : fileData}
        fileDataHIS={fileDataHIS}
        formChange={state.formChange}
        mode={mode || MODE.editing}
        config={config}
      ></File2>
      {!headless && (
        <LazyLoad
          component={() => import("../ModalAddNameTemplate")}
          ref={refModalName}
        />
      )}
    </>
  );
};

export default memo(forwardRef(File));
