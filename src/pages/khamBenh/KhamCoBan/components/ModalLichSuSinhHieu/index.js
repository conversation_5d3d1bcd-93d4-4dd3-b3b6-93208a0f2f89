import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { Spin } from "antd";

import { useEnum, useStore, useThietLap } from "hooks";

import { ModalTemplate } from "components";
import VitalSigns from "components/VitalSigns/components/VitalSigns/index";
import { Main } from "./styled";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";

const ModalLichSuSinhHieu = ({ draw, ...props }, ref) => {
  const [state, _setState] = useState({
    show: false,
  });
  const [loading, setLoading] = useState(true);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", []);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [dataMA_CSS_VONG_CANH_TAY, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_CSS_VONG_CANH_TAY
  );
  const [dataHIEN_THI_TRUONG_SINH_HIEU, loadFinishHienThiTruongSinhHieu] =
    useThietLap(THIET_LAP_CHUNG.HIEN_THI_TRUONG_SINH_HIEU);

  const {
    vitalSigns: { getDataVitalSigns, updateData },
    chiSoSong: { getListAllChiSoSong },
    nbDotDieuTri: { getById },
  } = useDispatch();

  const refModal = useRef(null);
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useImperativeHandle(ref, () => ({
    show: ({ nbDotDieuTriId }, callBack) => {
      setState({
        show: true,
      });
      getById(nbDotDieuTriId);
      getListAllChiSoSong({
        page: "",
        size: "",
        active: true,
        saveCache: false,
        isForceCall: true,
      }).then(() => {
        getDataVitalSigns({ nbDotDieuTriId, isLog: true })
          .then(() => {
            setLoading(false);
          })
          .catch((err) => {
            console.error(err);
            setLoading(false);
          });
      });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    return () => {
      updateData({ values: null, moreValueIds: null });
    };
  }, []);

  const width = useMemo(() => {
    return window.screen.width - 100;
  }, [window.screen.width]);

  const tenBenhNhan = useMemo(() => {
    let result = "";
    if (thongTinBenhNhan) {
      const gioiTinh = listGioiTinh.find(
        (i) => i.id === thongTinBenhNhan.gioiTinh
      )?.ten;
      result = [thongTinBenhNhan.tenNb, gioiTinh, thongTinBenhNhan.ngaySinh2];
      result = result.filter((i) => !!i).join(" - ");
    }
    return result;
  }, [thongTinBenhNhan]);

  const onOK = (isOk) => () => {
    if (isOk) {
    } else {
      setState({
        show: false,
      });
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onOK(false)}
      title={tenBenhNhan}
      width={width}
    >
      <Main>
        {(loading || !state.show || !loadFinish) && (
          <div className="loading">
            <Spin />
          </div>
        )}
        {!loading &&
          state.show &&
          loadFinish &&
          loadFinishHienThiTruongSinhHieu && (
            <VitalSigns
              isLog={true}
              isEdit={false}
              isModal={false}
              show={state.show}
              dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
              dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
            />
          )}
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalLichSuSinhHieu);
