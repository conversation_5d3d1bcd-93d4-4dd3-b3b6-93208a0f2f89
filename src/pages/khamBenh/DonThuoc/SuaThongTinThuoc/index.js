import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { Input, message, Row, Col, Select as AntdSelect } from "antd";
import { Main } from "./styled";
import { useSelector, useDispatch } from "react-redux";
import ModalThemLieuDung from "pages/khamBenh/DonThuoc/ModalThemLieuDung";
import moment, { isMoment } from "moment";
import { useTranslation } from "react-i18next";
import {
  useEnum,
  useStore,
  useListAll,
  useThietLap,
  useLazyKVMap,
} from "hooks";
import {
  DOI_TUONG,
  ENUM,
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  THOI_DIEM_DUNG,
} from "constants/index";
import {
  DatePicker,
  DateTimePicker,
  Button,
  Select,
  ModalTemplate,
  InputTimeout,
  Checkbox,
  TableWrapper,
  HeaderSearch,
  TimeInput,
  InputNumberField,
} from "components";
import { containText, roundNumberPoint } from "utils/index";
import { cloneDeep, isNumber, lowerFirst, uniq } from "lodash";
import {
  evalString,
  tinhSoLuongV2,
  tinhSoNgayV2,
} from "utils/chi-dinh-thuoc-utils";
import { TRANG_THAI_NB } from "constants/index";
import { SVG } from "assets";
import {
  firstLetterUpper,
  getCachDung,
} from "pages/khamBenh/components/TableDonThuoc/utils";
import useThietLapChung from "pages/khamBenh/components/TableDonThuoc/useThietLapChung";
import { checkRole } from "lib-utils/role-utils";
import { convertPayloadThuoc } from "./config";

const { Setting } = TableWrapper;
const { RangePicker } = DatePicker;
const formatTime = "HH:mm:ss";

const FormItem = ({ children, label, style, required, ...props }) => {
  return (
    <div className="form-item" style={style}>
      <div className="label">
        {label || ""}
        {required && <span className="required">*</span>}
      </div>
      <div>{children}</div>
    </div>
  );
};

const SuaThongTinThuoc = (
  { isReadonlyDvNoiTru, thuocDaKe, isShowSoLuongDinhMuc = false, modeThuoc },
  ref
) => {
  const configData = useStore("chiDinhKhamBenh.configData");

  const refSettings = useRef(null);
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
    data: {},
    thuocNhaThuoc: false,
  });
  const refCallback = useRef(null);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalThemLieuDung = useRef(null);
  const refModal = useRef(null);
  const {
    lieuDungThuoc: { createOrEdit: createOrEditLieuDungThuoc },
    mucDichSuDung: { onSearch },
    chiDinhDichVuKho: {
      suaThongTinThuoc,
      suaThongTinThuocKeNgoai,
      suaThongTinThuocNhaThuoc,
      suaThongTinThuocDaKe,
      onDeleteAll,
    },
    lieuDung: { createOrEdit: createOrEditLieuDung, getListAllLieuDung },
    thuocKeNgoaiLieuDung: { createOrEdit: createOrEditThuocKeNgoaiLieuDung },
  } = useDispatch();

  const listDataLieuDung = useStore("lieuDung.listAllLieuDung", []);
  const listMucDichSuDung = useStore("mucDichSuDung.listMucDichSuDung", []);
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [getDonViTocDoTruyen] = useLazyKVMap(listDonViTocDoTruyen);

  const {
    auth: { nhanVienId },
  } = useSelector((state) => state.auth);
  const [TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
  );
  const [TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC
  );
 const [CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY] = useThietLap(
    THIET_LAP_CHUNG.CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY
  );
  const {
    isTinhSLTongTheoSLBuoi,
    dataThoiGianVaTenBuoiSang,
    dataThoiGianVaTenBuoiChieu,
    dataThoiGianVaTenBuoiToi,
    dataThoiGianVaTenBuoiDem,
  } = useThietLapChung();

  const isNbDaRaVien = useMemo(() => {
    return configData?.thongTinNguoiBenh?.trangThai >= TRANG_THAI_NB.DA_RA_VIEN;
  }, [configData?.thongTinNguoiBenh]);

  const isToDieuTriThuocNhaThuoc = useMemo(() => {
    return (
      window.location.pathname.indexOf(
        "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri"
      ) >= 0 && state.thuocNhaThuoc // tờ điều trị + loại thuốc = thuốc nhà thuốc
    );
  }, [state.thuocNhaThuoc]);

  const isNoiTruThuocKho = useMemo(() => {
    return (
      !state.thuocNhaThuoc &&
      (window.location.pathname.indexOf(
        "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri"
      ) >= 0 ||
        window.location.pathname.indexOf("/phau-thuat-thu-thuat") >= 0 ||
        window.location.pathname.indexOf("/chan-doan-hinh-anh") >= 0)
    );
  }, [state.thuocNhaThuoc]);

  const isKhamBenh = useMemo(() => {
    return isToDieuTriThuocNhaThuoc || configData.isNgoaiTru;
  }, [isToDieuTriThuocNhaThuoc, configData.isNgoaiTru]);

  useImperativeHandle(ref, () => ({
    show: async (options = {}, callback) => {
      const { data, isTuTruc, isKeNgoai, khoBhyt, thuocNhaThuoc } = options;
      const item = {
        ...data,
        sttNgaySuDung:
          !data?.sttNgaySuDung || data?.sttNgaySuDung == 0
            ? null
            : data?.sttNgaySuDung,
        ngayThucHien: [
          data?.ngayThucHienTu && moment(data?.ngayThucHienTu),
          data?.ngayThucHienDen && moment(data?.ngayThucHienDen),
        ],
        dienGiaiLieuDung: data?.tenDuongDung,
        thoiGianBatDau:
          data?.thoiGianBatDau &&
          moment(data.thoiGianBatDau).format("HH:mm:ss"),
        thoiGianThucHien:
          data?.thoiGianThucHien && moment(data.thoiGianThucHien),
        thoiGianChiDinh: data?.thoiGianChiDinh && moment(data.thoiGianChiDinh),
        dotXuat: data.loaiChiDinh === LOAI_CHI_DINH.DOT_XUAT,
        boSung: data.loaiChiDinh === LOAI_CHI_DINH.BO_SUNG,
      };

      if (isKeNgoai) {
        item.tenDichVu = item.tenDichVu || item.tenThuocChiDinhNgoai;
      }
      getListAllLieuDung(
        {
          bacSiId: nhanVienId,
          dichVuId: data.dichVuId,
          page: "",
          size: "",
          active: true,
        },
        nhanVienId + "_" + data.dichVuId
      );
      onSearch({ dataSearch: { dichVuId: data.dichVuId } });
      setState({
        show: true,
        data: {
          ...item,
          soLuong: String(item.soLuong),
          soGiot:
            !item.soGiot && item.donViTocDoTruyen === 10 ? 20 : item.soGiot,
        },
        isTuTruc,
        isKeNgoai,
        khoBhyt,
        soLuong: String(item.soLuong),
        thuocNhaThuoc,
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  const getErrorMessage = () => {
    if (state.data.donViTocDoTruyen === 10 && !state.data.soGiot) {
      return t("danhMuc.vuiLongNhapTitle", {
        title: lowerFirst(t("quanLyNoiTru.toDieuTri.soGiot/ml")),
      });
    }
    return false;
  };

  const onCancel = () => {
    setState({ show: false });
    refModal.current && refModal.current.hide();
  };

  const onOK = (isOk) => () => {
    const errorMessage = getErrorMessage();
    if (errorMessage) {
      message.error(errorMessage);
      return;
    }
    const _soLuong = parseFloat(String(state.data.soLuong).replace(/,/g, "."));
    const _soNgay = parseFloat(String(state.data.soNgay).replace(/,/g, "."));
    if (
      CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY?.eval() &&
      _soNgay &&
      Number(_soNgay) > 7 &&
      configData.thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      message.error(
        t("khamBenh.vuiLongNhapSoNgayChoDon", {
          num: 7,
        })
      );
      return;
    }

    if (isOk) {
      if (state.data.soLuongHuy > _soLuong) {
        message.error(t("common.slHuyLonHonSL"));
      } else {
        onHandleSubmit(state.data);
      }
    } else {
      setState({ show: false });
    }
  };

  const onHandleSubmit = async (values) => {
    const _soLuong = parseFloat(String(values.soLuong).replace(/,/g, "."));
    const _soLuongHuy = parseFloat(
      String(values?.soLuongHuy || 0).replace(/,/g, ".")
    );

    if (!state.isKeNgoai && !state.thuocNhaThuoc && !thuocDaKe) {
      let payload = [];

      if ((values.dsThuocGop || []).length > 1) {
        let soLuongTemp = 0;
        let soLuongHuyTemp = 0;
        //trường hợp gộp thuốc khác số lô => xử lý sửa cho toàn bộ các thuốc đó
        payload = values.dsThuocGop.reverse().map((item, idx) => {
          const isLast = idx === values.dsThuocGop.length - 1;
          soLuongTemp += item.soLuong;
          soLuongHuyTemp += item.soLuongHuy || 0;

          return convertPayloadThuoc({
            values,
            id: item.id,
            soLuong:
              soLuongTemp > _soLuong
                ? item.soLuong - (soLuongTemp - _soLuong) > 0
                  ? item.soLuong - (soLuongTemp - _soLuong)
                  : 0
                : isLast
                ? item.soLuong + (_soLuong - soLuongTemp)
                : item.soLuong,
            soLuongHuy:
              soLuongHuyTemp > _soLuongHuy
                ? item.soLuongHuy - (soLuongHuyTemp - _soLuongHuy) > 0
                  ? item.soLuongHuy - (soLuongHuyTemp - _soLuongHuy)
                  : 0
                : isLast
                ? item.soLuongHuy + (_soLuongHuy - soLuongHuyTemp)
                : item.soLuongHuy,
          });
        });
      } else {
        payload = [
          convertPayloadThuoc({
            values,
            id: values.id,
            soLuong: _soLuong,
            soLuongHuy: _soLuongHuy,
          }),
        ];
      }

      //nếu tồn tại thuốc gộp bị xóa số lượng về 0 => thực hiện xóa thuốc
      if ((values.dsThuocGop || []).length > 1) {
        const dsThuocXoa = payload
          .filter((x) => x.nbDichVu.soLuong == 0)
          .map((x) => x.id);
        if (dsThuocXoa.length > 0) {
          onDeleteAll(dsThuocXoa).then((s) => {
            let data = (s?.data || [])
              .filter((item) => {
                return item.code !== 0 && item?.message;
              })
              ?.map((item) => item?.message);
            if (data?.length > 0) {
              message.error(
                `${t("khamBenh.donThuoc.khongTheXoaDichVu")} :  ${uniq(
                  data
                )?.join("; ")}`
              );
            } else {
              message.success(t("khamBenh.donThuoc.xoaDonThuocThanhCong"));
            }
          });
        }

        payload = payload.filter((x) => x.nbDichVu.soLuong > 0);
      }
      suaThongTinThuoc(payload)
        .then((s) => {
          if (s?.code === 0) {
            if (refCallback.current) refCallback.current();
            onCancel();
          } else {
            message.error(s?.[0]?.message || s?.message);
          }
        })
        .catch((err) => {
          console.log("err: ", err);
        });
    } else {
      let payload = {
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        nbDotDieuTriId: configData.nbDotDieuTriId,
        thoiGianThucHien: moment(values.thoiGianThucHien).format(
          "YYYY/MM/DD HH:mm:ss"
        ),
        thoiGianChiDinh: moment(values.thoiGianChiDinh).format(
          "YYYY/MM/DD HH:mm:ss"
        ),
        soNgay: values?.soNgay,
        soLan1Ngay: values.soLan1Ngay,
        soLuong1Lan: evalString(values.soLuong1Lan),
        soLuong: _soLuong,
        cachDung: values.cachDung,
        duongDungId: values.duongDungId,
        dotDung: values.dotDung,
        id: values.id,
        nguonKhacId: values.nguonKhacId || null,
        slSang: evalString(values.slSang),
        slChieu: evalString(values.slChieu),
        slToi: evalString(values.slToi),
        slDem: evalString(values.slDem),
      };
      if (!thuocDaKe) {
        payload = {
          ...payload,
          thuocChiDinhNgoaiId: thuocDaKe
            ? values.dichVuId
            : values.thuocChiDinhNgoaiId,
          ghiChu: values.ghiChu,
          lieuDungId: values.lieuDungId,
          ngayThucHienTu:
            values.ngayThucHien && values.ngayThucHien[0]
              ? moment(values.ngayThucHien[0]).format("YYYY-MM-DD")
              : null,
          ngayThucHienDen:
            values.ngayThucHien && values.ngayThucHien[1]
              ? moment(values.ngayThucHien[1]).format("YYYY-MM-DD")
              : null,
          soLuongHuy: evalString(values?.soLuongHuy || 0),
          lyDoHuy: values?.lyDoHuy,
          dungKemId: values?.dungKemId,
          sttNgaySuDung: !values.sttNgaySuDung ? 0 : values.sttNgaySuDung,
          bacSiChiDinhId: values?.bacSiChiDinhId,
        };
      } else {
        payload = {
          ...payload,
          khoaChiDinhId: values?.khoaChiDinhId,
          bacSiChiDinhId: values?.bacSiChiDinhId,
          lieuDungId: values.lieuDungId,
          tocDoTruyen: values?.tocDoTruyen,
          donViTocDoTruyen: values?.donViTocDoTruyen,
        };
      }
      let api = state.thuocNhaThuoc
        ? suaThongTinThuocNhaThuoc
        : thuocDaKe
        ? suaThongTinThuocDaKe
        : suaThongTinThuocKeNgoai;

      try {
        const s = await api(payload);
        if (s?.code === 0) {
          if (refCallback.current) refCallback.current();
          onCancel();
        } else {
          message.error(s?.message || s[0]?.message);
        }
      } catch (error) {
        console.error(error);
      }
    }
  };

  //xử lý tính lại số lượng + SL sơ cấp khi các field số ngày, số lần / ngày, số lượng / lần thay đổi
  const genarateSoLuong = (data, soLuong) => {
    if (data.heSoDinhMuc != 1) {
      const soLuongSoCap = isKhamBenh
        ? Math.ceil(soLuong / data.heSoDinhMuc) // nếu màn khám bệnh thì làm tròn hàng đơn vị
        : roundNumberPoint(soLuong / data.heSoDinhMuc, 6); // nếu các màn khác thì lấy đến số thập phân thứ 2

      if (data.dvtSuDungId == data.donViTinhId) {
        data.soLuong = soLuong;
        data.soLuongSoCap = soLuongSoCap;
        if (isKhamBenh) {
          data.soLuong = soLuongSoCap * data.heSoDinhMuc;
        }
      } else {
        if (isKhamBenh) {
          data.soLuong = soLuong || 1;
          data.soLuongSoCap = soLuongSoCap || 1;
        }
      }
    } else {
      if (data.dvtSuDungId == data.donViTinhId) {
        const tuDongLamTron =
          isKhamBenh &&
          TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true";
        data.soLuong = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
        data.soLuongSoCap = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
      } else {
        if (isKhamBenh) {
          data.soLuong = soLuong || 1;
          data.soLuongSoCap = soLuong || 1;
        }
      }
    }

    data.soLuong = roundNumberPoint(data.soLuong, 6);
    data.soLuongSoCap = roundNumberPoint(data.soLuongSoCap, 6);
  };

  const expressionSymbol = /^(\d+(\.\d+)?|\d+\/\d+)$/;

  const listOptions = useMemo(() => {
    let options = [
      {
        value: THOI_DIEM_DUNG.TRUOC_AN,
        label: THOI_DIEM_DUNG.TRUOC_AN,
      },
      { value: THOI_DIEM_DUNG.SAU_AN, label: THOI_DIEM_DUNG.SAU_AN },
      {
        value: THOI_DIEM_DUNG.TRONG_KHI_AN,
        label: THOI_DIEM_DUNG.TRONG_KHI_AN,
      },
      {
        value: THOI_DIEM_DUNG.KHI_SOT,
        label: THOI_DIEM_DUNG.KHI_SOT,
      },
      {
        value: THOI_DIEM_DUNG.BUOI_TOI,
        label: THOI_DIEM_DUNG.BUOI_TOI,
      },
    ].map((item, index) => (
      <AntdSelect.Option key={index} value={item?.value}>
        {item?.label}
      </AntdSelect.Option>
    ));
    return options;
  }, []);

  const { tenBuoiSang, tenBuoiChieu, tenBuoiToi, tenBuoiDem } = useMemo(() => {
    const createTenBuoi = (dataThoiGian, fallbackKey) => {
      const customName = dataThoiGian?.[2];
      const displayName = customName ? `SL ${customName}` : t(fallbackKey);
      return firstLetterUpper(displayName);
    };

    return {
      tenBuoiSang: createTenBuoi(dataThoiGianVaTenBuoiSang, "common.slSang"),
      tenBuoiChieu: createTenBuoi(dataThoiGianVaTenBuoiChieu, "common.slChieu"),
      tenBuoiToi: createTenBuoi(dataThoiGianVaTenBuoiToi, "common.slToi"),
      tenBuoiDem: createTenBuoi(dataThoiGianVaTenBuoiDem, "common.slDem"),
    };
  }, [
    dataThoiGianVaTenBuoiSang,
    dataThoiGianVaTenBuoiChieu,
    dataThoiGianVaTenBuoiToi,
    dataThoiGianVaTenBuoiDem,
    t,
  ]);

  const onChange = (type) => (e, list) => {
    let value = "";
    if (e?.target) {
      value = e.target.value;
    } else if (e?._d) value = e._d.format("MM/dd/yyyy");
    else value = e;

    const data = { ...state.data };
    if (type == "tuTra" && e) {
      data["khongTinhTien"] = false;
    } else if (type == "khongTinhTien" && e) {
      data["tuTra"] = false;
    }
    if (type == "dotXuat" && e) {
      data["boSung"] = false;
    } else if (type == "boSung" && e) {
      data["dotXuat"] = false;
    }
    if (type == "thoiGianThucHien" && e == null) {
      data["thoiGianThucHien"] = data.thoiGianThucHien;
    }
    if (type == "thoiGianChiDinh" && e == null) {
      data["thoiGianChiDinh"] = data.thoiGianChiDinh;
    }
    if (type === "donViTocDoTruyen") {
      data["soGiot"] = !data.soGiot && e === 10 ? 20 : data.soGiot;
    }
    data[type] = e;
    //làm tròn giá trị số lượng / số lượng sơ cấp
    if (
      (type === "soLuong" || type === "soLuongSoCap") &&
      isKhamBenh &&
      data.heSoDinhMuc == 1 &&
      data.dvtSuDungId == data.donViTinhId &&
      TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true" &&
      isNumber(data[type])
    ) {
      data[type] = Math.ceil(data[type]);
    }
    if (type === "soLuong1Lan" && value) {
      data.soLuong1LanStr = value;
    }

    if (type === "soLuong") {
      // const _soLuong = parseFloat(String(data[type]).replace(/,/g, ".")) || 0;
      // data.soLuongSoCap = _soLuong / (data.heSoDinhMuc || 1);
      // if (isKhamBenh) {
      //   data.soLuongSoCap = Math.ceil(data.soLuongSoCap);
      //   data.soLuong = Math.ceil(data.soLuongSoCap) * (data.heSoDinhMuc || 1);
      //   list?.callback && list?.callback(data.soLuong);
      // }

      if (Number(value) <= 0 && value) {
        data.soLuong = 0;
        message.error(t("khamBenh.donThuoc.nhapSoLuongLonHon0"));
      } else {
        //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
        //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức

        // const _soLuong = parseFloat(String(data[type]).replace(/,/g, ".")) || 0;
        const _soLuong = parseFloat(String(e).replace(/,/g, "."));

        data.soLuongSoCap = _soLuong / (data.heSoDinhMuc || 1);
        if (isKhamBenh) {
          data.soLuongSoCap = Math.ceil(data.soLuongSoCap);
          data.soLuong = Math.ceil(data.soLuongSoCap) * (data.heSoDinhMuc || 1);
          list?.callback && list?.callback(data.soLuong);
        }

        if (e) {
          if (
            (data.heSoDinhMuc == 1 && data.dvtSuDungId == data.donViTinhId) ||
            (data.heSoDinhMuc != 1 &&
              TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true")
          ) {
            if (isNoiTruThuocKho && !data.phieuLinhId) {
              // const _soLuongHuy =
              //   _soLuong - data.soLan1Ngay * data.soLan1Ngay * data.soLuong1Lan;
              // data.soLuongHuy = floorNumberPoint(_soLuongHuy, 6);
            } else {
              data.soNgay =
                tinhSoNgayV2({
                  isTinhSLTongTheoSLBuoi,
                  soLuong: _soLuong,
                  soLuong1Lan: data.soLuong1Lan,
                  soLan1Ngay: data.soLan1Ngay,
                  soLuongHuy: data.soLuongHuy,
                  slSang: data.slSang,
                  slChieu: data.slChieu,
                  slToi: data.slToi,
                  slDem: data.slDem,
                }) || data.soNgay;
            }
          }
        }
      }
    }

    if (type === "soLuongSoCap") {
      if (isKhamBenh) {
        data.soLuongSoCap = Math.ceil(Number(data.soLuongSoCap) || 0);
        list?.callback && list?.callback(data.soLuongSoCap);
      }
      data.soLuong = (Number(data.soLuongSoCap) || 0) * (data.heSoDinhMuc || 1);

      //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
      //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức
      if (
        (data.heSoDinhMuc == 1 && data.dvtSuDungId == data.donViTinhId) ||
        (data.heSoDinhMuc != 1 &&
          TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true")
      ) {
        data.soNgay =
          tinhSoNgayV2({
            isTinhSLTongTheoSLBuoi,
            soLuong: data.soLuong,
            soLuong1Lan: data.soLuong1Lan,
            soLan1Ngay: data.soLan1Ngay,
            soLuongHuy: data.soLuongHuy,
            slSang: data.slSang,
            slChieu: data.slChieu,
            slToi: data.slToi,
            slDem: data.slDem,
          }) || data.soNgay;
      }
    }

    if (type === "lieuDungId" && list) {
      columns.forEach((col) => {
        if (col?.dataIndex === "slSang") {
          data.slSang = list?.slSang;
        }
        if (col?.dataIndex === "slChieu") {
          data.slChieu = list?.slChieu;
        }
        if (col?.dataIndex === "slToi") {
          data.slToi = list?.slToi;
        }
        if (col?.dataIndex === "slDem") {
          data.slDem = list?.slDem;
        }
      });

      const soLuong1Lan = list?.soLuong1Lan || data.soLuong1Lan;
      const soLan1Ngay = list?.soLan1Ngay || data.soLan1Ngay;

      if (soLuong1Lan && soLan1Ngay) {
        const tenDuongDung = `${
          list?.tenDuongDung
            ? list?.tenDuongDung
            : data?.tenDuongDung
            ? data?.tenDuongDung
            : ""
        }`;
        data.cachDung = getCachDung(
          t,
          tenDuongDung,
          soLan1Ngay,
          data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số,
          data.tenDvtSuDung,
          data.thoiDiem,
          data.slSang,
          data.slChieu,
          data.slToi,
          data.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          data.tocDoTruyen,
          getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
        );
      }

      data.lieuDungId = e;

      if (data.lieuDungId) {
        if (data.soNgay) {
          genarateSoLuong(
            data,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: data.soNgay,
              soLuong1Lan: data.soLuong1Lan,
              soLan1Ngay: data.soLan1Ngay,
              soLuongHuy: data.soLuongHuy,
              slSang: data.slSang,
              slChieu: data.slChieu,
              slToi: data.slToi,
              slDem: data.slDem,
            })
          );
        } else if (data.soLuong) {
          data.soNgay =
            tinhSoNgayV2({
              isTinhSLTongTheoSLBuoi,
              soLuong: data.soLuong,
              soLuong1Lan: data.soLuong1Lan,
              soLan1Ngay: data.soLan1Ngay,
              soLuongHuy: data.soLuongHuy,
              slSang: data.slSang,
              slChieu: data.slChieu,
              slToi: data.slToi,
              slDem: data.slDem,
            }) || data.soNgay;
        }
      }
    }

    if (type === "soNgay" && e) {
      if (isKhamBenh || modeThuoc == "DonThuocRaVien") {
        const { ngayThucHienDen, ngayThucHienTu } = data || {};
        let tuNgay = ngayThucHienTu,
          denNgay = ngayThucHienDen;
        if (!ngayThucHienTu) {
          tuNgay = moment().add(1, "days");
          if (!ngayThucHienDen) {
            denNgay = moment(tuNgay).add(1, "days");
          }
        }
        if (!ngayThucHienDen && tuNgay) {
          denNgay = moment(tuNgay).add(e, "days");
        }
        if (tuNgay && denNgay) {
          let _tuNgay = !isMoment(tuNgay)
            ? moment(tuNgay, "YYYY-MM-DD")
            : tuNgay;
          let _denNgay = !isMoment(denNgay)
            ? moment(denNgay, "YYYY-MM-DD")
            : denNgay;
          data.ngayThucHien = [_tuNgay, _denNgay];
        }
      }
      if (!data.phieuLinhId) {
        genarateSoLuong(
          data,
          tinhSoLuongV2({
            isTinhSLTongTheoSLBuoi,
            soNgay: e,
            soLuong1Lan: data.soLuong1Lan,
            soLan1Ngay: data.soLan1Ngay,
            soLuongHuy: data.soLuongHuy,
            slSang: data.slSang,
            slChieu: data.slChieu,
            slToi: data.slToi,
            slDem: data.slDem,
          })
        );
      }
    }

    if ((type === "soLan1Ngay" || type === "soLuong1Lan") && e) {
      if (data.soNgay) {
        if (!data.phieuLinhId) {
          genarateSoLuong(
            data,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: data.soNgay,
              soLan1Ngay: type === "soLan1Ngay" ? e : data.soLan1Ngay,
              soLuong1Lan: type === "soLuong1Lan" ? e : data.soLuong1Lan,
              soLuongHuy: data.soLuongHuy,
              slSang: data.slSang,
              slChieu: data.slChieu,
              slToi: data.slToi,
              slDem: data.slDem,
            })
          );
        }
      } else if (data.soLuong) {
        data.soNgay = tinhSoNgayV2({
          isTinhSLTongTheoSLBuoi,
          soLuong: data.soLuong,
          soLan1Ngay: type === "soLan1Ngay" ? e : data.soLan1Ngay,
          soLuong1Lan: type === "soLuong1Lan" ? e : data.soLuong1Lan,
          soLuongHuy: data.soLuongHuy,
          slSang: data.slSang,
          slChieu: data.slChieu,
          slToi: data.slToi,
          slDem: data.slDem,
        });
      }
    }

    if (
      ((type === "soLan1Ngay" && data.soLuong1Lan) ||
        (type === "soLuong1Lan" && data.soLan1Ngay)) &&
      value
    ) {
      data.cachDung = getCachDung(
        t,
        data.tenDuongDung,
        type === "soLuong1Lan" ? data.soLan1Ngay : value,
        data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số
        data.tenDvtSuDung,
        data.thoiDiem,
        data.slSang,
        data.slChieu,
        data.slToi,
        data.slDem,
        dataThoiGianVaTenBuoiSang,
        dataThoiGianVaTenBuoiChieu,
        dataThoiGianVaTenBuoiToi,
        dataThoiGianVaTenBuoiDem,
        data.tocDoTruyen,
        getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
      );
    }

    if (type === "thoiDiem") {
      data.thoiDiem = e;
      if (
        data.soLuong1Lan ||
        data.soLan1Ngay ||
        data.slSang ||
        data.slChieu ||
        data.slToi ||
        data.slDem
      ) {
        data.cachDung = getCachDung(
          t,
          data.tenDuongDung,
          data.soLan1Ngay,
          data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số
          data.tenDvtSuDung,
          data.thoiDiem,
          data.slSang,
          data.slChieu,
          data.slToi,
          data.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          data.tocDoTruyen,
          getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
        );
      }
    }

    if (
      type === "soLan1Ngay" ||
      type === "soLuong1Lan" ||
      type === "cachGio" ||
      type === "thoiGianBatDau"
    ) {
      const thoiGianSang = [];
      const thoiGianChieu = [];
      const thoiGianToi = [];
      const thoiGianDem = [];
      let slSang = null;
      let slChieu = null;
      let slToi = null;
      let slDem = null;
      for (let i = 0; i < data.soLan1Ngay; i++) {
        const thoiGian = moment(data.thoiGianBatDau, formatTime).add(
          data.cachGio * i,
          "hours"
        );

        if (
          thoiGian.diff(moment("00:00:00", formatTime)) >= 0 &&
          thoiGian.diff(moment("12:00:00", formatTime)) <= 0
        ) {
          thoiGianSang.push(thoiGian);
        }

        if (
          thoiGian.diff(moment("12:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("18:00:00", formatTime)) <= 0
        ) {
          thoiGianChieu.push(thoiGian);
        }
        if (
          thoiGian.diff(moment("18:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("22:00:00", formatTime)) <= 0
        ) {
          thoiGianToi.push(thoiGian);
        }

        if (
          thoiGian.diff(moment("22:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("23:59:59", formatTime)) <= 0
        ) {
          thoiGianDem.push(thoiGian);
        }
      }
      if (thoiGianSang.length) {
        slSang = thoiGianSang.length * evalString(data.soLuong1Lan);
      }
      if (thoiGianChieu.length) {
        slChieu = thoiGianChieu.length * evalString(data.soLuong1Lan);
      }
      if (thoiGianToi.length) {
        slToi = thoiGianToi.length * evalString(data.soLuong1Lan);
      }
      if (thoiGianDem.length) {
        slDem = thoiGianDem.length * evalString(data.soLuong1Lan);
      }
      data.slSang = slSang;
      data.slChieu = slChieu;
      data.slToi = slToi;
      data.slDem = slDem;
      const soLuong1Lan = list?.soLuong1Lan || data.soLuong1Lan;
      const soLan1Ngay = list?.soLan1Ngay || data.soLan1Ngay;

      if (soLuong1Lan && soLan1Ngay) {
        const tenDuongDung = `${
          list?.tenDuongDung
            ? list?.tenDuongDung
            : data?.tenDuongDung
            ? data?.tenDuongDung
            : ""
        }`;

        data.cachDung = getCachDung(
          t,
          tenDuongDung,
          soLan1Ngay,
          data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số,
          data.tenDvtSuDung,
          data.thoiDiem,
          data.slSang,
          data.slChieu,
          data.slToi,
          data.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          data.tocDoTruyen,
          getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
        );
      }
    }

    if (type === "soLuongHuy") {
      if (!data.phieuLinhId) {
        genarateSoLuong(
          data,
          tinhSoLuongV2({
            isTinhSLTongTheoSLBuoi,
            soNgay: data.soNgay,
            soLuong1Lan: data.soLuong1Lan,
            soLan1Ngay: data.soLan1Ngay,
            soLuongHuy: e,
            slSang: data.slSang,
            slChieu: data.slChieu,
            slToi: data.slToi,
            slDem: data.slDem,
          })
        );
      }
    }

    if (type === "tocDoTruyen" || type === "donViTocDoTruyen") {
      data.cachDung = getCachDung(
        t,
        data.tenDuongDung,
        data.soLan1Ngay,
        data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số
        data.tenDvtSuDung,
        data.thoiDiem,
        data.slSang,
        data.slChieu,
        data.slToi,
        data.slDem,
        dataThoiGianVaTenBuoiSang,
        dataThoiGianVaTenBuoiChieu,
        dataThoiGianVaTenBuoiToi,
        dataThoiGianVaTenBuoiDem,
        data.tocDoTruyen,
        getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
      );
    }

    if (["slSang", "slChieu", "slToi", "slDem"].includes(type)) {
      if (value && !expressionSymbol.test(value))
        data[`warning_${type}`] = true;
      else {
        data[`warning_${type}`] = false;
        data[type] = value;

        if (data.soNgay) {
          genarateSoLuong(
            data,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: data.soNgay,
              soLuong1Lan: data.soLuong1Lan,
              soLan1Ngay: data.soLan1Ngay,
              soLuongHuy: data.soLuongHuy,
              slSang: data.slSang,
              slChieu: data.slChieu,
              slToi: data.slToi,
              slDem: data.slDem,
            })
          );
        } else if (data.soLuong) {
          data.soNgay =
            tinhSoNgayV2({
              isTinhSLTongTheoSLBuoi,
              soLuong: data.soLuong,
              soLuong1Lan: data.soLuong1Lan,
              soLan1Ngay: data.soLan1Ngay,
              soLuongHuy: data.soLuongHuy,
              slSang: data.slSang,
              slChieu: data.slChieu,
              slToi: data.slToi,
              slDem: data.slDem,
            }) || data.soNgay;
        }

        data.cachDung = getCachDung(
          t,
          data.tenDuongDung,
          data.soLan1Ngay,
          data.soLuong1Lan,
          data.tenDvtSuDung,
          data.thoiDiem,
          data.slSang,
          data.slChieu,
          data.slToi,
          data.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem
        );
      }
    }

    if (type === "duongDungId") {
      data.tenDuongDung = list?.ten || "";
      if (data.soLuong1Lan && data.soLan1Ngay && !data.lieuDungId) {
        data.cachDung = getCachDung(
          t,
          data.tenDuongDung,
          data.soLan1Ngay,
          data.soLuong1LanStr || data.soLuong1Lan, //dùng biến này để giữ giá trị phân số
          data.tenDvtSuDung,
          data.thoiDiem,
          data.slSang,
          data.slChieu,
          data.slToi,
          data.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          data.tocDoTruyen,
          getDonViTocDoTruyen(data.donViTocDoTruyen)?.ten
        );
      }
    }

    setState({ data });
  };

  const onChangeSelect =
    (type, index) =>
    (e = [], list) => {
      const data = { ...state.data };
      if (type === "thoiDiem") {
        data.thoiDiem = e[0];

        if (data.soLuong1Lan || data.soLan1Ngay) {
          const tenDuongDung = `${
            data.tenDuongDung ? data.tenDuongDung + " - " : ""
          }`;
          let cachDung = `${tenDuongDung} ngày ${data.soLan1Ngay} lần, mỗi lần ${data.soLuong1Lan} ${data.tenDonViTinh}`;
          if (data.slSang) {
            cachDung = `${cachDung}, sáng ${data.slSang}`;
          }

          if (data.slChieu) {
            cachDung = `${cachDung}, chiều ${data.slChieu}`;
          }

          if (data.slToi) {
            cachDung = `${cachDung}, tối ${data.slToi}`;
          }

          if (data.slDem) {
            cachDung = `${cachDung}, đêm ${data.slDem}`;
          }
          if (e) {
            cachDung = `${cachDung} ${e}`;
          }
          data.cachDung = cachDung;
        }
      }

      setState({ data });
    };

  const listAllLieuDung = useMemo(() => {
    if (
      state.data &&
      state.data.lieuDungId &&
      state.data.tenLieuDung &&
      !state.searchLieuDungWord &&
      !listDataLieuDung.map((item) => item.id).includes(state.data.lieuDungId)
    ) {
      return [
        ...listDataLieuDung,
        { id: state.data.lieuDungId, ten: state.data.tenLieuDung },
      ];
    } else {
      return listDataLieuDung;
    }
  }, [listDataLieuDung, state.data, state?.searchLieuDungWord]);

  const onSearchLieuDung = (e) => {
    if (e) {
      getListAllLieuDung({
        page: "",
        size: "",
        ten: e,
      });
    } else {
      getListAllLieuDung({
        bacSiId: nhanVienId,
        dichVuId: state?.data.dichVuId,
        page: "",
        size: "",
      });
    }
    setState({ searchLieuDungWord: e || "" });
  };

  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  const render = (type) => {
    const isReadonly =
      isNbDaRaVien || (isNoiTruThuocKho && state.data.phieuLinhId);
    switch (type) {
      case "lieuDungId":
        return (
          <Select
            data={listAllLieuDung}
            value={state.data.lieuDungId}
            onChange={onChange("lieuDungId")}
            onSearch={onSearchLieuDung}
            disabled={isReadonlyDvNoiTru}
            popupClassName="table-thuoc-lieu-dung"
            notFoundContent={
              <div>
                <div style={{ color: "#7A869A", textAlign: "center" }}>
                  <small>{t("common.khongCoDuLieuPhuHop")}</small>
                </div>
                <Row justify="center">
                  <Button
                    trigger="click"
                    style={{
                      border: "1px solid",
                      borderRadius: "10px",
                      width: "215px",
                      margin: "auto",
                      lineHeight: 0,
                      // boxShadow: "-1px 3px 1px 1px #d9d9d9",
                      cursor: "pointer",
                    }}
                    onClick={() =>
                      refModalThemLieuDung &&
                      refModalThemLieuDung.current.show(
                        {
                          tenLieuDung: state.searchLieuDungWord,
                          data: state?.data,
                          isThuocKeNgoai: state.isKeNgoai,
                        },
                        (res) => {
                          const { values } = res;
                          values.bacSiId = nhanVienId;
                          if (!state.isKeNgoai) {
                            createOrEditLieuDung(values).then(async (s) => {
                              const dataCustom = {
                                lieuDung: {
                                  ...s,
                                },
                                lieuDungId: s.id,
                                dichVuId: state?.data?.dichVuId,
                              };
                              await createOrEditLieuDungThuoc(dataCustom);
                              getListAllLieuDung(
                                {
                                  bacSiId: nhanVienId,
                                  dichVuId: state?.data?.dichVuId,
                                  page: "",
                                  size: "",
                                  active: true,
                                },
                                nhanVienId + "_" + state?.data.dichVuId
                              );
                              let updateDataSelected = cloneDeep(state.data);
                              updateDataSelected.lieuDungId = s?.id;
                              setState({
                                data: updateDataSelected,
                              });
                            });
                          } else {
                            createOrEditLieuDung(values).then(async (s) => {
                              const dataCustom = {
                                lieuDung: {
                                  ...s,
                                },
                                lieuDungId: s.id,
                                thuocChiDinhNgoaiId:
                                  state.data?.thuocChiDinhNgoaiId,
                              };
                              await createOrEditThuocKeNgoaiLieuDung(
                                dataCustom,
                                {
                                  isFromDm: false,
                                }
                              );
                              getListAllLieuDung({
                                page: "",
                                size: "",
                                active: true,
                                isForceCall: true,
                              });
                              let updateDataSelected = cloneDeep(state.data);
                              updateDataSelected.lieuDungId = s?.id;
                              setState({
                                data: updateDataSelected,
                              });
                            });
                          }
                        }
                      )
                    }
                  >
                    {state.isKeNgoai
                      ? t("khamBenh.donThuoc.themNhanhLieuDung")
                      : t("khamBenh.donThuoc.themNhanhLieuDungBS")}
                  </Button>
                </Row>
              </div>
            }
          ></Select>
        );
      case "dotDung":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.dotDung}
            onChange={onChange("dotDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "ngayThucHien":
        return (
          <RangePicker
            placeholder={[t("common.tuNgay"), t("common.denNgay")]}
            disabled={isReadonlyDvNoiTru}
            format="DD/MM/YYYY"
            value={state.data.ngayThucHien}
            onChange={onChange("ngayThucHien")}
            showTime={false}
          />
        );
      case "ghiChu":
        return (
          <InputTimeout
            value={state.data.ghiChu}
            onChange={onChange("ghiChu")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soLuong":
        return (
          <InputTimeout
            // type="number"
            min={0}
            value={state.data.soLuong}
            onChange={onChange("soLuong")}
            disabled={isReadonly || isReadonlyDvNoiTru}
          />
        );
      case "soLuongSoCap":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.soLuongSoCap}
            onChange={onChange("soLuongSoCap")}
            disabled={isReadonly || isReadonlyDvNoiTru}
          />
        );
      case "tenDonViTinh":
        return <Input value={state.data.tenDonViTinh} disabled />;
      case "tenDvtSoCap":
        return <Input value={state.data.tenDvtSoCap} disabled />;
      case "tenDvtSuDung":
        return <Input value={state.data.tenDvtSuDung} disabled />;
      case "soLuongDinhMuc":
        return <Input value={state.data.soLuongDinhMuc} disabled />;
      case "tt20":
        return (
          <Select
            data={listMucDichSuDung}
            placeholder="TT20"
            value={state.data.mucDichId}
            onChange={onChange("mucDichId")}
            disabled={
              configData?.thongTinNguoiBenh?.doiTuong ===
                DOI_TUONG.KHONG_BAO_HIEM || isReadonlyDvNoiTru
            }
          />
        );
      case "tuTra":
        return (
          <Checkbox
            checked={state.data.tuTra}
            onChange={(e) => onChange("tuTra")(e.target.checked)}
            disabled={listMucDichSuDung.length || isReadonlyDvNoiTru}
          />
        );
      case "khongTinhTien":
        return (
          <Checkbox
            disabled={listMucDichSuDung.length || isReadonlyDvNoiTru}
            checked={state.data.khongTinhTien}
            onChange={(e) => onChange("khongTinhTien")(e.target.checked)}
          ></Checkbox>
        );
      case "thoiGianThucHien":
        return (
          <DateTimePicker
            value={state.data.thoiGianThucHien}
            defaultValue={moment(state.data.thoiGianThucHien).format(
              "DD/MM/YYYY HH:mm:ss"
            )}
            onChange={onChange("thoiGianThucHien")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "thoiGianChiDinh":
        return (
          <DateTimePicker
            value={state.data.thoiGianChiDinh}
            defaultValue={moment(state.data.thoiGianChiDinh).format(
              "DD/MM/YYYY HH:mm:ss"
            )}
            onChange={onChange("thoiGianChiDinh")}
            disabled={true}
          />
        );
      case "duongDungId":
        return (
          <Select
            data={listAllDuongDung}
            placeholder={t("common.duongDung")}
            value={state.data.duongDungId}
            onChange={onChange("duongDungId")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "nguonKhacId":
        return (
          <Select
            data={listAllNguonKhacChiTra}
            placeholder={t("danhMuc.nguonKhac")}
            value={state.data.nguonKhacId}
            onChange={onChange("nguonKhacId")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soNgay":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.soNgay}
            onChange={onChange("soNgay")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "soLan1Ngay":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.soLan1Ngay}
            onChange={onChange("soLan1Ngay")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "soLuong1Lan":
        return (
          <InputTimeout
            min={0}
            value={state.data.soLuong1Lan}
            onChange={onChange("soLuong1Lan")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "cachDung":
        return (
          <InputTimeout
            value={state.data.cachDung}
            onChange={onChange("cachDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "lyDoHuy":
        return (
          <InputTimeout
            value={state.data.lyDoHuy}
            onChange={onChange("lyDoHuy")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soLuongHuy":
        return (
          <InputTimeout
            min={0}
            value={state.data.soLuongHuy}
            onChange={onChange("soLuongHuy")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "thoiDiem":
        if (state.data.thoiDiem) {
          return (
            <InputTimeout
              value={state.data.thoiDiem}
              onChange={onChange("thoiDiem")}
              disabled={isReadonlyDvNoiTru}
            />
          );
        } else {
          return (
            <AntdSelect
              mode="tags"
              style={{
                width: "100%",
              }}
              value={state.data.thoiDiem || []}
              placeholder={t("khamBenh.donThuoc.nhapThoiDiemDung")}
              onChange={onChangeSelect("thoiDiem")}
              disabled={isReadonlyDvNoiTru}
              filterOption={filterOption}
              showSearch
              optionFilterProp="children"
            >
              {listOptions}
            </AntdSelect>
          );
        }
      case "slSang":
        return (
          <InputTimeout
            value={state.data.slSang}
            onChange={onChange("slSang")}
            timeDelay={300}
            style={{
              border: state.data[`warning_slSang`] ? "1px solid red" : "",
            }}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "slChieu":
        return (
          <InputTimeout
            value={state.data.slChieu}
            onChange={onChange("slChieu")}
            timeDelay={300}
            style={{
              border: state.data[`warning_slChieu`] ? "1px solid red" : "",
            }}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "slToi":
        return (
          <InputTimeout
            value={state.data.slToi}
            onChange={onChange("slToi")}
            timeDelay={300}
            style={{
              border: state.data[`warning_slToi`] ? "1px solid red" : "",
            }}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "slDem":
        return (
          <InputTimeout
            value={state.data.slDem}
            onChange={onChange("slDem")}
            timeDelay={300}
            style={{
              border: state.data[`warning_slDem`] ? "1px solid red" : "",
            }}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "sttNgaySuDung":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.sttNgaySuDung}
            onChange={onChange("sttNgaySuDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "thoiGianBatDau":
        return (
          <TimeInput
            value={state.data.thoiGianBatDau}
            onChange={onChange("thoiGianBatDau")}
            placeholder={t("common.chonThoiGian")}
            disabled={isReadonlyDvNoiTru}
            timeDelay={0}
          />
        );
      case "tocDoTruyen":
        return (
          <InputTimeout
            type="number"
            value={state.data.tocDoTruyen}
            onChange={onChange("tocDoTruyen")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "donViTocDoTruyen":
        return (
          <Select
            value={state.data.donViTocDoTruyen}
            onChange={onChange("donViTocDoTruyen")}
            data={listDonViTocDoTruyen}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soGiot":
        return (
          <>
            <InputNumberField
              allowNegative={false}
              allowLeadingZeros={false}
              value={state.data.soGiot}
              onChange={onChange("soGiot")}
              disabled={isReadonlyDvNoiTru}
              isAllowed={({ floatValue }) => {
                return floatValue !== undefined
                  ? floatValue > 0 && floatValue <= 100
                  : true;
              }}
            />
            {state.data.donViTocDoTruyen === 10 && !state.data.soGiot && (
              <div className="required">
                {t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("quanLyNoiTru.toDieuTri.soGiot/ml")),
                })}
              </div>
            )}
          </>
        );
      case "cachGio":
        return (
          <InputTimeout
            value={state.data.cachGio}
            onChange={onChange("cachGio")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      default:
        break;
    }
  };
  const columns = [
    {
      title: <HeaderSearch title={t("common.tenThuoc")} />,
      width: "200px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "common.tenDichVu",
      show: true,
      render: (item) => {
        return <Input value={state.data.tenDichVu} disabled />;
      },
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: "80px",
      dataIndex: "soLuong",
      key: "soLuong",
      i18Name: "common.soLuong",
      show: true,
      render: (item, dataItem, index) => {
        return render("soLuong");
      },
    },
    {
      title: <HeaderSearch title={t("common.slSoCap")} />,
      width: "80px",
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      i18Name: "common.slSoCap",
      show: true,
      render: (item, dataItem, index) => {
        return render("soLuongSoCap");
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.dvtSoCap")} />,
      width: "100px",
      dataIndex: "tenDvtSoCap",
      key: "tenDvtSoCap",
      i18Name: "quanLyNoiTru.toDieuTri.dvtSoCap",
      show: true,
      render: (item, dataItem, index) => {
        return render("tenDvtSoCap");
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.dvt")} />,
      width: "70px",
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "quanLyNoiTru.toDieuTri.dvt",
      show: true,
      render: (item, dataItem, index) => {
        return render("tenDonViTinh");
      },
    },
    {
      title: <HeaderSearch title={t("common.dvtSuDung")} />,
      width: "70px",
      dataIndex: "tenDvtSuDung",
      key: "tenDvtSuDung",
      i18Name: "common.dvtSuDung",
      show: true,
      render: (item, dataItem, index) => {
        return render("tenDvtSuDung");
      },
    },
    {
      title: (
        <HeaderSearch title={t("common.lieuDung")} sort_key="lieuDungId" />
      ),
      width: "200px",
      dataIndex: "lieuDungId",
      key: "lieuDungId",
      i18Name: "common.lieuDung",
      show: true,
      render: (item, data, index) => {
        return render("lieuDungId");
      },
    },

    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.gioBatDau")} />,
      width: 160,
      dataIndex: "gioBatDau",
      key: "gioBatDau",
      i18Name: "quanLyNoiTru.toDieuTri.gioBatDau",
      show: true,
      render: () => {
        return render("thoiGianBatDau");
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.tocDoTruyen")} />,
      width: 160,
      dataIndex: "tocDoTruyen",
      key: "tocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.tocDoTruyen",
      show: true,
      render: () => {
        return render("tocDoTruyen");
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.dvTocDoTruyen")} />,
      width: 160,
      dataIndex: "donViTocDoTruyen",
      key: "donViTocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.dvTocDoTruyen",
      show: true,
      render: () => {
        return render("donViTocDoTruyen");
      },
    },

    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.soGiot/ml")} />,
      width: 160,
      dataIndex: "soGiot",
      key: "soGiot",
      i18Name: "quanLyNoiTru.toDieuTri.soGiot/ml",
      show: true,
      render: () => {
        return render("soGiot");
      },
    },

    {
      title: <HeaderSearch title={t("quanLyNoiTru.toDieuTri.cachGio")} />,
      width: 160,
      dataIndex: "cachGio",
      key: "cachGio",
      i18Name: "quanLyNoiTru.toDieuTri.cachGio",
      show: true,
      render: () => {
        return render("cachGio");
      },
    },

    {
      title: <HeaderSearch title={t("common.cachDung")} />,
      width: "250px",
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
      show: true,
      render: (item, dataItem, index) => {
        return render("cachDung");
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.dotDung")} />,
      width: 80,
      dataIndex: "dotDung",
      key: "dotDung",
      i18Name: "khamBenh.donThuoc.dotDung",
      show: true,
      render: (item, data, index) => {
        return render("dotDung");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.thoiGianDung")}
          sort_key="ngayThucHien"
        />
      ),
      width: "250px",
      dataIndex: "ngayThucHien",
      key: "ngayThucHien",
      i18Name: "khamBenh.donThuoc.thoiGianDung",
      show: true,
      render: (item, data, index) => {
        return render("ngayThucHien");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.thoiGianThucHien")}
          sort_key="thoiGianThucHien"
        />
      ),
      width: "200px",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      show: true,
      i18Name: "common.thoiGianThucHien",
      render: (item, data, index) => {
        return render("thoiGianThucHien");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.thoiGianChiDinh")}
          sort_key="thoiGianChiDinh"
        />
      ),
      width: "200px",
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      show: true,
      i18Name: "cdha.thoiGianChiDinh",
      render: (item, data, index) => {
        return render("thoiGianChiDinh");
      },
    },
    {
      title: <HeaderSearch title={t("common.luuY")} />,
      width: "150px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      i18Name: "common.luuY",
      show: true,
      render: (item, data, index) => {
        return render("ghiChu");
      },
    },
    {
      title: <HeaderSearch title={t("common.soNgay")} />,
      width: "80px",
      dataIndex: "soNgay",
      key: "soNgay",
      i18Name: "common.soNgay",
      show: true,
      render: (item, dataItem, index) => {
        return render("soNgay");
      },
    },
    {
      title: <HeaderSearch title={t("common.lan/ngay")} />,
      width: "80px",
      dataIndex: "soLan1Ngay",
      key: "soLan1Ngay",
      i18Name: "common.lan/ngay",
      show: true,
      render: (item, dataItem, index) => {
        return render("soLan1Ngay");
      },
    },
    {
      title: <HeaderSearch title={t("common.sl/lan")} />,
      width: "80px",
      dataIndex: "soLuong1Lan",
      key: "soLuong1Lan",
      i18Name: "common.sl/lan",
      show: true,
      render: (item, dataItem, index) => {
        return render("soLuong1Lan");
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.thoiDiemDung")} />,
      width: "200px",
      dataIndex: "thoiDiem",
      key: "thoiDiem",
      i18Name: "khamBenh.donThuoc.thoiDiemDung",
      show: false,
      render: (item, dataItem, index) => {
        return render("thoiDiem");
      },
    },
    {
      title: (
        <HeaderSearch title={t("common.duongDung")} sort_key="duongDungId" />
      ),
      width: "200px",
      dataIndex: "duongDungId",
      key: "duongDungId",
      i18Name: "common.duongDung",
      show: true,
      render: (item, data, index) => {
        return render("duongDungId");
      },
    },
    {
      title: (
        <HeaderSearch title={t("danhMuc.nguonKhac")} sort_key="nguonKhacId" />
      ),
      width: "200px",
      dataIndex: "nguonKhacId",
      key: "nguonKhacId",
      i18Name: "danhMuc.nguonKhac",
      show: true,
      render: (item, data, index) => {
        return render("nguonKhacId");
      },
    },
    {
      title: <HeaderSearch title={t("common.tt20")} />,
      width: "150px",
      dataIndex: "tt20",
      key: "tt20",
      hidden: !(
        configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM ||
        !(
          configData.thongTinNguoiBenh?.doiTuong !== DOI_TUONG.BAO_HIEM ||
          state.isKeNgoai
        )
      ),
      i18Name: "common.tt20",
      show: true,
      render: (item, data, index) => {
        return render("tt20");
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      width: "50px",
      dataIndex: "tuTra",
      key: "tuTra",
      i18Name: "common.tuTra",
      show: true,
      hidden: !(
        configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM ||
        state.isTuTruc ||
        state.khoBhyt
      ),
      align: "center",
      render: (item, data, index) => {
        return render("tuTra");
      },
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      width: "100px",
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      i18Name: "common.khongTinhTien",
      show: true,
      hidden: !(
        configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM ||
        state.isTuTruc ||
        state.khoBhyt
      ),
      align: "center",
      render: (item, data, index) => {
        return render("khongTinhTien");
      },
    },
    {
      title: <HeaderSearch title={tenBuoiSang} />,
      width: "80px",
      dataIndex: "slSang",
      key: "slSang",
      i18Name: tenBuoiSang,
      show: true,
      render: (item, dataItem, index) => {
        return render("slSang");
      },
    },
    {
      title: <HeaderSearch title={tenBuoiChieu} />,
      width: "80px",
      dataIndex: "slChieu",
      key: "slChieu",
      i18Name: tenBuoiChieu,
      show: true,
      render: (item, dataItem, index) => {
        return render("slChieu");
      },
    },
    {
      title: <HeaderSearch title={tenBuoiToi} />,
      width: "80px",
      dataIndex: "slToi",
      key: "slToi",
      i18Name: tenBuoiToi,
      show: true,
      render: (item, dataItem, index) => {
        return render("slToi");
      },
    },
    {
      title: <HeaderSearch title={tenBuoiDem} />,
      width: "80px",
      dataIndex: "slDem",
      key: "slDem",
      i18Name: tenBuoiDem,
      show: true,
      render: (item, dataItem, index) => {
        return render("slDem");
      },
    },
  ];

  const dataSource = useMemo(() => {
    if (state.data) return [state.data];
    return [];
  }, [state.data]);

  return (
    <ModalTemplate
      ref={refModal}
      title={
        configData.isNgoaiTru ? (
          <div style={{ display: "flex", alignItems: "center" }}>
            {t("khamBenh.donThuoc.thongTinThuoc")}{" "}
            <Setting refTable={refSettings} />
          </div>
        ) : (
          t("khamBenh.donThuoc.thongTinThuoc")
        )
      }
      onCancel={onCancel}
      width={configData.isNgoaiTru ? "90%" : 900}
      actionLeft={<Button.QuayLai onClick={onOK(false)} />}
      actionRight={
        !isReadonlyDvNoiTru && (
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onOK(true)}
          >
            {t("common.luu")}
          </Button>
        )
      }
    >
      <Main>
        {configData.isNgoaiTru ? (
          <TableWrapper
            columns={columns}
            dataSource={dataSource}
            scroll={{ y: 700, x: 1500 }}
            ref={refSettings}
            tableName="table_KhamBenh_SuaThongTinThuoc"
            columnResizable={true}
          />
        ) : (
          <Row gutter={6}>
            <Col xs={11}>
              <FormItem label={t("common.tenThuoc")} name="tenDichVu">
                <Input value={state.data.tenDichVu} disabled />
              </FormItem>
            </Col>
            <Col xs={3} offset={1}>
              <FormItem label={t("common.soLuong")} name="soLuong">
                {render("soLuong")}
              </FormItem>
            </Col>
            <Col xs={3}>
              <FormItem
                label={t("quanLyNoiTru.toDieuTri.dvt")}
                name="tenDonViTinh"
              >
                {render("tenDonViTinh")}
              </FormItem>
            </Col>
            <Col xs={2}>
              <FormItem label={t("common.soNgay")} name="soNgay">
                {render("soNgay")}
              </FormItem>
            </Col>
            <Col xs={2}>
              <FormItem label={t("common.lan/ngay")} name="soLan1Ngay">
                {render("soLan1Ngay")}
              </FormItem>
            </Col>
            <Col xs={2}>
              <FormItem label={t("common.sl/lan")} name="soLuong1Lan">
                {render("soLuong1Lan")}
              </FormItem>
            </Col>
            <Col xs={isShowSoLuongDinhMuc ? 3 : 4}>
              <FormItem label={t("common.dvtSuDung")} name="tenDvtSuDung">
                {render("tenDvtSuDung")}
              </FormItem>
            </Col>

            <Col xs={isShowSoLuongDinhMuc ? 2 : 3}>
              <FormItem label={t("common.slSoCap")} name="soLuongSoCap">
                {render("soLuongSoCap")}
              </FormItem>
            </Col>
            {isShowSoLuongDinhMuc && (
              <Col xs={3}>
                <FormItem label={t("khamBenh.donThuoc.slDinhMuc")}>
                  {render("soLuongDinhMuc")}
                </FormItem>
              </Col>
            )}
            <Col xs={isShowSoLuongDinhMuc ? 3 : 4}>
              <FormItem label={t("kho.dvtSoCap")} name="tenDvtSoCap">
                {render("tenDvtSoCap")}
              </FormItem>
            </Col>
            <Col xs={12} offset={1}>
              <FormItem
                label={t("khamBenh.donThuoc.thoiDiemDung")}
                name="thoiDiem"
              >
                {render("thoiDiem")}
              </FormItem>
            </Col>
            <Col xs={11}>
              <FormItem label={t("common.lieuDung")} name="lieuDungId">
                {render("lieuDungId")}
              </FormItem>
            </Col>
            <Col xs={12} offset={1}>
              <FormItem label={t("common.cachDung")} name="cachDung">
                {render("cachDung")}
              </FormItem>
            </Col>
            <Col xs={11}>
              <FormItem label={t("common.luuY")} name="ghiChu">
                {render("ghiChu")}
              </FormItem>
            </Col>
            {configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM && (
              <>
                <Col xs={3} offset={1}>
                  <FormItem
                    label={t("quanLyNoiTru.toDieuTri.tocDoTruyen")}
                    name="tocDoTruyen"
                  >
                    <InputTimeout
                      type="number"
                      value={state.data.tocDoTruyen}
                      onChange={onChange("tocDoTruyen")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </FormItem>
                </Col>
                <Col xs={4}>
                  <FormItem
                    label={t("quanLyNoiTru.toDieuTri.dvTocDoTruyen")}
                    name="donViTocDoTruyen"
                  >
                    <Select
                      value={state.data.donViTocDoTruyen}
                      onChange={onChange("donViTocDoTruyen")}
                      data={listDonViTocDoTruyen}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </FormItem>
                </Col>
                <Col xs={3}>
                  <FormItem
                    label={t("quanLyNoiTru.toDieuTri.soGiot/ml")}
                    name="soGiot"
                    required={state.data.donViTocDoTruyen === 10}
                  >
                    <InputNumberField
                      allowNegative={false}
                      allowLeadingZeros={false}
                      value={state.data.soGiot}
                      onChange={onChange("soGiot")}
                      disabled={isReadonlyDvNoiTru}
                      isAllowed={({ floatValue }) => {
                        return floatValue !== undefined
                          ? floatValue > 0 && floatValue <= 100
                          : true;
                      }}
                    />
                    {state.data.donViTocDoTruyen === 10 &&
                      !state.data.soGiot && (
                        <div className="required">
                          {t("danhMuc.vuiLongNhapTitle", {
                            title: lowerFirst(
                              t("quanLyNoiTru.toDieuTri.soGiot/ml")
                            ),
                          })}
                        </div>
                      )}
                  </FormItem>
                </Col>
                <Col xs={2}>
                  <FormItem
                    label={t("quanLyNoiTru.toDieuTri.cachGio")}
                    name="cachGio"
                  >
                    <InputTimeout
                      value={state.data.cachGio}
                      onChange={onChange("cachGio")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </FormItem>
                </Col>

                <Col xs={3}>
                  <FormItem
                    label={t("quanLyNoiTru.toDieuTri.gioBatDau")}
                    name="thoiGianBatDau"
                  >
                    <TimeInput
                      value={state.data.thoiGianBatDau}
                      onChange={onChange("thoiGianBatDau")}
                      placeholder={t("common.chonThoiGian")}
                      disabled={isReadonlyDvNoiTru}
                      timeDelay={0}
                    />
                  </FormItem>
                </Col>
              </>
            )}
            <Col xs={8}>
              <FormItem label={t("common.duongDung")} name="duongDungId">
                {render("duongDungId")}
              </FormItem>
            </Col>
            <Col xs={3} offset={1}>
              <FormItem label={t("khamBenh.donThuoc.dotDung")} name="dotDung">
                {render("dotDung")}
              </FormItem>
            </Col>
            <Col xs={9}>
              <FormItem
                label={t("quanLyNoiTru.toDieuTri.thoiGianDung")}
                name="ngayThucHien"
              >
                {render("ngayThucHien")}
              </FormItem>
            </Col>

            {(configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM ||
              !(
                configData.thongTinNguoiBenh?.doiTuong !== DOI_TUONG.BAO_HIEM ||
                state.isKeNgoai
              )) && (
              <Col xs={11}>
                <FormItem label="TT20" name="mucDichId">
                  {render("tt20")}
                </FormItem>
              </Col>
            )}
            {configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM && (
              <Col xs={12} offset={1}>
                <FormItem
                  label={t("quanLyNoiTru.toDieuTri.dienGiaiLieuDung")}
                  name="dienGiaiLieuDung"
                >
                  <Input value={state.data.dienGiaiLieuDung} disabled />
                </FormItem>
              </Col>
            )}
            {/*  */}
            <Col xs={11}>
              <FormItem
                label={t("vatTu.thoiGianThucHien")}
                name="thoiGianThucHien"
              >
                {render("thoiGianThucHien")}
              </FormItem>
            </Col>

            <Col xs={3} offset={1}>
              <FormItem label={t("common.slHuy")} name="soLuongHuy">
                {render("soLuongHuy")}
              </FormItem>
            </Col>
            <Col xs={9}>
              <FormItem label={t("common.lyDoHuy")} name="lyDoHuy">
                {render("lyDoHuy")}
              </FormItem>
            </Col>
            {/*  */}
            <Col xs={11}>
              <FormItem
                label={t("hsba.thoiGianChiDinh")}
                name="thoiGianChiDinh"
              >
                {render("thoiGianChiDinh")}
              </FormItem>
            </Col>
            {(configData.chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM ||
              state.isTuTruc ||
              state.khoBhyt) && (
              <>
                <Col xs={4} offset={1}>
                  <FormItem
                    label={t("common.ngaySDThuoc")}
                    name="sttNgaySuDung"
                  >
                    {render("sttNgaySuDung")}
                  </FormItem>
                </Col>
                <Col xs={3}>
                  <FormItem
                    name="tuTra"
                    valuePropName="checked"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      paddingLeft: "10px",
                    }}
                  >
                    <Checkbox
                      checked={state.data.tuTra}
                      onChange={(e) => onChange("tuTra")(e.target.checked)}
                      disabled={isReadonlyDvNoiTru}
                      // disabled={listMucDichSuDung.length}
                    >
                      {t("common.tuTra")}
                    </Checkbox>
                  </FormItem>
                </Col>
                <Col xs={5}>
                  <FormItem
                    name="khongTinhTien"
                    valuePropName="checked"
                    style={{ display: "flex", alignItems: "center" }}
                  >
                    <Checkbox
                      // disabled={listMucDichSuDung.length}
                      disabled={
                        isReadonlyDvNoiTru ||
                        !checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                        ])
                      }
                      checked={state.data.khongTinhTien}
                      onChange={(e) =>
                        onChange("khongTinhTien")(e.target.checked)
                      }
                    >
                      {t("common.khongTinhTien")}{" "}
                    </Checkbox>
                  </FormItem>
                </Col>
              </>
            )}
            <Col xs={5}>
              <FormItem label={tenBuoiSang} name="slSang">
                {render("slSang")}
              </FormItem>
            </Col>
            <Col xs={6}>
              <FormItem label={tenBuoiChieu} name="slChieu">
                {render("slChieu")}
              </FormItem>
            </Col>
            <Col xs={6} offset={1}>
              <FormItem label={tenBuoiToi} name="slToi">
                {render("slToi")}
              </FormItem>
            </Col>
            <Col xs={6}>
              <FormItem label={tenBuoiDem} name="slDem">
                {render("slDem")}
              </FormItem>
            </Col>
            <Col xs={11}>
              <FormItem label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                {render("nguonKhacId")}
              </FormItem>
            </Col>
            <Col xs={3} offset={1}>
              <FormItem
                name="dotXuat"
                valuePropName="checked"
                style={{
                  display: "flex",
                  alignItems: "center",
                  paddingLeft: "10px",
                }}
              >
                <Checkbox
                  checked={state.data.dotXuat}
                  onChange={(e) => onChange("dotXuat")(e.target.checked)}
                  disabled={isReadonlyDvNoiTru || !!state?.data?.soPhieuLinh}
                >
                  {t("kho.dotXuat")}
                </Checkbox>
              </FormItem>
            </Col>
            <Col xs={5}>
              <FormItem
                name="boSung"
                valuePropName="checked"
                style={{ display: "flex", alignItems: "center" }}
              >
                <Checkbox
                  checked={state.data.boSung}
                  onChange={(e) => onChange("boSung")(e.target.checked)}
                  disabled={isReadonlyDvNoiTru || !!state?.data?.soPhieuLinh}
                >
                  {t("kho.boSung")}
                </Checkbox>
              </FormItem>
            </Col>
            <div className="thoi-gian-ket-thuc">
              {(state.data?.dsThoiGianKetThuc || [])
                .map(
                  (item, idx) =>
                    `Giờ kết thúc lần ${idx + 1}: ${moment(item).format(
                      "DD/MM/YYYY HH:mm:ss"
                    )}`
                )
                .join(". ")}
            </div>
          </Row>
        )}
      </Main>
      <ModalThemLieuDung ref={refModalThemLieuDung} />
    </ModalTemplate>
  );
};

export default forwardRef(SuaThongTinThuoc);
