import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useRef,
  createRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Input, message } from "antd";
import { useFillMaHoSo, useFillMaNb, useWindowSize } from "hooks";
import { useHuongDieuTri } from "pages/khamBenh/hooks/useHuongDieuTri";
import {
  HeaderSearch,
  Select,
  InputTimeout,
  TableWrapper,
  Pagination,
  Checkbox,
  DatePicker,
} from "components";
import { TRANG_THAI_KHAM_BN } from "../../configs";
import {
  ENUM,
  HOTKEY,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  YES_NO,
  ROLES,
} from "constants/index";
import DateDropdown from "./DateDropdown";
import ModalTemplate from "../ModalTemplate";
import {
  useConfirm,
  useEnum,
  useGuid,
  useLazyKVMap,
  useListAll,
  useStore,
  useThietLap,
} from "hooks";
import { SVG } from "assets";
import isArray from "lodash/isArray";
import { Main, SearchDate } from "./styled";
import { refConfirm } from "app";
import classNames from "classnames";
import { checkKhamKLKhacBacSi } from "pages/khamBenh/utils";
import { checkRole } from "lib-utils/role-utils";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

const { Setting } = TableWrapper;

const listLoaiThoiGian = [
  { id: "ngayDangKy", i18n: "khamBenh.ngayDangKy" },
  { id: "ngayThucHiem", i18n: "khamBenh.ngayThucHienKham" },
];

let refDangKhamError = createRef();
export const ModalDanhSachBN = forwardRef((props, ref) => {
  const { formatMaNb, testMaNb } = useFillMaNb();
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const layerId = useGuid();
  const refInputNhapSoKham = useRef(null);
  const refShowDate = useRef(null);

  const refModal = useRef(null);
  const refSettings = useRef(null);
  const windowSize = useWindowSize();
  const { formatMaHoSo } = useFillMaHoSo();

  const [state, _setState] = useState({
    show: false,
    data: {},
    tuThoiGianThucHien: moment().format("DD/MM/YYYY"),
    denThoiGianThucHien: moment().format("DD/MM/YYYY"),
    // các biến có chữ moment sinh ra để dùng cho case search date từ ds.
    // Dùng để disabled và set lại giá trị nếu bộ lọc bị xoá
    tuThoiGianVaoVienMoment: moment()
      .startOf("date")
      .format("DD/MM/YYYY HH:mm:ss"),
    denThoiGianVaoVienMoment: moment()
      .endOf("date")
      .format("DD/MM/YYYY HH:mm:ss"),
    tuThoiGianThucHienMoment: moment()
      .startOf("date")
      .format("DD/MM/YYYY HH:mm:ss"),
    denThoiGianThucHienMoment: moment()
      .endOf("date")
      .format("DD/MM/YYYY HH:mm:ss"),
    localPhongThucHienId: null,
    loaiThoiGian: "ngayThucHiem",
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    nbKhamBenh: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData: updateDataNbKhamBenh,
    },
    khamBenh: {
      kiemTraTrangThaiLoadNguoiBenh,
      boQuaKham,
      updateData,
      dangKham,
      kiemTraDieuKienThanhToan,
    },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    goiSo: { kiemTraNbDaDenLuot },
  } = useDispatch();

  const { listHuongDieuTriKham, getHuongDieuTri } = useHuongDieuTri({
    callApi: state.show,
  });

  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [dataPageSize] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE);
  const [listPhanLoaiDoiTuong] = useEnum(ENUM.PHAN_LOAI_DOI_TUONG);
  const [listPhanLoaiNBCapCuu] = useEnum(ENUM.PHAN_LOAI_NB_CAP_CUU);
  const [listTrangThaiXacNhanBaoHiem] = useEnum(
    ENUM.TRANG_THAI_XAC_NHAN_BAO_HIEM
  );
  const [getPhanLoaiDoiTuong] = useLazyKVMap(listPhanLoaiDoiTuong);
  const [getPhanLoaiNBCapCuu] = useLazyKVMap(listPhanLoaiNBCapCuu);
  const [getTrangThaiXacNhanBaoHiem] = useLazyKVMap(
    listTrangThaiXacNhanBaoHiem
  );

  const [KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM] = useThietLap(
    THIET_LAP_CHUNG.KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM,
    "FALSE"
  );
  const [dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TRANG_THAI_XAC_NHAN_BHYT
  );
  const [dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN
  );
  const [dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH] = useThietLap(
    THIET_LAP_CHUNG.KHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH,
    "FALSE"
  );
  const [dataTEKMEDI_ON_OFF] = useThietLap(THIET_LAP_CHUNG.TEKMEDI_ON_OFF);
  const [dataCHI_HIEN_THI_STT_KHAM] = useThietLap(
    THIET_LAP_CHUNG.CHI_HIEN_THI_STT_KHAM
  );
  const isHienThiSttKham = dataCHI_HIEN_THI_STT_KHAM?.eval();

  const { nhanVienId, full_name } = useStore(
    "auth.auth",
    {},
    { fields: "full_name, nhanVienId" }
  );
  const {
    listData,
    totalElements,
    page,
    size,
    dataSearch,
    dataSortColumn,
    phongThucHienId,
    khoaNhapVienId,
  } = useSelector((state) => state.nbKhamBenh);
  const [listAllKhoa] = useListAll("khoa", {}, state.show);
  const [listAllNhanVien] = useListAll("nhanVien", {}, state.show);
  const [listAllPhong] = useListAll("phong", {}, true);
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const { infoNb, dangKhamError, listPhongKham } = useSelector(
    (state) => state.khamBenh
  );
  const [getPhongKhamById] = useLazyKVMap(listPhongKham || []);
  const [getKhoaById] = useLazyKVMap(listAllKhoa);

  useEffect(() => {
    if (
      dangKhamError &&
      (refDangKhamError.current != dangKhamError?.message ||
        [8332, 8340].includes(dangKhamError.code))
    ) {
      refDangKhamError.current = dangKhamError?.message;
      let msgContent = dangKhamError?.message;
      if (dangKhamError?.code === 8340) {
        msgContent += ` .${t(
          "khamBenh.deTiepTucThucHienKhamBenhVuiLongChonDongY"
        )}`;
      }
      showConfirm(
        {
          title: t("common.thongBao"),
          content: msgContent,
          cancelText: t("common.dong"),
          // cancelText:
          //   dangKhamError?.code === 8332 ? t("common.huy") : t("common.dong"),
          okText: t("common.dongY"),
          showBtnOk: dangKhamError?.code !== 8332, //ẩn với lỗi vượt quá số lượng người bệnh khám tối đa 1 trong ngày, theo thiết lập SL_NB_KHAM_TOI_DA_1_NGAY
        },
        () => {
          dangKham({
            id: dangKhamError.id,
            boQuaSlKhamToiDa: true,
          }).then((s) => {
            updateData({ dangKhamError: "" });
          });
        },
        () => {
          updateData({ dangKhamError: "" });
        }
      );
    }
  }, [dangKhamError]);

  useEffect(() => {
    renderData();
  }, [listData]);

  const renderData = () => {
    let data = listData.map((item) => {
      let age =
        item.thangTuoi <= 36
          ? ` - ${item.tuoi2}`
          : item.tuoi
          ? ` - ${item.tuoi} ${t("common.tuoi")}`
          : "";
      return {
        ...item,
        mucHuongTheBhyt: item.mucHuongBhyt,
        thongTin: `${item.tenNb}${age}${
          item.tenTinhThanhPho ? " - " + item.tenTinhThanhPho : ""
        }`,
      };
    });
    setState({ data });
  };

  useImperativeHandle(ref, () => ({
    show: (option = {}) => {
      const {
        search = false,
        timKiem = "",
        soPhieu = "",
        maHoSo,
        tenNb,
        maThe,
        maNb,
      } = option;
      refModal.current.show({});
      setState({
        // all: false,
        show: true,
        timKiem: timKiem.trim(),
        tenNb: tenNb,
        maHoSo: maHoSo,
        maNb,
        maThe,
        // tuThoiGianVaoVien: moment().format("DD/MM/YYYY"),
        // denThoiGianVaoVien: moment().format("DD/MM/YYYY"),
        localPhongThucHienId: isArray(phongThucHienId)
          ? phongThucHienId.map((item) => Number(item))
          : phongThucHienId
          ? Number(phongThucHienId)
          : null,
        thoiGianVaoVien: null,
        thoiGianThucHien: null,
      });
      const param = {
        stt2: state.stt2,
        maNb: state.maNb,
        maHoSo: state.maHoSo,
        tenNb: state.tenNb,
        doiTuong: state.doiTuong,
        tenDichVu: state.tenDichVu,
        bacSiKhamId: state.bacSiKhamId,
        dsTrangThai: state.dsTrangThai,
        huongDieuTri: state.huongDieuTri,
        dsPhongThucHienId: phongThucHienId,
        dsKhoaNhapVienId: state.dsKhoaNhapVienId,
        uuTien: state.uuTien,
        dsPhanLoaiDoiTuong: state.dsPhanLoaiDoiTuong,
      };
      const data = {
        ...param,
        ignoreDataSearch: true,
        size: dataPageSize || size || 10,
        dsTrangThaiHoan: [0, 10],
        dsTrangThaiNbKskBoQua: [10],
        khongThucHien: false,
        ...(search
          ? {
              tenNb: tenNb,
              maHoSo: maHoSo,
              maNb,
              soPhieu: soPhieu,
              maThe: maThe,
            }
          : {
              tenNb: "",
              maNb: "",
              maHoSo: "",
              soPhieu: "",
              maThe: "",
            }),
        ...(dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN?.eval()
          ? { dieuKienThanhToan: true }
          : {}),
      };
      if (search) {
        onSearchThongTin({
          ...data,
        });
      } else {
        onSearchThongTin({
          ...data,
          tuThoiGianThucHien: state.tuThoiGianThucHien
            ? `${state.tuThoiGianThucHien} 00:00:00`
            : undefined,
          denThoiGianThucHien: state.denThoiGianThucHien
            ? `${state.denThoiGianThucHien} 23:59:59`
            : undefined,
          tuThoiGianVaoVien: state.tuThoiGianVaoVien
            ? `${state.tuThoiGianVaoVien} 00:00:00`
            : undefined,
          denThoiGianVaoVien: state.denThoiGianVaoVien
            ? `${state.denThoiGianVaoVien} 23:59:59`
            : undefined,
        });
      }
      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F6, //F6
            onEvent: () => {
              refInputNhapSoKham.current && refInputNhapSoKham.current.focus();
            },
          },
        ],
      });
    },
  }));

  useEffect(() => {
    if (!state.show) {
      onRemoveLayer({ layerId: layerId });
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  // useEffect(() => {
  //   if (state.show) {
  //     setTimeout(() => {
  //       refTenNguoiBenh.current.setValue(state.tenNb);
  //       refMaHoSo.current.setValue(state.maHoSo);
  //     }, [1000]);
  //   }
  // }, [state.show, state.tenNb, state.maHoSo]);

  const onSetNbTiepTheo = (record, chuyenTrangThai) => {
    return new Promise((resolve, reject) => {
      kiemTraTrangThaiLoadNguoiBenh({
        data: record,
        dichVuId: record.id,
        nbDotDieuTriId: record.nbDotDieuTriId,
        chuyenTrangThai,
        nbId: record.id,
      })
        .then((s) => {
          //nếu load thành công thì update lại phòng thực hiện theo nb mới
          updateDataNbKhamBenh({
            phongThucHienId: record?.phongThucHienId,
          });
          resolve(true);
        })
        .catch((e) => {
          showConfirm(
            {
              showBtnOk: true,
              title: t("common.thongBao"),
              content: e,
              cancelText: t("common.dong"),
              okText: t("common.xacNhan"),
              typeModal: "warning",
            },
            () => {
              setState({ show: false });
              kiemTraTrangThaiLoadNguoiBenh({
                data: record,
                dichVuId: record.id,
                nbDotDieuTriId: record.nbDotDieuTriId,
                chuyenTrangThai,
                forceUpdate: true,
                nbId: record.id,
              })
                .then((s) => {
                  //nếu load thành công thì update lại phòng thực hiện theo nb mới
                  updateDataNbKhamBenh({
                    phongThucHienId: record?.phongThucHienId,
                  });
                  resolve(true);
                })
                .catch((e) => {
                  reject(e);
                });
            },
            () => {
              reject();
            }
          );
        });
    });
  };

  const onKiemTraNbDaDenSTT = async (record) => {
    if (
      dataTEKMEDI_ON_OFF?.eval() &&
      !checkRole([
        ROLES["KHAM_BENH"].BO_QUA_KHONG_BAT_BUOC_CHECK_NB_CHUA_DEN_STT,
      ]) &&
      (record.thoiGianVaoVien
        ? moment(record.thoiGianVaoVien).isSame(moment(), "day")
        : true)
    ) {
      debugger;
      const isDenLuot = await kiemTraNbDaDenLuot({
        maPhong: record?.maPhongThucHien,
        maHoSo: record?.maHoSo,
      });

      if (!isDenLuot) {
        refConfirm.current &&
          refConfirm.current.show({
            title: t("common.canhBao"),
            content: t("khamBenh.nbTenNbChuaDenLuotVaoPhong", {
              tenNb: record.tenNb,
            }),
            cancelText: t("common.quayLai"),
            classNameOkText: "button-warning",
            showBtnOk: false,
            typeModal: "warning",
          });
      }

      return isDenLuot;
    }

    return true;
  };

  const onRow = (record) => {
    return {
      onClick: async () => {
        if (
          !(await checkKhamKLKhacBacSi(
            record,
            nhanVienId,
            KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
          ))
        ) {
          message.error(
            t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
              bacSiKham: record.tenBacSiKham,
              bacSiHienTai: full_name,
            })
          );
          return;
        } else {
          let dieuKienThanhToan = true;

          if (
            [
              TRANG_THAI_DICH_VU.CHO_KHAM,
              TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM,
              TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
              TRANG_THAI_DICH_VU.BO_QUA,
              TRANG_THAI_DICH_VU.BO_QUA_KET_LUAN,
            ].includes(record.trangThai)
          ) {
            dieuKienThanhToan = await kiemTraDieuKienThanhToan(record.id);
          }

          if (
            [
              TRANG_THAI_DICH_VU.CHO_KHAM,
              TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
              TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM,
              TRANG_THAI_DICH_VU.BO_QUA,
            ].includes(record.trangThai)
          ) {
            if (!(await onKiemTraNbDaDenSTT(record))) {
              return;
            }
          }

          if (dieuKienThanhToan) {
            onSetNbTiepTheo(record, false).then((s) => {
              setState({ show: false });
            });
          } else {
            refConfirm.current &&
              refConfirm.current.show({
                title: t("common.canhBao"),
                content: t(
                  "khamBenh.canThanhToanDichVuDeTiepTucThucHienDichVu",
                  {
                    tenNb: record.tenNb,
                    tenDv: record.tenDichVu,
                  }
                ),
                cancelText: t("common.quayLai"),
                classNameOkText: "button-warning",
                showBtnOk: false,
                typeModal: "warning",
              });
          }
        }
      },
    };
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    if (key == "dsTrangThai") {
      if (value == 20) {
        value = [value, 30, 40];
      } else if (value == 100) {
        value = [value, 110, 120];
      }
      if (value == 50) {
        value = [value, 130];
      }
      onSearchThongTin({
        [key]: value,
        dsTrangThaiHoan: [0, 10],
      });
    } else if (key == "dsPhongThucHienId") {
      if (value) {
        setState({ localPhongThucHienId: value, dsPhongKhamId: null });
        onSearchThongTin({
          dsPhongThucHienId: value,
        });
      } else {
        const dsPhongKhamId = (listPhongKham || [])
          .map((item) => item?.id)
          ?.join(",");
        setState({ localPhongThucHienId: null, dsPhongKhamId });
        onSearchThongTin({
          dsPhongThucHienId: dsPhongKhamId,
        });
      }
    } else if (key == "dsPhanLoaiNbCapCuu") {
      onSearchThongTin({
        [key]: value?.length === listPhanLoaiNBCapCuu?.length ? null : value,
      });
      setState({ [key]: value });
    } else if (key === "maHoSo") {
      onSearchThongTin({
        [key]: formatMaHoSo(value.trim()),
        dsTrangThaiHoan: [0, 10],
      });
    } else if (key === "chuaKetThuc") {
      onSearchThongTin({
        chuaKetThuc: value,
        dsTrangThaiHoan: [0, 10],
        nbCapCuu: true,
      });
    } else if (key === "dsPhanLoaiNbId") {
      onSearchThongTin({
        [key]: value?.length === listAllPhanLoaiNB?.length ? null : value,
        dsTrangThaiHoan: [0, 10],
      });
    } else {
      onSearchThongTin({
        [key]: typeof value === "string" ? value.trim() : value,
        dsTrangThaiHoan: [0, 10],
      });
    }
    setState({ [key]: value });
  };

  const onSearchThongTin = ({ ...payload }) => {
    if (!listPhongKham?.length) return;

    let _addParams = {};
    if (!payload.dsPhongThucHienId && !state.localPhongThucHienId) {
      _addParams.dsPhongThucHienId = state.dsPhongKhamId;
    }

    //nếu search mã NB hoặc mã HS thì đánh dấu là mở popup confirm nếu NB ở phòng khác
    const isShowPopup = payload.maNb || payload.maHoSo;

    onChangeInputSearch({ ...payload, ..._addParams, isShowPopup }).then(
      (res) => {
        if (res.length) {
          const { tenNb, maHoSo, phongThucHienId } = res[0] || {};
          const _phong = listAllPhong.find(
            (item) => item.id == phongThucHienId
          );

          showConfirm(
            {
              title: t("common.thongBao"),
              content: t("khamBenh.nguoiBenhMaHoSoDangOphongKhoa", {
                tenNb,
                maHoSo,
                phong: _phong?.ten,
                khoa: _phong?.khoa,
              }),
              okText: t("common.dong"),
              showImg: false,
              showBtnOk: true,
              showBtnCancel: false,
              typeModal: "warning",
            },
            () => {}
          );
        }
      }
    );
  };

  const onClickBoQua = (record) => () => {
    boQuaKham({
      loadNbTiepTheo: false,
      id: record.id,
      trangThai: record.trangThai,
      isShowMessage: true,
    }).then((s) => {
      record.trangThai = s?.data?.nbDvKyThuat?.trangThai;
      setState({ data: [...state.data] });
    });
  };

  const onClickGoi = (record) => async (e) => {
    e.stopPropagation();

    if (
      !(await checkKhamKLKhacBacSi(
        record,
        nhanVienId,
        KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
      ))
    ) {
      message.error(
        t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
          bacSiKham: record.tenBacSiKham,
          bacSiHienTai: full_name,
        })
      );
      return;
    }

    if (
      [
        TRANG_THAI_DICH_VU.CHO_KHAM,
        TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
        TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM,
        TRANG_THAI_DICH_VU.BO_QUA,
      ].includes(record.trangThai)
    ) {
      if (!(await onKiemTraNbDaDenSTT(record))) {
        return;
      }
    }

    onSetNbTiepTheo(record, true).then((s) => {
      setState({ show: false });
    });
  };

  const renderGoiButton = (record) => {
    if (dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH?.eval())
      return null;
    const trangThai = record?.trangThai;
    switch (trangThai) {
      case TRANG_THAI_DICH_VU.CHO_KHAM:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KHAM:
      case TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM:
      case TRANG_THAI_DICH_VU.DA_CHECKIN_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHO_KET_LUAN:
      case TRANG_THAI_DICH_VU.BO_QUA:
      case TRANG_THAI_DICH_VU.BO_QUA_KET_LUAN:
        //cho phép button Gọi nhấn
        return (
          <div className="btn-action" onClick={onClickGoi(record)}>
            {t("khamBenh.goi")}
          </div>
        );
      case TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU:
      case TRANG_THAI_DICH_VU.DA_KET_LUAN:
      case TRANG_THAI_DICH_VU.DA_DUYET:
        //button gọi không cho phép
        return (
          <div disabled className="btn-action">
            {t("khamBenh.goi")}
          </div>
        );
      default:
        break;
    }
  };

  const renderBoQuaButton = (record) => {
    if (dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH?.eval())
      return null;
    const trangThai = record?.trangThai;
    switch (trangThai) {
      case TRANG_THAI_DICH_VU.DANG_KHAM:
      case TRANG_THAI_DICH_VU.CHO_KHAM:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KHAM:
      case TRANG_THAI_DICH_VU.CHO_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN:
      case TRANG_THAI_DICH_VU.DANG_KET_LUAN:
        //cho phép button Gọi nhấn
        return (
          <div className="btn-action" onClick={onClickBoQua(record)}>
            {t("common.boQua")}
          </div>
        );
      default:
        break;
    }
  };

  const isNgayDangKy = state.loaiThoiGian === "ngayDangKy";

  const onSearchDate = (key) => (value) => {
    const tu = value ? value.format("DD/MM/YYYY 00:00:00") : null;
    const den = value ? value.format("DD/MM/YYYY 23:59:59") : null;

    setState({ [key]: value });

    onSearchThongTin({
      ...(key === "thoiGianThucHien"
        ? {
            tuThoiGianThucHien: tu ?? state?.tuThoiGianThucHienMoment,
            denThoiGianThucHien: den ?? state?.denThoiGianThucHienMoment,
          }
        : {
            tuThoiGianVaoVien: tu ?? state?.tuThoiGianVaoVienMoment,
            denThoiGianVaoVien: den ?? state?.denThoiGianVaoVienMoment,
          }),
      dsTrangThaiHoan: [0, 10],
    });
  };

  const onChangeLoaiThoiGian = (value) => {
    if (value === state.loaiThoiGian) return;

    const isNgayDangKy = value === "ngayDangKy";

    setState({
      loaiThoiGian: value,
      tuThoiGianVaoVien: isNgayDangKy ? state.tuThoiGianThucHien : null,
      denThoiGianVaoVien: isNgayDangKy ? state.denThoiGianThucHien : null,
      tuThoiGianThucHien: isNgayDangKy ? null : state.tuThoiGianVaoVien,
      denThoiGianThucHien: isNgayDangKy ? null : state.denThoiGianVaoVien,
      tuThoiGianVaoVienMoment: isNgayDangKy
        ? state.tuThoiGianThucHienMoment
        : null,
      denThoiGianVaoVienMoment: isNgayDangKy
        ? state.denThoiGianThucHienMoment
        : null,
      tuThoiGianThucHienMoment: isNgayDangKy
        ? null
        : state.tuThoiGianVaoVienMoment,
      denThoiGianThucHienMoment: isNgayDangKy
        ? null
        : state.denThoiGianVaoVienMoment,
      thoiGianVaoVien: null,
      thoiGianThucHien: null,
    });

    onSearchThongTin({
      tuThoiGianVaoVien: isNgayDangKy
        ? state?.tuThoiGianThucHienMoment
        : undefined,
      denThoiGianVaoVien: isNgayDangKy
        ? state?.denThoiGianThucHienMoment
        : undefined,
      tuThoiGianThucHien: isNgayDangKy
        ? undefined
        : state?.tuThoiGianVaoVienMoment,
      denThoiGianThucHien: isNgayDangKy
        ? undefined
        : state?.denThoiGianVaoVienMoment,
      dsTrangThaiHoan: [0, 10],
    });
  };

  const onSelectedDate = (e) => {
    const tu = isNgayDangKy ? e.tuThoiGianVaoVien : e.tuThoiGianThucHien;
    const den = isNgayDangKy ? e.denThoiGianVaoVien : e.denThoiGianThucHien;

    setState({
      all: e.all,
      ...(isNgayDangKy
        ? {
            tuThoiGianVaoVien: tu?.format("DD/MM/YYYY") || "",
            denThoiGianVaoVien: den?.format("DD/MM/YYYY") || "",
            tuThoiGianVaoVienMoment: tu?.format("DD/MM/YYYY 00:00:00") || "",
            denThoiGianVaoVienMoment: den?.format("DD/MM/YYYY 23:59:59") || "",
            tuThoiGianThucHien: null,
            denThoiGianThucHien: null,
            tuThoiGianThucHienmoment: null,
            denThoiGianThucHienmoment: null,
          }
        : {
            tuThoiGianThucHien: tu?.format("DD/MM/YYYY") || "",
            denThoiGianThucHien: den?.format("DD/MM/YYYY") || "",
            tuThoiGianThucHienMoment: tu?.format("DD/MM/YYYY 00:00:00") || "",
            denThoiGianThucHienMoment: den?.format("DD/MM/YYYY 23:59:59") || "",
            tuThoiGianVaoVien: null,
            denThoiGianVaoVien: null,
            tuThoiGianVaoVienMoment: null,
            denThoiGianVaoVienMoment: null,
          }),
    });

    onSearchThongTin({
      tuThoiGianVaoVien: isNgayDangKy
        ? tu?.format("DD/MM/YYYY 00:00:00")
        : undefined,
      denThoiGianVaoVien: isNgayDangKy
        ? den?.format("DD/MM/YYYY 23:59:59")
        : undefined,
      tuThoiGianThucHien: !isNgayDangKy
        ? tu?.format("DD/MM/YYYY 00:00:00")
        : undefined,
      denThoiGianThucHien: !isNgayDangKy
        ? den?.format("DD/MM/YYYY 23:59:59")
        : undefined,
      dsTrangThaiHoan: [0, 10],
    });
  };

  const onShowAll = () => {
    onSelectedDate({
      all: true,
      tuThoiGianVaoVien: null,
      denThoiGianVaoVien: null,
    });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} isTitleCenter={true} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.ngayDangKy")}
          sort_key="thoiGianVaoVien"
          dataSort={dataSortColumn["thoiGianVaoVien"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <DatePicker
              placeholder={t("common.chonNgay")}
              onChange={onSearchDate("thoiGianVaoVien")}
              value={state?.thoiGianVaoVien}
              disabledDate={(current) => {
                if (
                  !current ||
                  !state?.tuThoiGianVaoVienMoment ||
                  !state?.denThoiGianVaoVienMoment ||
                  state.loaiThoiGian !== "ngayDangKy"
                )
                  return false;

                const tu = moment(
                  state.tuThoiGianVaoVienMoment,
                  "DD/MM/YYYY HH:mm:ss"
                );
                const den = moment(
                  state.denThoiGianVaoVienMoment,
                  "DD/MM/YYYY HH:mm:ss"
                );

                return (
                  current.isBefore(tu.startOf("day")) ||
                  current.isAfter(den.endOf("day"))
                );
              }}
            />
          }
        />
      ),
      render: (item) => {
        return moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
      width: 150,
      show: true,
      i18Name: "khamBenh.dsBenhNhan.ngayDangKy",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.ngayThucHienKham")}
          sort_key="thoiGianThucHien"
          dataSort={dataSortColumn["thoiGianThucHien"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <DatePicker
              placeholder={t("common.chonNgay")}
              onChange={onSearchDate("thoiGianThucHien")}
              value={state?.thoiGianThucHien}
              disabledDate={(current) => {
                if (
                  !current ||
                  !state?.tuThoiGianThucHienMoment ||
                  !state?.denThoiGianThucHienMoment ||
                  state.loaiThoiGian !== "ngayThucHiem"
                )
                  return false;

                const tu = moment(
                  state.tuThoiGianThucHienMoment,
                  "DD/MM/YYYY HH:mm:ss"
                );
                const den = moment(
                  state.denThoiGianThucHienMoment,
                  "DD/MM/YYYY HH:mm:ss"
                );

                return (
                  current.isBefore(tu.startOf("day")) ||
                  current.isAfter(den.endOf("day"))
                );
              }}
            />
          }
        />
      ),
      render: (item) => {
        return moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
      width: 150,
      show: false,
      i18Name: "khamBenh.ngayThucHienKham",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.soKham")}
          sort_key={isHienThiSttKham ? "stt" : "stt2"}
          dataSort={dataSortColumn[isHienThiSttKham ? "stt" : "stt2"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              ref={refInputNhapSoKham}
              placeholder={t("khamBenh.dsBenhNhan.nhapSoKham")}
              onChange={onSearchInput("stt2")}
              value={state?.stt2}
            />
          }
        />
      ),
      width: 100,
      dataIndex: isHienThiSttKham ? "stt" : "stt2",
      key: isHienThiSttKham ? "stt" : "stt2",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.soKham",
      align: "center",
      render: (field, record) => {
        return (
          <>
            {field}
            {record?.khamSucKhoe && " - SKTQ"}
          </>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.maNb")}
          sort_key="maNb"
          dataSort={dataSortColumn["maNb"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              timeDelay={0}
              placeholder={t("khamBenh.dsBenhNhan.nhapMaNb")}
              onChange={(e) => {
                setState({ maNb: e });

                if (!e) {
                  onSearchInput("maNb")(e);
                  return;
                }

                if (testMaNb(e, true)) {
                  onSearchInput("maNb")(formatMaNb(e));
                }
              }}
              value={state?.maNb}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  onSearchInput("maNb")(formatMaNb(state.maNb));
                }
              }}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "maNb",
      key: "maNb",
      show: true,
      i18Name: "common.maNb",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          dataSort={dataSortColumn["maHoSo"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("khamBenh.dsBenhNhan.nhapMaHoSo")}
              onChange={onSearchInput("maHoSo")}
              value={state?.maHoSo}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "maHoSo",
      key: "maHoSo",
      show: true,
      i18Name: "common.maHoSo",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.tenTuoiDiaChi")}
          sort_key="tenNb"
          dataSort={dataSortColumn["tenNb"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("khamBenh.dsBenhNhan.nhapTenTuoiDiaChi")}
              onChange={onSearchInput("tenNb")}
              value={state?.tenNb}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "thongTin",
      key: "thongTin",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.tenTuoiDiaChi",
      render: (item) => <div style={{ fontWeight: "bold" }}>{item}</div>,
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.phanLoaiNb")}
          sort_key="dsPhanLoaiNbId"
          dataSort={dataSortColumn["dsPhanLoaiNbId"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("common.phanLoaiNb")}
              onChange={onSearchInput("dsPhanLoaiNbId")}
              data={listAllPhanLoaiNB || []}
              dropdownMatchSelectWidth={250}
              value={state?.dsPhanLoaiNbId}
              mode="multiple"
            />
          }
        />
      ),
      width: 100,
      dataIndex: "dsPhanLoaiNbId",
      key: "dsPhanLoaiNbId",
      show: true,
      i18Name: "common.phanLoaiNb",
      render: (item) => {
        return (
          <ListPhanLoaiNguoiBenh value={item} listAll={listAllPhanLoaiNB} />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.doiTuong")}
          sort_key="doiTuong"
          dataSort={dataSortColumn["doiTuong"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("khamBenh.dsBenhNhan.doiTuong")}
              onChange={onSearchInput("doiTuong")}
              data={listDoiTuong || []}
              dropdownMatchSelectWidth={250}
              value={state?.doiTuong}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "doiTuong",
      key: "doiTuong",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.doiTuong",
      render: (item) => {
        return listDoiTuong.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.tenDichVu")}
          sort_key="tenDichVu"
          dataSort={dataSortColumn["tenDichVu"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("khamBenh.dsBenhNhan.nhapTenDichVu")}
              onChange={onSearchInput("tenDichVu")}
              value={state?.tenDichVu}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      show: true,
      i18Name: "common.tenDichVu",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.tenBacSiKham")}
          sort_key="tenBacSiKham"
          dataSort={dataSortColumn["bacSiKhamId"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("khamBenh.dsBenhNhan.tenBacSiKham")}
              onChange={onSearchInput("bacSiKhamId")}
              data={listAllNhanVien}
              dropdownMatchSelectWidth={200}
              value={state?.bacSiKhamId}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "tenBacSiKham",
      key: "tenBacSiKham",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.tenBacSiKham",
      render: (value, record) => `${record.hocHamHocVi || ""} ${value || ""}`,
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.trangThai")}
          sort_key="trangThai"
          dataSort={dataSortColumn["trangThai"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("common.chonTrangThai")}
              onChange={onSearchInput("dsTrangThai")}
              data={TRANG_THAI_KHAM_BN}
              defaultValue=""
              dropdownMatchSelectWidth={200}
              hasAllOption={true}
              value={state?.dsTrangThai}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "trangThai",
      key: "trangThai",
      show: true,
      i18Name: "common.trangThai",
      render: (item) => {
        const res = listTrangThaiDichVu.find((el) => el.id === item) || {};
        return res.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.huongDieuTri")}
          sort_key="huongDieuTri"
          dataSort={dataSortColumn["huongDieuTri"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("khamBenh.dsBenhNhan.huongDieuTri")}
              onChange={onSearchInput("huongDieuTri")}
              data={listHuongDieuTriKham}
              dropdownMatchSelectWidth={250}
              value={state?.huongDieuTri}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "huongDieuTri",
      key: "huongDieuTri",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.huongDieuTri",
      render: (item) => {
        return getHuongDieuTri(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.phongThucHien")}
          sort_key="phongThucHienId"
          dataSort={dataSortColumn["phongThucHienId"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("common.chonPhongKham")}
              onChange={onSearchInput("dsPhongThucHienId")}
              data={listPhongKham || []}
              value={state.localPhongThucHienId}
              dropdownMatchSelectWidth={320}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "phongThucHienId",
      key: "phongThucHienId",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.phongThucHien",
      render: (item) => {
        return getPhongKhamById(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.khoaNhapVien")}
          sort_key="khoaNhapVienId"
          dataSort={dataSortColumn["khoaNhapVienId"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("khamBenh.dsBenhNhan.chonKhoaNhapVien")}
              onChange={onSearchInput("dsKhoaNhapVienId")}
              data={listAllKhoa}
              value={khoaNhapVienId}
              dropdownMatchSelectWidth={320}
              valueNumber
            />
          }
        />
      ),
      width: 150,
      dataIndex: "khoaNhapVienId",
      key: "khoaNhapVienId",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.khoaNhapVien",
      render: (item) => {
        return getKhoaById(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.uuTien")}
          sort_key="uuTien"
          dataSort={dataSortColumn["uuTien"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              data={YES_NO}
              defaultValue=""
              onChange={onSearchInput("uuTien")}
              placeholder={t("khamBenh.dsBenhNhan.uuTien")}
              hasAllOption={true}
              value={state?.uuTien}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "uuTien",
      key: "uuTien",
      align: "center",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.uuTien",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("tiepDon.phanLoaiDoiTuong")}
          searchSelect={
            <Select
              placeholder={t("tiepDon.phanLoaiDoiTuong")}
              onChange={onSearchInput("dsPhanLoaiDoiTuong")}
              data={listPhanLoaiDoiTuong}
              dropdownMatchSelectWidth={150}
              mode="multiple"
              value={state?.dsPhanLoaiDoiTuong}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "phanLoaiDoiTuong",
      key: "phanLoaiDoiTuong",
      show: true,
      i18Name: "tiepDon.phanLoaiDoiTuong",
      render: (item) => {
        return getPhanLoaiDoiTuong(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu")}
          searchSelect={
            <Select
              placeholder={t("khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu")}
              onChange={onSearchInput("dsPhanLoaiNbCapCuu")}
              data={listPhanLoaiNBCapCuu}
              dropdownMatchSelectWidth={180}
              mode="multiple"
              value={state?.dsPhanLoaiNbCapCuu}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "phanLoaiNbCapCuu",
      key: "phanLoaiNbCapCuu",
      show: true,
      i18Name: "khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu",
      className: "phanLoaiNbCapCuu",
      render: (item) => {
        return getPhanLoaiNBCapCuu(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("hsba.chanDoanChinh")} />
      ),
      width: 150,
      dataIndex: "cdChinh",
      key: "cdChinh",
      align: "left",
      show: true,
      i18Name: "hsba.chanDoanChinh",
    },
    {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("hsba.chanDoanKemTheo")} />
      ),
      width: 150,
      dataIndex: "cdKemTheo",
      key: "cdKemTheo",
      align: "left",
      show: true,
      i18Name: "hsba.chanDoanKemTheo",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.chanDoan.chanDoanSoBo")}
        />
      ),
      width: 150,
      dataIndex: "cdSoBo",
      key: "cdSoBo",
      align: "left",
      show: true,
      i18Name: "khamBenh.chanDoan.chanDoanSoBo",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.chanDoan.moTaChiTiet")}
        />
      ),
      width: 150,
      dataIndex: "moTa",
      key: "moTa",
      align: "left",
      show: true,
      i18Name: "khamBenh.chanDoan.moTaChiTiet",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.chuaLinhThuoc")}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("baoCao.chonGiaTri")}
              onChange={onSearchInput("tonTaiThuocChuaLinh")}
              hasAllOption={true}
              value={state.tonTaiThuocChuaLinh}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "tonTaiThuocChuaLinh",
      key: "tonTaiThuocChuaLinh",
      align: "center",
      show: true,
      i18Name: "khamBenh.chuaLinhThuoc",
      render: (item) => {
        return <Checkbox checked={!!item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("thuNgan.xacNhanBhyt")}
          searchSelect={
            <Select
              data={listTrangThaiXacNhanBaoHiem}
              placeholder={t("baoCao.chonGiaTri")}
              onChange={onSearchInput("dsTrangThaiXacNhanBh")}
              hasAllOption={true}
              value={state.dsTrangThaiXacNhanBh}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "trangThaiXacNhanBh",
      key: "trangThaiXacNhanBh",
      show: dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      hidden: !dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      i18Name: "thuNgan.xacNhanBhyt",
      render: (item) => {
        return getTrangThaiXacNhanBaoHiem(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.nguoiXacNhanBhyt")}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenNguoiXacNhan")}
              value={state?.tenNguoiXacNhan}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "tenNguoiXacNhan",
      key: "tenNguoiXacNhan",
      show: dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      hidden: !dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      i18Name: "khamBenh.nguoiXacNhanBhyt",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.nbCapCuuChuaKetThuc")}
          sort_key="chuaKetThuc"
          dataSort={dataSortColumn["chuaKetThuc"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              data={YES_NO}
              defaultValue=""
              onChange={onSearchInput("chuaKetThuc")}
              placeholder={t("khamBenh.dsBenhNhan.nbCapCuuChuaKetThuc")}
              hasAllOption={true}
              value={state?.chuaKetThuc}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "chuaKetThuc",
      key: "chuaKetThuc",
      align: "center",
      show: true,
      i18Name: "khamBenh.dsBenhNhan.nbCapCuuChuaKetThuc",
      render: (item, record) => {
        return <Checkbox checked={record.nbCapCuu && record.chuaKetThuc} />;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.ngaySinh")}
          sort_key="ngaySinh"
          dataSort={dataSortColumn["ngaySinh"] || 0}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      align: "center",
      show: true,
      i18Name: "common.ngaySinh",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY");
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={
            <>
              {t("common.thaoTac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "100px",
      dataIndex: "action",
      key: "action",
      fixed: "right",
      render: (item, record) => {
        return (
          <div className="action-group">
            {renderGoiButton(record)}
            {renderBoQuaButton(record)}
          </div>
        );
      },
    },
  ];

  const handleChangePage = (page) => {
    if (!listPhongKham?.length) return;
    onSearch({ page: page - 1, dsPhongThucHienId: state.dsPhongKhamId });
  };

  const handleSizeChange = (size) => {
    if (!listPhongKham?.length) return;
    onSizeChange({ size: size, dataSortColumn, dataSearch });
  };

  const rowClassName = (record) => {
    return classNames("", {
      active: record.id === infoNb?.id,
      "phan-loai-khan-cap": record.phanLoaiNbCapCuu == 10, //Ưu tiên 1 (Khẩn cấp)
      "phan-loai-cap-cuu": record.phanLoaiNbCapCuu == 20, //Ưu tiên 2 (Cấp cứu)
      "phan-loai-cap-cuu-tri-hoan": record.phanLoaiNbCapCuu == 30, //Ưu tiên 3 (Cấp cứu trì hoãn)
      "phan-loai-khong-cap-cuu-45": record.phanLoaiNbCapCuu == 40, //Ưu tiên 4 (Không cấp cứu < 45')
      "phan-loai-khong-cap-cuu-1h": record.phanLoaiNbCapCuu == 60, //Ưu tiên 5 (Không cấp cứu < 1 giờ)
    });
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={"98%"}
      layerId={layerId}
      title={
        <>
          {t("khamBenh.titleDanhSachNguoiBenh")}
          <Select
            style={{
              fontSize: 14,
              fontStyle: "normal",
              fontWeight: 600,
              marginLeft: 24,
              marginRight: 10,
              width: 200,
            }}
            data={listLoaiThoiGian}
            onChange={onChangeLoaiThoiGian}
            value={state.loaiThoiGian}
            allowClear={true}
          />
          <SearchDate>
            <DateDropdown
              onSelectedDate={onSelectedDate}
              ref={refShowDate}
              keyFrom={
                isNgayDangKy ? "tuThoiGianVaoVien" : "tuThoiGianThucHien"
              }
              keyTo={
                isNgayDangKy ? "denThoiGianVaoVien" : "denThoiGianThucHien"
              }
            />
            <Input
              className="filter"
              value={
                state.all
                  ? t("common.tatCa")
                  : `${
                      state[
                        isNgayDangKy
                          ? "tuThoiGianVaoVien"
                          : "tuThoiGianThucHien"
                      ]
                    } - ${
                      state[
                        isNgayDangKy
                          ? "denThoiGianVaoVien"
                          : "denThoiGianThucHien"
                      ]
                    }`
              }
              onChange={null}
              onClick={() => refShowDate.current.show()}
            />
            {!state.all ? (
              <SVG.IcCancel
                style={{
                  position: "absolute",
                  right: 5,
                  top: 6,
                  width: 20,
                  height: 20,
                }}
                onClick={onShowAll}
              />
            ) : (
              <SVG.IcCalendar
                style={{
                  position: "absolute",
                  right: 5,
                  top: 6,
                  width: 20,
                  height: 20,
                }}
              />
            )}
          </SearchDate>
        </>
      }
      onCancel={() => {
        setState({
          show: false,
        });
      }}
    >
      <Main>
        <TableWrapper
          rowClassName={rowClassName}
          columns={columns}
          dataSource={state.data}
          onRow={onRow}
          scroll={{ y: 450, x: 2000 }}
          rowKey={(record) => `${record.id}-${record.tenNb}`}
          styleWrap={{
            height: Math.min(
              (state.data || []).length * 60 + 120,
              windowSize.height * 0.725
            ),
            minHeight: 260,
          }}
          ref={refSettings}
          tableName="table_KHAMBENH_ModalDsBenhNhan"
        />
        {!!totalElements && (
          <Pagination
            listData={state.data}
            onChange={handleChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            onShowSizeChange={handleSizeChange}
          />
        )}
      </Main>
    </ModalTemplate>
  );
});

export default ModalDanhSachBN;
