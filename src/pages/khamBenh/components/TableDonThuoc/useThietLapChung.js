import { useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";

const useThietLapChung = () => {
  const [BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT
  );
  const [TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
  );
  const [TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC
  );
  const [dataTINH_SL_TONG_THEO_SL_BUOI] = useThietLap(
    THIET_LAP_CHUNG.TINH_SL_TONG_THEO_SL_BUOI,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_SANG] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_SANG,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_CHIEU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_CHIEU,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_TOI] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_TOI,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_DEM] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_DEM,
    ""
  );

  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );

  const [dataKHONG_HIEN_THI_CHECKBOX_DOT_XUAT_KHI_KE_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_CHECKBOX_DOT_XUAT_KHI_KE_TU_TRUC
  );

  return {
    isTinhSLTongTheoSLBuoi:
      dataTINH_SL_TONG_THEO_SL_BUOI?.toUpperCase() === "TRUE",
    dataThoiGianVaTenBuoiChieu: dataTHOI_GIAN_VA_TEN_BUOI_CHIEU.split("/"),
    dataThoiGianVaTenBuoiToi: dataTHOI_GIAN_VA_TEN_BUOI_TOI.split("/"),
    dataThoiGianVaTenBuoiSang: dataTHOI_GIAN_VA_TEN_BUOI_SANG.split("/"),
    dataThoiGianVaTenBuoiDem: dataTHOI_GIAN_VA_TEN_BUOI_DEM.split("/"),
    isBatBuocLieuDungCachDungThuocBHYT:
      BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT.toLowerCase() === "true",
    isTuDongCapNhatSoNgayChoDonThuoc:
      TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true",
    isTuDongLamTronSoLuongKeThuoc:
      TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true",
    isHienThiCheckboxTuTruc: dataHIEN_THI_CHECKBOX_TU_TRUC?.eval(),
    isKhongHienThiCheckBoxDotXuatKhiKeTuTruc:
      dataKHONG_HIEN_THI_CHECKBOX_DOT_XUAT_KHI_KE_TU_TRUC?.eval(),
  };
};

export default useThietLapChung;
