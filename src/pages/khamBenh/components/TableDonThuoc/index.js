import React, { useState, useEffect, useRef, useMemo } from "react";
import { Menu, message, Row, Select as SelectAntd } from "antd";
import {
  Checkbox,
  Button,
  TableWrapper,
  Select,
  Pagination,
  HeaderSearch,
  Tooltip,
  SplitPanel,
  InputTimeout,
  Dropdown,
  DatePicker,
  TimeInput,
} from "components";
import CircleCheck from "assets/images/khamBenh/circle-check.png";
import { GlobalStyle, WrapperInput, WrapperSelect } from "./styled";
import { Main } from "../TableThuocKeNgoai/styled";
import {
  cloneDeep,
  debounce,
  find,
  flatten,
  isNil,
  isNumber,
  orderBy,
  some,
  uniq,
} from "lodash";
import {
  containText,
  isArray,
  openInNewTab,
  roundNumberPoint,
  sortString,
} from "utils/index";
import { useSelector, useDispatch } from "react-redux";
import ModalThemLieuDung from "../../DonThuoc/ModalThemLieuDung";
import lieuDungProvider from "data-access/categories/dm-lieu-dung-provider";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useEnum,
  useLazyKVMap,
  useListAll,
  useStore,
  useThietLap,
} from "hooks";
import stringUtils from "mainam-react-native-string-utils";
import {
  DOI_TUONG,
  LOAI_DON_THUOC,
  LOAI_DICH_VU,
  LIST_THOI_DIEM_DUNG,
  ENUM,
  LOAI_CHI_DINH,
  THIET_LAP_CHUNG,
  DOI_TUONG_KCB,
  CO_CHE_DUYET_PHAT,
  ROLES,
} from "constants/index";
import { DEFAULT_POPUP_CHI_DINH_THUOC } from "pages/application/TuyChinhGiaoDienPhamMem/KhamBenh/config";
import {
  evalString,
  tinhSoLuongV2,
  tinhSoNgayV2,
} from "utils/chi-dinh-thuoc-utils";
import { SVG } from "assets";
import useThuocDaKe from "./useThuocDaKe";
import {
  getCachDung,
  renderEmptyTextLeftTable,
  renderPhaChungColumn,
  EMPTY_TEXT,
  TOOLTIP_LUUY,
  expressionSymbol,
  renderSoTien,
} from "./utils";
import useThietLapChung from "./useThietLapChung";
import useManHinh from "./useManHinh";
import classNames from "classnames";
import moment from "moment";
import useRegisterHotkey from "./hooks/useRegisterHotkey";
import usePageSize from "./hooks/usePageSize";
import useGetDataThuoc from "./hooks/useGetDataThuoc";
import { LEFT_COLUMNS, RIGHT_COLUMNS } from "./hooks/useColumns";
import { checkRole } from "lib-utils/role-utils";
import { lazyGetRecordByKey } from "redux-store/selectors";
import { VN_SEVERITY_RANKING } from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc/vnConstants";
import ModalCanhBaoTrungDuocLy from "../ModalCanhBaoTrungDuocLy";

const { Setting } = TableWrapper;
const { Option } = SelectAntd;

const TableDonThuoc = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const {
    onSelected,
    thanhTien,
    khoId,
    loaiDonThuoc,
    visible,
    layerId,
    keyword,
    theoSoLuongTonKho,
    nbDotDieuTriId,
    onSetData,
    modeThuoc,
    isExam = true,
    phanNhomDvKhoId,
    splitCacheCustomize,
    onResizeSplit = () => {},
    listToDieuTri = [],
    onChangeConfig = () => {},
    tenHoatChat,
    isShowSoLuongDinhMuc = false,
    notShowModal = false,
    activeHotKey = true,
    tongTien,
    loaiChiDinh = null,
    ngayThucHienTu = null,
    ngayThucHienDen = null,
    isKeThuocRaVienTheoTungNgay = false,
    isShowing,
    dungKemId,
    dsKho,
    isTuTruc = false,
    rawMimsData,
    getColorTuongTacThuoc,
    isDisabledThemDungKem = false,
    showTuNgayDenNgay = false,
  } = props;
  const { listDvThuoc, listDvThuocNhaThuoc, listDvThuocDaKe } = useThuocDaKe({
    visible,
    isExam,
    loaiDonThuoc,
  });
  const isNoiTru =
    window.location.pathname.indexOf(
      "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru"
    ) >= 0;
  const [dataCANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY
  );
  const [dataBAT_BUOC_DIEN_LY_DO_CHI_DINH_THUOC_TRUNG_HOAT_CHAT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_DIEN_LY_DO_CHI_DINH_THUOC_TRUNG_HOAT_CHAT
  );
  const [dataKET_NOI_MIMS] = useThietLap(THIET_LAP_CHUNG.KET_NOI_MIMS);

  const {
    isTinhSLTongTheoSLBuoi,
    dataThoiGianVaTenBuoiSang,
    dataThoiGianVaTenBuoiChieu,
    dataThoiGianVaTenBuoiToi,
    dataThoiGianVaTenBuoiDem,
    isBatBuocLieuDungCachDungThuocBHYT,
    isTuDongCapNhatSoNgayChoDonThuoc,
    isTuDongLamTronSoLuongKeThuoc,
    isHienThiCheckboxTuTruc,
    isKhongHienThiCheckBoxDotXuatKhiKeTuTruc,
  } = useThietLapChung();

  const {
    isKhamBenh,
    isNoiTruToDieuTri,
    isPttt,
    isCDHA,
    isNoiTruDonThuocRaVien,
  } = useManHinh({
    modeThuoc,
    loaiDonThuoc,
  });

  const { page, size, totalElements } = usePageSize({ loaiDonThuoc });

  const refModalThemLieuDung = useRef(null);
  const refInput = useRef(null);
  const refSettings = useRef(null);
  const refSettingsLeft = useRef(null);
  const refSelectLieuDung = useRef(null);
  const refBtnThemNhanh = useRef(null);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const refLieuDung = useRef({});
  const refSelectRow = useRef(null);
  const refAddService = useRef(null);
  const refSplitPanelLeft = useRef(null);
  const refSplitPanelRight = useRef(null);
  const showMessLessThan0Ref = useRef(null);
  const refModalCanhBaoTrungDuocLy = useRef(null);
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [getDonViTocDoTruyen] = useLazyKVMap(listDonViTocDoTruyen);
  const [dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM] =
    useThietLap(
      THIET_LAP_CHUNG.CHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM
    );
  const { listDvTonKhoNhaThuoc, listDvTonKho } = useSelector(
    (state) => state.chiDinhDichVuKho
  );
  const { boChiDinh } = useSelector((state) => state.boChiDinh);
  const {
    lieuDung: { createOrEdit: createOrEditLieuDung },
    lieuDungThuoc: { create: createOrEditLieuDungThuoc },
  } = useDispatch();
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllLieuDung, reloadListAllLieuDung] = useListAll(
    "lieuDung",
    {},
    true
  );

  const configData = useStore("chiDinhKhamBenh.configData", {});
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const listThietLapChonKho = useStore(
    "thietLapChonKho.listThietLapChonKhoThuoc"
  );
  const listDvTiepDon = useStore("chiDinhKhamBenh.listDvTiepDon", []);

  const nbThongTinRaVien = useStore("nbDotDieuTri.nbThongTinRaVien", {});
  const [dataCHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY] = useThietLap(
    THIET_LAP_CHUNG.CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY
  );
  const { nbKetLuan } = thongTinChiTiet || {};

  const isValidateLieuDungCachDung =
    thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
    isBatBuocLieuDungCachDungThuocBHYT &&
    loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO &&
    configData.chiDinhTuLoaiDichVu !== LOAI_DICH_VU.PHAU_THUAT_THU_THUAT;

  const maxSoNgay =
    modeThuoc == "DonThuocRaVien" &&
    loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO &&
    thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
    dataCHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY?.eval()
      ? 7
      : undefined;

  const isLamTronLen = useMemo(() => {
    if (
      (isPttt || isCDHA || isNoiTruToDieuTri || isNoiTruDonThuocRaVien) &&
      loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
    )
      return false;
    return true;
  }, [isPttt, isCDHA, isNoiTruToDieuTri, loaiDonThuoc]);

  const checkIsLamTronLenKhamBenh = (record) => {
    if (isKhamBenh) {
      let currentKho = (dsKho || []).find((i) => i.id == khoId);
      return (
        isTuDongLamTronSoLuongKeThuoc &&
        record.dvtSuDungId == record.donViTinhId &&
        (loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO
          ? (currentKho?.dsCoCheDuyetPhat || []).includes(
              CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
            )
          : true)
      );
    }
    return true;
  };

  const listAllBoChiDinh = useMemo(() => {
    const listAllBoChiDinh = (boChiDinh?.data || []).map((item) => {
      // item.id = item.dichVuId;
      return item;
    });
    return listAllBoChiDinh;
  }, [boChiDinh]);
  const [state, _setState] = useState({
    listServiceSelected: [],
    elementKey: 1,
    keySelected: [],
    dataSelected: [],
    boChiDinhSelected: null,
    msgWarned: [],
    openLieuDung: {},
    expandedKeys: [],
    toDieuTriSelectedId: null,
  });
  const setState = (data) => {
    _setState((pre) => ({
      ...pre,
      ...data,
    }));
  };

  useEffect(() => {
    //khi tắt hoặc bật thì mặc đinh reset popup
    setState({
      selectedRowKeys: [],
      listServiceSelected: [],
      dataSelected: [],
      msgWarned: [],
      keySelected: [],
      keyword: "",
      phanNhomDvKhoId: null,
      boChiDinhSelected: null,
      ma: null,
      dichVuCha: null,
      isKeThuocDungKem: false,
    });
  }, [loaiDonThuoc, visible]);

  useEffect(() => {
    if (
      configData.isTuVanThuoc &&
      listToDieuTri &&
      listToDieuTri.length > 0 &&
      !state.toDieuTriSelectedId
    ) {
      onChangeConfig({
        chiDinhTuDichVuId: listToDieuTri[0]?.id,
        khoaChiDinhId: listToDieuTri[0]?.khoaChiDinhId,
      });

      setState({ toDieuTriSelectedId: listToDieuTri[0].id });
    }
  }, [listToDieuTri, configData]);

  const { debounceFunc, onChangePage, onSizeChange } = useGetDataThuoc({
    nbDotDieuTriId,
    isHienThiCheckboxTuTruc,
    loaiDonThuoc,
    khoId,
    dsKho,
    phanNhomDvKhoId,
    theoSoLuongTonKho,
    tenHoatChat,
    boChiDinhId: state.boChiDinhSelected?.id,
    isNoiTruToDieuTri,
    keyword,
    size,
  });

  const isKhoDuyetPhatNgayKhiKe = useMemo(() => {
    if (khoId && listThietLapChonKho) {
      const _selectKho = (listThietLapChonKho || []).find((x) => x.id == khoId);
      if (_selectKho) {
        //Nếu kho có cơ chế duyệt phát là Duyệt phát ngay khi kê (20) thì mặc định hiển thị cột SL tồn thứ cấp, ẩn cột SL tồn Sơ cấp
        return (_selectKho.dsCoCheDuyetPhat || []).includes(20);
      }
    }
    return false;
  }, [listThietLapChonKho, khoId]);

  const dataKey = (() => {
    switch (true) {
      case isKhamBenh:
        return "khamBenh.popupChiDinhThuocKhamBenh";
      case isNoiTruToDieuTri:
        return "noiTru.popupChiDinhThuocNoiTru";
      case isCDHA:
        return "cdha.popupChiDinhThuocCdha";
      case isPttt:
        return "phauThuat.popupChiDinhThuocPttt";
      default:
        break;
    }
  })();

  const dataThietLap = useStore(
    `thietLap.thietLapGiaoDien.${dataKey}`,
    DEFAULT_POPUP_CHI_DINH_THUOC
  );

  useRegisterHotkey({
    layerId,
    visible,
    activeHotKey,
    refAddService,
    refSelectRow,
    refInput,
  });

  const showLuuY = useMemo(() => {
    const isTuTra = (state.dataSelected || []).some((x) => x.tuTra);
    const isThuocNhaThuoc = loaiDonThuoc === LOAI_DON_THUOC.NHA_THUOC;

    return isTuTra || isThuocNhaThuoc;
  }, [state.dataSelected, loaiDonThuoc]);

  useEffect(() => {
    if (visible) {
      debounceFunc(
        keyword,
        state.boChiDinhSelected?.id,
        theoSoLuongTonKho,
        phanNhomDvKhoId
      );
    }
  }, [
    keyword,
    phanNhomDvKhoId,
    visible,
    khoId,
    state.boChiDinhSelected,
    theoSoLuongTonKho,
  ]);

  const { isCheckAllDotXuat, isCheckAllBoSung } = useMemo(() => {
    if (!isArray(state.dataSelected, true)) {
      return { isCheckAllDotXuat: false, isCheckAllBoSung: false };
    }
    return {
      isCheckAllDotXuat: state.dataSelected.every((i) => i.dotXuat),
      isCheckAllBoSung: state.dataSelected.every((i) => i.boSung),
    };
  }, [state.dataSelected]);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (dataSource?.findIndex((item) => item.key === state?.key) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < dataSource.length) {
      setState({ key: dataSource[indexNextItem]?.key });
      document
        .getElementsByClassName(
          "table-row-odd " + dataSource[indexNextItem]?.key
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const onSelectChangeLeft = (selectedRowKeys, data, msg) => {
    let selectedRowKeysService = data;
    // khi search thì selected data bị mất giá trị soLuong
    // => xử lý giữ lại giá trị số lượng đã nhập
    data.forEach((item1) => {
      state.dataSelected.forEach((item2) => {
        if (
          item1.dichVuId &&
          item2.dichVuId &&
          item1.dichVuId == item2.dichVuId &&
          item1.key === item2.key
        ) {
          item1.soLuong = item2.soLuong;
          item1.lieuDungId = item2.lieuDungId;
        }
      });
    });

    //tạo biến lưu giá trị state cũ
    const oldSelectedRowKeys = cloneDeep(state.selectedRowKeys);
    const oldListServiceSelected = cloneDeep(state.listServiceSelected);
    const oldKeySelected = cloneDeep(state.keySelected);
    const oldDataSelected = cloneDeep(state.dataSelected);
    const oldMsgWarned = cloneDeep(state.msgWarned);

    setState({
      selectedRowKeys: selectedRowKeys,
      listServiceSelected: data,
      keySelected: selectedRowKeys,
      dataSelected: data,
      msgWarned: msg ? [...state.msgWarned, msg] : state.msgWarned,
    });
    onSelected(selectedRowKeysService, false, () => {
      //case có lỗi => reset lại giá trị cũ
      setState({
        selectedRowKeys: oldSelectedRowKeys,
        listServiceSelected: oldListServiceSelected,
        keySelected: oldKeySelected,
        dataSelected: oldDataSelected,
        msgWarned: oldMsgWarned,
      });
    });
  };

  const onSelectChangeRight = (selectedRowKeys, data, item) => {
    setState({
      listServiceSelected: data,
      keySelected: selectedRowKeys,
      dataSelected: data,
    });
    // onSelectedNoPayment(selectedRowKeysService);
    onSelected(data);
  };

  const onCheckAllLoaiChiDinh = (type, isCheck) => (e) => {
    let data = cloneDeep(state.dataSelected);
    let otherKey = type === "dotXuat" ? "boSung" : "dotXuat";
    if (isCheck) {
      data = data.map((item) => ({
        ...item,
        [type]: true,
        [otherKey]: false,
      }));
    } else {
      data = data.map((item) => ({
        ...item,
        [type]: false,
      }));
    }
    setState({ dataSelected: data });
    onSelected(data);
  };

  const onShowCanhBaoKeTrung = ({
    objDupplicate = [],
    objDupplicateHoatChat = [],
    selectedRowKeys = [],
    listServiceSelected = [],
    objDupplicateThuocKho = [],
    onSelectChangeLeft = () => {},
    isKeBoChiDinh,
  }) => {
    let messThuoc = null;
    let data = [];
    if (objDupplicate?.length) {
      messThuoc = t("khamBenh.chiDinh.tenThuoc").replace(
        "{ten_thuoc}",
        objDupplicate[0].tenDichVu
      );
      data = objDupplicate;
    } else if (objDupplicateHoatChat?.length) {
      messThuoc =
        `${t("common.hoatChat")} ` +
        t("khamBenh.chiDinh.tenThuoc").replace(
          "{ten_thuoc}",
          objDupplicateHoatChat[0].tenHoatChat
        );
      data = objDupplicateHoatChat;
    }
    const messCanhBao =
      isNoiTru && dataCANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()
        ? _.uniq(
            data.map((x) =>
              t("khamBenh.chiDinh.canhBaoKeTrungThuocTaiKhoa")
                .replace("{ten_bac_si}", x.tenBacSiChiDinh)
                .replace("{so_luong}", x.soLuong)
                .replace("{dvt}", x.tenDonViTinh)
                .replace("{tenKhoa}", x.tenKhoaChiDinh)
            )
          ).join(", ")
        : _.uniq(
            data.map((x) =>
              t("khamBenh.chiDinh.canhBaoKeTrungThuoc")
                .replace("{ten_bac_si}", x.tenBacSiChiDinh)
                .replace("{so_luong}", x.soLuong)
                .replace("{dvt}", x.tenDonViTinh)
            )
          ).join(", ");
    const msg = `${messThuoc} ${messCanhBao}`;
    let _listServiceSelected = listServiceSelected;
    if (isKeBoChiDinh) {
      _listServiceSelected = listServiceSelected.filter(
        (item) => !item.dungKemBoChiDinhChiTietId
      );
      let listDichVuDungKem = listServiceSelected.filter(
        (item) => item.dungKemBoChiDinhChiTietId
      );
      const expandedKeys = [];
      _listServiceSelected = _listServiceSelected.map((item, index) => {
        expandedKeys.push(item.key);
        item.children = listDichVuDungKem
          .filter(
            (item2) =>
              item2.dungKemBoChiDinhChiTietId === item.boChiDinhChiTietId
          )
          .map((x) => {
            x.thuocDungKem = true;
            x.parentKey = item.key;
            return x;
          });
        return item;
      });
      setState({
        expandedKeys: [...state?.expandedKeys, ...expandedKeys],
      });
    }

    if (!state.msgWarned.includes(msg)) {
      const duocChiDinhCungThuoc = () => {
        let currentKho = (dsKho || []).find((i) => i.id == khoId);
        return (
          dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM?.eval() &&
          [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
            DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
          ]?.includes(thongTinNguoiBenh?.doiTuongKcb) &&
          loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO &&
          (currentKho?.dsCoCheDuyetPhat || []).includes(
            CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
          ) &&
          isKhamBenh &&
          isArray(objDupplicateThuocKho, true)
        );
      };

      showConfirm(
        {
          title: t("common.canhBao"),
          content: `${msg}. ${
            !duocChiDinhCungThuoc() ? t("khamBenh.chiDinh.tiepTuc") : ""
          }`,
          cancelText: t("common.huy"),
          okText: t("common.xacNhan"),
          showImg: false,
          showBtnOk: duocChiDinhCungThuoc() ? false : true,
          typeModal: "warning",
        },
        () => {
          onSelectChangeLeft(selectedRowKeys, _listServiceSelected, msg);
        }
      );
    } else {
      onSelectChangeLeft(selectedRowKeys, _listServiceSelected);
    }
  };

  const onSelectLeft = async (record, isSelected) => {
    record = cloneDeep(record);
    if (!record.listLieuDung) {
      if (refLieuDung.current[record.id || record.dichVuId]) {
        //check trong ref có liều dùng cho dv này chưa, nếu có rồi thì lôi ra dùng
        //còn chưa có thì gọi api
        record.listLieuDung = refLieuDung.current[record.id || record.dichVuId];
      } else {
        const listLieuDung = await lieuDungProvider
          .searchAll({
            bacSiId: nhanVienId,
            dichVuId: record.id || record.dichVuId,
            page: "",
            size: "",
          })
          .then((s) => {
            return s?.data || [];
          });
        refLieuDung.current[record.id || record.dichVuId] = listLieuDung;
        record.listLieuDung = listLieuDung;
      }
    }
    let listServiceSelected = [];

    function onSelectLeftContinue() {
      let selectedRowKeys = [];

      listServiceSelected.forEach((item) => {
        selectedRowKeys.push(item.key);
        item?.children?.forEach((x) => {
          selectedRowKeys.push(x.key);
        });
      });

      const dsDichVuThuoc =
        isNoiTru && dataCANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()
          ? listDvTiepDon
          : [...listDvThuoc, ...listDvThuocNhaThuoc];

      if (dsDichVuThuoc?.length && isSelected) {
        //kiểm tra mở popup khi dịch vụ trùng
        let objDupplicate = dsDichVuThuoc.filter(
          (item1) =>
            item1.dichVuId === record.dichVuId || item1.key === record.key
        );
        let objDupplicateHoatChat = dsDichVuThuoc.filter(
          (item1) => record.maHoatChat && item1.maHoatChat === record.maHoatChat
        );
        let objDupplicateThuocKho = dsDichVuThuoc.filter((item1) => {
          let currentKho = (dsKho || []).find((i) => i.id == item1.khoId);
          return (
            (currentKho?.dsCoCheDuyetPhat || []).includes(
              CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
            ) &&
            (item1.dichVuId === record.dichVuId || item1.key === record.key)
          );
        });

        if (objDupplicate.length || objDupplicateHoatChat.length) {
          onShowCanhBaoKeTrung({
            objDupplicate,
            objDupplicateHoatChat,
            selectedRowKeys,
            listServiceSelected,
            onSelectChangeLeft,
            objDupplicateThuocKho,
          });
          return false;
        } else {
          onSelectChangeLeft(selectedRowKeys, listServiceSelected);
        }
      } else {
        onSelectChangeLeft(selectedRowKeys, listServiceSelected);
      }
    }

    if (isSelected) {
      //xử lý khi kê thuốc ko chọn kho
      if (isHienThiCheckboxTuTruc) {
        record.khoId = khoId || undefined; //lấy giá trị theo kho đang chọn
      }
      //nếu có loaiChiDinh => đang kê dv kèm theo => mặc định theo dv cha
      //nếu đang kê dv kèm theo isKeThuocDungKem = true => mặc định theo dv cha
      //mặc định loại chỉ định giống với line trước đó
      if (loaiChiDinh) {
        record.loaiChiDinh = loaiChiDinh;
      } else if (state.isKeThuocDungKem) {
        record.loaiChiDinh = state.dichVuCha.loaiChiDinh;
      } else if (state.dataSelected.length > 0) {
        record.loaiChiDinh =
          state.dataSelected[state.dataSelected.length - 1].loaiChiDinh;
      } else if (listDvThuoc.length > 0 && modeThuoc !== "DonThuocRaVien") {
        const _listDvThuocDaKe = orderBy(
          listDvThuoc,
          "thoiGianChiDinh",
          "desc"
        );
        record.loaiChiDinh = _listDvThuocDaKe[0].loaiChiDinh;
      }
      record.boSung = record.loaiChiDinh === LOAI_CHI_DINH.BO_SUNG;
      record.dotXuat = record.loaiChiDinh === LOAI_CHI_DINH.DOT_XUAT;

      //kiểm tra thuốc trùng với thuốc đã chọn
      let dupplicateDaChon = state.dataSelected.filter(
        (item1) => item1.dichVuId === record.dichVuId
      );

      let dupplicateDaChonHoatChat = state.dataSelected.filter(
        (item1) => record.maHoatChat && item1.maHoatChat === record.maHoatChat
      );
      let dupplicateDaChonDacTinhDuocLy = state.dataSelected.filter(
        (item1) =>
          record.dacTinhDuocLyId &&
          item1.dacTinhDuocLyId === record.dacTinhDuocLyId
      );

      function onCheckDuplicate() {
        if (
          !isNoiTruToDieuTri &&
          ((dupplicateDaChon && dupplicateDaChon.length > 0) ||
            dupplicateDaChonHoatChat.length ||
            dupplicateDaChonDacTinhDuocLy.length)
        ) {
          const duplicateSoLuong = dupplicateDaChon.reduce(
            (a, b) =>
              a + Number(b.heSoDinhMuc != 1 ? b.soLuongSoCap : b.soLuong),
            0
          );
          let mes = null;
          let type = null;
          //check trùng theo tên hoạt chất
          if (dupplicateDaChon.length) {
            type = "maHoatChat";
            mes = t(
              "khamBenh.{{hoatChat}}dangDuocKeChiDinh{{soLuong}}{{tenDvtSoCap}}",
              {
                hoatChat: dupplicateDaChon[0].ten,
                soLuong: duplicateSoLuong,
                tenDvtSoCap: dupplicateDaChon[0].tenDvtSoCap,
              }
            );
          } else if (dupplicateDaChonHoatChat.length) {
            type = "maHoatChat";
            mes = `${t("khamBenh.hoatChat{{tenHoatChat}}dangDuocChon", {
              tenHoatChat: dupplicateDaChonHoatChat[0].tenHoatChat,
            })}.\n${t("common.thuoc")}: ${dupplicateDaChonHoatChat[0].ten}`;
          } else if (dupplicateDaChonDacTinhDuocLy.length) {
            type = "dacTinhDuocLyId";
            mes = `${t(
              "khamBenh.dacTinhDuocLy{{tenDacTinhDuocLy}}dangDuocChon",
              {
                tenDacTinhDuocLy:
                  dupplicateDaChonDacTinhDuocLy[0].tenDacTinhDuocLy,
              }
            )}.\n${t("common.thuoc")}: ${dupplicateDaChonDacTinhDuocLy[0].ten}`;
          }

          if (
            dataBAT_BUOC_DIEN_LY_DO_CHI_DINH_THUOC_TRUNG_HOAT_CHAT?.eval() &&
            type &&
            !dataKET_NOI_MIMS?.eval()
          ) {
            refModalCanhBaoTrungDuocLy.current.show(
              {
                data: {
                  content: mes,
                  title: t("khamBenh.donThuoc.canhBaoTrungDuocLy"),
                },
              },
              ({ lyDoChiDinh }) => {
                record.lyDoChiDinh = lyDoChiDinh;
                let dataSelected = [
                  ...state.dataSelected.map((item) =>
                    item[type] === record[type]
                      ? { ...item, lyDoChiDinh }
                      : item
                  ),
                  record,
                ];
                let selectedRowKeys = [...state.selectedRowKeys, record.key];
                if (state.isKeThuocDungKem) {
                  onAddThuocDungKem();

                  dataSelected = state.dataSelected;
                  selectedRowKeys = state.selectedRowKeys;
                  setState({
                    expandedKeys: [...state.expandedKeys, state.dichVuCha.key],
                  });
                }

                onSelectChangeLeft(selectedRowKeys, dataSelected);
              }
            );
          } else {
            showConfirm(
              {
                title: t("common.canhBao"),
                content: `${mes}. <br/> ${t("khamBenh.chiDinh.tiepTuc")}`,
                cancelText: t("common.huy"),
                okText: t("common.xacNhan"),
                showImg: false,
                showBtnOk: true,
                typeModal: "warning",
              },
              () => {
                let dataSelected = [...state.dataSelected, record];
                let selectedRowKeys = [...state.selectedRowKeys, record.key];
                if (state.isKeThuocDungKem) {
                  onAddThuocDungKem();

                  dataSelected = state.dataSelected;
                  selectedRowKeys = state.selectedRowKeys;
                  setState({
                    expandedKeys: [...state.expandedKeys, state.dichVuCha.key],
                  });
                }

                onSelectChangeLeft(selectedRowKeys, dataSelected);
              },
              () => {}
            );
          }
        } else {
          record.key = record.key + stringUtils.guid();

          let objectFormatRecord = {
            ...record,
            soNgay: nbKetLuan?.soNgayChoDon
              ? nbKetLuan?.soNgayChoDon
              : modeThuoc == "DonThuocRaVien"
              ? nbThongTinRaVien?.soNgayChoDon
              : isNoiTruToDieuTri || isPttt || isCDHA
              ? 1
              : null,
            ...(isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay
              ? {
                  tuNgay: ngayThucHienTu,
                  denNgay: ngayThucHienDen,
                }
              : {}),
          };

          if (!(isNoiTruToDieuTri || isPttt || isCDHA)) {
            if (
              (record.heSoDinhMuc == 1 &&
                record.dvtSuDungId !== record.donViTinhId) ||
              (record.heSoDinhMuc != 1 &&
                record.donViTinhId != record.dvtSoCapId &&
                record.donViTinhId != record.dvtSuDungId &&
                record.dvtSoCapId != record.dvtSuDungId)
            ) {
              objectFormatRecord.soLuongSoCap = 1;
              objectFormatRecord.soLuong = 1;
            }
          }

          let dataSelected = [...state.dataSelected, objectFormatRecord];

          if (state.isKeThuocDungKem) {
            onAddThuocDungKem();

            dataSelected = state.dataSelected;
            setState({
              expandedKeys: [...state.expandedKeys, state.dichVuCha.key],
            });
          }
          listServiceSelected = dataSelected;

          onSelectLeftContinue();
        }
      }

      function onAddThuocDungKem() {
        const currentEditDv = state.dataSelected.find(
          (x) => x.key === state.dichVuCha.key
        );
        if (currentEditDv) {
          currentEditDv.children = [
            ...(currentEditDv.children || []),
            {
              ...record,
              thuocDungKem: true,
              parentKey: state.dichVuCha.key,
              soNgay: isNoiTruToDieuTri ? 1 : currentEditDv.soNgay ?? null,
            },
          ];
        }
      }

      //kiểm tra thuốc dấu sao
      if (record?.thuocDauSao && dupplicateDaChon?.length == 0) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t("khamBenh.{{tenThuoc}}laThuocDauSao", {
              tenThuoc: record?.ten,
            }),
            okText: t("common.dong"),
            showImg: false,
            showBtnCancel: false,
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {},
          () => {},
          () => {
            onCheckDuplicate();
          }
        );
      } else {
        onCheckDuplicate();
      }
    } else {
      listServiceSelected = state.dataSelected.filter(
        (item) => record.key !== item.key
      );

      onSelectLeftContinue();
    }
  };

  const onCheckAll = async (e) => {
    setState({
      selectedRowKeys: [],
      listServiceSelected: [],
      keySelected: [],
      dataSelected: [],
      isCheckAll: false,
    });
  };

  const onSelectRight = (record) => {
    if (record?.children?.length) {
      message.error(t("common.vuiLongXoaThuocDungDungKemTruoc"));
      return;
    }
    let listServiceSelected = state.dataSelected.filter(
      (item) => record.key !== item.key
    );
    let selectedRowKeys = state.selectedRowKeys.filter(
      (item) => item !== record.key
    );
    if (record?.thuocDungKem) {
      listServiceSelected.forEach((item) => {
        if (item.key === record.parentKey) {
          item.children = item?.children.filter((x) => x.key !== record.key);
        }
      });
      listServiceSelected = listServiceSelected;
    } else {
      setState({ isKeThuocDungKem: false });
    }

    onSelectChangeRight(selectedRowKeys, listServiceSelected);
  };

  const rowSelectionLeft = {
    columnTitle: <HeaderSearch title={t("common.chon")} />,
    columnWidth: 40,
    selectedRowKeys: [],
    preserveSelectedRowKeys: true,
    onSelect: onSelectLeft,
  };

  const rowSelectionRight = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onCheckAll} checked={true} />}
        isTitleCenter={true}
      />
    ),
    columnWidth: 40,
    // onChange: onSelectChangeRight,
    selectedRowKeys: state.keySelected,
    preserveSelectedRowKeys: true,
    onSelect: onSelectRight,
  };

  const showMessLessThan0 = (index, type) => {
    let tenCot =
      type === "soLan1Ngay" ? t("common.lan/ngay") : t("common.sl/lan");

    let value = state.dataSelected[index][type];
    if (value <= 0) {
      message.error(`${t("common.cot")} ${tenCot} ${t("common.phaiLonHon0")}`);
    }
  };

  showMessLessThan0Ref.current = showMessLessThan0;
  //xử lý tính lại số lượng + SL sơ cấp khi các field số ngày, số lần / ngày, số lượng / lần thay đổi

  const genarateSoLuong = (record, soLuong) => {
    if (isNil(soLuong)) return; // bỏ qua nếu giá trị soLuong là null hoặc undefined
    let currentKho = (dsKho || []).find((i) => i.id == khoId);
    if (record.heSoDinhMuc != 1) {
      const soLuongSoCap =
        isLamTronLen && checkIsLamTronLenKhamBenh(record)
          ? Math.ceil(soLuong / record.heSoDinhMuc)
          : roundNumberPoint(soLuong / record.heSoDinhMuc, 6); // nếu các màn khác thì lấy đến số thập phân thứ 2

      if (record.dvtSuDungId == record.donViTinhId) {
        record.soLuong = soLuong;
        record.soLuongSoCap = soLuongSoCap;
        if (isKhamBenh) {
          record.soLuong = soLuongSoCap * record.heSoDinhMuc;
        }
      } else {
        if (isKhamBenh) {
          record.soLuong = record.soLuong ? record.soLuong : 1;
          record.soLuongSoCap = record.soLuongSoCap ? record.soLuongSoCap : 1;
        }
      }
    } else {
      if (record.dvtSuDungId == record.donViTinhId) {
        const tuDongLamTron =
          isKhamBenh &&
          isTuDongLamTronSoLuongKeThuoc &&
          !(
            (currentKho?.dsCoCheDuyetPhat || []).includes(
              CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
            ) &&
            [
              DOI_TUONG_KCB.NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
            ]?.includes(thongTinNguoiBenh?.doiTuongKcb)
          );
        record.soLuong = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
        record.soLuongSoCap = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
      } else {
        if (isKhamBenh) {
          record.soLuong = record.soLuong ? record.soLuong : 1;
          record.soLuongSoCap = record.soLuongSoCap ? record.soLuongSoCap : 1;
        }
      }
    }

    record.soLuong = roundNumberPoint(record.soLuong, 6);
    record.soLuongSoCap = roundNumberPoint(record.soLuongSoCap, 6);
  };

  const onChangeInput = (type, index, data) => (e, list) => {
    let value = "";
    if (e?.target) {
      value = e.target.value;
    } else if (e?._d) value = e._d.format("MM/dd/yyyy");
    else value = e;

    if (
      ["tuTra", "khongTinhTien", "dotXuat", "boSung", "dungChoCon"].includes(
        type
      )
    ) {
      value = e.target.checked;
    }

    let dataSelect = cloneDeep(state.dataSelected);

    const _addIndex = listDvThuocDaKe.length;
    const _index = notShowModal ? index - _addIndex : index;
    let record = dataSelect[_index] || {};

    if (data.thuocDungKem) {
      record = dataSelect.find((x) => x.key === data.parentKey)?.children[
        _index
      ];
    }

    //convert soLuong, soLuongSoCap về dạng số
    if (
      (type === "soLuong" ||
        type === "soLuongSoCap" ||
        type === "soLuong1Lan") &&
      value
    ) {
      if (type === "soLuong1Lan") {
        //sử dụng biến này để lưu dạng phân số, ghép cho cách dùng
        record.soLuong1LanStr = value;
      }
      value = evalString(value);

      if (isNumber(value)) {
        value = roundNumberPoint(value, 6);
      }
    }
    //làm tròn lên giá trị số lượng / số lượng sơ cấp
    if (
      (type === "soLuong" || type === "soLuongSoCap") &&
      isLamTronLen &&
      checkIsLamTronLenKhamBenh(record) &&
      isNumber(value)
    ) {
      debugger;
      value = Math.ceil(value);
    }

    if (type === "soLuong") {
      if (Number(value) <= 0 && value) {
        record.soLuong = 0;
        message.error(t("khamBenh.donThuoc.nhapSoLuongLonHon0"));
      } else {
        record[type] = value;
        list?.callback && list?.callback(value);
        if (record.heSoDinhMuc == 1) {
          record.soLuongSoCap = value;
        }

        //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
        //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức
        if (
          (value &&
            record.heSoDinhMuc == 1 &&
            record.dvtSuDungId == record.donViTinhId) ||
          (record.heSoDinhMuc != 1 && isTuDongCapNhatSoNgayChoDonThuoc)
        ) {
          record.soNgay =
            tinhSoNgayV2({
              isTinhSLTongTheoSLBuoi,
              soLuong: value,
              soLuong1Lan: record.soLuong1Lan,
              soLan1Ngay: record.soLan1Ngay,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
              maxSoNgay: maxSoNgay,
            }) || record.soNgay;
        }
      }
    } else if (type == "dsPhaChungId") {
      record.dsPhaChungId = (list || [])
        .filter((item) => item.isThuocDaKe)
        .map((item) => item.id);
      record.dsPhaChung = (list || []).filter((item) => !item.isThuocDaKe);
    } else {
      // nếu type ko phải soLuong thì sẽ set trực tiếp giá trị vào record
      record[type] = value;
    }

    if (type === "tuTra" && value) {
      record["khongTinhTien"] = false;
    }

    if (type === "khongTinhTien" && value) {
      record["tuTra"] = false;
    }
    if (type === "dotXuat" && value) {
      record["boSung"] = false;
    }

    if (type === "boSung" && value) {
      record["dotXuat"] = false;
    }

    if (type === "lieuDungId" && list) {
      // nếu trong thiết lập ẩn trường đó đi thì trường đó = undefined

      columnsTableRightMemo.forEach((col) => {
        if (col?.dataIndex === "slSang") {
          record.slSang = list?.slSang;
        }
        if (col?.dataIndex === "slChieu") {
          record.slChieu = list?.slChieu;
        }
        if (col?.dataIndex === "slToi") {
          record.slToi = list?.slToi;
        }
        if (col?.dataIndex === "slDem") {
          record.slDem = list?.slDem;
        }
      });

      const soLuong1Lan = list?.soLuong1Lan || record.soLuong1Lan;
      const soLan1Ngay = list?.soLan1Ngay || record.soLan1Ngay;

      if (soLuong1Lan && soLan1Ngay) {
        const tenDuongDung = `${list?.tenDuongDung ? list?.tenDuongDung : ""}`;
        record.cachDung = getCachDung(
          t,
          tenDuongDung,
          soLan1Ngay,
          record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số,
          record.tenDvtSuDung,
          record.thoiDiem,
          record.slSang,
          record.slChieu,
          record.slToi,
          record.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          record.tocDoTruyen,
          getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
        );
      }
      record.soLuong1Lan = soLuong1Lan;
      record.soLan1Ngay = soLan1Ngay;
      record.tenDuongDung = list?.tenDuongDung || record?.tenDuongDung;

      if (record.lieuDungId) {
        if (record.soNgay) {
          genarateSoLuong(
            record,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: record.soNgay,
              soLuong1Lan: record.soLuong1Lan,
              soLan1Ngay: record.soLan1Ngay,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
            })
          );
        } else if (record.soLuong) {
          record.soNgay =
            tinhSoNgayV2({
              isTinhSLTongTheoSLBuoi,
              soLuong: record.soLuong,
              soLuong1Lan: record.soLuong1Lan,
              soLan1Ngay: record.soLan1Ngay,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
              maxSoNgay: maxSoNgay,
            }) || record.soNgay;
        }
      }
    }

    if (type === "soNgay" && value) {
      debugger;
      if (Number(value) <= 0) {
        record.soNgay = 0;
        message.error(
          t("khamBenh.soNgayPhaiLonHon{{soNgay}}", {
            soNgay: 0,
          })
        );
      } else if (maxSoNgay && Number(value) > maxSoNgay) {
        record.soNgay = maxSoNgay;
        message.error(
          t("khamBenh.soNgayPhaiNhoHonHoacBang{{soNgay}}", {
            soNgay: maxSoNgay,
          })
        );
      } else {
        genarateSoLuong(
          record,
          tinhSoLuongV2({
            isTinhSLTongTheoSLBuoi,
            soNgay: value,
            soLuong1Lan: record.soLuong1Lan,
            soLan1Ngay: record.soLan1Ngay,
            soLuongHuy: record.soLuongHuy,
            slSang: record.slSang,
            slChieu: record.slChieu,
            slToi: record.slToi,
            slDem: record.slDem,
          })
        );

        if (isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay) {
          if (record.tuNgay) {
            record.denNgay = moment(record.tuNgay)
              .add(Number(value) - 1, "days")
              ._d.format("MM/dd/yyyy");
          }
        }
      }
    }

    if (isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay) {
      let _isChangedSoNgay = false;
      if (type === "denNgay" && value && record.tuNgay) {
        const soNgay = moment(value).diff(moment(record.tuNgay), "days") + 1;
        if (maxSoNgay && Number(soNgay) > maxSoNgay) {
          record.soNgay = maxSoNgay;
        } else {
          record.soNgay = soNgay;
        }
        _isChangedSoNgay = true;
      }
      if (type === "tuNgay" && value && record.denNgay) {
        const soNgay = moment(record.denNgay).diff(moment(value), "days") + 1;
        if (maxSoNgay && Number(soNgay) > maxSoNgay) {
          record.soNgay = maxSoNgay;
        } else {
          record.soNgay = soNgay;
        }
        _isChangedSoNgay = true;
      }
      if (_isChangedSoNgay) {
        genarateSoLuong(
          record,
          tinhSoLuongV2({
            isTinhSLTongTheoSLBuoi,
            soNgay: record.soNgay,
            soLuong1Lan: record.soLuong1Lan,
            soLan1Ngay: record.soLan1Ngay,
            soLuongHuy: record.soLuongHuy,
            slSang: record.slSang,
            slChieu: record.slChieu,
            slToi: record.slToi,
            slDem: record.slDem,
          })
        );
      }
    }

    if (type == "soLuong" && value && record.heSoDinhMuc != 1) {
      let soLuongSoCap = (Number(value) || 0) / record.heSoDinhMuc;
      if (isLamTronLen && !isKhamBenh) {
        record.soLuongSoCap = Math.ceil(soLuongSoCap);
        record.soLuong = Math.ceil(soLuongSoCap) * record.heSoDinhMuc;
        list?.callback && list?.callback(record.soLuong);
      } else {
        record.soLuongSoCap = roundNumberPoint(soLuongSoCap, 6);
      }
    }

    if (type === "soLuongHuy") {
      genarateSoLuong(
        record,
        tinhSoLuongV2({
          isTinhSLTongTheoSLBuoi,
          soNgay: record.soNgay,
          soLuong1Lan: record.soLuong1Lan,
          soLan1Ngay: record.soLan1Ngay,
          soLuongHuy: value,
          slSang: record.slSang,
          slChieu: record.slChieu,
          slToi: record.slToi,
          slDem: record.slDem,
        })
      );
    }
    if ((type === "soLan1Ngay" || type === "soLuong1Lan") && value) {
      if (Number(value) <= 0 && value) {
        record[type] = 0;

        setTimeout(() => {
          showMessLessThan0Ref.current &&
            showMessLessThan0Ref.current(index, type);
        }, 1000);
      } else {
        if (record.soNgay) {
          genarateSoLuong(
            record,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: record.soNgay,
              soLan1Ngay: type === "soLan1Ngay" ? value : record.soLan1Ngay,
              soLuong1Lan: type === "soLuong1Lan" ? value : record.soLuong1Lan,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
            })
          );
        } else if (record.soLuong) {
          record.soNgay = tinhSoNgayV2({
            isTinhSLTongTheoSLBuoi,
            soLuong: record.soLuong,
            soLan1Ngay: type === "soLan1Ngay" ? value : record.soLan1Ngay,
            soLuong1Lan: type === "soLuong1Lan" ? value : record.soLuong1Lan,
            soLuongHuy: record.soLuongHuy,
            slSang: record.slSang,
            slChieu: record.slChieu,
            slToi: record.slToi,
            slDem: record.slDem,
            maxSoNgay: maxSoNgay,
          });
        }
      }
    }

    if (
      ((type === "soLan1Ngay" && record.soLuong1Lan) ||
        (type === "soLuong1Lan" && record.soLan1Ngay)) &&
      value
    ) {
      record.cachDung = getCachDung(
        t,
        record.tenDuongDung,
        type === "soLuong1Lan" ? record.soLan1Ngay : value,
        record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số
        record.tenDvtSuDung,
        record.thoiDiem,
        record.slSang,
        record.slChieu,
        record.slToi,
        record.slDem,
        dataThoiGianVaTenBuoiSang,
        dataThoiGianVaTenBuoiChieu,
        dataThoiGianVaTenBuoiToi,
        dataThoiGianVaTenBuoiDem,
        record.tocDoTruyen,
        getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
      );
    }

    if (type === "thoiDiem") {
      record.thoiDiem = value;
      if (
        record.soLuong1Lan ||
        record.soLan1Ngay ||
        record.slSang ||
        record.slChieu ||
        record.slToi ||
        record.slDem
      ) {
        record.cachDung = getCachDung(
          t,
          record.tenDuongDung,
          record.soLan1Ngay,
          record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số
          record.tenDvtSuDung,
          record.thoiDiem,
          record.slSang,
          record.slChieu,
          record.slToi,
          record.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          record.tocDoTruyen,
          getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
        );
      }
    }

    if (type === "soLuongSoCap") {
      if (Number(value) <= 0 && value) {
        record.soLuongSoCap = 0;
        message.error(
          `${t("common.cot")} ${t("common.slSoCap")} ${t("common.phaiLonHon0")}`
        );
      } else {
        if (
          isKhamBenh &&
          isTuDongLamTronSoLuongKeThuoc &&
          record.heSoDinhMuc === 1 &&
          record.dvtSuDungId == record.donViTinhId
        ) {
          record.soLuongSoCap = Math.ceil(Number(value) || 0);
          list?.callback && list?.callback(record.soLuongSoCap);
        }
        const soLuong = record.soLuongSoCap * record.heSoDinhMuc;

        if (isLamTronLen && checkIsLamTronLenKhamBenh(record)) {
          record.soLuong = Math.ceil(soLuong);
        } else {
          record.soLuong = roundNumberPoint(soLuong, 6);
        }

        //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
        //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức
        if (
          (record.heSoDinhMuc == 1 &&
            record.dvtSuDungId == record.donViTinhId) || // dv sử dụng = dv thứ cấp
          (record.heSoDinhMuc != 1 && isTuDongCapNhatSoNgayChoDonThuoc)
        ) {
          record.soNgay = tinhSoNgayV2({
            isTinhSLTongTheoSLBuoi,
            soLuong,
            soLan1Ngay: record.soLan1Ngay,
            soLuong1Lan: record.soLuong1Lan,
            soLuongHuy: record.soLuongHuy,
            slSang: record.slSang,
            slChieu: record.slChieu,
            slToi: record.slToi,
            slDem: record.slDem,
            maxSoNgay: maxSoNgay,
          });
        }
      }
    }

    if (type === "ghiChu" || type === "soLuongHuy" || type === "lyDoHuy") {
      record[type] = value;
      onSetData(dataSelect);
      setState({ dataSelected: dataSelect });
      return;
    }

    if (type === "duongDungId") {
      record.tenDuongDung = list?.ten || "";
      if (record.soLuong1Lan && record.soLan1Ngay) {
        record.cachDung = getCachDung(
          t,
          record.tenDuongDung,
          record.soLan1Ngay,
          record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số
          record.tenDvtSuDung,
          record.thoiDiem,
          record.slSang,
          record.slChieu,
          record.slToi,
          record.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          record.tocDoTruyen,
          getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
        );
      }
    }

    if (type === "tocDoTruyen" || type === "donViTocDoTruyen") {
      record.cachDung = getCachDung(
        t,
        record.tenDuongDung,
        record.soLan1Ngay,
        record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số
        record.tenDvtSuDung,
        record.thoiDiem,
        record.slSang,
        record.slChieu,
        record.slToi,
        record.slDem,
        dataThoiGianVaTenBuoiSang,
        dataThoiGianVaTenBuoiChieu,
        dataThoiGianVaTenBuoiToi,
        dataThoiGianVaTenBuoiDem,
        record.tocDoTruyen,
        getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
      );
    }

    if (["slSang", "slChieu", "slToi", "slDem"].includes(type)) {
      if (value && !expressionSymbol.test(value))
        record[`warning_${type}`] = true;
      else {
        record[`warning_${type}`] = false;

        record[type] = value;

        if (record.soNgay) {
          genarateSoLuong(
            record,
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: record.soNgay,
              soLuong1Lan: record.soLuong1Lan,
              soLan1Ngay: record.soLan1Ngay,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
            })
          );
        } else if (record.soLuong) {
          record.soNgay =
            tinhSoNgayV2({
              isTinhSLTongTheoSLBuoi,
              soLuong: record.soLuong,
              soLuong1Lan: record.soLuong1Lan,
              soLan1Ngay: record.soLan1Ngay,
              soLuongHuy: record.soLuongHuy,
              slSang: record.slSang,
              slChieu: record.slChieu,
              slToi: record.slToi,
              slDem: record.slDem,
              maxSoNgay: maxSoNgay,
            }) || record.soNgay;
        }
        record.cachDung = getCachDung(
          t,
          record.tenDuongDung,
          record.soLan1Ngay,
          record.soLuong1LanStr, //dùng biến này để giữ giá trị phân số
          record.tenDvtSuDung,
          record.thoiDiem,
          record.slSang,
          record.slChieu,
          record.slToi,
          record.slDem,
          dataThoiGianVaTenBuoiSang,
          dataThoiGianVaTenBuoiChieu,
          dataThoiGianVaTenBuoiToi,
          dataThoiGianVaTenBuoiDem,
          record.tocDoTruyen,
          getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten
        );
      }
    }

    if (record.soLuong > record.soLuongKhaDungConHsd && loaiDonThuoc === 10) {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("khamBenh.vuotQuaSoLuongTon")}: ${t(
            "khamBenh.soLuongTon{{soLuongTon}}",
            {
              soLuongTon: record.soLuongKhaDungConHsd,
            }
          )}, ${t("khamBenh.soLuongKe{{soLuongKe}}", {
            soLuongKe: record.soLuong,
          })}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          setState({ dataSelected: dataSelect });
          onSelected(dataSelect, ["thoiDiem"].includes(type));
        },
        () => {
          record.soLuong = 0;
          record.soLuongSoCap = 0;
          setState({ dataSelected: dataSelect });
          onSelected(dataSelect, ["thoiDiem"].includes(type));
        }
      );
    } else {
      setState({ dataSelected: dataSelect });
      onSelected(dataSelect, ["thoiDiem"].includes(type));
    }
  };

  const blockInvalidChar = (e) =>
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

  const isShowSLTonThuCap =
    (isNoiTruToDieuTri || isPttt || isCDHA) && isKhoDuyetPhatNgayKhiKe;

  const columnsTableLeft = [
    LEFT_COLUMNS.MA_THUOC,
    LEFT_COLUMNS.TEN_THUOC_HAM_LUONG,
    LEFT_COLUMNS.GIA_KHONG_BH,
    LEFT_COLUMNS.SL_TON_THU_CAP(isShowSLTonThuCap),
    LEFT_COLUMNS.SL_TON_SO_CAP(!isShowSLTonThuCap),
    LEFT_COLUMNS.HDSD,
    LEFT_COLUMNS.QUY_CACH,
  ];

  const onSearchLieuDung = (data) => async (e) => {
    if (e) {
      const listLieuDung = await lieuDungProvider
        .searchAll({
          lieuDung: e,
          page: "",
          size: "",
        })
        .then((s) => {
          return s?.data || [];
        });
      data.listLieuDung = listLieuDung;
    } else {
      const listLieuDung = await lieuDungProvider
        .searchAll({
          bacSiId: nhanVienId,
          dichVuId: data.id || data.dichVuId,
          page: "",
          size: "",
        })
        .then((s) => {
          return s?.data || [];
        });
      data.listLieuDung = listLieuDung;
    }
    setState({ searchLieuDungWord: e || "" });
  };

  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  const renderListThoiDiemDung = (listThoiDiemDung, index, data) =>
    listThoiDiemDung.map((item) => ({
      key: item.key,
      label: (
        <a
          href={() => false}
          onClick={() => onChangeInput("thoiDiem", index, data)(item.label)}
        >
          {item.label}
        </a>
      ),
    }));

  const onKeThuocDungKem = (index, data) => () => {
    setState({ isKeThuocDungKem: true, currentIndex: index, dichVuCha: data });
  };

  const onKetThucKeDungKem = () => {
    setState({ isKeThuocDungKem: false, currentIndex: -1, dichVuCha: null });
  };

  const listDsPhaChung = useMemo(() => {
    const listAllDsPhaChungId = uniq(
      flatten(listDvThuoc.map((x) => x.dsPhaChungId).filter((x) => !!x))
    );

    return [
      ...listDvThuoc
        .filter(
          (item) =>
            !item.dungKemId &&
            !item.dsPhaChungId &&
            !listAllDsPhaChungId.includes(item.id)
        )
        .map((item) => ({
          ...item,
          isThuocDaKe: true,
          ten: `${item.tenDichVu} - ${item.soLuong} ${
            item.tenDvtSuDung || ""
          } - ${item.cachDung || ""}`,
          id: item.id,
        })),
      ...state.dataSelected.map((item) => ({
        ...item,
        ten: `${item.ten} - ${item.soLuong || ""} ${
          item.tenDvtSuDung || ""
        } - ${item.cachDung || ""}`,
        id: item.dichVuId,
      })),
    ]?.filter((x) => !x.dungKemId);
  }, [listDvThuoc, state.dataSelected]);

  const columnsTableRight = {
    dungKem: {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("khamBenh.dungKem")} />
      ),
      dataIndex: "dungKem",
      key: "dungKem",
      width: 90,
      i18Name: "khamBenh.dungKem",
      show: true,
      hidden: !checkRole([
        ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_VA_CHO_PHEP_KE_KEM_THUOC,
      ]),
      render: (item, data, index) => {
        if (!data.thuocDungKem) {
          if (state.isKeThuocDungKem && state.currentIndex === index) {
            return (
              <Button type="warning" onClick={onKetThucKeDungKem} minWidth={90}>
                {t("common.ketThuc")}
              </Button>
            );
          }
          return (
            <Button
              type="success"
              onClick={onKeThuocDungKem(index, data)}
              disabled={isDisabledThemDungKem}
              minWidth={90}
            >
              {t("common.them")}
            </Button>
          );
        }
      },
    },
    ten: RIGHT_COLUMNS.TEN,
    soLuong: {
      title: <HeaderSearch title={t("common.soLuong")} isTitleCenter={true} />,
      dataIndex: "soLuong",
      key: "soLuong",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              id={`${data.dichVuId}-sl-input`}
              value={item}
              style={{
                border: !data.soLuong ? "1px solid red" : "unset",
                marginRight: 2,
              }}
              onBlur={onChangeInput("soLuong", index, data)}
              onKeyDown={blockInvalidChar}
            />
            <Tooltip title={data.tenDonViTinh}>
              <span className="span-ellipsis">{data.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.soLuong",
    },
    soLuongTheoNgay: {
      title: (
        <HeaderSearch title={t("common.slTheoNgay")} isTitleCenter={true} />
      ),
      dataIndex: "soLuong",
      key: "soLuongTheoNgay",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        const _value =
          isNumber(data.soNgay) && data.soNgay > 0 ? item / data.soNgay : null;

        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              value={_value || ""}
              style={{
                border: !data.soLuong ? "1px solid red" : "unset",
                marginRight: 2,
              }}
              disabled
            />
            <Tooltip title={data.tenDonViTinh}>
              <span className="span-ellipsis">{data.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.soLuong",
    },
    ...(isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay
      ? {
          tuNgay: {
            title: (
              <HeaderSearch title={t("common.tuNgay")} isTitleCenter={true} />
            ),
            dataIndex: "tuNgay",
            key: "tuNgay",
            show: true,
            width: 140,
            i18Name: "common.tuNgay",
            render: (item, data, index) => {
              return (
                <DatePicker
                  value={item ? moment(item) : null}
                  format="DD/MM/YYYY"
                  onChange={onChangeInput("tuNgay", index, data)}
                  disabledDate={(current) =>
                    current && data.denNgay && current > moment(data.denNgay)
                  }
                  style={{
                    border: !item ? "1px solid red" : "unset",
                  }}
                />
              );
            },
          },
          denNgay: {
            title: (
              <HeaderSearch title={t("common.denNgay")} isTitleCenter={true} />
            ),
            dataIndex: "denNgay",
            key: "denNgay",
            show: true,
            width: 140,
            i18Name: "common.denNgay",
            render: (item, data, index) => {
              return (
                <DatePicker
                  value={item ? moment(item) : null}
                  format="DD/MM/YYYY"
                  onChange={onChangeInput("denNgay", index, data)}
                  disabledDate={(current) =>
                    current && data.tuNgay && current < moment(data.tuNgay)
                  }
                  style={{
                    border: !item ? "1px solid red" : "unset",
                  }}
                />
              );
            },
          },
        }
      : {}),
    ...(isShowSoLuongDinhMuc
      ? {
          soLuongDinhMuc: {
            title: (
              <HeaderSearch
                isTitleCenter={true}
                title={t("khamBenh.donThuoc.slDinhMuc")}
              />
            ),
            width: 80,
            dataIndex: "soLuongDinhMuc",
            key: "soLuongDinhMuc",
            align: "center",
            i18Name: t("khamBenh.donThuoc.slDinhMuc"),
            show: true,
            render: (_, list) => {
              const dsDinhMucThuocChiTiet =
                configData?.dsDinhMucThuocChiTiet || [];

              const _findIndex = dsDinhMucThuocChiTiet.findIndex(
                (item) => item.dichVuId === list.dichVuId
              );
              if (_findIndex > -1) {
                return dsDinhMucThuocChiTiet[_findIndex].soLuong;
              }
              return "";
            },
          },
        }
      : {}),
    soLuongSoCap: {
      title: <HeaderSearch title={t("common.slSoCap")} isTitleCenter={true} />,
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              value={item}
              // type="number"
              onChange={onChangeInput("soLuongSoCap", index, data)}
              onKeyDown={blockInvalidChar}
              style={{
                marginRight: 2,
              }}
              // disabled={data.heSoDinhMuc <= 1}
              timeDelay={100}
            />
            <Tooltip title={data.tenDvtSoCap}>
              <span className="span-ellipsis">{data.tenDvtSoCap}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.slSoCap",
    },
    tuTra: RIGHT_COLUMNS.TU_TRA({ onChangeInput, hidden: loaiDonThuoc !== 20 }),
    khongTinhTien: RIGHT_COLUMNS.KHONG_TINH_TIEN({
      onChangeInput,
      hidden: loaiDonThuoc !== 20,
    }),
    lieuDungId: {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={
            <div
              className="pointer"
              onClick={() => openInNewTab("/danh-muc/lieu-dung")}
            >
              {t("common.lieuDung")}
            </div>
          }
        />
      ),
      dataIndex: "lieuDungId",
      key: "lieuDungId",
      width: 150,
      show: true,
      i18Name: "common.lieuDung",
      render: (item, data, index) => {
        let newDataRender = [];
        if (item) {
          newDataRender = listAllLieuDung;
        } else {
          newDataRender = Object.assign([], data?.listLieuDung);
          newDataRender?.sort(sortString("ten", 1));
        }
        return (
          <WrapperSelect
            style={{
              border:
                !data.lieuDungId && !data.cachDung && isValidateLieuDungCachDung
                  ? "1px solid red"
                  : "unset",
            }}
          >
            <ModalThemLieuDung ref={refModalThemLieuDung} />
            <SelectAntd
              ref={refSelectLieuDung}
              showSearch={true}
              open={
                state.openLieuDung?.key === data.key
                  ? state.openLieuDung[index]
                  : false
              }
              onDropdownVisibleChange={(open) =>
                setState({
                  openLieuDung: {
                    ...state.openLieuDung,
                    [index]: open,
                    key: null,
                  },
                })
              }
              value={item}
              onChange={onChangeInput("lieuDungId", index, data)}
              onSearch={debounce(onSearchLieuDung(data), 500)}
              onFocus={() => {
                setState({
                  openLieuDung: {
                    ...state.openLieuDung,
                    [index]: true,
                    key: data.key,
                  },
                });
              }}
              filterOption={filterOption}
              dropdownClassName="table-thuoc-right-lieu-dung"
              notFoundContent={
                <div>
                  <div style={{ color: "#7A869A", textAlign: "center" }}>
                    <small>{t("common.khongCoDuLieuPhuHop")}</small>
                  </div>
                  <Row justify="center" ref={refBtnThemNhanh}>
                    <Button
                      style={{
                        border: "1px solid",
                        borderRadius: "10px",
                        margin: "auto",
                        lineHeight: 0,
                        // boxShadow: "-1px 3px 1px 1px #d9d9d9",
                        cursor: "pointer",
                      }}
                      onClick={async (e) => {
                        refSelectLieuDung.current.blur();
                        return (
                          refModalThemLieuDung &&
                          refModalThemLieuDung.current.show(
                            {
                              visible: true,
                              data,
                              tenLieuDung: state.searchLieuDungWord,
                            },
                            (res) => {
                              const { values } = res;
                              values.bacSiId = nhanVienId;
                              createOrEditLieuDung(values).then(async (s) => {
                                const dataCustom = {
                                  lieuDung: {
                                    ...s,
                                  },
                                  lieuDungId: s.id,
                                  dichVuId: data?.id || data?.dichVuId,
                                };
                                await createOrEditLieuDungThuoc(dataCustom);
                                // await reRenderListLieuDungDependDichVu();
                                const listLieuDung = await lieuDungProvider
                                  .searchAll({
                                    bacSiId: nhanVienId,
                                    dichVuId: data?.id || data?.dichVuId,
                                    page: "",
                                    size: "",
                                  })
                                  .then((s) => {
                                    return s?.data || [];
                                  });
                                setTimeout(() => {
                                  reloadListAllLieuDung();
                                }, 1000);
                                let updateDataSelected = cloneDeep(
                                  state.dataSelected
                                );
                                let _row = updateDataSelected.find(
                                  (x) =>
                                    x.dichVuId == (data?.id || data?.dichVuId)
                                );

                                if (data.thuocDungKem) {
                                  const parentService = updateDataSelected.find(
                                    (x) => x.key == data?.parentKey
                                  );
                                  _row = parentService?.children?.find(
                                    (x) => x.key === data.key
                                  );
                                }

                                if (_row) {
                                  _row.listLieuDung = listLieuDung;
                                  _row.lieuDungId = dataCustom?.lieuDungId;
                                  setState({
                                    dataSelected: updateDataSelected,
                                  });
                                  onSelected(updateDataSelected);
                                }
                              });
                            },
                            (err) => {
                              // setState({...state})
                            }
                          )
                        );
                      }}
                    >
                      {t("khamBenh.donThuoc.themNhanhLieuDungBS")}
                    </Button>
                  </Row>
                </div>
              }
            >
              {newDataRender?.map((option) => {
                return (
                  <Option
                    lists={option}
                    key={
                      option[`${props.id}`] ? option[`${props.id}`] : option.id
                    }
                    value={
                      option[`${props.id}`] ? option[`${props.id}`] : option.id
                    }
                    ref={option}
                  >
                    {option.ma
                      ? `${option.ma} - ${
                          option[`${props.ten}`]
                            ? option[`${props.ten}`]
                            : option.ten
                        }`
                      : option[`${props.ten}`]
                      ? option[`${props.ten}`]
                      : option.ten}
                  </Option>
                );
              })}
            </SelectAntd>
          </WrapperSelect>
        );
      },
    },
    cachDung: {
      title: <HeaderSearch title={t("common.cachDung")} isTitleCenter={true} />,
      width: 320,
      align: "center",
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
      show: true,
      render: (item, data, index) => {
        return (
          <InputTimeout
            onChange={onChangeInput("cachDung", index, data)}
            value={item}
            style={{
              border:
                !data.cachDung && !data.lieuDungId && isValidateLieuDungCachDung
                  ? "1px solid red"
                  : "unset",
            }}
            timeDelay={100}
          />
        );
      },
    },
    soNgay: {
      title: <HeaderSearch title={t("common.soNgay")} isTitleCenter={true} />,
      width: 60,
      align: "center",
      dataIndex: "soNgay",
      key: "soNgay",
      i18Name: "common.soNgay",
      show: true,
      render: (item, data, index) => {
        //
        return (
          <WrapperInput>
            <InputTimeout
              onChange={onChangeInput("soNgay", index, data)}
              value={item}
              type="number"
              max={maxSoNgay}
              style={{ width: "100%" }}
              timeDelay={100}
            />
          </WrapperInput>
          // <InputNumber
          //   onChange={onChangeInput("soNgay", index)}
          //   value={item}
          //   max={maxSoNgay}
          //   min={0}
          //   style={{ width: "100%" }}
          // ></InputNumber>
        );
      },
    },
    thoiDiem: {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.thoiDiemDung")}
          isTitleCenter={true}
        />
      ),
      width: 120,
      align: "center",
      dataIndex: "thoiDiem",
      key: "thoiDiem",
      i18Name: "khamBenh.donThuoc.thoiDiemDung",
      show: true,
      render: (item, data, index) => {
        const _listThoiDiemDung = item
          ? LIST_THOI_DIEM_DUNG.filter(
              (x) => x.label.toLowerCase().indexOf(item.toLowerCase()) > -1
            )
          : LIST_THOI_DIEM_DUNG;

        return (
          <Dropdown
            overlayStyle={
              _listThoiDiemDung.length == 0 ? { display: "none" } : {}
            }
            overlay={
              <Menu
                items={renderListThoiDiemDung(_listThoiDiemDung, index, data)}
              />
            }
            trigger={["click"]}
          >
            <InputTimeout
              onChange={onChangeInput("thoiDiem", index, data)}
              value={item}
              timeDelay={100}
            />
          </Dropdown>
        );
      },
    },
    tenDvtSuDung: RIGHT_COLUMNS.TEN_DVT_SU_DUNG,
    soLan1Ngay: {
      title: <HeaderSearch title={t("common.lan/ngay")} isTitleCenter={true} />,
      width: 60,
      align: "center",
      dataIndex: "soLan1Ngay",
      key: "soLan1Ngay",
      i18Name: "common.lan/ngay",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <InputTimeout
              onChange={onChangeInput("soLan1Ngay", index, data)}
              value={item}
              type="number"
              timeDelay={100}
            />
          </WrapperInput>
        );
      },
    },
    soLuong1Lan: {
      title: <HeaderSearch title={t("common.sl/lan")} isTitleCenter={true} />,
      width: 120,
      align: "center",
      dataIndex: "soLuong1Lan",
      key: "soLuong1Lan",
      i18Name: "common.sl/lan",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex gap-5">
            <InputTimeout
              onChange={onChangeInput("soLuong1Lan", index, data)}
              value={item}
              timeDelay={100}
              className="flex1"
            ></InputTimeout>
            {data?.tenDvtSuDung}
          </WrapperInput>
        );
      },
    },
    ghiChu: RIGHT_COLUMNS.GHI_CHU({ onChangeInput }),
    soLuongHuy: {
      title: <HeaderSearch title={t("common.slHuy")} isTitleCenter={true} />,
      width: 80,
      align: "center",
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      i18Name: "common.slHuy",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <InputTimeout
              onChange={onChangeInput("soLuongHuy", index, data)}
              value={item}
              style={{ width: "100%" }}
              timeDelay={100}
            ></InputTimeout>
          </WrapperInput>
        );
      },
    },
    lyDoHuy: RIGHT_COLUMNS.LY_DO_HUY({ onChangeInput }),
    duongDung: {
      title: (
        <HeaderSearch title={t("common.duongDung")} isTitleCenter={true} />
      ),
      width: 160,
      dataIndex: "duongDungId",
      key: "duongDungId",
      i18Name: "common.duongDung",
      show: true,
      render: (item, data, index) => (
        <Select
          showAction={["focus"]}
          defaultValue={item}
          data={listAllDuongDung}
          onChange={onChangeInput("duongDungId", index, data)}
        />
      ),
    },
    dotXuat: {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={isCheckAllDotXuat}
              onChange={onCheckAllLoaiChiDinh("dotXuat", !isCheckAllDotXuat)}
            >
              {t("kho.dotXuat")}
            </Checkbox>
          }
          isTitleCenter={true}
        />
      ),
      width: 100,
      dataIndex: "dotXuat",
      key: "dotXuat",
      i18Name: "kho.dotXuat",
      align: "center",
      show: true,
      render: (item, data, index) => {
        const currentKho = (dsKho || []).find((i) => i.id == data?.khoId);
        const isKhoTuTruc = currentKho?.dsCoCheDuyetPhat?.includes(
          CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
        );
        if (
          isKhongHienThiCheckBoxDotXuatKhiKeTuTruc &&
          (isKhoTuTruc || isTuTruc)
        ) {
          return null;
        }

        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("dotXuat", index, data)}
          />
        );
      },
    },
    boSung: {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={isCheckAllBoSung}
              onChange={onCheckAllLoaiChiDinh("boSung", !isCheckAllBoSung)}
            >
              {t("kho.boSung")}
            </Checkbox>
          }
          isTitleCenter={true}
        />
      ),
      width: 100,
      dataIndex: "boSung",
      key: "boSung",
      i18Name: "kho.boSung",
      align: "center",
      show: true,
      render: (item, data, index) => {
        const currentKho = (dsKho || []).find((i) => i.id == data?.khoId);
        const isKhoTuTruc = currentKho?.dsCoCheDuyetPhat?.includes(
          CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
        );
        if (
          isKhongHienThiCheckBoxDotXuatKhiKeTuTruc &&
          (isKhoTuTruc || isTuTruc)
        ) {
          return null;
        }

        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("boSung", index, data)}
          />
        );
      },
    },
    slSang: RIGHT_COLUMNS.SL_SANG({ dataThoiGianVaTenBuoiSang, onChangeInput }),
    slChieu: RIGHT_COLUMNS.SL_CHIEU({
      dataThoiGianVaTenBuoiChieu,
      onChangeInput,
    }),
    slToi: RIGHT_COLUMNS.SL_TOI({ dataThoiGianVaTenBuoiToi, onChangeInput }),
    slDem: RIGHT_COLUMNS.SL_DEM({ dataThoiGianVaTenBuoiDem, onChangeInput }),
    nguonKhacId: {
      title: (
        <HeaderSearch title={t("danhMuc.nguonKhac")} isTitleCenter={true} />
      ),
      width: 160,
      dataIndex: "nguonKhacId",
      key: "nguonKhacId",
      i18Name: "danhMuc.nguonKhac",
      show: true,
      render: (item, data, index) => (
        <Select
          showAction={["focus"]}
          defaultValue={item}
          data={listAllNguonKhacChiTra}
          onChange={onChangeInput("nguonKhacId", index, data)}
        />
      ),
    },
    dsPhaChungId: {
      title: <HeaderSearch title={t("common.phaChung")} isTitleCenter={true} />,
      width: 200,
      dataIndex: "dsPhaChungId",
      key: "dsPhaChungId",
      i18Name: "common.phaChung",
      show: false,
      hidden: !!dungKemId,
      render: renderPhaChungColumn({
        dataSelected: state.dataSelected,
        listDsPhaChung: listDsPhaChung,
        onChangeInput: onChangeInput,
      }),
    },
    tocDoTruyen: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.tocDoTruyen")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "tocDoTruyen",
      key: "tocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.tocDoTruyen",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          type="number"
          onChange={onChangeInput("tocDoTruyen", index, data)}
          value={item}
          timeDelay={100}
        />
      ),
    },
    donViTocDoTruyen: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.dvTocDoTruyen")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "donViTocDoTruyen",
      key: "donViTocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.dvTocDoTruyen",
      show: true,
      render: (item, data, index) => (
        <Select
          showAction={["focus"]}
          value={item}
          data={listDonViTocDoTruyen}
          onChange={onChangeInput("donViTocDoTruyen", index, data)}
        />
      ),
    },
    soGiot: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.soGiot/ml")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "soGiot",
      key: "soGiot",
      i18Name: "quanLyNoiTru.toDieuTri.soGiot/ml",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          style={{
            border:
              data.donViTocDoTruyen === 10 && !data.soGiot
                ? "1px solid red"
                : "unset",
          }}
          onChange={onChangeInput("soGiot", index, data)}
          value={item}
          timeDelay={100}
        />
      ),
    },
    cachGio: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.cachGio")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "cachGio",
      key: "cachGio",
      i18Name: "quanLyNoiTru.toDieuTri.cachGio",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          onChange={onChangeInput("cachGio", index, data)}
          value={item}
          timeDelay={100}
        />
      ),
    },
    thoiGianBatDau: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.gioBatDau")}
          isTitleCenter={true}
        />
      ),
      width: 120,
      dataIndex: "thoiGianBatDau",
      key: "thoiGianBatDau",
      i18Name: "quanLyNoiTru.toDieuTri.gioBatDau",
      show: true,
      render: (item, data, index) => (
        <TimeInput
          value={item}
          onChange={onChangeInput("thoiGianBatDau", index, data)}
          placeholder={t("common.chonThoiGian")}
          timeDelay={0}
        />
      ),
    },
    dungChoCon: {
      title: (
        <HeaderSearch title={t("khamBenh.dungChoCon")} isTitleCenter={true} />
      ),
      width: 100,
      dataIndex: "dungChoCon",
      key: "dungChoCon",
      i18Name: "khamBenh.dungChoCon",
      align: "center",
      show: true,
      render: (item, data, index) => {
        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("dungChoCon", index, data)}
          />
        );
      },
    },
  };

  //nếu kê thuốc ra viện theo ngày thì show 2 cột số lượng + số lượng theo ngày
  const dataThietLapUpdate = useMemo(() => {
    const _soLuongIdx = dataThietLap.indexOf("soLuong");

    if (
      (isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay) &&
      _soLuongIdx > -1
    ) {
      return [
        ...dataThietLap.slice(0, _soLuongIdx),
        "soLuongTheoNgay",
        ...dataThietLap.slice(_soLuongIdx),
      ];
    }
    return dataThietLap;
  }, [dataThietLap, isKeThuocRaVienTheoTungNgay, showTuNgayDenNgay]);

  const columnsTableRightMemo =
    dataThietLapUpdate?.map((key) => columnsTableRight[key]) ||
    Object.values(columnsTableRight || {});

  const dataSource = useMemo(() => {
    if (!isShowing) return [];

    const isThuocNhaThuoc = loaiDonThuoc === LOAI_DON_THUOC.NHA_THUOC;
    const keySoLuong = isThuocNhaThuoc ? "soLuongBoChiDinh" : "soLuongChiDinh";
    const keyLieuDung = isThuocNhaThuoc ? "lieuDungBoChiDinhId" : "lieuDungId";
    const _dataSource = (
      (isThuocNhaThuoc
        ? listDvTonKhoNhaThuoc
        : khoId || isHienThiCheckboxTuTruc //nếu thiết lập checkbox tủ trực thì ko bắt buộc khoId
        ? listDvTonKho
        : []) || []
    ).map((item, index) => {
      item.key = state?.boChiDinhSelected?.id
        ? item.ma + stringUtils.guid()
        : item.id + stringUtils.guid() || item.dichVuId + item.khoId || index;
      item.boSung = item.loaiChiDinh == LOAI_CHI_DINH.BO_SUNG;
      item.dotXuat = item.loaiChiDinh == LOAI_CHI_DINH.DOT_XUAT;
      return item;
    });
    if (state.boChiDinhSelected?.id) {
      let listServiceSelected = [];
      listServiceSelected = [
        ...state?.dataSelected,
        ..._dataSource.map((item) => {
          const soLuong =
            item[keySoLuong] ||
            tinhSoLuongV2({
              isTinhSLTongTheoSLBuoi,
              soNgay: nbKetLuan?.soNgayChoDon
                ? nbKetLuan?.soNgayChoDon
                : isNoiTruToDieuTri
                ? 1
                : null,
              soLuong1Lan: item.soLuong1Lan,
              soLan1Ngay: item.soLan1Ngay,
              soLuongHuy: item.soLuongHuy,
              slSang: item.slSang,
              slChieu: item.slChieu,
              slToi: item.slToi,
              slDem: item.slDem,
            });
          return {
            ...item,
            soLuong:
              isLamTronLen && checkIsLamTronLenKhamBenh(item)
                ? Math.ceil(soLuong)
                : roundNumberPoint(soLuong, 6),
            lieuDungId: item[keyLieuDung],
            soLuongSoCap:
              isLamTronLen && checkIsLamTronLenKhamBenh(item)
                ? Math.ceil(soLuong / item.heSoDinhMuc)
                : roundNumberPoint(soLuong / item.heSoDinhMuc, 6),
            soNgay: nbKetLuan?.soNgayChoDon
              ? nbKetLuan?.soNgayChoDon
              : isNoiTruToDieuTri
              ? 1
              : null,
            cachDung:
              item.cachDung ||
              getCachDung(
                t,
                item.tenDuongDung,
                item.soLan1Ngay,
                item.soLuong1LanStr, //dùng biến này để giữ giá trị phân số,
                item.tenDvtSuDung,
                item.thoiDiem,
                item.slSang,
                item.slChieu,
                item.slToi,
                item.slDem,
                dataThoiGianVaTenBuoiSang,
                dataThoiGianVaTenBuoiChieu,
                dataThoiGianVaTenBuoiToi,
                dataThoiGianVaTenBuoiDem,
                item.tocDoTruyen,
                getDonViTocDoTruyen(item.donViTocDoTruyen)?.ten
              ),
          };
        }),
      ];
      let selectedRowKeys = (listServiceSelected || []).map((item) => item.key);
      const dsDichVuThuoc = [...listDvThuoc, ...listDvThuocNhaThuoc];
      let objDupplicate = dsDichVuThuoc.filter((item1) =>
        listServiceSelected.map((x) => x.dichVuId)?.includes(item1.dichVuId)
      );
      let objDupplicateThuocKho = dsDichVuThuoc.filter((item1) => {
        let currentKho = (dsKho || []).find((i) => i.id == item1.khoId);
        return (
          (currentKho?.dsCoCheDuyetPhat || []).includes(
            CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
          ) &&
          listServiceSelected.map((x) => x.dichVuId)?.includes(item1.dichVuId)
        );
      });
      if (objDupplicate?.length) {
        onShowCanhBaoKeTrung({
          objDupplicate,
          onSelectChangeLeft,
          selectedRowKeys,
          listServiceSelected,
          objDupplicateThuocKho,
          isKeBoChiDinh: true,
        });
      } else {
        let _listServiceSelected = listServiceSelected.filter(
          (item) => !item.dungKemBoChiDinhChiTietId
        );
        let listDichVuDungKem = listServiceSelected.filter(
          (item) => item.dungKemBoChiDinhChiTietId
        );
        const expandedKeys = [];
        _listServiceSelected = _listServiceSelected.map((item, index) => {
          expandedKeys.push(item.key);
          item.children = listDichVuDungKem
            .filter(
              (item2) =>
                item2.dungKemBoChiDinhChiTietId === item.boChiDinhChiTietId
            )
            .map((x) => {
              x.thuocDungKem = true;
              x.parentKey = item.key;
              return x;
            });
          return item;
        });
        setState({
          expandedKeys: [...state?.expandedKeys, ...expandedKeys],
        });
        onSelectChangeLeft(selectedRowKeys, _listServiceSelected);
      }
    }
    return _dataSource;
  }, [
    isShowing,
    khoId,
    listDvTonKhoNhaThuoc,
    listDvTonKho,
    loaiDonThuoc,
    isTinhSLTongTheoSLBuoi,
    listDvThuoc,
    listDvThuocNhaThuoc,
    isHienThiCheckboxTuTruc,
  ]);

  const onChangeBoChiDinh = (value, item) => {
    if (value && item?.id !== state.boChiDinhSelected?.id) {
      setState({ boChiDinhSelected: item });
      //nếu item không giống thì sẽ thêm vào
    } else {
      setState({ boChiDinhSelected: null });
    }
  };

  const onEnterAddService = () => {
    let data = dataSource.find((x) => x.key === state?.key);
    if (data) onSelectLeft(data, true);
  };

  refAddService.current = onEnterAddService;

  useEffect(() => {
    if (state.keySelected.length) {
      const cellToFocus = document.querySelector(
        `.table-right tr[data-row-key*='${
          state.keySelected[state.keySelected.length - 1]
        }'] input:not([disabled]):not(.ant-checkbox-input) `
      );
      cellToFocus && cellToFocus.focus();
    }
  }, [state.keySelected]);

  const handleResizeSplit = async () => {
    const widthSplitLeft =
      refSplitPanelLeft?.current?.getBoundingClientRect()?.width;
    const widthSplitRight =
      refSplitPanelRight?.current?.getBoundingClientRect()?.width;
    onResizeSplit(
      "widthCacheDonThuoc",
      "DATA_CUSTOMIZE_COLUMN_split_KHAMBENH_KeDonThuoc_DonThuoc",
      [widthSplitLeft, widthSplitRight]
    );
  };

  const onChangeToDieuTri = (value, item) => {
    onChangeConfig({
      chiDinhTuDichVuId: value,
      khoaChiDinhId: item?.khoaChiDinhId,
    });
    setState({ toDieuTriSelectedId: value });
  };

  const listAllDsThuoc = useMemo(() => {
    return [...(rawMimsData?.dsThuoc || []), ...state.dataSelected];
  }, [rawMimsData, state.dataSelected]);

  return (
    <Main className="content-chi-dinh-thuoc-table">
      <GlobalStyle />
      <SplitPanel onDragEnd={() => handleResizeSplit()}>
        <div
          className="content-left"
          ref={refSplitPanelLeft}
          style={splitCacheCustomize && { width: splitCacheCustomize[0] }}
        >
          <Row justify="space-between" align="middle" className="bo-chi-dinh">
            {!configData.isTuVanThuoc ? (
              <Select
                placeholder={t("khamBenh.donThuoc.chonBoChiDinh")}
                data={listAllBoChiDinh}
                value={state?.boChiDinhSelected?.id}
                onChange={onChangeBoChiDinh}
                style={{ width: "100%" }}
              ></Select>
            ) : (
              <Select
                placeholder={t("quanLyNoiTru.toDieuTri.vuiLongChonToTieuTri")}
                data={listToDieuTri}
                value={state?.toDieuTriSelectedId}
                onChange={onChangeToDieuTri}
                style={{ width: "100%" }}
              ></Select>
            )}
            <Setting
              refTable={refSettingsLeft}
              className="setting-table-inside-select"
            />
          </Row>

          <div className="content-left-header-table">
            <TableWrapper
              rowKey={(record) => {
                return record?.key;
              }}
              columns={columnsTableLeft}
              dataSource={dataSource}
              rowSelection={rowSelectionLeft}
              // showHeader={false}
              rowClassName={(record, index) =>
                state?.key === record.key
                  ? "row-actived table-row-odd " + record.key
                  : "table-row-odd " + record.key
              }
              onRow={(record) => {
                return {
                  onClick: (row) => {
                    row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                  },
                };
              }}
              locale={{
                emptyText: renderEmptyTextLeftTable(t, khoId, loaiDonThuoc),
              }}
              scroll={{ x: 400 }}
              tableName="table_KHAMBENH_KeDonThuoc_DsThuoc"
              ref={refSettingsLeft}
            />
            {!!dataSource.length && (
              <Pagination
                listData={dataSource}
                onChange={onChangePage}
                current={page + 1}
                pageSize={size}
                total={totalElements}
                onShowSizeChange={onSizeChange}
                stylePagination={{ justifyContent: "flex-start" }}
                showSplash={true}
              />
            )}
          </div>
        </div>
        <div
          className="content-right"
          ref={refSplitPanelRight}
          style={splitCacheCustomize && { width: splitCacheCustomize[1] }}
        >
          <div className="title">
            <div className="title__left">
              <img src={CircleCheck} alt="" /> {t("common.daChon")}
            </div>
            <div className="title__right">
              <div className="title__right_left">
                {renderSoTien({ thanhTien, tongTien })}
                {showLuuY && TOOLTIP_LUUY}
              </div>
              <Setting refTable={refSettings} />
            </div>
          </div>
          <div className="content-right_table">
            <TableWrapper
              rowKey={(record) => {
                return record?.key;
              }}
              rowSelection={rowSelectionRight}
              className="table-right"
              columnResizable={true}
              columns={columnsTableRightMemo || []}
              dataSource={[
                ...(notShowModal
                  ? listDvThuocDaKe.map((item) => ({
                      ...item,
                      readOnly: true,
                      ten: item.tenDichVu,
                    }))
                  : []),
                ...state.dataSelected,
              ]}
              tableName="table_KHAMBENH_KeDonThuoc"
              ref={refSettings}
              expandIconColumnIndex={3}
              expandedRowKeys={state?.expandedKeys}
              expandable={{
                expandIcon: ({ expanded, onExpand, record }) =>
                  record?.children?.length ? (
                    expanded ? (
                      <SVG.IcExpandDown
                        onClick={(e) => {
                          onExpand(record, e);
                          setState({
                            expandedKeys: state?.expandedKeys.filter(
                              (x) => x !== record.key
                            ),
                          });
                          e.stopPropagation();
                        }}
                      />
                    ) : (
                      <SVG.IcExpandRight
                        onClick={(e) => {
                          onExpand(record, e);
                          setState({
                            expandedKeys: [...state?.expandedKeys, record.key],
                          });
                          e.stopPropagation();
                        }}
                      />
                    )
                  ) : null,
              }}
              rowClassName={(record, index) => {
                const colorTuongTacThuoc = getColorTuongTacThuoc(
                  record.dichVuId,
                  listAllDsThuoc
                );

                let isSeverityRed = colorTuongTacThuoc === "red";
                let isSeverityOrange = colorTuongTacThuoc === "orange";

                return classNames("", {
                  "table-row-even": index % 2 === 0,
                  "table-row-odd": index % 2 === 1,
                  "add-border": index == state.listServiceSelected?.length - 1,
                  "disabled-row": record.readOnly,
                  "table-row-severity-red": isSeverityRed,
                  "table-row-severity-orange": isSeverityOrange,
                });
              }}
              onRow={() => {
                return {
                  onClick: (row) => {
                    if (
                      row?.target?.firstElementChild?.hasAttribute(
                        "clickcheckbox"
                      ) ||
                      row?.target?.hasAttribute("clickcheckbox")
                    ) {
                      row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                    }
                  },
                };
              }}
              locale={{ emptyText: EMPTY_TEXT }}
            />
          </div>
        </div>
      </SplitPanel>
      <ModalCanhBaoTrungDuocLy ref={refModalCanhBaoTrungDuocLy} />
    </Main>
  );
};

export default TableDonThuoc;
