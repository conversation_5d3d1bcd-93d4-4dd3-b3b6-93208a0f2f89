import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import {
  Button,
  DatePicker,
  ModalTemplate,
  Select as Select2,
} from "components";
import { Form, Select, TimePicker, Row, message } from "antd";
import moment, { isMoment } from "moment";
import { useTranslation } from "react-i18next";
import {
  DOI_TUONG_KCB,
  DOI_TUONG_KCB_BA_DAI_HAN,
  HOTKEY,
  HUONG_DIEU_TRI_KHAM,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { useStore } from "hooks";
import { containText, isArray } from "utils/index";

const dateFormat = "DD-MM-YYYY";

const ModalKetThuc<PERSON>ham = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const infoNb = useSelector((state) => state.khamBenh.infoNb);
  const auth = useSelector((state) => state.auth.auth);
  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const {
    nhanVien: { getListNhanVienTheoMaChucVu, getListAllNhanVien },
    nbDotDieuTri: { getDoiTuongKcb },
    nbMaBenhAn: { getDsMaBADaiHan },
    dieuTriDaiHan: { postLapBenhAnDaiHan },
  } = useDispatch();
  const listNhanVienTheoChucVu = useStore(
    "nhanVien.listNhanVienTheoChucVu",
    []
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const dsMaBADaiHan = useStore("nbMaBenhAn.dsMaBADaiHan", []);
  const doiTuongKcb = useStore("nbDotDieuTri.doiTuongKcb", null);

  const [state, _setState] = useState({
    thoiGianKetLuan: null,
    thoiGianKetLuanTime: null,
    showDoiTuongKcbField: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (doiTuongKcb) {
      form.setFieldsValue({ doiTuongKcb });
    }
  }, [doiTuongKcb]);


  useImperativeHandle(ref, () => ({
    show: ({ }, onOK) => {
      refCallback.current = onOK;
      form.resetFields();
      getListAllNhanVien({
        page: "",
        size: "",
        active: true,
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      });
      getListNhanVienTheoMaChucVu({
        dsMaThietLapChucVu: "MA_CHUC_VU",
        page: "",
        size: "",
      });
      getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
      getDoiTuongKcb(thongTinBenhNhan?.id);
      //nam.mn 20/09/2025 
      //thời gian local đã xử lý bằng thời gian server - 30s trong trường hợp kết thúc khám ngay sau khi kết luận khám
      //thì sẽ báo kết thúc khám < thời gian tiếp nhận, thế nên case này + 30 nữa để về thời gian đúng
      const thoiGianKetLuanValue = moment((Date.now() + 30000).toDateObject());
      form.setFieldsValue({
        thoiGianKetLuan: thoiGianKetLuanValue,
        thoiGianKetLuanTime: thoiGianKetLuanValue,
      })
      setState({
        show: true,
        thoiGianKetLuan: thoiGianKetLuanValue,
        thoiGianKetLuanTime: thoiGianKetLuanValue,
        showDoiTuongKcbField: false,
      });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    let hour = state?.thoiGianKetLuanTime?.get("hour");
    let minute = state?.thoiGianKetLuanTime?.get("minute");
    let second = state?.thoiGianKetLuanTime?.get("second");
    let result = state?.thoiGianKetLuan?.set({ hour, minute, second });
    setState({ thoiGianKetLuan: result });
  }, [state.thoiGianKetLuan, state.thoiGianKetLuanTime]);

  const listMaBaDaiHan = useMemo(() => {
    return dsMaBADaiHan.filter(
      (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
    );
  }, [dsMaBADaiHan]);

  let thoiGianHenKham = thongTinChiTiet?.nbKetLuan?.thoiGianHenKham;
  const henKhamMoment = thoiGianHenKham
    ? moment(thoiGianHenKham).startOf("day")
    : null;

  const onOK = (isOk) => async () => {
    if (isOk) {
      if (
        state.thoiGianKetLuan?.unix() <
        moment(infoNb?.thoiGianVaoVien, "YYYY-MM-DD HH:mm:ss")?.unix()
      ) {
        return null;
      }
      if (
        thongTinChiTiet?.nbKetLuan?.huongDieuTri ===
        HUONG_DIEU_TRI_KHAM.HEN_KHAM &&
        henKhamMoment &&
        isMoment(state.thoiGianKetLuan) &&
        state.thoiGianKetLuan.isSameOrAfter(henKhamMoment, "day")
      ) {
        return null;
      }

      const maBenhAn = form.getFieldValue("maBenhAn");
      const doiTuongKcb = form.getFieldValue("doiTuongKcb");
      if (maBenhAn && !doiTuongKcb) {
        message.error(t("khamBenh.vuiLongChonDoiTuongKcb") + "!");
        return;
      }

      //lập bệnh án dài hạn
      if (maBenhAn) {
        const { dsCdChinhId, dsCdKemTheoId, moTa } =
          thongTinChiTiet?.nbChanDoan || {};

        const _selectMaBa = (dsMaBADaiHan || []).find((x) => x.ten == maBenhAn);
        let body = {
          nbDotDieuTriId: thongTinBenhNhan?.id,
          maBenhAn: _selectMaBa?.ten,
          doiTuongKcb,
          dsCdVaoVienId: dsCdChinhId,
          dsCdVaoVienKemTheoId: dsCdKemTheoId,
          moTa,
          nbDvKhamId: thongTinChiTiet?.id,
        };
        if (_selectMaBa) {
          body = {
            nbDotDieuTriId: thongTinBenhNhan?.id,
            maBenhAn: _selectMaBa.ten,
            doiTuongKcb,
          };
        }
        await postLapBenhAnDaiHan(body);
      }

      refCallback.current({
        thoiGianKetLuan: state.thoiGianKetLuan,
        bacSiKetLuanId: form.getFieldValue("bacSiKetLuanId"),
        nguoiPhienDichId: form.getFieldValue("nguoiPhienDichId"),
      });

      setState({ show: false });
    } else {
      setState({ show: false });
    }
  };

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOK(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOK(true)();
      },
    },
  ];
  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.hasOwnProperty("maBenhAn")) {
      setState({ showDoiTuongKcbField: !!changedValues.maBenhAn });
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={t("khamBenh.ketLuanKham.thongTinKetThucKham")}
      onCancel={onOK(false)}
      width={560}
      hotKeys={hotKeys}
      actionLeft={
        <Button.QuayLai type="default" onClick={onOK(false)} minWidth={100} />
      }
      actionRight={
        <Button type="primary" onClick={onOK(true)} minWidth={100}>
          {t("common.dongY")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          initialValues={{
            // thoiGianKetLuan: thoiGianKetLuanValue,
            // thoiGianKetLuanTime: thoiGianKetLuanValue,
            bacSiKetLuanId: auth.nhanVienId,
          }}
          style={{ width: "100%" }}
          onValuesChange={onValuesChange}
        >
          <Form.Item
            label={t("khamBenh.ketLuanKham.bacSiKetLuan")}
            name="bacSiKetLuanId"
            className="form-item-label"
          >
            <Select
              className="input-option"
              showSearch
              optionFilterProp="children"
              placeholder={t("khamBenh.ketLuanKham.vuiLongNhapBsKetLuan")}
              filterOption={(input, option) =>
                option &&
                option.children &&
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {(listAllNhanVien || [])?.map((option, index) => {
                return (
                  <Select.Option key={index} value={option.id}>
                    {`${option.taiKhoan || ""}${option.taiKhoan && option.ten ? " - " : ""
                      }${option.ten || ""}`}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {checkRole([ROLES["KHAM_BENH"].NGUOI_PHIEN_DICH]) && (
            <Form.Item
              label={t("khamBenh.ketLuanKham.nguoiPhienDich")}
              name="nguoiPhienDichId"
              className="form-item-label"
            >
              <Select
                className="input-option"
                showSearch
                optionFilterProp="children"
                placeholder={t("khamBenh.ketLuanKham.nguoiPhienDich")}
                filterOption={filterOption}
              >
                {(listNhanVienTheoChucVu || [])?.map((option, index) => {
                  return (
                    <Select.Option key={index} value={option.id}>
                      {`${option.taiKhoan} - ${option.ten}`}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          )}
          <Row>
            <Form.Item
              label={t("khamBenh.ketLuanKham.thoiGianKetLuan")}
              name="thoiGianKetLuan"
              style={{ marginRight: 10 }}
              className="form-item-label"
            >
              <DatePicker
                format={dateFormat}
                value={state.thoiGianKetLuan}
                onChange={(e) => {
                  setState({ thoiGianKetLuan: e });
                }}
                disabledDate={(date) => {
                  // Cho phép chọn hôm ngay
                  if (new Date().ddmmyyyy() == date._d.ddmmyyyy()) return false;

                  // Không cho chọn ngày trong tương lai
                  if (new Date() < date._d) return true;

                  return false;
                }}
                placeholder={t(
                  "khamBenh.ketLuanKham.vuiLongNhapThoiGianKetLuan"
                )}
              />
            </Form.Item>
            <Form.Item label=" " style={{ flex: 1 }}>
              <TimePicker
                value={state.thoiGianKetLuanTime}
                onChange={(e) => {
                  setState({ thoiGianKetLuanTime: e });
                }}
              />
            </Form.Item>
          </Row>

          {thongTinChiTiet?.nbKetLuan?.huongDieuTri !==
            HUONG_DIEU_TRI_KHAM.NHAP_VIEN &&
            [
              DOI_TUONG_KCB.NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
              DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
            ].includes(infoNb?.doiTuongKcb) &&
            thongTinBenhNhan?.maBenhAn === null &&
            isArray(listMaBaDaiHan, 1) && (
              <>
                <Form.Item
                  label={t("khamBenh.ganMaBADaiHan")}
                  name="maBenhAn"
                  className="form-item-label"
                >
                  <Select2
                    data={dsMaBADaiHan}
                    placeholder={t("khamBenh.chonMaBenhAnDaiHan")}
                  />
                </Form.Item>
              </>
            )}
          {state.showDoiTuongKcbField && (
            <Form.Item
              label={t("khamBenh.loaiKcb")}
              name="doiTuongKcb"
              className="form-item-label"
              rules={[
                {
                  required: true,
                  message: t("khamBenh.vuiLongNhapLoaiKcb"),
                },
              ]}
            >
              <Select2
                value={state.doiTuongKcb}
                data={DOI_TUONG_KCB_BA_DAI_HAN}
                placeholder={t("khamBenh.chonLoaiKcb")}
              />
            </Form.Item>
          )}

          {state.thoiGianKetLuan?.unix() <
            moment(infoNb?.thoiGianVaoVien, "YYYY-MM-DD HH:mm:ss")?.unix() ? (
            <span style={{ color: "red" }}>
              {t("khamBenh.ketLuanKham.thoiGianKetLuanPhaiSauThoiGianVaoVien")}
            </span>
          ) : (
            <></>
          )}
          {thongTinChiTiet?.nbKetLuan?.huongDieuTri ===
            HUONG_DIEU_TRI_KHAM.HEN_KHAM &&
            henKhamMoment &&
            isMoment(state.thoiGianKetLuan) &&
            state.thoiGianKetLuan.isSameOrAfter(henKhamMoment, "day") ? (
            <span style={{ color: "red" }}>
              {t(
                "khamBenh.ketLuanKham.thoiGianKetLuanPhaiTruocTimeThoiGianHenKham",
                {
                  thoiGianHenKham: henKhamMoment.format("DD/MM/YYYY"),
                }
              )}
            </span>
          ) : (
            <></>
          )}
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalKetThucKham);
