import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Spin } from "antd";

import { useEnum, usePrevious, useStore, useThietLap } from "hooks";

import { ModalTemplate } from "components";
import VitalSigns from "components/VitalSigns/components/VitalSigns/index";
import { Main } from "./styled";
import { ENUM, LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";

const ModalDoSinhHieu = ({ refreshList, isSinhHieu, isKhamBenh }, ref) => {
  const [state, _setState] = useState({
    show: false,
  });
  const [loading, setLoading] = useState(true);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", []);
  const actionLoading = useSelector((state) => state.vitalSigns.actionLoading);
  const prevActionLoading = usePrevious(actionLoading);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const [dataMA_CSS_VONG_CANH_TAY, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_CSS_VONG_CANH_TAY
  );
  const [dataHIEN_THI_TRUONG_SINH_HIEU, loadFinishHienThiTruongSinhHieu] =
    useThietLap(THIET_LAP_CHUNG.HIEN_THI_TRUONG_SINH_HIEU);

  const {
    vitalSigns: { getDataVitalSigns, updateData },
    chiSoSong: { getListAllChiSoSong },
    nbDotDieuTri: { getById },
  } = useDispatch();

  const refModal = useRef(null);
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useImperativeHandle(ref, () => ({
    show: ({ nbDotDieuTriId, khoaChiDinhId, orgData }) => {
      setState({
        show: true,
        orgData,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu: isKhamBenh
          ? LOAI_DICH_VU.KHAM
          : LOAI_DICH_VU.TIEP_DON,
      });

      const fetchData = async () => {
        try {
          setLoading(true);
          if (!isKhamBenh) {
            updateData({
              configData: {
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
                chiDinhTuDichVuId: null,
                dsChiDinhTuLoaiDichVu: isSinhHieu
                  ? [LOAI_DICH_VU.TIEP_DON, LOAI_DICH_VU.KHAM]
                  : [LOAI_DICH_VU.TIEP_DON],
              },
            });
          }
          const res = await getById(nbDotDieuTriId);
          await getListAllChiSoSong({
            page: "",
            size: "",
            active: true,
            saveCache: false,
            isForceCall: true,
          });

          let _khoaChiDinhId = khoaChiDinhId ?? res?.data?.khoaId;

          await getDataVitalSigns({
            nbDotDieuTriId,
            khoaChiDinhId: _khoaChiDinhId,
          });
        } catch (error) {
          console.error(error?.message || error);
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (prevActionLoading && !actionLoading) {
      refreshList && refreshList();
    }
  }, [prevActionLoading, actionLoading]);

  useEffect(() => {
    return () => {
      updateData({ values: null, moreValueIds: null });
    };
  }, []);

  const width = useMemo(() => {
    return window.screen.width - 100;
  }, [window.screen.width]);

  const tenBenhNhan = useMemo(() => {
    let result = "";
    if (thongTinBenhNhan) {
      const gioiTinh = listGioiTinh.find(
        (i) => i.id === thongTinBenhNhan.gioiTinh
      )?.ten;
      result = [thongTinBenhNhan.tenNb, gioiTinh, thongTinBenhNhan.ngaySinh2];
      result = result.filter((i) => !!i).join(" - ");
    }
    return result;
  }, [thongTinBenhNhan]);

  const onCancel = () => {
    setState({ show: false, orgData: null });
  };

  return (
    <ModalTemplate
      forceRender
      title={tenBenhNhan}
      width={width}
      ref={refModal}
      onCancel={onCancel}
    >
      <Main>
        {(loading || !state.show || !loadFinish) && (
          <div className="loading">
            <Spin />
          </div>
        )}
        {!loading &&
          state.show &&
          loadFinish &&
          loadFinishHienThiTruongSinhHieu && (
            <VitalSigns
              isEdit={true}
              isModal={true}
              show={state.show}
              nbDotDieuTriId={state.nbDotDieuTriId}
              dataMA_CSS_VONG_CANH_TAY={dataMA_CSS_VONG_CANH_TAY}
              dataHIEN_THI_TRUONG_SINH_HIEU={dataHIEN_THI_TRUONG_SINH_HIEU}
            />
          )}
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalDoSinhHieu);
