import React, {
  useEffect,
  useRef,
  forwardRef,
  useImperative<PERSON>andle,
} from "react";
import {
  Pa<PERSON><PERSON>,
  HeaderSearch,
  TableWrapper,
  Checkbox,
  Tooltip,
  ModalLichSuKCB,
} from "components";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useEnum,
  useLoading,
  useListAll,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";
import { ENUM, THIET_LAP_CHUNG, ROLES } from "constants/index";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { cloneDeep } from "lodash";
import { SVG } from "assets";
import { formatPhoneHideCharacters, transformQueryString } from "utils";
import classNames from "classnames";
import { checkRole } from "lib-utils/role-utils";
import { query } from "redux-store/stores";
import { Main } from "./styled";
import ListPhanLoaiNguoiBenh from "../components/ListPhanLoaiNguoiBenh";

const Setting = TableWrapper.Setting;

const DanhSach = ({ setParentState }, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const refModalLichSuKCB = useRef(null);
  const { showLoading, hideLoading } = useLoading();

  const {
    danhSachNbTiepDon: {
      getListNguoiBenhTiepDon,
      onSizeChange,
      onSortChange,
      getTaiKhoanIsc,
      clearData,
    },
    danhSachNbHuyTiepDon: { huyTiepDon },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    settingTable: () => {
      onSettings();
    },
  }));
  const userName = useStore("auth.auth.username", "");
  const {
    dataSortColumn,
    loadingDsNbDaTiepDon,
    listNguoiBenhTiepDon,
    totalElements,
    page,
    size,
    first,
    last,
  } = useStore(
    "danhSachNbTiepDon",
    {},
    {
      fields:
        "dataSortColumn,loadingDsNbDaTiepDon, listNguoiBenhTiepDon, totalElements, page, size, first, last",
    }
  );

  const [listTrangThaiNbKsk] = useEnum(ENUM.TRANG_THAI_NB_KSK);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listPhanLoaiNBCapCuu] = useEnum(ENUM.PHAN_LOAI_NB_CAP_CUU);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const { data: listKhoaTheoTaiKhoan, isFetched: isFetchedKhoaTheoTaiKhoan } =
    useQueryAll(query.khoa.queryKhoaTheoTaiKhoan);
  const { data: listAllLoaiDoiTuong, isFetched: isFetchedLoaiDoiTuong } =
    useQueryAll(query.loaiDoiTuong.queryAllLoaiDoiTuong);
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE);
  const [MA_LOI_BH_DUOC_TIEP_DON] = useThietLap(
    THIET_LAP_CHUNG.MA_LOI_BH_DUOC_TIEP_DON
  );
  const [dataXemSdt] = useThietLap(THIET_LAP_CHUNG.XEM_SDT);
  const [dataHIEN_THI_KET_NOI_MEMO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_NOI_MEMO
  );
  useEffect(() => {
    if (!listKhoaTheoTaiKhoan.length || !isFinish || !isFetchedKhoaTheoTaiKhoan)
      return null;
    const {
      page,
      size,
      dataSortColumn = "{}",
      namSinh,

      ...queries
    } = transformQueryString({
      tuThoiGianVaoVien: {
        format: (date) => date.format("YYYY-MM-DD HH:mm:ss"),
        type: "dateOptions",
        defaultValue: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
      },
      denThoiGianVaoVien: {
        format: (date) => date.format("YYYY-MM-DD HH:mm:ss"),
        type: "dateOptions",
        defaultValue: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
      },
      doiTuong: {
        format: (value) => parseInt(value),
      },
      nhanVienTiepDonId: {
        format: (value) => parseInt(value),
      },
      nguonNbId: {
        format: (value) => parseInt(value),
      },
      nguoiGioiThieuId: {
        format: (value) => parseInt(value),
      },
      nhanVienKinhDoanhId: {
        format: (value) => parseInt(value),
      },
      dsKhoaNbId: {
        format: (value) => value.split(",").map(Number),
        defaultValue: listKhoaTheoTaiKhoan?.map((o) => o.id),
      },
    });
    const sort = JSON.parse(dataSortColumn);

    onSizeChange({
      page: parseInt(page || 0),
      size: parseInt(size || dataPageSize || 10),
      dataSearch: {
        ...queries,
        tuNgaySinh: namSinh
          ? moment(+namSinh).startOf("year").format("YYYY-MM-DD")
          : null,
        denNgaySinh: namSinh
          ? moment(+namSinh).endOf("year").format("YYYY-MM-DD")
          : null,
      },
      dataSortColumn: sort,
    });
    setParentState({
      ...queries,
      namSinh: namSinh ? moment(+namSinh) : null,
    });
  }, [listKhoaTheoTaiKhoan, isFinish, isFetchedKhoaTheoTaiKhoan]);

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    getListNguoiBenhTiepDon({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size: size });
  };

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value });
  };

  const history = useHistory();

  const onRow = (record) => {
    return {
      onClick: (e) => {
        const selection = window.getSelection();
        const isSelection =
          selection.type === "Range" && selection.containsNode(e.target, true);
        if (!isSelection) {
          const { id } = record;
          history.push({
            pathname: `/quan-ly-tiep-don/danh-sach-nb-da-tiep-don/${id}`,
            state: getAllQueryString(undefined, {
              filterEmpty: false,
            }),
          });
        }
      },
    };
  };

  const onEdit = (record) => {
    history.push("/tiep-don/" + record?.id);
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onGetTkIsofhCare = (record) => (e) => {
    e.stopPropagation();
    e.preventDefault();

    getTaiKhoanIsc(record?.id).then((res) => {
      const { username, password, note } = res || {};
      const nguoiBenh = `NB: ${record?.tenNb} - Mã HS: ${record?.maHoSo}`;
      const luuY = note ? `\nLưu ý: ${note}` : "";

      showConfirm(
        {
          title: t("tiepDon.thongTinTaiKhoanIVIE"),
          content: `${nguoiBenh}\n\n ${t("common.taiKhoan")}: ${
            username || ""
          }\n ${t("common.matKhau")}: ${password || ""}${luuY}`,
          cancelText: t("common.dong"),
          typeModal: "warning",
        },
        () => {}
      );
    });
  };

  const widthStt = () => {
    const length = String(((page || 0) + 1) * (size || 10)).length;
    return length * 10 + 30;
  };

  const renderMaLoi = (item, record) => {
    const listMaLoi = MA_LOI_BH_DUOC_TIEP_DON.split(",").map((i) => i.trim());
    if (listMaLoi.includes(item) && !record.boQuaTheLoi) return null;
    const ketQua = record.ketQua && JSON.parse(record.ketQua);
    return [item, ketQua?.ghiChu].filter(Boolean).join(" - ");
  };

  const onShowLichSuKCB = (record) => (e) => {
    refModalLichSuKCB &&
      refModalLichSuKCB.current.show({ soCCCD: record.maSoGiayToTuyThan });
    e.preventDefault();
    e.stopPropagation();
  };

  const onHuyTiepDon = (data) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    showConfirm(
      {
        title: t("tiepDon.huyTiepDon"),
        content: t("tiepDon.banCoChacChanMuonHuyTiepDonKhong"),
        cancelText: t("common.quayLai"),
        okText: t("tiepDon.huyTiepDon"),
        classNameOkText: "button-error",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyTiepDon({ dsId: [data.id], active: false })
          .then(() => {
            getListNguoiBenhTiepDon({ page: page - 1 });
          })
          .finally(hideLoading);
      }
    );
  };

  const openMemoPopup = (pid) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    const url = `https://note.hpimc.info/Memo?pid=${pid}&username=${userName}`;
    window.open(
      url,
      "_blank",
      "width=800,height=600,resizable=yes,scrollbars=yes"
    );
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: widthStt(),
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.ngayDangKy")}
          sort_key="thoiGianVaoVien"
          dataSort={dataSortColumn["thoiGianVaoVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 150,
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "tiepDon.ngayDangKy",
      show: true,
      render: (field, item, index) =>
        field ? moment(field).format("DD / MM / YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maNb")}
          sort_key="maNb"
          dataSort={dataSortColumn["maNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHs")}
          sort_key="maHoSo"
          dataSort={dataSortColumn["maHoSo"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHs",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.hoTenNguoiBenh")}
          sort_key="tenNb"
          dataSort={dataSortColumn["tenNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "tiepDon.hoTenNguoiBenh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ngaySinh")}
          sort_key="ngaySinh"
          dataSort={dataSortColumn["ngaySinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "common.ngaySinh",
      show: true,
      render: (field, item, index) =>
        field
          ? moment(field)
              .utcOffset("+0700")
              .format(item.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.phanLoaiNb")}
          sort_key="dsPhanLoaiNbId"
          dataSort={dataSortColumn["dsPhanLoaiNbId"] || 0}
          onClickSort={onClickSort}
        />
      ),
      width: 100,
      dataIndex: "dsPhanLoaiNbId",
      key: "dsPhanLoaiNbId",
      show: true,
      i18Name: "common.phanLoaiNb",
      render: (item) => {
        return (
          <ListPhanLoaiNguoiBenh value={item} listAll={listAllPhanLoaiNB} />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.gioiTinh")}
          sort_key="gioiTinh"
          dataSort={dataSortColumn["gioiTinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "gioiTinh",
      key: "gioiTinh",
      i18Name: "common.gioiTinh",
      show: true,
      render: (item) => {
        return listGioiTinh.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.CMT_CCCD")}
          sort_key="maSoGiayToTuyThan"
          dataSort={dataSortColumn["maSoGiayToTuyThan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "maSoGiayToTuyThan",
      key: "maSoGiayToTuyThan",
      i18Name: "tiepDon.CMT_CCCD",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.diaChi")}
          // sort_key="diaChi"
          // dataSort={dataSortColumn["diaChi"] || ""}
          // onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "common.diaChi",
      show: true,
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.loaiDoiTuong")}
          sort_key="loaiDoiTuongId"
          dataSort={dataSortColumn["loaiDoiTuongId"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "70px",
      dataIndex: "loaiDoiTuongId",
      key: "loaiDoiTuongId",
      i18Name: "tiepDon.loaiDoiTuong",
      show: true,
      render: (value) =>
        listAllLoaiDoiTuong?.find((o) => o.id === value)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu")}
          sort_key="phanLoaiNbCapCuu"
          dataSort={dataSortColumn["phanLoaiNbCapCuu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "phanLoaiNbCapCuu",
      key: "phanLoaiNbCapCuu",
      i18Name: "khamBenh.chanDoan.phanLoaiNguoiBenhCapCuu",
      show: true,
      className: "phanLoaiNbCapCuu",
      render: (value) =>
        listPhanLoaiNBCapCuu?.find((o) => o.id === value)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soDienThoai")}
          sort_key="soDienThoai"
          dataSort={dataSortColumn["soDienThoai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "soDienThoai",
      key: "soDienThoai",
      i18Name: "common.soDienThoai",
      show: true,
      render: (val) =>
        dataXemSdt === "true" ? val : formatPhoneHideCharacters(val),
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.soBHYT")}
          sort_key="maTheBhyt"
          dataSort={dataSortColumn["maTheBhyt"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "maTheBhyt",
      key: "maTheBhyt",
      i18Name: "tiepDon.soBHYT",
      show: true,
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.doiTuong")}
          sort_key="doiTuong"
          dataSort={dataSortColumn["doiTuong"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "70px",
      dataIndex: "doiTuong",
      key: "doiTuong",
      i18Name: "common.doiTuong",
      show: true,
      render: (item) => {
        return listDoiTuong.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.boQuaCheckThe")}
          sort_key="boQuaTheLoi"
          dataSort={dataSortColumn["boQuaTheLoi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "50px",
      dataIndex: "boQuaTheLoi",
      key: "boQuaTheLoi",
      align: "center",
      i18Name: "tiepDon.boQuaCheckThe",
      show: true,
      render: (item) => {
        return <Checkbox checked={item}></Checkbox>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="tenKhoaNb"
          dataSort={dataSortColumn["tenKhoaNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      align: "center",
      i18Name: "common.khoa",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.tenCongTy")}
          sort_key="tenDoiTac"
          dataSort={dataSortColumn["tenDoiTac"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "90px",
      dataIndex: "tenDoiTac",
      key: "tenDoiTac",
      i18Name: "tiepDon.tenCongTy",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.nguoiBenhKSK")}
          sort_key="khamSucKhoe"
          dataSort={dataSortColumn["khamSucKhoe"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "60px",
      dataIndex: "khamSucKhoe",
      key: "khamSucKhoe",
      align: "center",
      i18Name: "tiepDon.nguoiBenhKSK",
      show: true,
      render: (item) => {
        return <Checkbox checked={item}></Checkbox>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.trangThaiKSK")}
          sort_key="trangThaiKsk"
          dataSort={dataSortColumn["trangThaiKsk"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "60px",
      dataIndex: "trangThaiKsk",
      key: "trangThaiKsk",
      align: "center",
      i18Name: "tiepDon.trangThaiKSK",
      show: true,
      render: (item) => {
        return (
          (listTrangThaiNbKsk || []).find((x) => x?.id === item)?.ten || ""
        );
      },
    },
    {
      title: <HeaderSearch title={t("thuNgan.soTienConLai")} />,
      width: "70px",
      dataIndex: "tienConLai",
      key: "tienConLai",
      align: "right",
      i18Name: "thuNgan.soTienConLai",
      show: true,
      render: (item) => {
        return item?.formatPrice();
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.nguonNguoiBenh")} />,
      width: "100px",
      dataIndex: "tenNguonNb",
      key: "tenNguonNb",
      align: "center",
      i18Name: "tiepDon.nguonNguoiBenh",
      show: true,
    },
    {
      title: <HeaderSearch title={t("tiepDon.maHoSoNgoaiVien")} />,
      width: "100px",
      dataIndex: "maHoSoNgoaiVien",
      key: "maHoSoNgoaiVien",
      align: "center",
      i18Name: "tiepDon.maHoSoNgoaiVien",
      show: true,
    },
    {
      title: <HeaderSearch title={t("tiepDon.maNguoiBenhNgoaiVien")} />,
      width: "100px",
      dataIndex: "maNbNgoaiVien",
      key: "maNbNgoaiVien",
      align: "center",
      i18Name: "tiepDon.maNguoiBenhNgoaiVien",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.maLoi")} />,
      width: "100px",
      dataIndex: "maKetQua",
      key: "maKetQua",
      align: "center",
      i18Name: "common.maLoi",
      show: true,
      render: renderMaLoi,
    },
    {
      title: <HeaderSearch title={t("quyetToanBhyt.thoiGianCheckThe")} />,
      width: "120px",
      dataIndex: "thoiGianKiemTraThe",
      key: "thoiGianKiemTraThe",
      align: "center",
      i18Name: "quyetToanBhyt.thoiGianCheckThe",
      show: true,
      render: (item, data) => {
        const listMaLoi = MA_LOI_BH_DUOC_TIEP_DON.split(",").map((i) =>
          i.trim()
        );
        if (listMaLoi.includes(data.maKetQua) && !data.boQuaTheLoi) return null;
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.maNBCu")}
          sort_key="maNbCu"
          dataSort={dataSortColumn["maNbCu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "maNbCu",
      key: "maNbCu",
      i18Name: "tiepDon.maNBCu",
      show: true,
    },
    {
      title: <HeaderSearch title={t("tiepDon.noiGioiThieu")} />,
      width: "150px",
      dataIndex: "tenNoiGioiThieu",
      key: "tenNoiGioiThieu",
      i18Name: "tiepDon.tenNoiGioiThieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.ngayChuyenTuyen")}
          sort_key="tuNgayGiayChuyen"
          dataSort={dataSortColumn["tuNgayGiayChuyen"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tuNgayGiayChuyen",
      key: "tuNgayGiayChuyen",
      show: true,
      i18Name: "tiepDon.ngayChuyenTuyen",
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.giaHanThe.nguoiChuyenDoi")} />
      ),
      width: "150px",
      dataIndex: "tenNguoiThucHienTheBh",
      key: "tenNguoiThucHienTheBh",
      i18Name: "quanLyNoiTru.giaHanThe.nguoiChuyenDoi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.giaHanThe.ngayChuyenDoi")}
          sort_key="thoiGianThucHienTheBh"
          dataSort={dataSortColumn["thoiGianThucHienTheBh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "thoiGianThucHienTheBh",
      key: "thoiGianThucHienTheBh",
      show: true,
      i18Name: "quanLyNoiTru.giaHanThe.ngayChuyenDoi",
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.giayChuyenVienCoGiaTriTrong01Nam")}
          sort_key="giayChuyen1Nam"
          dataSort={dataSortColumn["giayChuyen1Nam"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "giayChuyen1Nam",
      key: "giayChuyen1Nam",
      align: "center",
      show: true,
      i18Name: "tiepDon.giayChuyenVienCoGiaTriTrong01Nam",
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThaiNb")}
          sort_key="trangThaiNb"
          dataSort={dataSortColumn["trangThaiNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "trangThaiNb",
      key: "trangThaiNb",
      i18Name: "common.trangThaiNb",
      show: true,
      render: (item) => {
        return (listTrangThaiNb || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maTheRFID")}
          sort_key="maThe"
          dataSort={dataSortColumn["maThe"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "maThe",
      key: "maThe",
      align: "left",
      i18Name: "common.maTheRFID",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.thaoTac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "140px",
      dataIndex: "",
      key: "",
      align: "center",
      i18Name: "common.thaoTac",
      fixed: "right",
      ignore: true,
      render: (item, data) => {
        return (
          <>
            {dataHIEN_THI_KET_NOI_MEMO?.eval() && (
              <Tooltip title="Tool Memo" placement="bottomLeft">
                <SVG.IcMemo
                  onClick={openMemoPopup(data?.maNb)}
                  className="ic-action"
                />
              </Tooltip>
            )}
            <Tooltip
              title={t("tiepDon.taoTaiKhoanMatKhauIvie")}
              placement="bottomLeft"
            >
              <SVG.IcSend
                color={"var(--color-blue-primary)"}
                onClick={onGetTkIsofhCare(data)}
                className="ic-action"
              />
            </Tooltip>

            {checkRole([ROLES["HO_SO_BENH_AN"].XEM_LICH_SU_KCB]) && (
              <Tooltip
                title={t("common.lichSuKhamChuaBenh")}
                placement="topLeft"
              >
                <SVG.IcLichSuKCB
                  className="ic-action"
                  onClick={onShowLichSuKCB(data)}
                />
              </Tooltip>
            )}
            <Tooltip title={t("tiepDon.huyTiepDon")} placement="topLeft">
              <SVG.IcDelete
                className="ic-action"
                onClick={onHuyTiepDon(data)}
              />
            </Tooltip>
          </>
        );
      },
    },
  ];

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listNguoiBenhTiepDon}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        scroll={{ x: 3000 }}
        tableName="table_TIEP_DON_DSNBDaTiepDon"
        ref={refSettings}
        loading={loadingDsNbDaTiepDon}
        rowClassName={(record, index) => {
          return classNames("", {
            "phan-loai-khan-cap": record.phanLoaiNbCapCuu == 10, //Ưu tiên 1 (Khẩn cấp)
            "phan-loai-cap-cuu": record.phanLoaiNbCapCuu == 20, //Ưu tiên 2 (Cấp cứu)
            "phan-loai-cap-cuu-tri-hoan": record.phanLoaiNbCapCuu == 30, //Ưu tiên 3 (Cấp cứu trì hoãn)
            "phan-loai-khong-cap-cuu-45": record.phanLoaiNbCapCuu == 40, //Ưu tiên 4 (Không cấp cứu < 45')
            "phan-loai-khong-cap-cuu-1h": record.phanLoaiNbCapCuu == 60, //Ưu tiên 5 (Không cấp cứu < 1 giờ)
          });
        }}
      />

      <Pagination
        onChange={onChangePage}
        current={page + 1}
        pageSize={size}
        listData={listNguoiBenhTiepDon}
        total={totalElements}
        onShowSizeChange={handleSizeChange}
        isLoading={loadingDsNbDaTiepDon}
        first={first}
        last={last}
      />

      <ModalLichSuKCB ref={refModalLichSuKCB} />
    </Main>
  );
};

export default forwardRef(DanhSach);
