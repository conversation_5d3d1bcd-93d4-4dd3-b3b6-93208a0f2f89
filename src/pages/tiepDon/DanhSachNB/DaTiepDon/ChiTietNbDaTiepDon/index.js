import React, { useEffect, useMemo, useRef, useState } from "react";
import { Main, GlobalStyle, MainPage } from "./styled";
import { Row, Menu, Radio, Space, message } from "antd";
import ThongTinBN from "pages/tiepDon/KeDichVuKham/LeftPanel/ThongTinBN";
import DanhSachDichVu from "pages/tiepDon/components/DanhSachDichVu";
import { useParams, useHistory, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Popover, Dropdown, Button, ModalSignPrint } from "components";
import { useTranslation } from "react-i18next";
import PhatHanhThe from "pages/tiepDon/components/PhatHanhThe";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider.js";
import { flatten } from "lodash";
import ModalInXacNhanThucHienTheoDV from "pages/tiepDon/KeDichVuKham/RightPanel/ModalInXacNhanThucHienTheoDV";
import { isArray, openInNewTab } from "utils";
import {
  LOAI_DICH_VU,
  LOAI_IN,
  MA_BIEU_MAU_EDITOR,
  ROLES,
  THIET_LAP_CHUNG,
  DOI_TUONG_KCB,
  CACHE_KEY,
  TRANG_THAI_NB,
  HOTKEY,
  LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU,
  ENUM,
} from "constants/index";
import { SVG } from "assets";
import printProvider, { printJS } from "data-access/print-provider";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import {
  useCache,
  useConfirm,
  useEnum,
  useGuid,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import ModalCheckBaoHiem from "pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem";
import { parseKiemTraCongKhiInBangKeBHYT } from "utils/thiet-lap-chung-utils";
import moment from "moment";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import ModalDoSinhHieu from "pages/sinhHieu/ModalDoSinhHieu";
import { checkRole } from "lib-utils/role-utils";
import { createUniqueText } from "lib-utils";
import ModalSuaNgayThucHien from "pages/tiepDon/components/ModalSuaNgayThucHien";
import ModalGanMaBADaiHan from "pages/khamBenh/components/StepWrapper/ModalGanMaBADaiHan";
import ModalChonTieuChi from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ModalChonTieuChi";
import { useKeDichVuKham } from "pages/tiepDon/KeDichVuKham";
import ModalInChiDinhTheoDV from "pages/khamBenh/components/StepWrapper/ModalInChiDinhTheoDV";
import { showError } from "utils/message-utils";
import ModalInGiayNghiHuongBHXH from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInGiayNghiHuongBHXH";
import ModalChuyenKhoaNgoaiTru from "../ModalChuyenKhoaNgoaiTru";
import ModalInPhieuCamDoanChapNhanPTTT from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuCamDoanChapNhanPTTT";

const ChiTietNbDaTiepDon = () => {
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();
  const { showConfirm } = useConfirm();
  const layerId = useGuid();
  const { t } = useTranslation();
  const { id } = useParams();
  const { showLoading, hideLoading } = useLoading();
  const history = useHistory();
  const { state: locationState } = useLocation();
  const refModalSignPrint = useRef(null);
  const refModalInChiDinhTheoDV = useRef(null);
  const refModalInXacNhanThucHienTheoDV = useRef(null);
  const refButtonGanMaBADaiHan = useRef(null);
  const refDsDichVu = useRef(null);
  const refModalCheckBaoHiem = useRef();
  const refModalDoSinhHieu = useRef(null);
  const refSuaNgayThucHien = useRef(null);
  const refModalGanMaBADaiHan = useRef(null);
  const refModalChonTieuChi = useRef(null);
  const refModalInGiayNghiHuongBHXH = useRef(null);
  const refModalChuyenKhoaNgoaiTru = useRef(null);
  const refModalInPhieuCamDoanChapNhanPTTT = useRef();

  const [valuePhieuXacNhanThucHien, setValuePhieuXacNhanThucHien] = useCache(
    "",
    CACHE_KEY.TIEP_DON_OPTION_IN_PHIEU_XAC_NHAN_THUC_HIEN,
    1
  );
  const [dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP,
      "FALSE"
    );

  const [valuePhieuChiDinh, setValuePhieuChiDinh] = useCache(
    "",
    CACHE_KEY.TIEP_DON_OPTION_IN_PHIEU_CHI_DINH,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 1
  );

  const [CANH_BAO_NGHI_HUONG_BHXH] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_NGHI_HUONG_BHXH
  );
  const [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
  );
  const [listNgonNgu] = useEnum(ENUM.NGON_NGU, []);

  const [state, _setState] = useState({
    listPhieu: [],
    isEdit: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    nbDotDieuTri: { getById, getByTongHopId, onDongBoNbSangGut },
    nbMaBenhAn: { getDsMaBADaiHan },
    nbKSK: { tiepDonNBKSK, huyTiepNhanNbKSK, huyHoanThanhNbKSK },
    phieuIn: {
      getListPhieu,
      getFilePhieuIn,
      showFileEditor,
      getDataDanhSachPhieu,
    },
    tiepDonDichVu: { thayDoiThongTinDichVu },
    danhSachDichVuNbTiepDon: { onSizeChange },
    tiepDon: { giamDinhThe, onUpdate },
    vitalSigns: { updateData: updateDataVitalSigns },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    danhSachNbHuyTiepDon: { huyTiepDon },
  } = useDispatch();
  const { thongTinBenhNhanTongHop } = useSelector(
    (state) => state.nbDotDieuTri
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const listDichVuTiepDon = useStore(
    "danhSachDichVuNbTiepDon.listDichVuTiepDon",
    []
  );
  const dsMaBADaiHan = useStore("nbMaBenhAn.dsMaBADaiHan", []);
  const [DANG_KY_NB_SANG_PMGUT, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.DANG_KY_NB_SANG_PMGUT
  );
  const [dataTIEP_DON_KE_DICH_VU_CON, isLoadFinishTIEP_DON_KE_DICH_VU_CON] =
    useThietLap(THIET_LAP_CHUNG.TIEP_DON_KE_DICH_VU_CON);

  const listNbGoiDv = useStore("nbGoiDv.listNbGoiDv", []);
  const isKsk = useMemo(() => {
    return (
      thongTinBenhNhanTongHop?.khamSucKhoe ||
      thongTinBenhNhanTongHop?.loaiDoiTuongKsk
    );
  }, [thongTinBenhNhanTongHop]);

  const isDongBoNbGut = useMemo(() => {
    if (isLoadFinish) {
      const maDichVuDongBoGut = DANG_KY_NB_SANG_PMGUT.split(",") || [];
      return (
        !thongTinBenhNhan.nbNgoaiVien?.maHoSo &&
        !thongTinBenhNhan.nbNgoaiVien?.maNb &&
        thongTinBenhNhan.nbNgoaiVien?.loai != 40 &&
        listDichVuTiepDon.find((x) => maDichVuDongBoGut.includes(x.maDichVu))
      );
    }
  }, [thongTinBenhNhan, listDichVuTiepDon, isLoadFinish]);

  useEffect(() => {
    onAddLayer({ layerId });
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F2,
          ctrlKey: true,
          onEvent: () => {
            refButtonGanMaBADaiHan.current?.click();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  const { kiemTra, dsManHinh } = useMemo(() => {
    if (dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT) {
      return parseKiemTraCongKhiInBangKeBHYT(
        dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT
      );
    }

    return {};
  }, [dataKIEM_TRA_CONG_KHI_IN_BANG_KE_BHYT]);

  const isBADaiHan = useMemo(() => {
    if (thongTinBenhNhan?.maBenhAn) {
      const regexBADaiHan = /^([A-Za-z]{1}).*[0-9]{9}$/;

      return regexBADaiHan.test(thongTinBenhNhan?.maBenhAn);
    }
    return false;
  }, [thongTinBenhNhan?.maBenhAn]);

  useEffect(() => {
    // vì response có 1 số trường ko đồng bộ (có cái có, có cái ko và ngược lại)
    // nên hiện tại dùng 2 API /nb-dot-dieu-tri/:id và /nb-dot-dieu-tri/tong-hop/:id
    if (id) {
      getById(id);
      updateDataVitalSigns({
        configData: {
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          chiDinhTuDichVuId: null,
          dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.TIEP_DON, LOAI_DICH_VU.KHAM],
        },
      });
    }
    // if (id) getChiTietById(id);
  }, [id]);

  const fetchPhieuIn = async () => {
    if (id && isLoadFinishTIEP_DON_KE_DICH_VU_CON) {
      const listPhieu = await getListPhieu({
        nbDotDieuTriId: id,
        maManHinh: "002",
        maViTri: "00201",
        dsChiDinhTuLoaiDichVu: [
          ...(dataTIEP_DON_KE_DICH_VU_CON?.eval() ? [LOAI_DICH_VU.KHAM] : []),
          LOAI_DICH_VU.TIEP_DON,
          LOAI_DICH_VU.DAT_KHAM,
          LOAI_DICH_VU.CRM,
          LOAI_DICH_VU.GOI_KSK,
        ],
      });

      setState({ listPhieu: listPhieu || [] });
    }
  };

  useEffect(() => {
    fetchPhieuIn();
  }, [id, isLoadFinishTIEP_DON_KE_DICH_VU_CON]);

  useEffect(() => {
    if (thongTinBenhNhan?.nbThongTinId) {
      getDsMaBADaiHan(thongTinBenhNhan?.nbThongTinId);
    }
  }, [thongTinBenhNhan]);

  const refreshList = () => {
    const params = { size: 50, nbDotDieuTriId: id };
    if (!isKsk) {
      params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
    }
    onSizeChange(params);
  };

  //function
  function tiepNhanNbKSK() {
    showLoading();
    tiepDonNBKSK([thongTinBenhNhanTongHop.id])
      .then(() => {
        getById(id);
        getByTongHopId(id);
        refreshList();
      })
      .finally(hideLoading);
  }

  const onHuyTiepNhanNbKSK = () => {
    showLoading();
    huyTiepNhanNbKSK([thongTinBenhNhanTongHop.id])
      .then((s) => {
        if (s?.length) {
          showConfirm({
            title: t("common.thongBao"),
            content: `${t(
              "tiepDon.chiDuocPhepHuyTiepNhanVoiHoSoCoTrangThaiKhamSucKhoeTaoMoi",
              { maHoSo: s.join(", ") }
            )}`,
            cancelText: t("common.dongY"),
            classNameOkText: "button-warning",
            typeModal: "warning",
          });
        } else {
          message.success(t("tiepDon.huyTiepNhanNbKskThanhCong"));
          getById(id);
          getByTongHopId(id);
          refreshList();
        }
      })
      .finally(hideLoading);
  };

  const onHuyHoanThanhNbKSK = () => {
    huyHoanThanhNbKSK([thongTinBenhNhanTongHop.id]).then((s) => {
      if (s?.length) {
        showConfirm({
          title: t("common.thongBao"),
          content: `${t(
            "tiepDon.chiDuocPhepHuyHoanThanhVoiHoSoCoTrangThaiKhamSucKhoeDaTiepDon",
            { maHoSo: s.join(", ") }
          )}`,
          cancelText: t("common.dongY"),
          classNameOkText: "button-warning",
          typeModal: "warning",
        });
      } else {
        message.success(t("tiepDon.huyHoanThanhNbKskThanhCong"));
        getById(id);
        getByTongHopId(id);
      }
    });
  };

  function keThemDV() {
    gotoKeDichVuTiepDon(id, false);
  }

  const onKeDichVu = (e, payload = {}) => {
    refModalSignPrint.current.show({
      nbDotDieuTriId: id,
      maManHinh: "002",
      maViTri: "00201",
      dsChiDinhTuLoaiDichVu: [
        LOAI_DICH_VU.TIEP_DON,
        LOAI_DICH_VU.DAT_KHAM,
        LOAI_DICH_VU.CRM,
        LOAI_DICH_VU.GOI_KSK,
      ],
    });
  };

  const onPrintGiayNghiHuongBHXH = (item) => {
    let _tuoi = moment().diff(
      moment(thongTinBenhNhanTongHop?.ngaySinh),
      "years"
    );
    if (CANH_BAO_NGHI_HUONG_BHXH?.eval() && _tuoi >= 7) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("khamBenh.canhBaoNghiHuongBHXH"),
          cancelText: t("common.dong"),
          okText: t("common.dongY"),
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          printPhieu(item);
        },
        () => {}
      );
    } else {
      printPhieu(item);
    }
  };

  const printPhieu = async (item) => {
    if (item.type == "editor") {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: id,
          maManHinh: "002",
          maViTri: "00201",
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";
      if (
        item.ma == "P032" &&
        kiemTra &&
        (!dsManHinh?.length || dsManHinh?.includes("002"))
      ) {
        let data = {
          hoTen: thongTinBenhNhanTongHop.tenNb,
          maThe: thongTinBenhNhanTongHop?.maTheBhyt,
          ngaySinh: moment(thongTinBenhNhanTongHop?.ngaySinh).format(
            "YYYY-MM-DD"
          ),
        };
        const showFileEditorFunc = () =>
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: id,
            id: id,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            lichSuKyId,
          });

        const showModalCheckBH = (_data) =>
          refModalCheckBaoHiem.current.show(
            {
              show: true,
              data: data,
              hoTen: thongTinBenhNhanTongHop.tenNb,
              diaChi: thongTinBenhNhanTongHop.diaChi,
            },
            (res) => {
              if (res.boQuaTheLoi) {
                onUpdate({
                  id: id,
                  nbTheBaoHiem: { boQuaTheLoi: true },
                });

                showFileEditorFunc();
              }
            }
          );
        giamDinhThe({ ...data, keepCode: true })
          .then((res) => {
            if (res?.code === 0) {
              showFileEditorFunc();
            } else {
              showModalCheckBH(res);
            }
          })
          .catch((e) => {
            showModalCheckBH(e);
          });
      } else if (LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU.includes(item.ma)) {
        const dsTieuChi = (item.dsSoPhieu || []).map((item) => {
          const ngayThucHien = item?.thoiGianThucHien
            ? moment(item.thoiGianThucHien).format("DD/MM/YYYY")
            : "";
          const titleText = `${item?.ten1} - ${item?.ten2} - ${ngayThucHien}`;
          return {
            id: item.soPhieu,
            ten: (
              <span title={titleText}>
                <b>{titleText}</b>
              </span>
            ),
            uniqueText: createUniqueText(titleText),
            value: item.soPhieu,
          };
        });
        refModalChonTieuChi?.current?.show({ data: dsTieuChi }, (idTieuChi) => {
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: id,
            ma: item.ma,
            id: idTieuChi,
            mhParams,
          });
        });
      } else if (["P044"].includes(item.ma)) {
        refModalInGiayNghiHuongBHXH.current &&
          refModalInGiayNghiHuongBHXH.current.show(
            {
              dsSoPhieu: item.dsSoPhieu || [],
              ten: item.ten,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                chiDinhTuDichVuId: id,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
                thoiGianThucHien: filterData?.thoiGianThucHien,
                mhParams,
              });
            }
          );
      } else if (
        ["P933", "P1240", "P11129", "P11126", "P11124", "P11127"].includes(
          item.ma
        )
      ) {
        const _dsSoPhieu = (item.dsSoPhieu || []).filter(
          (x) => x.soPhieu != id
        );
        refModalInPhieuCamDoanChapNhanPTTT.current &&
          refModalInPhieuCamDoanChapNhanPTTT.current.show(
            {
              khoaChiDinhId: thongTinBenhNhanTongHop?.khoaNbId,
              dsSoPhieu: _dsSoPhieu || [],
              ten: item.ten,
              ma: item.ma,
            },
            (filterData) => {
              showFileEditor({
                phieu: item,
                id: filterData?.id,
                nbDotDieuTriId: id,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                khoaChiDinhId: filterData?.khoaChiDinhId,
                thoiGianThucHien: filterData?.thoiGianThucHien,
                mhParams,
              });
            }
          );
      } else {
        showFileEditor({
          phieu: item,
          nbDotDieuTriId: id,
          id: id,
          ma: item.ma,
          maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
            ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
            : "",
          lichSuKyId,
          mhParams: {
            ...mhParams,
            baoCaoId: item.baoCaoId,
          },
          goiDvId: item.goiDvId,
        });
      }
    } else {
      try {
        showLoading();
        let listPhieus = [];
        if (item.ma == "P011") {
          listPhieus = (item.dsSoPhieu || []).map((x) => ({
            ...item,
            dsSoPhieu: [x],
          }));
        } else {
          listPhieus = [item];
        }
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus,
          nbDotDieuTriId: id,
          trangThai: 20,
          dsChiDinhTuLoaiDichVu: [
            ...(["P047", "P010", "P011", "P067"].includes(item.ma) &&
            dataTIEP_DON_KE_DICH_VU_CON?.eval()
              ? [LOAI_DICH_VU.KHAM]
              : []),
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
            LOAI_DICH_VU.GOI_KSK,
          ],
          showError: true,
          ngonNguParams: {
            ngonNgu: state[`valueNgonNgu_${item.id}`],
            baoCaoId: state[`baoCaoId_${item.id}`],
          },
        });
        if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieu);
          // printJS({
          //   printable: finalFile,
          //   type: "pdf",
          // });
        }
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const onPrintPhieu = (item) => async () => {
    if (item.key == 0) {
      onKeDichVu(null, { isInPhieu: true });
    } else if (item.ma == "P044") {
      onPrintGiayNghiHuongBHXH(item);
    } else {
      printPhieu(item);
    }
  };

  const onPrint = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
        nbDotDieuTriId: id,
        chiDinhTuDichVuId: id,
        dsChiDinhTuLoaiDichVu: [
          LOAI_DICH_VU.TIEP_DON,
          LOAI_DICH_VU.DAT_KHAM,
          LOAI_DICH_VU.CRM,
          LOAI_DICH_VU.GOI_KSK,
        ],
        ...(dataTIEP_DON_KE_DICH_VU_CON?.eval()
          ? { chiDinhTuDichVuId: undefined, dsChiDinhTuLoaiDichVu: undefined }
          : {}),
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }

    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintTatCaChiDinh = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuXacNhanThucHienDichVu({
        nbDotDieuTriId: id,
        chiDinhTuDichVuId: id,
        dsChiDinhTuLoaiDichVu: [
          LOAI_DICH_VU.TIEP_DON,
          LOAI_DICH_VU.DAT_KHAM,
          LOAI_DICH_VU.CRM,
          LOAI_DICH_VU.GOI_KSK,
        ],
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }

    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintPhieuChuaIn = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
        nbDotDieuTriId: id,
        chiDinhTuDichVuId: id,
        dsChiDinhTuLoaiDichVu: [
          LOAI_DICH_VU.TIEP_DON,
          LOAI_DICH_VU.DAT_KHAM,
          LOAI_DICH_VU.CRM,
          LOAI_DICH_VU.GOI_KSK,
        ],
        inPhieuChiDinh: false,
        ...(dataTIEP_DON_KE_DICH_VU_CON?.eval()
          ? { chiDinhTuDichVuId: undefined, dsChiDinhTuLoaiDichVu: undefined }
          : {}),
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintPhieuXacNhanChuaIn = async () => {
    let res = null;
    try {
      res = await nbDvKyThuatProvider.getPhieuXacNhanThucHienDichVu({
        nbDotDieuTriId: id,
        chiDinhTuDichVuId: id,
        dsChiDinhTuLoaiDichVu: [
          LOAI_DICH_VU.TIEP_DON,
          LOAI_DICH_VU.DAT_KHAM,
          LOAI_DICH_VU.CRM,
          LOAI_DICH_VU.GOI_KSK,
        ],
        inPhieuChiDinh: false,
      });
    } catch (e) {
      message.error(e?.message);
      return null;
    }
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = Array.isArray(res?.data)
      ? res?.data.map((itemChild) => itemChild.file.pdf)
      : res?.data.file.pdf
      ? [res?.data.file.pdf]
      : [];
    if (!dsFilePdf.length) {
      return null;
    }
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintTheoDichVu = async (chuaThanhToan) => {
    showLoading();
    try {
      let res = null;
      if (!chuaThanhToan) {
        res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
          nbDotDieuTriId: id,
          chiDinhTuDichVuId: id,
          dsChiDinhTuLoaiDichVu: [
            ...(dataTIEP_DON_KE_DICH_VU_CON?.eval() ? [LOAI_DICH_VU.KHAM] : []),
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
            LOAI_DICH_VU.GOI_KSK,
          ],
          ...(dataTIEP_DON_KE_DICH_VU_CON?.eval()
            ? { chiDinhTuDichVuId: undefined, dsChiDinhTuLoaiDichVu: undefined }
            : {}),
        });
        refModalInChiDinhTheoDV.current.show({
          data: res.data,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          nbDotDieuTriId: id,
          chiDinhTuDichVuId: id,
          isInPhieuChiDinhTongHop: true,
        });
      } else {
        res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId: id,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          chiDinhTuDichVuId: id,
          thanhToan: false,
        });
        await printProvider.printPdf(res?.data);
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuXacNhanTheoDV = async () => {
    let res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
      nbDotDieuTriId: id,
      chiDinhTuDichVuId: id,
      dsChiDinhTuLoaiDichVu: [
        LOAI_DICH_VU.TIEP_DON,
        LOAI_DICH_VU.DAT_KHAM,
        LOAI_DICH_VU.CRM,
        LOAI_DICH_VU.GOI_KSK,
      ],
    });
    refModalInXacNhanThucHienTheoDV.current.show(res.data);
  };

  const onPrintPhieuChiDinh = async (e) => {
    // e.preventDefault();
    // e.stopPropagation();
    showLoading();
    switch (valuePhieuChiDinh) {
      case 1: {
        // tất cả chỉ định
        await onPrint();
        break;
      }
      case 2: {
        // chỉ định chưa in
        await onPrintPhieuChuaIn();
        break;
      }
      case 3: {
        // in chỉ định theo dịch vụ
        await onPrintTheoDichVu();
        break;
      }
      case 4: {
        // in chỉ định theo dịch vụ
        await onPrintTheoDichVu(true);
        break;
      }
      default:
        // tất cả chỉ định
        await onPrint();
        break;
    }
    hideLoading();
  };

  const onPrintPhieuXacNhanThucHien = async (e) => {
    showLoading();
    switch (valuePhieuXacNhanThucHien) {
      case 1: {
        // tất cả chỉ định
        await onPrintTatCaChiDinh();
        break;
      }
      case 2: {
        // chỉ định chưa in
        await onPrintPhieuXacNhanChuaIn();
        break;
      }
      case 3: {
        // in chỉ định theo dịch vụ
        await onPrintPhieuXacNhanTheoDV();
        break;
      }
      default:
        // tất cả chỉ định
        await onPrintTatCaChiDinh();
        break;
    }
    hideLoading();
  };

  const contentPhieuChiDinh = () => {
    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinh || 2}
          onChange={(e) => {
            setValuePhieuChiDinh(e.target.value);
            setState({
              popoverVisible: false,
            });
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const contentNgonNgu = (item) => {
    let _dsNgonNgu = [
      { id: 10, ten: t("account.tiengViet"), baoCaoId: item.baoCaoId },
    ];
    item.dsNgonNgu.forEach((element) => {
      const _selected = listNgonNgu.find((x) => x.id == element.ngonNgu);

      _dsNgonNgu.push({
        id: element.ngonNgu,
        ten: _selected?.ten || "",
        baoCaoId: element.id,
      });
    });

    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={state[`valueNgonNgu_${item.id}`] || 10}
          onChange={(e) => {
            setState({
              [`valueNgonNgu_${item.id}`]: e.target.value,
              [`baoCaoId_${item.id}`]: e.target.baoCaoId,
              [`openPopover_${item.id}`]: false,
            });
          }}
        >
          {/* Tiếng Việt là mặc định */}
          <Space direction="vertical">
            {_dsNgonNgu.map((item, index) => (
              <Radio key={index} value={item.id} baoCaoId={item.baoCaoId}>
                {item.ten}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const contentPhieuXacNhanThucHien = () => {
    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuXacNhanThucHien || 2}
          onChange={(e) => {
            setValuePhieuXacNhanThucHien(e.target.value);
            setState({
              popoverVisible2: false,
            });
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };
  const menu = useMemo(() => {
    const phieus = [
      { key: 0, ten: t("phieuIn.inTatCa") },
      ...(state.listPhieu || []),
    ];
    return (
      <Menu
        items={(phieus || []).map((item, index) => {
          if (item.ma === "P075") {
            return {
              key: index + "",
              label: (
                <div style={{ display: "flex" }}>
                  <div onClick={onPrintPhieuChiDinh} style={{ flex: 1 }}>
                    {item.ten || item.tenBaoCao}
                  </div>

                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"tiep-don-in-options right"}
                    placement="leftBottom"
                    content={contentPhieuChiDinh()}
                    trigger="click"
                    open={state.popoverVisible}
                    onOpenChange={(open) => {
                      setState({
                        popoverVisible: open,
                      });
                    }}
                  >
                    <SVG.IcOption
                      className="ic-action"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  </Popover>
                </div>
              ),
            };
          }
          if (item.ma === "P082") {
            return {
              key: index + "",
              label: (
                <div style={{ display: "flex" }}>
                  <div
                    onClick={onPrintPhieuXacNhanThucHien}
                    style={{ flex: 1 }}
                  >
                    {item.ten || item.tenBaoCao}
                  </div>

                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"tiep-don-in-options right"}
                    placement="leftBottom"
                    content={contentPhieuXacNhanThucHien()}
                    trigger="click"
                    open={state.popoverVisible2}
                    onOpenChange={(open) => {
                      setState({
                        popoverVisible2: open,
                      });
                    }}
                  >
                    <SVG.IcOption
                      className="ic-action"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  </Popover>
                </div>
              ),
            };
          }
          if (item.ma === "P040") {
            return {
              key: index + "",
              label: <div>{item.ten || item.tenBaoCao}</div>,
              children: listNbGoiDv?.map((o) => ({
                key: o.id,
                label: (
                  <a onClick={onPrintPhieu({ ...item, goiDvId: o.id })}>
                    {o.tenGoiDv}
                  </a>
                ),
              })),
            };
          }

          return {
            key: index + "",
            label: (
              <div className="flex">
                <div style={{ flex: 1 }} onClick={onPrintPhieu(item)}>
                  {item.ten || item.tenBaoCao}
                </div>
                {item.dsNgonNgu?.length > 0 && (
                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"tiep-don-in-options right"}
                    placement="rightTop"
                    content={contentNgonNgu(item)}
                    trigger="click"
                    open={state[`openPopover_${item.id}`] || false}
                    onOpenChange={(value) => {
                      setState({
                        [`openPopover_${item.id}`]: value,
                      });
                    }}
                  >
                    <SVG.IcLanguage
                      color={"var(--color-blue-primary)"}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  </Popover>
                )}
              </div>
            ),
          };
        })}
      />
    );
  }, [
    valuePhieuChiDinh,
    valuePhieuXacNhanThucHien,
    thongTinBenhNhanTongHop,
    listNbGoiDv,
    state,
  ]);

  const onSetData = (data) => {
    setState({ dataSource: data });
  };

  const onXoaNhieuDichVu = () => {
    refDsDichVu.current && refDsDichVu.current.onXoaNhieuDichVu();
  };

  const onSuaNgayThucHienDichVu = (values) => {
    refDsDichVu.current && refDsDichVu.current.onSuaNgayThucHienDichVu(values);
  };

  const onDoSinhHieu = () => {
    refModalDoSinhHieu.current &&
      refModalDoSinhHieu.current.show({
        nbDotDieuTriId: id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
        khoaChiDinhId: thongTinBenhNhanTongHop?.khoaNbId,
      });
  };

  const onSave = () => {
    const data = (state.dataSource || []).map((item) => {
      return {
        id: Number(item?.id),
        nbDotDieuTriId: item?.nbDotDieuTriId,
        bacSiKhamId: item?.bacSiKhamId,
        nbDvKyThuat: {
          phongThucHienId: item?.phongThucHienId,
          khongThucHien: item?.khongThucHien,
          lyDoKhongThucHien: item?.lyDoKhongThucHien,
        },
        nbDichVu: {
          dichVuId: item?.dichVuId,
          loaiDichVu: item?.loaiDichVu,
          loaiHinhThanhToanId: item?.loaiHinhThanhToanId,
          tuTra: item?.tuTra,
          khongTinhTien: item?.khongTinhTien,
          thoiGianThucHien:
            item?.thoiGianThucHien &&
            moment(item?.thoiGianThucHien).format("YYYY-MM-DD HH:mm:ss"),
          bacSiChiDinhId: item?.bacSiChiDinhId,
        },
      };
    });
    showLoading();
    thayDoiThongTinDichVu({
      data: data,
    })
      .then(() => {
        setState({ isEdit: false });
        onSizeChange({
          size: 10,
          nbDotDieuTriId: id,
          dsChiDinhTuLoaiDichVu: [
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
          ],
        });
      })
      .finally(() => hideLoading());
  };

  const onSuaNgayThucHien = () => {
    refSuaNgayThucHien.current &&
      refSuaNgayThucHien.current.show({ dataSource: state.dataSource }, () => {
        setState({ isEdit: false });
        onSizeChange({
          size: 10,
          nbDotDieuTriId: id,
          dsChiDinhTuLoaiDichVu: [
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
          ],
        });
      });
  };

  const dongBoNb = () => {
    try {
      showLoading();
      onDongBoNbSangGut({ id: id })
        .then(() => getById(id))
        .catch((e) => {
          showConfirm({
            title: t("common.thongBao"),
            content: e?.message,
            cancelText: t("common.huy"),
          });
        });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClickGanMaBADaiHan =
    ({ title, type }) =>
    () => {
      refModalGanMaBADaiHan.current &&
        refModalGanMaBADaiHan.current.show({
          nbDotDieuTriId: id,
          title,
          type,
          maBenhAn: thongTinBenhNhan?.maBenhAn,
        });
    };

  const onViewDetailLapBADaiHan = () => {
    history.push(`/dieu-tri-dai-han/chi-tiet-lap-benh-an/${id}`);
  };

  const listMaBaDaiHan = useMemo(() => {
    return dsMaBADaiHan.filter(
      (i) => i.trangThai === TRANG_THAI_NB.DANG_DIEU_TRI
    );
  }, [dsMaBADaiHan]);

  const onHuyTiepDon = () => {
    showConfirm(
      {
        title: t("tiepDon.huyTiepDon"),
        content: t("tiepDon.banCoChacChanMuonHuyTiepDonKhong"),
        cancelText: t("common.quayLai"),
        okText: t("tiepDon.huyTiepDon"),
        classNameOkText: "button-error",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyTiepDon({ dsId: [id], active: false })
          .then(() => {
            getById(id);
            getByTongHopId(id);
          })
          .finally(hideLoading);
      }
    );
  };

  const onChuyenKhoaNgoaiTru = () => {
    refModalChuyenKhoaNgoaiTru.current &&
      refModalChuyenKhoaNgoaiTru.current.show(
        { data: thongTinBenhNhan },
        () => {
          getById(id);
          getByTongHopId(id);
        }
      );
  };

  return (
    <MainPage
      breadcrumb={[
        {
          title: t("tiepDon.quanLyTiepDon"),
          link: "/quan-ly-tiep-don",
        },
        {
          title: t("tiepDon.danhSachNguoiBenhDaTiepDon"),
          link:
            "/quan-ly-tiep-don/danh-sach-nb-da-tiep-don" +
            transformObjToQueryString(locationState),
        },
        {
          title: thongTinBenhNhanTongHop?.tenNb,
          link: `/quan-ly-tiep-don/danh-sach-nb-da-tiep-don/${id}`,
        },
      ]}
      title={t("tiepDon.chiTietNguoiBenhDaTiepDon")}
      actionRight={
        <>
          {/* trạng thái nb  < 100 + 0300309 - Chuyển khoa NB ngoại trú  */}
          {checkRole([ROLES["TIEP_DON"].CHUYEN_KHOA_NB_NGOAI_TRU]) &&
            thongTinBenhNhan.trangThai < TRANG_THAI_NB.DA_RA_VIEN && (
              <Button onClick={onChuyenKhoaNgoaiTru}>
                {t("pttt.chuyenKhoa")}
              </Button>
            )}

          <Button onClick={onHuyTiepDon}>{t("tiepDon.huyTiepDon")}</Button>
          {[
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(thongTinBenhNhanTongHop?.doiTuongKcb) &&
            thongTinBenhNhan?.maBenhAn === null && (
              <Button onClick={onViewDetailLapBADaiHan}>
                {t("khamBenh.lapBADaiHan")}
              </Button>
            )}
          {[
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(thongTinBenhNhanTongHop?.doiTuongKcb) &&
            thongTinBenhNhan?.maBenhAn === null &&
            isArray(listMaBaDaiHan, 1) && (
              <Button
                onClick={onClickGanMaBADaiHan({
                  title: t("khamBenh.ganMaBADaiHan"),
                })}
                ref={refButtonGanMaBADaiHan}
              >
                {t("khamBenh.ganMaBADaiHan")} [CTRL + F2]
              </Button>
            )}
          {[
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(thongTinBenhNhanTongHop?.doiTuongKcb) &&
            isBADaiHan &&
            isArray(listMaBaDaiHan, 2) && (
              <Button
                onClick={onClickGanMaBADaiHan({
                  title: t("khamBenh.doiMaBADaiHan"),
                  type: "doiMaBADaiHan",
                })}
              >
                {t("khamBenh.doiMaBADaiHan")}
              </Button>
            )}
          {isDongBoNbGut && (
            <Button onClick={dongBoNb}>{t("tiepDon.dongBoNbNgoaiVien")}</Button>
          )}
          {!state.isEdit && (
            <Button onClick={onDoSinhHieu}>{t("tiepDon.doSinhHieu")}</Button>
          )}
          {!state.isEdit && <PhatHanhThe />}
          {!state.isEdit && (
            <Dropdown trigger="click" overlay={menu}>
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  fetchPhieuIn();
                }}
                icon={<SVG.IcPrint />}
              >
                {t("tiepDon.inGiayTo")}
              </Button>
            </Dropdown>
          )}
          {!state.isEdit && (
            <Button onClick={keThemDV}>{t("tiepDon.themDichVu")}</Button>
          )}
          {!state.isEdit && (
            <Button onClick={() => setState({ isEdit: true })}>
              {t("common.sua")}
            </Button>
          )}
          {state.isEdit && (
            <Button onClick={onXoaNhieuDichVu}>{t("common.xoa")}</Button>
          )}
          {state.isEdit && (
            <Button onClick={() => setState({ isEdit: false })}>
              {t("common.quayLai")}
            </Button>
          )}
          {state.isEdit && <Button onClick={onSave}>{t("common.luu")}</Button>}
          {state.isEdit && (
            <Button onClick={onSuaNgayThucHien}>
              {t("tiepDon.suaNgayThucHien")}
            </Button>
          )}
          {isKsk && thongTinBenhNhanTongHop?.trangThaiKsk === 10 && (
            <Button type="primary" onClick={tiepNhanNbKSK}>
              {t("tiepDon.tiepNhanNguoiBenhKSK")}
            </Button>
          )}
          {isKsk &&
            thongTinBenhNhanTongHop?.trangThaiKsk === 20 &&
            checkRole([ROLES["TIEP_DON"].HUY_TIEP_NHAN_NB_KSK]) && (
              <Button type="primary" onClick={onHuyTiepNhanNbKSK}>
                {t("tiepDon.huyTiepNhanNBKSK")}
              </Button>
            )}
          {isKsk &&
            thongTinBenhNhanTongHop?.trangThaiKsk === 30 &&
            checkRole([ROLES["TIEP_DON"].HUY_HOAN_THANH_NB_KSK]) && (
              <Button type="primary" onClick={onHuyHoanThanhNbKSK}>
                {t("tiepDon.huyHoanThanhNBKSK")}
              </Button>
            )}
        </>
      }
    >
      <Main>
        <GlobalStyle />
        <Row span={24}>
          <ThongTinBN />
        </Row>
        <Row className="content">
          <DanhSachDichVu
            nbDotDieuTriId={id}
            isNBKhamSucKhoe={isKsk}
            isEdit={state.isEdit}
            onSetData={onSetData}
            ref={refDsDichVu}
          />
        </Row>
      </Main>
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalInChiDinhTheoDV ref={refModalInChiDinhTheoDV} />
      <ModalInXacNhanThucHienTheoDV ref={refModalInXacNhanThucHienTheoDV} />
      <ModalCheckBaoHiem ref={refModalCheckBaoHiem} isShowButtonOk={true} />
      <ModalDoSinhHieu isSinhHieu ref={refModalDoSinhHieu} />{" "}
      <ModalSuaNgayThucHien
        ref={refSuaNgayThucHien}
        onSuaNgayThucHienDichVu={onSuaNgayThucHienDichVu}
      />
      <ModalGanMaBADaiHan isTiepDon ref={refModalGanMaBADaiHan} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
      <ModalInGiayNghiHuongBHXH ref={refModalInGiayNghiHuongBHXH} />
      <ModalChuyenKhoaNgoaiTru ref={refModalChuyenKhoaNgoaiTru} />
      <ModalInPhieuCamDoanChapNhanPTTT
        ref={refModalInPhieuCamDoanChapNhanPTTT}
      />
    </MainPage>
  );
};
export default ChiTietNbDaTiepDon;
