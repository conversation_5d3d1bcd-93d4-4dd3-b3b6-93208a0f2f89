import React, {
  useRef,
  useImperativeHandle,
  useState,
  forwardRef,
  useEffect,
} from "react";
import { Main } from "./styled";
import {
  ModalTemplate,
  But<PERSON>,
  TableWrapper,
  HeaderSearch,
  InputTimeout,
} from "components";
import moment, { isMoment } from "moment";
import { message, Radio } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useConfirm, useEnum, useRefFunc, useStore, useThietLap } from "hooks";
import {
  DOI_TUONG_KCB,
  ENUM,
  PHAN_LOAI_DOI_TUONG,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import { DEFAULT_THONG_TIN_CHUNG } from "pages/application/TuyChinhGiaoDienPhamMem/TiepDon/config";
import { isArray } from "lodash";
import { useKeDichVuKham } from "pages/tiepDon/KeDichVuKham";
import { checkRole } from "lib-utils/role-utils";
import { renderNbDiaChi } from "utils/index";
import ModalCanhBaoTraThuocBhyt from "../ModalCanhBaoTraThuocBhyt";

const removeNullFromObject = (obj) => {
  const newObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (
        (obj[key] !== null) & (typeof obj[key] === "object") &&
        !isMoment(obj[key]) &&
        !isArray(obj[key])
      ) {
        newObj[key] = removeNullFromObject(obj[key]);
        // Check if the nested object is empty after removing null values
        if (Object.keys(newObj[key]).length === 0) {
          delete newObj[key];
        }
      } else if (obj[key] !== null) {
        newObj[key] = obj[key];
      }
    }
  }

  return newObj;
};

const ModalTrungThongTin = (
  {
    isTiepDonTiemChung,
    renderMessTheoMaLoi,
    boQuaKiemTraThanhToan = false,
    ...props
  },
  ref
) => {
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();

  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refModal = useRef(null);
  const {
    tiepDon: { kiemTraThanhToan, updateDetail, updateData },
  } = useDispatch();
  const refCallback = useRef(null);
  const refModalCanhBaoTraThuocBhyt = useRef(null);
  const [state, _setState] = useState({
    data: [],
  });
  const listAllNguonNguoiBenh = useStore(
    "nguonNguoiBenh.listAllNguonNguoiBenh"
  );
  const dataMacDinh = useStore("tiepDon.dataMacDinh", {});
  const nbTiemChung = useStore("tiepDon.nbTiemChung", {});
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const {
    doiTuong,
    nbTheBaoHiem,
    loaiDoiTuongId,
    maNb: maNbTiepDon,
  } = useStore(
    "tiepDon",
    {},
    {
      fields: "doiTuong,nbTheBaoHiem,loaiDoiTuongId,maNb",
    }
  );
  const capCuu = useStore("tiepDon.capCuu", false);
  const thongTinChung = useStore(
    "thietLap.thietLapGiaoDien.tiepDon.thongTinChung",
    DEFAULT_THONG_TIN_CHUNG
  );
  const [dataMA_THE_BH_BO_QUA_CHECK_CONG] = useThietLap(
    THIET_LAP_CHUNG.MA_THE_BH_BO_QUA_CHECK_CONG
  );
  const [dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TICH_THONG_TUYEN_NB_BHYT
  );

  const [KIEM_TRA_THE_BAO_HIEM_ON_OFF] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_THE_BAO_HIEM_ON_OFF
  );
  const [dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM
  );

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    data,
    checked,
    maNb,
    soDienThoai,
    tenNb,
    sdtNguoiBaoLanh,
    maSoGiayToTuyThan,
    diaChi,
    dataIndex,
  } = state;
  useImperativeHandle(ref, () => ({
    show: (options = {}, callback) => {
      let dataCheck = options?.data?.map((option) => {
        return {
          ...option,
        };
      });
      setState({
        data: [...dataCheck],
        dataIndex: [...dataCheck],
        show: options?.show ?? true,
        checked: "",
        dataBVE: options?.dataBVE,
        tiemChung: options.tiemChung,
      });
      refCallback.current = callback;
    },
    onXacNhan: () => onImplicitlyClickRow(),
  }));

  const onImplicitlyClickRow = useRefFunc(() => {
    onClickRow(state.data?.[0]);
  });

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);
  const onCancel = () => {
    setState({
      show: false,
    });
  };
  const onSearch = (variables) => (value) => {
    setState({ [`${variables}`]: value });
    let soDienThoaiSearch =
      variables === "soDienThoai" ? value : soDienThoai ? soDienThoai : "";
    let maNbSearch = variables === "maNb" ? value : maNb ? maNb : "";
    let tenNbSearch = variables === "tenNb" ? value : tenNb ? tenNb : "";
    let sdtNguoiBaoLanhSearch =
      variables === "sdtNguoiBaoLanh"
        ? value
        : sdtNguoiBaoLanh
        ? sdtNguoiBaoLanh
        : "";
    let maSoGiayToTuyThanSearch =
      variables === "maSoGiayToTuyThan"
        ? value
        : maSoGiayToTuyThan
        ? maSoGiayToTuyThan
        : "";
    let diaChiSearch = variables === "diaChi" ? value : diaChi ? diaChi : "";
    let dataSearchText = (data || []).filter((item) => {
      return (
        (item.soDienThoai || "").indexOf(soDienThoaiSearch) !== -1 &&
        (item.maNb || "").indexOf(maNbSearch) !== -1 &&
        (item.tenNb || "")
          .toLocaleLowerCase()
          .unsignText()
          .indexOf(tenNbSearch?.toLocaleLowerCase().unsignText()) !== -1 &&
        (item.sdtNguoiBaoLanh || "").indexOf(sdtNguoiBaoLanhSearch) !== -1 &&
        (item.maSoGiayToTuyThan || "").indexOf(maSoGiayToTuyThanSearch) !==
          -1 &&
        (item.diaChi || "")
          .toLocaleLowerCase()
          .unsignText()
          .indexOf(diaChiSearch?.toLocaleLowerCase().unsignText()) !== -1
      );
    });
    setState({ dataIndex: dataSearchText });
  };
  const onBack = (data, code) => {
    setState({
      show: false,
      data: {},
    });
    if (refCallback.current) refCallback.current(data, code);
  };

  const onClickRow = (value) => {
    setState({ checked: value?.id?.toString() });
    if (boQuaKiemTraThanhToan) {
      const payload = {
        ...value,
        nbDiaChi: {
          diaChi: value.diaChi,
          quocGiaId: value.quocGiaId,
          quocGia: value.quocGia,
          tinhThanhPho: value.tinhThanhPho,
          tinhThanhPhoId: value.tinhThanhPhoId,
          quanHuyen: value.quanHuyen,
          quanHuyenId: value.quanHuyenId,
          xaPhuong: value.xaPhuong,
          xaPhuongId: value.xaPhuongId,
          soNha: value.soNha,
        },
        nbNguoiBaoLanh: {
          soDienThoai: value.sdtNguoiBaoLanh,
          hoTen: value.tenNguoiBaoLanh,
        },
      };
      onBack(payload);
    } else {
      onKiemTraThanhToan(value?.maNb, value?.maTiemChung)();
    }
  };

  const onKiemTraThanhToan = (value, maTiemChung) => () => {
    kiemTraThanhToan(
      {
        maNb: value,
        nguonNbId: state.dataBVE?.nbNguonNb?.nguonNbId,
        doiTuong: state.dataBVE?.doiTuong,
        capCuu: state.dataBVE?.capCuu || capCuu,
        tiemChung: state.tiemChung,
        ...(dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM?.eval() && {
          maNb: maNbTiepDon ?? value,
          loaiDoiTuongId,
          doiTuong,
        }),
      },
      state.dataBVE,
      nbTiemChung
    ).then((s) => {
      if (s?.data?.thoiGianHen) {
        const _thoiGianHen = moment(s?.data?.thoiGianHen).startOf("day");
        const _soNgay = _thoiGianHen.diff(moment().startOf("day"), "days");

        if (_soNgay > 0) {
          message.warning(
            t("tiepDon.nbDenSomSoVoiLichHen", {
              tenNb: s?.data?.tenNb || "",
              thoiGianHen: s?.data?.thoiGianHen
                ? moment(s?.data?.thoiGianHen).format("DD/MM/YYYY HH:mm:ss")
                : "",
              soNgay: _soNgay,
            })
          );
        }
      }

      const phanLoaiDoiTuong = [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(s?.data?.doiTuongKcb)
        ? s?.data?.trangThaiNb >= 100
          ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
          : PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
        : PHAN_LOAI_DOI_TUONG.TAI_KHAM;
      const nbDiaChi = renderNbDiaChi({
        dataDiachi: s?.data?.nbDiaChi,
        dataMacDinh,
      });

      let payload = {
        maNb: s?.data?.maNb,
        tenNb: s?.data?.tenNb,
        soDienThoai: s?.data?.soDienThoai,
        ngaySinh: s?.data?.ngaySinh,
        gioiTinh: s?.data?.gioiTinh,
        nbDiaChi: nbDiaChi,
        quocTichId: s?.data?.quocTichId,
        ...(doiTuong === s?.data?.doiTuong && !state.dataBVE
          ? {
              nbTheBaoHiem: {
                boQuaTheLoi: false,
                thongTuyen:
                  dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT?.eval() && !capCuu
                    ? true
                    : false,
                maThe: nbTheBaoHiem?.maThe
                  ? nbTheBaoHiem?.maThe
                  : s?.data?.nbTheBaoHiem?.maThe,
                denNgayGiayChuyen: s?.data?.nbTheBaoHiem?.denNgayGiayChuyen,
                tuNgayGiayChuyen: s?.data?.nbTheBaoHiem?.tuNgayGiayChuyen,
                ...(dataMA_THE_BH_BO_QUA_CHECK_CONG
                  ?.split(",")
                  .includes(s?.data?.nbTheBaoHiem?.maThe.substring(0, 2)) ||
                !KIEM_TRA_THE_BAO_HIEM_ON_OFF?.eval()
                  ? {
                      tuNgay: s?.data?.nbTheBaoHiem?.tuNgay,
                      denNgay: s?.data?.nbTheBaoHiem?.denNgay,
                      noiDangKyId: s?.data?.nbTheBaoHiem?.noiDangKyId,

                      diaChi: s?.data?.nbTheBaoHiem?.diaChi,
                    }
                  : {}),
              },
            }
          : { nbTheBaoHiem: nbTheBaoHiem }),
        email: s?.data?.email,
        nbGiayToTuyThan: s?.data?.nbGiayToTuyThan,
        danTocId: s?.data?.danTocId,
        soBaoHiemXaHoi: s?.data?.soBaoHiemXaHoi,
        ngheNghiepId: s?.data?.ngheNghiepId,
        nbNguoiBaoLanh: s?.data?.nbNguoiBaoLanh,
        chiNamSinh: s?.data?.chiNamSinh,
        anhDaiDien: s?.data?.anhDaiDien,
        maHoSoCu: s?.data?.maHoSo,
        dsPhanLoaiNbId: s?.data?.dsPhanLoaiNbId,
        doiTuong: doiTuong,
        doiTuongCu: s?.data?.doiTuong,
        doiTuongKcbCu: s?.data?.doiTuongKcb,
        bangLaiXeId: s?.data?.bangLaiXeId,
        ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
          ? { phanLoaiDoiTuong: phanLoaiDoiTuong }
          : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
        nbThongTinId: s.data?.nbThongTinId,
        congTyBaoHiemId: s?.data?.congTyBaoHiemId,
        ...(state.dataBVE
          ? {
              nbNguonNb: { nguonNbId: state.dataBVE.nbNguonNb?.nguonNbId },
              dsDichVu: (state.dataBVE.dsDichVu || [])
                .filter((x) => !x.chiDinh)
                .map((item) => {
                  return {
                    ...item,
                    phongId: item.phongThucHienId,
                    ngoaiVienId: item?.id,
                  };
                }),
              nbNgoaiVien: state.dataBVE.nbNgoaiVien,
            }
          : {
              nbNguonNb: {
                nguonNbId: dataMacDinh?.nguonNbId,
                ghiChu: s?.data?.nbNguonNb?.ghiChu,
              },
            }),
        ...(isTiepDonTiemChung
          ? {
              tiemChung: !!(maTiemChung ? maTiemChung : nbTiemChung),
              nbTiemChung: maTiemChung
                ? { maTiemChung: maTiemChung }
                : nbTiemChung,
            }
          : {}),
        codeKiemTraThanhToan: s?.code,
        nbTongKetRaVien: {
          cdNoiGioiThieu: s?.data?.nbTongKetRaVien?.cdNoiGioiThieu,
        },
        dataTemp: {
          /*
          SAKURA-67495 FE (OP) [Tiếp đón] Không tự động fill địa chỉ cũ NB đã inactive trong danh mục
          nam.mn 16/07/2025 - //sinh địa chỉ 2 cấp từ dữ liệu tên xã tỉnh của người bệnh, nếu không thì tự xử lý tách từ địa chỉ cũ.
          không truyền thông tin địa chỉ vào nbDiaChi nữa mà sẽ truyền vào dataTemp đẻ component tự parse
          */
          diaChi:
            [s?.data?.nbDiaChi?.tenXaPhuong, s?.data?.nbDiaChi?.tenTinhThanhPho]
              .filter((item) => item)
              .join(", ") || s?.data?.nbDiaChi?.diaChi?.toAddress()?.diaChi,
        },
      };

      let dataUpdate = removeNullFromObject(payload);
      updateDetail(dataUpdate);

      if (s?.code === 0 || s?.code === 8031) {
        if (s?.code === 8031) {
          message.error(s?.message);
        }
        if (s.code === 0) {
          updateData({
            thongTinThanhToan: {
              daThanhToan: true,
              messageChuaThanhToan: s.message,
            },
          });
        }
        onBack(payload);
      } else if (s?.code === 7921) {
        const { content, subContent } = renderMessTheoMaLoi(s, {
          ...payload,
          thoiGianVaoVien: s?.data.thoiGianVaoVien,
          maHoSo: s?.data?.maHoSo,
          loaiDoiTuong: s?.data?.loaiDoiTuong,
        });
        showConfirm(
          {
            title: t("common.thongBao"),
            content: content,
            okText: t("common.dongY"),
            showImg: false,
            showBtnOk:
              s.code === 7925 &&
              !checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_DV_CHUA_THANH_TOAN])
                ? false
                : true,
            showBtnCancel: false,
            typeModal: "warning",
            subContent: subContent,
          },
          () => {
            onBack(payload);
          }
        );
      } else if (s?.code === 7968) {
        showConfirm(
          {
            title: t("common.thongBao"),
            content: `Người bệnh tồn tại mã hồ sơ ${s?.data.maHoSo} trong ngày. Vui lòng tiếp tục sử dụng hồ sơ cũ`,
            okText: t("common.dongY"),
            showImg: false,
            showBtnOk: true,
            showBtnCancel: false,
            typeModal: "warning",
          },
          () => {
            gotoKeDichVuTiepDon(s?.data?.id, false);
          }
        );
      } else if (s?.code === 7920) {
        showConfirm(
          {
            title: t("common.thongBao"),
            content: s?.message,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            showImg: false,
            showBtnOk: true,
            showBtnCancel: true,
            typeModal: "warning",
          },
          () => {
            onBack(payload, true);
          },
          () => {
            onBack(payload);
          }
        );
      } else if (
        s?.code === 7922 ||
        s?.code === 7923 ||
        s?.code === 7924 ||
        s?.code === 7925 ||
        s?.code === 7935 ||
        s?.code === 7951 ||
        s?.code === 7936
      ) {
        const { content, subContent } = renderMessTheoMaLoi(s, {
          ...payload,
          thoiGianVaoVien: s?.data.thoiGianVaoVien,
          maHoSo: s?.data?.maHoSo,
          loaiDoiTuong: s?.data?.loaiDoiTuong,
        });
        if (
          s.code === 7951 &&
          checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_PHIEU_THU_BH_0_DONG])
        ) {
          updateData({
            thongTinThanhToan: {
              daThanhToan: false,
              messageChuaThanhToan: s.message,
            },
          });
        } else if (s.code === 7936) {
          refModalCanhBaoTraThuocBhyt.current &&
            refModalCanhBaoTraThuocBhyt.current.show(
              {
                data: s?.data,
              },
              (payload) => {
                const { value } = payload || {};
                if (value === 1) {
                  updateDetail(dataUpdate);
                  updateData({
                    thongTinThanhToan: {
                      daThanhToan: true,
                      messageChuaThanhToan: null,
                      boQuaCheckTaiKhamSom: true,
                    },
                  });
                }
              }
            );
        } else {
          showConfirm(
            {
              showBtnOk:
                s.code === 7925 &&
                !checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_DV_CHUA_THANH_TOAN])
                  ? false
                  : true,
              title: t("common.thongBao"),
              content: content,
              subContent: subContent,
              showBtnCancel: s.code === 7935 ? false : true,
            },
            () => {
              if (s.code === 7935 && !state.dataBVE) {
                gotoKeDichVuTiepDon(s?.data?.id, false);
              } else {
                updateData({
                  thongTinThanhToan: {
                    daThanhToan: false,
                    messageChuaThanhToan: s.message,
                  },
                });
                onBack({
                  ...payload,
                  id: s?.data?.id,
                  khoaId: s?.data?.khoaId,
                });
              }
            },
            () => {
              gotoKeDichVuTiepDon(s?.data?.id, false);
            }
          );
        }
      }
      if (s?.code === 8043) {
        showConfirm(
          {
            title: t("common.thongBao"),
            content: s?.message,
            cancelText: t("common.huy"),
            okText: t("common.dongY"),
            showImg: false,
            showBtnOk: true,
            showBtnCancel: false,
            typeModal: "warning",
          },
          () => {
            setState({
              show: false,
            });
          }
        );
      }
    });
  };

  const onXacNhan = useRefFunc(() => {
    if (maNb && !boQuaKiemTraThanhToan) {
      onKiemTraThanhToan(maNb);
    }
  });

  return (
    <ModalTemplate
      ref={refModal}
      title={t("tiepDon.danhSachNguoiBenhTrungThongTinHanhChinh")}
      onCancel={onCancel}
      width={1260}
      actionLeft={<Button.QuayLai onClick={() => onBack()}></Button.QuayLai>}
      actionRight={
        <>
          <Button type="primary" onClick={onXacNhan} minWidth={100}>
            {t("common.xacNhan")}
          </Button>
        </>
      }
    >
      <Main>
        <TableWrapper
          columns={[
            {
              title: <HeaderSearch title="" />,
              width: "50px",
              dataIndex: "action",
              key: "action",
              align: "center",
              fixed: "left",
              render: (item, list) => {
                return (
                  <>
                    <Radio
                      value={list?.id}
                      checked={checked === list?.id?.toString()}
                      onClick={(e) => {
                        setState({
                          checked: e.target.value,
                          maNb: list?.maNb,
                        });
                      }}
                    />
                  </>
                );
              },
            },
            {
              title: <HeaderSearch title={t("common.doiTuong")} />,
              width: "130px",
              dataIndex: "doiTuong",
              key: "doiTuong",
              render: (item) => (
                <b> {listDoiTuong.find((x) => x.id === item)?.ten}</b>
              ),
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.sdt")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timSDT")}
                      onChange={onSearch("soDienThoai")}
                      value={soDienThoai}
                    />
                  }
                />
              ),
              width: "120px",
              dataIndex: "soDienThoai",
              key: "soDienThoai",
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.maNb")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timMaNb")}
                      onChange={onSearch("maNb")}
                      value={maNb}
                    />
                  }
                />
              ),
              width: "120px",
              dataIndex: "maNb",
              key: "maNb",
            },
            {
              title: (
                <HeaderSearch
                  title={t("tiepDon.tenNb")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timTenNb")}
                      onChange={onSearch("tenNb")}
                      value={tenNb}
                    />
                  }
                />
              ),
              width: "180px",
              dataIndex: "tenNb",
              key: "tenNb",
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.sdtNguoiBaoLanh")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timSdtNguoiBaoLanh")}
                      onChange={onSearch("sdtNguoiBaoLanh")}
                      value={sdtNguoiBaoLanh}
                    />
                  }
                />
              ),
              width: "120px",
              dataIndex: "sdtNguoiBaoLanh",
              key: "sdtNguoiBaoLanh",
            },
            {
              title: (
                <HeaderSearch
                  title={t("tiepDon.cmtHc")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timCmtHc")}
                      onChange={onSearch("maSoGiayToTuyThan")}
                      value={maSoGiayToTuyThan}
                    />
                  }
                />
              ),
              width: "100px",
              dataIndex: "maSoGiayToTuyThan",
              key: "maSoGiayToTuyThan",
            },
            {
              title: (
                <HeaderSearch
                  title={t("common.diaChi")}
                  search={
                    <InputTimeout
                      placeholder={t("tiepDon.timDiaChi")}
                      onChange={onSearch("diaChi")}
                      value={diaChi}
                    />
                  }
                />
              ),
              width: "250px",
              dataIndex: "diaChi",
              key: "diaChi",
            },
            {
              title: (
                <HeaderSearch title={t("tiepDon.thoiGianChuaBenhGanNhat")} />
              ),
              width: "180px",
              dataIndex: "thoiGianVaoVien",
              key: "thoiGianVaoVien",
              render: (item) => {
                return (
                  <div>
                    {item && moment(item).format("DD/MM/YYYY HH:mm:ss")}
                  </div>
                );
              },
            },
            {
              title: <HeaderSearch title={t("common.khoa")} />,
              width: "200px",
              dataIndex: "tenKhoa",
              key: "tenKhoa",
            },
            {
              title: <HeaderSearch title={t("common.chuyenKhoa")} />,
              width: "120px",
              dataIndex: "tenChuyenKhoa",
              key: "tenChuyenKhoa",
            },
            {
              title: <HeaderSearch title={t("tiepDon.nguonNguoiBenh")} />,
              width: "130px",
              dataIndex: "nguonNbId",
              key: "nguonNbId",
              render: (item) =>
                listAllNguonNguoiBenh.find((x) => x.id === item)?.ten,
            },
            {
              title: <HeaderSearch title={t("tiepDon.ngayHenKham")} />,
              width: 150,
              dataIndex: "thoiGianHenKham",
              key: "thoiGianHenKham",
              render: (item) => {
                const style =
                  item && moment() <= moment(item) ? { color: "red" } : {};
                return (
                  <div style={style}>
                    {" "}
                    {item && moment(item).format("DD/MM/YYYY HH:mm:ss")}{" "}
                  </div>
                );
              },
            },
            {
              title: <HeaderSearch title={t("tiepDon.thuocDangSuDung")} />,
              width: 200,
              dataIndex: "dsThuoc",
              key: "dsThuoc",
              render: (item) => {
                const data = (item || [])
                  .filter((x) => x.soNgay > x.soNgayThamChieu)
                  .map((x1) => {
                    return `${x1.tenDichVu} - Số ngày còn lại: ${x1.soNgay}`;
                  });
                return <div style={{ color: "red" }}>{data.join(", ")}</div>;
              },
            },
          ]}
          onRow={(record, rowIndex) => {
            return {
              onClick: (event) => {
                onClickRow(record);
              },
            };
          }}
          dataSource={dataIndex}
          // scroll={{ y: 400, x: 700 }}
        ></TableWrapper>
        <ModalCanhBaoTraThuocBhyt ref={refModalCanhBaoTraThuocBhyt} />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalTrungThongTin);
