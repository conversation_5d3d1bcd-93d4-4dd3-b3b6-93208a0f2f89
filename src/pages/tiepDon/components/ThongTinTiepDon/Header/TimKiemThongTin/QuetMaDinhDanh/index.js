import React, { useRef, useEffect, memo, useContext, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { hexToUtf8, decodeBase64, converStringCCCD } from "utils";
import moment from "moment";
import { InputTimeout } from "components";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useFillMaHoSo,
  useListAll,
  useStore,
  useThietLap,
} from "hooks";
import {
  DOI_TUONG,
  DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
  PHAN_LOAI_DOI_TUONG,
  GIOI_TINH_BY_VALUE,
  ROLES,
} from "constants/index";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { Main } from "./styled";
import { SVG } from "assets";
import { message } from "antd";
import { DEFAULT_THONG_TIN_CHUNG } from "pages/application/TuyChinhGiaoDienPhamMem/TiepDon/config";
import { toSafePromise } from "lib-utils";
import { useKeDichVuKham } from "pages/tiepDon/KeDichVuKham";
import { checkRole } from "lib-utils/role-utils";
import { renderNbDiaChi } from "utils/index";
import ModalCanhBaoTraThuocBhyt from "pages/tiepDon/components/ThongTinTiepDon/ModalCanhBaoTraThuocBhyt";

const QuetMaDinhDanh = (
  { fromSetting, renderMessTheoMaLoi, tiemChung, ...props },
  ref
) => {
  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const {
    disableTiepDon,
    onCheckCardInsurance,
    onCheckTrungThongTinBVE,
    showTrungThongTin,
    nbDotDieuTriId,
  } = useContext(TiepDonContext);

  const { listAllTinh, listAllQuanHuyen, listAllQuocGia } = useSelector(
    (state) => state.ttHanhChinh
  );
  const maDinhDanh = useStore("tiepDon.maDinhDanh", null);
  const listXaPhuong = useStore("address.listXaPhuong", []);
  const [dataNGUON_NGUOI_BENH] = useThietLap(THIET_LAP_CHUNG.NGUON_NGUOI_BENH);
  const [dataTEKMEDI_ON_OFF] = useThietLap(
    THIET_LAP_CHUNG.TEKMEDI_ON_OFF,
    "false"
  );
  const [TU_DONG_DIEN_THONG_TIN_THE_CCCD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_THONG_TIN_THE_CCCD,
    "false"
  );
  const [MOI_QUAN_HE_ME] = useThietLap(THIET_LAP_CHUNG.MOI_QUAN_HE_ME, "false");
  const [BAT_TRUNG_NB_CO_1_MA_HO_SO] = useThietLap(
    THIET_LAP_CHUNG.BAT_TRUNG_NB_CO_1_MA_HO_SO
  );

  const [MOI_QUAN_HE_CHA] = useThietLap(
    THIET_LAP_CHUNG.MOI_QUAN_HE_CHA,
    "false"
  );
  const [dataMA_THE_BH_BO_QUA_CHECK_CONG] = useThietLap(
    THIET_LAP_CHUNG.MA_THE_BH_BO_QUA_CHECK_CONG
  );
  const [dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TICH_THONG_TUYEN_NB_BHYT
  );

  const [KIEM_TRA_THE_BAO_HIEM_ON_OFF] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_THE_BAO_HIEM_ON_OFF
  );
  const [dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM
  );
  const [MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE
  );
  const [dataMAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON
    );
  // const listXaPhuong = useStore("address.listXaPhuong", null);
  const nbTheBaoHiem = useStore("tiepDon.nbTheBaoHiem");
  const dataMacDinh = useStore("tiepDon.dataMacDinh");
  const [listAllQuanHe] = useListAll("moiQuanHe", {}, true);
  const listAllBenhVien = useStore("benhVien.listAllBenhVien", []);
  const [listAllNgheNghiep] = useListAll("ngheNghiep", {}, true);
  const listAllNguonNguoiBenh = useStore(
    "nguonNguoiBenh.listAllNguonNguoiBenh",
    []
  );
  const maBenhVien = useStore("auth.auth.benhVien.ma");
  const listAllTheBaoHiem = useStore("theBaoHiem.listAllTheBaoHiem", []);
  const doiTuong = useStore("tiepDon.doiTuong", null);
  const loaiDoiTuongId = useStore("tiepDon.loaiDoiTuongId", null);
  const capCuu = useStore("tiepDon.capCuu", false);
  const thongTinChung = useStore(
    "thietLap.thietLapGiaoDien.tiepDon.thongTinChung",
    DEFAULT_THONG_TIN_CHUNG
  );
  const khoaQuayTiepDonId = useStore("goiSo.khoaQuayTiepDonId");
  const {
    tiepDon: {
      updateData,
      updateThongTinTiepDon,
      searchMaNbTiepDon,
      updateDetail,
      searchNbMaThetek,
    },
    ngoaiVien: { onSearch },
    information: { checkTrungThongTin },
    address: { getXaPhuongByMa },
  } = useDispatch();
  const refMaDinhDanh = useRef();
  const refModalCanhBaoTraThuocBhyt = useRef(null);

  useEffect(() => {
    if (!maDinhDanh) {
      setTimeout(() => {
        refMaDinhDanh.current && refMaDinhDanh.current.focus();
      }, 500);
    } else {
      onSearchMaThe({}, maDinhDanh);
    }
  }, [maDinhDanh]);

  const onSearchMaThe = async (e, value) => {
    if (e.key === "Enter" || e.key === "Tab" || value) {
      const data = value || e.target.value;
      const regexNumber = /^[0-9]{8}$/;
      const regexText = /^[0-9A-F]{8}$/;
      const regexBADaiHan = /^([A-Za-z]{1}).*[0-9]{9}$/;
      if (data) {
        console.log("[SILENT LOG]", "Mã định danh: ", data);
        if (
          dataTEKMEDI_ON_OFF?.eval() &&
          ((data.startsWith("PT") && data.length === 10) ||
            /*
            SAKURA-67344 FE (BVCR) [Tiếp đón] Bổ sung thêm cơ chế quét mã của phía Tek gửi sang
            nam.mn 14/07/2025 - bổ sung thêm điều kiện (data.startsWith("00") && data.indexOf("|") == -1)
            vì điều kiện ban đầu bắt đầu = 00 thì đan trùng với một số case cccd.
            000063000059|021528456|Dương Anh Văn|15111963|Nam|Số Nhà 1197/3D, Ql1a, Khu Phố 24, An Phú Đông, Quận 12, TP. Hồ Chí Minh|12042024            
            */
            (data.startsWith("00") && data.indexOf("|") == -1) ||
            testMaHoSo(data) ||
            (data.startsWith("CR") && data.length === 12))
        ) {
          searchMaTheTamTek(data);
        } else if (regexBADaiHan.test(data)) {
          searchMaBaDaiHan(data);
        } else if (regexNumber.test(data) || data.startsWith("TM")) {
          getInfoPatientFromBVE(data);
        } else if (regexText.test(data)) {
          getInfoRFID(data);
        } else if (data.startsWith("NB") && data.endsWith("$")) {
          searchQrNB(data);
        } else if (data.indexOf("$") == data.length - 1) {
          searchMaThe(data);
        } else if (data.endsWith("}")) {
          getInfoFromQrJson(JSON.parse(data));
        } else {
          getInfoFromCCCD(data.replaceAll("\n", ""));
        }
      }
    }
  };

  const showMessLoiTheoMaCode = (s, data) => {
    const { thoiGianVaoVien, ...dataUpdate } = data;
    const { content, subContent } = renderMessTheoMaLoi(s, data);
    showConfirm(
      {
        showBtnOk:
          s.code === 7925 &&
          !checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_DV_CHUA_THANH_TOAN])
            ? false
            : true,
        title: t("common.thongBao"),
        content: content,
        subContent: subContent,
        showBtnCancel: s.code === 7935 ? false : true,
      },
      () => {
        if (s.code === 7935) {
          gotoKeDichVuTiepDon(s?.data?.id, false);
        } else {
          updateDetail({
            ...dataUpdate,
            maHoSo: null,
            id: null,
            doiTuongCu: dataUpdate?.doiTuongCu,
            maHoSoCu: dataUpdate?.maHoSo,
            doiTuongKcbCu: dataUpdate?.doiTuongKcbCu,
          });
          updateData({
            thongTinThanhToan: {
              daThanhToan: false,
              messageChuaThanhToan: s.message,
            },
          });
        }
      },
      () => {
        if (s.code === 7925 || s.code === 7922 || s.code === 7923) {
          gotoKeDichVuTiepDon(s?.data?.id, false);
        } else {
          updateData({
            maNb: null,
            thongTinThanhToan: {
              daThanhToan: true,
              messageChuaThanhToan: null,
            },
          });
        }
      }
    );
  };
  const searchMaTheTamTek = (value) => {
    if (value) {
      searchNbMaThetek({ maLaySo: value }).then(async (s) => {
        let diaChi = "";
        let nbDiaChi = {
          soNha: s?.soNha,
          quocGiaId: dataMacDinh?.quocTich?.id,
        };
        if (s?.diaChi) {
          const address = s?.diaChi?.toAddress();
          if (address.soNha) nbDiaChi.soNha = address.soNha;
          diaChi = address.diaChi;
        }
        if (window.mapDiaChiTekmedi) {
          diaChi = window.mapDiaChiTekmedi(s);
        } else {
          if (!diaChi) {
            diaChi = [s.tenXaPhuong, s.tenQuanHuyen, s.tenTinhThanhPho]
              .filter((item) => item)
              .join(", ");
          }
        }
        let dataCheck = listAllTheBaoHiem?.find(
          (item) =>
            item.ma.toLowerCase() === s?.maTheBhyt?.substr(0, 3).toLowerCase()
        );

        let dataUpdate = {
          maNb: s?.maNb,
          tenNb: s?.tenNb,
          doiTuong: s?.doiTuong,
          doiTuongKcb: s?.doiTuongKcb,
          soDienThoai: s?.soDienThoai,
          chiNamSinh: s?.chiNamSinh,
          ngaySinh: s?.ngaySinh,
          gioiTinh: s?.gioiTinh,
          nbDiaChi: nbDiaChi,
          dataTemp: {
            diaChi,
          },
          ngheNghiepId: listAllNgheNghiep.find((x) => x.ma === s?.maNgheNghiep)
            ?.id,
          quocGiaId: listAllQuocGia?.find((x) => x.ma === s?.maQuocGia)?.id,
          uuTien: s?.uuTien,
          nbTheBaoHiem: {
            maThe: s?.maTheBhyt,
            mucHuong: dataCheck?.mucHuong,
            khongApTran: dataCheck?.khongApTran,
            khongGioiHanMucThanhToan: dataCheck?.khongGioiHanMucThanhToan,
            denNgay: s?.denNgayTheBhyt,
            tuNgay: s?.tuNgayTheBhyt,
            noiDangKyId: listAllBenhVien.find((x) => x.ma === s.maNoiDangKy)
              ?.id,
            noiGioi: listAllBenhVien.find((x) => x.ma === s.maNoiDangKy)?.id,
            noiGioiThieuId: listAllBenhVien.find(
              (x) => x.ma === s.maNoiGioiThieu
            )?.id,
            henKhamLai: s?.henKhamLai,
          },
          nbGiayToTuyThan: {
            loaiGiayTo: s?.loaiGiayToTuyThan,
            maSo: s?.maSoGiayToTuyThan,
            ngayCap: s?.ngayCapGiayToTuyThan,
            noiCap: s?.noiCapGiayToTuyThan,
          },
          nbNgoaiVien: {
            maHoSo: value,
            doiTuong: s?.doiTuong,
            doiTuongKcb: s?.doiTuongKcb,
            maNb: s?.maNb,
            loai: 25,
          },
        };
        updateDetail(dataUpdate);
        let data = {
          tenNb: dataUpdate?.tenNb?.toUpperCase(),
          gioiTinh: Number(dataUpdate?.gioiTinh),
          ngaySinh: moment(dataUpdate?.ngaySinh).format("YYYY-MM-DD"),
          tinhThanhPhoId: nbDiaChi?.tinhThanhPhoId,
          xaPhuongId: nbDiaChi?.xaPhuongId,
          quocGiaId: nbDiaChi?.quocGiaId,
        };

        checkTrungThongTin(data).then((s) => {
          if (s?.data?.length > 1) {
            showTrungThongTin(s?.data);
          } else if (s?.data.length === 1) {
            showTrungThongTin(
              s?.data,
              null,
              BAT_TRUNG_NB_CO_1_MA_HO_SO?.eval()
            );
          }
        });
      });
    }
  };
  const searchMaBaDaiHan = async (value = {}) => {
    const [err, response] = await toSafePromise(
      searchMaNbTiepDon({ maBenhAn: value }).then((s) => {
        if (s?.code === 0) {
          updateData({
            thongTinThanhToan: {
              daThanhToan: true,
              messageChuaThanhToan: s.message,
            },
          });
        }
        if (s.data) return s;
        throw s;
      })
    );

    function hasTenNumbers(str) {
      const numbers = str.match(/\d/g);
      return numbers && numbers.length === 10;
    }

    if (err) {
      if (hasTenNumbers(value)) {
        onSearchInfo(value);
      }
    } else {
      updateData({
        maBenhAn: value,
      });
      const phanLoaiDoiTuong = [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(response?.data?.doiTuongKcb)
        ? response?.data?.trangThaiNb >= 100
          ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
          : PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
        : PHAN_LOAI_DOI_TUONG.TAI_KHAM;

      const nbDiaChi = renderNbDiaChi({
        dataDiachi: response?.data?.nbDiaChi,
        dataMacDinh,
      });

      let dataUpdate = {
        maNb: response?.data?.maNb,
        tenNb: response?.data?.tenNb,
        soDienThoai: response?.data?.soDienThoai,
        ngaySinh: response?.data?.ngaySinh,
        gioiTinh: response?.data?.gioiTinh,
        nbDiaChi: nbDiaChi,
        nbThongTinId: response?.data?.nbThongTinId,
        quocTichId: response?.data?.quocTichId,
        dsPhanLoaiNbId: response?.data?.dsPhanLoaiNbId,
        bangLaiXeId: response?.data?.bangLaiXeId,
        email: response?.data?.email,
        nbGiayToTuyThan: response?.data?.nbGiayToTuyThan,
        danTocId: response?.data?.danTocId,
        soBaoHiemXaHoi: response?.data?.soBaoHiemXaHoi,
        ngheNghiepId: response?.data?.ngheNghiepId,
        nbNguoiBaoLanh: response?.data?.nbNguoiBaoLanh,
        chiNamSinh: response?.data?.chiNamSinh,
        anhDaiDien: response?.data?.anhDaiDien,
        doiTuong: doiTuong,
        maHoSo: response?.data?.maHoSo,
        maHoSoCu: response?.data?.maHoSoCu,
        doiTuongCu: response?.data?.doiTuong,
        doiTuongKcbCu: response?.data?.doiTuongKcb,
        ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
          ? { phanLoaiDoiTuong: phanLoaiDoiTuong }
          : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
        ...(doiTuong === response?.data?.doiTuong
          ? {
              nbTheBaoHiem: response?.data?.nbTheBaoHiem,
            }
          : {}),
        congTyBaoHiemId: response?.data?.congTyBaoHiemId,
        dataTemp: {
          /*
          SAKURA-67495 FE (OP) [Tiếp đón] Không tự động fill địa chỉ cũ NB đã inactive trong danh mục
          nam.mn 16/07/2025 - //sinh địa chỉ 2 cấp từ dữ liệu tên xã tỉnh của người bệnh, nếu không thì tự xử lý tách từ địa chỉ cũ.
          không truyền thông tin địa chỉ vào nbDiaChi nữa mà sẽ truyền vào dataTemp đẻ component tự parse
          */
          diaChi:
            [
              response?.data?.nbDiaChi?.tenXaPhuong,
              response?.data?.nbDiaChi?.tenTinhThanhPho,
            ]
              .filter((item) => item)
              .join(", ") ||
            response?.data?.nbDiaChi?.diaChi?.toAddress()?.diaChi,
        },
      };

      if (
        response?.code === 7925 ||
        response?.code === 7924 ||
        response?.code === 7922 ||
        response?.code === 7935 ||
        response?.code === 7923 ||
        (response.code === 7951 &&
          !checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_PHIEU_THU_BH_0_DONG]))
      ) {
        showMessLoiTheoMaCode(response, {
          ...dataUpdate,
          thoiGianVaoVien: response?.data.thoiGianVaoVien,
          loaiDoiTuong: response?.data?.loaiDoiTuong,
        });
      } else if (
        response?.code === 0 ||
        (response.code === 7951 &&
          checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_PHIEU_THU_BH_0_DONG]))
      ) {
        updateDetail(dataUpdate);
      }
    }
  };

  const getInfoRFID = (value = {}) => {
    checkTrungThongTin({ maThe: value, khoaId: khoaQuayTiepDonId }).then(
      (s) => {
        if (s?.data.length > 1) {
          showTrungThongTin(s?.data);
        } else {
          if (s?.data[0]?.maTheBhyt) {
            onCheckCardInsurance(
              {
                hoTen: s?.data[0]?.tenNb,
                maThe: s?.data[0].maTheBhyt,
                ngaySinh: new Date(
                  moment(new Date(s?.data[0].ngaySinh)).format("YYYY-MM-DD")
                ),
              },
              { ten: s?.data[0]?.tenNb }
            );
          } else {
            onSearchInfo(s?.data[0].maNb);
          }
        }
      }
    );
  };

  const getInfoFromQrJson = (value = {}) => {
    updateData({
      maNb: value?.maNb,
    });
    onSearchInfo(value?.maNb);
  };
  const getInfoFromCCCD = async (value = "") => {
    try {
      console.log("[SILENT LOG]", "CCCD: ", value);
      let array = value.split("|");
      if (array.length === 7 || array.length === 11) {
        let date = moment(array[3], "DDMMYYYY");
        let ngayCap = moment(array[6], "DDMMYYYY");
        const diaChi = array[5].toAddress();
        const tuDongDienThongTin = TU_DONG_DIEN_THONG_TIN_THE_CCCD?.split("/");
        updateData({
          nbGiayToTuyThan: {
            maSo: array[0],
            ngayCap: ngayCap._d,
          },
          tenNb: array[2],
          ngaySinh: { date: date._d, str: date._d.format("dd/MM/yyyy") },
          tuoi: date._d.getAge() || "",
          gioiTinh: array[4] === GIOI_TINH_BY_VALUE[2] ? 2 : 1,
          searchThe: true,
          dataTemp: {
            soNha: diaChi.soNha,
            diaChi: diaChi.diaChi,
            time: new Date().getTime(), //sử dụng trường này để tạo lại object mới phân biệt với object cũ.
          },
          ...((tuDongDienThongTin?.length === 1 &&
            tuDongDienThongTin[0]?.eval()) ||
          tuDongDienThongTin?.[1] == "0" ||
          (tuDongDienThongTin?.[1] == "1" && doiTuong == 2)
            ? {
                nbTheBaoHiem: {
                  maThe: array[0],
                  thongTuyen:
                    dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT?.eval() && !capCuu
                      ? true
                      : false,
                },
                doiTuong: DOI_TUONG.BAO_HIEM,
              }
            : {}),
          nbNguoiBaoLanh: {
            hoTen: array[8],
            moiQuanHeId: array[8]
              ? listAllQuanHe?.find((item) => item.ma == MOI_QUAN_HE_CHA)?.id
              : null,
            hoTen2: array[9],
            moiQuanHe2Id: array[9]
              ? listAllQuanHe?.find((item) => item.ma == MOI_QUAN_HE_ME)?.id
              : null,
          },
        });
        let data = {
          maSoGiayToTuyThan: array[0],
          khoaId: khoaQuayTiepDonId,
        };
        checkTrungThongTin(data).then((s) => {
          if (s?.data?.length > 1) {
            showTrungThongTin(s?.data);
          } else if (s?.data.length === 1) {
            showTrungThongTin(
              s?.data,
              null,
              BAT_TRUNG_NB_CO_1_MA_HO_SO?.eval()
            );
          } else if (
            tuDongDienThongTin?.length &&
            tuDongDienThongTin[0]?.eval()
          ) {
            if (
              tuDongDienThongTin?.length === 1 ||
              tuDongDienThongTin?.[1] == "0" ||
              (tuDongDienThongTin?.[1] == "1" && doiTuong == 2)
            ) {
              onCheckCardInsurance({
                hoTen: array[2].toUpperCase(),
                maThe: array[0],
                ngaySinh: date._d.format("yyyy-MM-dd"),
              });
            }
          }
        });
      }
    } catch (error) {}
  };

  const getInfoPatientFromBVE = (data) => {
    let payload = {};
    if (data.startsWith("TM")) {
      payload = { soPhieuId: data.substring(2) };
    } else {
      payload = { maHoSo: data };
    }
    onSearch({ ...payload }).then(async (s) => {
      if (s.length) {
        let dataBVE = await generateData(s[0]);
        updateDetail({
          ...dataBVE,
          ngoaiVien: true,
        });
        let data = {
          tenNb: dataBVE.tenNb?.toUpperCase(),
          soDienThoai: dataBVE.soDienThoai?.replaceAll(" ", ""),
          gioiTinh: dataBVE?.gioiTinh,
          ngaySinh: dataBVE?.ngaySinh,
          sdtNguoiBaoLanh: dataBVE.sdtNguoiBaoLanh,
          maTheBhyt: dataBVE?.nbTheBaoHiem?.maThe,
          quocTichId: dataBVE?.quocTichId,
          quocGiaId: dataBVE?.nbDiaChi?.quocGiaId,
          danTocId: dataBVE?.danTocId,
          tinhThanhPhoId: dataBVE?.nbDiaChi?.tinhThanhPhoId,
          quanHuyenId: dataBVE?.nbDiaChi?.quanHuyenId,
          xaPhuongId: dataBVE?.nbDiaChi?.xaPhuongId,
          soNha: dataBVE?.nbDiaChi?.soNha,
          chiNamSinh: dataBVE.chiNamSinh,
          doiTuong: dataBVE.doiTuong,
        };
        onCheckTrungThongTinBVE({ ...data }, dataBVE);
      }
    });
  };

  const generateData = async (data) => {
    let nguonNbId = listAllNguonNguoiBenh.find(
      (x) => x.ma === dataNGUON_NGUOI_BENH
    )?.id;
    let dsDichVu = (data.dsDichVu || [])
      .filter((x) => !x.chiDinh)
      .map((item) => {
        return {
          ...item,
          phongId: item.phongThucHienId,
          loaiDichVu: item?.loaiDichVu,
          ngoaiVienId: item?.id,
        };
      });

    let xaPhuongVienE = await getXaPhuongByMa(data.maXaPhuong);
    let tinhVienE = listAllTinh?.find((x) => x.ma === data.maTinhThanhPho);
    let quanHuyenVienE = listAllQuanHuyen?.find(
      (x) => x.ma === data.maQuanHuyen
    );

    let nbDiaChi = {
      soNha: data?.soNha,
      xaPhuongId: xaPhuongVienE?.id,
      tinhThanhPhoId: tinhVienE?.id,
      quanHuyenId: quanHuyenVienE?.id,
      diaChi: `${
        (xaPhuongVienE?.ten ? xaPhuongVienE?.ten + `,` : "") +
        (quanHuyenVienE?.ten ? quanHuyenVienE?.ten + `,` : "") +
        (tinhVienE?.ten ? tinhVienE?.ten : "")
      }`,
      quocGiaId: dataMacDinh?.quocTich?.id,
    };

    let nbTongKetRaVien = {
      cdNoiGioiThieu: data?.cdNoiGioiThieu,
      lyDoDenKham: data?.lyDoDenKham,
    };
    let nbTheBaoHiem = {
      maThe: data?.maTheBhyt,
      mucHuong: data?.mucHuongTheBhyt,
      denNgay: data?.denNgayTheBhyt,
      tuNgay: data?.tuNgayTheBhyt,
      noiDangKyId: listAllBenhVien.find((x) => x.ma === data.maNoiDangKy)?.id,
    };
    let nbNguoiBaoLanh = {
      hoTen: data.tenNguoiBaoLanh,
      soDienThoai: data.sdtNguoiBaoLanh,
      soCanCuoc: data.soCanCuocNguoiBaoLanh,
      moiQuanHeId: listAllQuanHe.find((x) => x.ma === data.maMoiQuanHe)?.id,
    };

    let nbNguonNb = {
      nguonNbId: nguonNbId,
    };
    let value = {
      ...data,
      nbDiaChi: nbDiaChi,
      nbTongKetRaVien: nbTongKetRaVien,
      nbTheBaoHiem: nbTheBaoHiem,
      nbNguoiBaoLanh: nbNguoiBaoLanh,
      nbNguonNb: nbNguonNb,
      ngheNghiepId: listAllNgheNghiep.find((x) => x.ma === data.maNgheNghiep)
        ?.id,
      danTocId: dataMacDinh?.danToc?.id,
      quocTichId: dataMacDinh?.quocTich?.id,
      dsDichVu: dsDichVu,
      soDienThoai: data.soDienThoai === "0" ? "" : data.soDienThoai,
      nbNgoaiVien: {
        maHoSo: data.maHoSo,
        doiTuong: data.doiTuong,
        doiTuongKcb: data.doiTuongKcb,
        maNb: data?.maNb,
      },
      maHoSo: null,
      maNb: null,
      capCuu: false,
      tuoi: data?.ngaySinh && moment(data?.ngaySinh)._d.getAge(),
    };
    return value;
  };
  const searchQrNB = async (value = "") => {
    let array = value.split("|");
    let maCsyt = array[1];
    let maHoSo = array[3];
    let maNb = array[4];
    let hoTen = array[5];
    let ngaySinh = {
      str: array[6],
      date: array[6].toDateObject("/", "dd/MM/yyyy"),
    };
    let gioiTinh = array[7];
    let soDienThoai = array[8];
    let soNha = array[9];
    let maPhuong = array[10];
    let maQuanHuyen = array[11];
    let maTinhThanhPho = array[12];
    let maQuocGia = array[13];
    let diaChiNb = array[14];
    let soTheBHYT = array[15];
    let giayToTuyThan = array[16];
    const xa = await getXaPhuongByMa(maPhuong);
    const huyen = listAllQuanHuyen?.find((x) => x.ma === maQuanHuyen);
    const tinh = listAllTinh?.find((x) => x.ma === maTinhThanhPho);
    // nếu qr không có địa chỉ thì sinh từ xã huyện tỉnh
    let diaChi =
      decodeBase64(diaChiNb) ||
      [xa?.ten, huyen?.ten, tinh?.ten].filter((item) => item).join(", ");
    let nbDiaChi = {
      soNha: decodeBase64(soNha),
      xaPhuongId: xa?.id,
      tinhThanhPhoId: tinh?.id,
      quanHuyenId: huyen?.id,
      diaChi: diaChi,
      quocGiaId: listAllQuocGia?.find((x) => x.ma === maQuocGia)?.id,
    };
    if (maCsyt === maBenhVien && maNb) {
      const res = await onSearchInfo(maNb);
      if (res) return;
    }
    updateThongTinTiepDon({
      nbTheBaoHiem: {
        maThe: soTheBHYT,
      },
      tenNb: decodeBase64(hoTen),
      ngaySinh: ngaySinh,
      gioiTinh: Number(gioiTinh),
      maHoSo: maHoSo,
      soDienThoai: soDienThoai,
      maNb: maNb,
      tuoi: ngaySinh?.date.getAge(),
      doiTuong: soTheBHYT ? 2 : 1,
      nbGiayToTuyThan: {
        maSo: giayToTuyThan,
      },
      nbDiaChi: nbDiaChi,
      nbNguoiBaoLanh: {
        hoTen: decodeBase64(array[17]),
        moiQuanHeId: listAllQuanHe?.find((item) => item.ma == array[18])?.id,
        soDienThoai: array[19],
        hoTen2: decodeBase64(array[20]),
        moiQuanHe2Id: listAllQuanHe?.find((item) => item.ma == array[21])?.id,
        soDienThoai2: array[22],
      },
    });

    let data = {
      tenNb: decodeBase64(hoTen).toUpperCase(),
      gioiTinh: Number(gioiTinh),
      ngaySinh: moment(ngaySinh?.date).format("YYYY-MM-DD"),
      tinhThanhPhoId: nbDiaChi?.tinhThanhPhoId,
      quanHuyenId: nbDiaChi?.quanHuyenId,
      xaPhuongId: nbDiaChi?.xaPhuongId,
      quocGiaId: nbDiaChi?.quocGiaId,
      khoaId: khoaQuayTiepDonId,
    };

    checkTrungThongTin(data).then((s) => {
      if (s?.data?.length > 1) {
        showTrungThongTin(s?.data);
      } else if (s?.data.length === 1) {
        showTrungThongTin(s?.data, null, BAT_TRUNG_NB_CO_1_MA_HO_SO?.eval());
      }
    });
  };

  const searchMaThe = (value = "") => {
    let array = value.split("|");
    let day = array[2]?.split("/") || [];
    let dayLate = `${day[2]}/${day[1]}/${day[0]}`;
    let date = {
      str: array[2],
      date: new Date(moment(new Date(dayLate)).format("YYYY-MM-DD")),
    };
    let day5nam = (array[12] && array[12].split("/")) || [];
    let ten = hexToUtf8(array[1]);
    let mucHuong = array[0]?.substr(0, 3);
    let dataCheck = listAllTheBaoHiem?.find(
      (item) => item.ma.toLowerCase() === mucHuong.toLowerCase()
    );
    let gioiTinh = array[3] && Number(array[3]);
    updateThongTinTiepDon({
      nbTheBaoHiem: {
        ...(nbTheBaoHiem || {}),
        mucHuong: dataCheck?.mucHuong,
        khongApTran: dataCheck?.khongApTran,
        khongGioiHanMucThanhToan: dataCheck?.khongGioiHanMucThanhToan,
        maThe: array[0],
        thoiGianDu5Nam:
          day5nam[2] && `${day5nam[2]}/${day5nam[1]}/${day5nam[0]}`,
        boQuaTheLoi: false,
      },
      tenNb: ten,
      ngaySinh: date,
      gioiTinh: gioiTinh,
      doiTuong: 2,
      // nbDiaChi: {
      //   diaChi: hexToUtf8(array[4]),
      // },
      searchThe: true,
      ...(!!nbDotDieuTriId ? { theTam: false } : {}),
    });
    if (ten && ten.length && date?.date) {
      checkTrungThongTin({
        maTheBhyt: array[0],
        khoaId: khoaQuayTiepDonId,
      }).then((s) => {
        if (s?.data?.length > 1) {
          showTrungThongTin(s?.data);
        } else if (s?.data.length === 1) {
          showTrungThongTin(s?.data, null, BAT_TRUNG_NB_CO_1_MA_HO_SO?.eval());
        } else {
          onCheckCardInsurance(
            {
              hoTen: ten,
              maThe: array[0],
              ngaySinh: date?.date,
            },
            { ten },
            !!nbDotDieuTriId
          );
        }
      });
    }
  };
  const onSearchInfo = (value = "") => {
    return new Promise(async (resolve, reject) => {
      if (value.trim()) {
        const s = await searchMaNbTiepDon({
          maNb: value,
          doiTuong: doiTuong,
          tiemChung,
          capCuu: capCuu,
          ...(dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM?.eval() && {
            loaiDoiTuongId,
          }),
        });
        if (s.data) {
          const phanLoaiDoiTuong = [
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(s?.data?.doiTuongKcb)
            ? s?.data?.trangThaiNb >= 100
              ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
              : PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
            : PHAN_LOAI_DOI_TUONG.TAI_KHAM;

          const nbDiaChi = renderNbDiaChi({
            dataDiachi: s?.data?.nbDiaChi,
            dataMacDinh,
          });

          let dataUpdate = {
            maNb: s?.data?.maNb,
            tenNb: s?.data?.tenNb,
            soDienThoai: s?.data?.soDienThoai,
            ngaySinh: s?.data?.ngaySinh,
            gioiTinh: s?.data?.gioiTinh,
            nbDiaChi: nbDiaChi,
            nbThongTinId: s.data?.nbThongTinId,
            quocTichId: s?.data?.quocTichId,
            dsPhanLoaiNbId: s?.data?.dsPhanLoaiNbId,
            email: s?.data?.email,
            nbGiayToTuyThan: s?.data?.nbGiayToTuyThan,
            danTocId: s?.data?.danTocId,
            soBaoHiemXaHoi: s?.data?.soBaoHiemXaHoi,
            ngheNghiepId: s?.data?.ngheNghiepId,
            nbNguoiBaoLanh: s?.data?.nbNguoiBaoLanh,
            chiNamSinh: s?.data?.chiNamSinh,
            anhDaiDien: s?.data?.anhDaiDien,
            maHoSo: s?.data.maHoSo,
            bangLaiXeId: s?.data?.bangLaiXeId,
            nbNguonNb: { ghiChu: s?.data?.nbNguonNb?.ghiChu },
            ...(doiTuong === s?.data.doiTuong
              ? {
                  nbTheBaoHiem: {
                    maThe: s?.data?.nbTheBaoHiem?.maThe,
                    mucHuong: s?.data?.nbTheBaoHiem?.mucHuong,
                    denNgayGiayChuyen: s?.data?.nbTheBaoHiem?.denNgayGiayChuyen,
                    tuNgayGiayChuyen: s?.data?.nbTheBaoHiem?.tuNgayGiayChuyen,
                    ...(dataMA_THE_BH_BO_QUA_CHECK_CONG
                      ?.split(",")
                      .includes(s?.data?.nbTheBaoHiem?.maThe.substring(0, 2)) ||
                    !KIEM_TRA_THE_BAO_HIEM_ON_OFF?.eval()
                      ? {
                          tuNgay: s?.data?.nbTheBaoHiem?.tuNgay,
                          denNgay: s?.data?.nbTheBaoHiem?.denNgay,
                          noiDangKyId: s?.data?.nbTheBaoHiem?.noiDangKyId,
                          diaChi: s?.data?.nbTheBaoHiem?.diaChi,
                        }
                      : {}),
                    ...(MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE?.eval()
                      ? {
                          noiGioiThieuId: s?.data?.nbTheBaoHiem?.noiGioiThieuId,
                          giayChuyenTuyen:
                            s?.data?.nbTheBaoHiem?.giayChuyenTuyen,
                        }
                      : {}),
                  },
                }
              : {}),
            capCuu: false,
            nbMienGiam: null,
            doiTuongCu: s?.data?.doiTuong,
            doiTuongKcbCu: s?.data?.doiTuongKcb,
            ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
              ? { phanLoaiDoiTuong: phanLoaiDoiTuong }
              : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
            congTyBaoHiemId: s?.data?.congTyBaoHiemId,
            nbTongKetRaVien: {
              cdNoiGioiThieu: s?.data?.nbTongKetRaVien?.cdNoiGioiThieu,
              ...(MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE?.eval()
                ? {
                    dsCdNoiGioiThieuId:
                      s?.data?.nbTongKetRaVien?.dsCdNoiGioiThieuId,
                  }
                : {}),
            },
            maNbCu: s?.data?.maNbCu,
            ...(dataMAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON?.eval()
              ? {
                  doiTuong: doiTuong,
                  nbNguonNb: {
                    nguoiGioiThieuId: s?.data?.nbNguonNb?.nguoiGioiThieuId,
                  },
                  nbNguonNb: { ten: s?.data?.nbNguonNb?.ten },
                }
              : {}),
            dataTemp: {
              /*
              SAKURA-67495 FE (OP) [Tiếp đón] Không tự động fill địa chỉ cũ NB đã inactive trong danh mục
              nam.mn 16/07/2025 - //sinh địa chỉ 2 cấp từ dữ liệu tên xã tỉnh của người bệnh, nếu không thì tự xử lý tách từ địa chỉ cũ.
              không truyền thông tin địa chỉ vào nbDiaChi nữa mà sẽ truyền vào dataTemp đẻ component tự parse
              */
              diaChi:
                [
                  s?.data?.nbDiaChi?.tenXaPhuong,
                  s?.data?.nbDiaChi?.tenTinhThanhPho,
                ]
                  .filter((item) => item)
                  .join(", ") || s?.data?.nbDiaChi?.diaChi?.toAddress()?.diaChi,
            },
          };
          if (
            s.code === 7925 ||
            s.code === 7924 ||
            s.code === 7922 ||
            s.code === 7923 ||
            s.code === 7935 ||
            s.code === 7951 ||
            s.code === 7936
          ) {
            if (
              s.code === 7951 &&
              checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_PHIEU_THU_BH_0_DONG])
            ) {
              updateDetail({
                ...dataUpdate,
                maHoSo: null,
                id: null,
                doiTuongCu: dataUpdate?.doiTuongCu,
                maHoSoCu: dataUpdate?.maHoSo,
                doiTuongKcbCu: dataUpdate?.doiTuongKcbCu,
              });
              updateData({
                thongTinThanhToan: {
                  daThanhToan: false,
                  messageChuaThanhToan: s.message,
                },
              });
            } else if (s.code === 7936) {
              refModalCanhBaoTraThuocBhyt.current &&
                refModalCanhBaoTraThuocBhyt.current.show(
                  {
                    data: s?.data,
                  },
                  (payload) => {
                    const { value } = payload || {};
                    if (value === 1) {
                      updateDetail(dataUpdate);
                      updateData({
                        thongTinThanhToan: {
                          daThanhToan: true,
                          messageChuaThanhToan: null,
                          boQuaCheckTaiKhamSom: true,
                        },
                      });
                    }
                  }
                );
            } else {
              showMessLoiTheoMaCode(s, {
                ...dataUpdate,
                thoiGianVaoVien: s?.data.thoiGianVaoVien,
                loaiDoiTuong: s?.data?.loaiDoiTuong,
              });
            }
          } else {
            if (s.code === 0) {
              updateDetail(dataUpdate);
              if (s?.data?.doiTuong === 2) {
                updateData({
                  thongTinThanhToan: {
                    daThanhToan: true,
                    messageChuaThanhToan: s.message,
                  },
                });
                let payload = s?.data;
                onCheckCardInsurance(
                  {
                    hoTen: payload?.tenNb,
                    maThe: payload?.nbTheBaoHiem?.maThe,
                    ngaySinh: new Date(
                      moment(new Date(payload?.ngaySinh)).format("YYYY-MM-DD")
                    ),
                  },
                  { ten: payload?.tenNb }
                );
              }
            }
          }
          resolve(s.data);
        } else {
          message.error(
            t("tiepDon.khongTonTaiMaNguoiBenh").replace("{0}", value)
          );
        }
      }
      resolve(null);
    });
  };

  let refTimeout = useRef();
  const handleOnchange = (e) => {
    if (refTimeout.current) {
      clearTimeout(refTimeout.current);
    }
    refTimeout.current = setTimeout(
      (value) => {
        let data = converStringCCCD(value);
        updateData({
          maDinhDanh: data?.trim(),
        });
      },
      300,
      e
    );
  };
  return (
    <Main sm={16} md={16} xl={16} xxl={16} {...props} ref={ref}>
      <div className="header-item">
        <div className="item-input input-ma-dinh-danh">
          <div className="d-flex">
            <label className="label">{t("tiepDon.maDinhDanh")}</label>
            <label className="label">
              <i className="sub-color">
                {t("tiepDon.quetQRCCCDBHYTmaNBtheRFID")}
              </i>
            </label>
          </div>
          <InputTimeout
            style={{ paddingRight: "35px" }}
            ref={refMaDinhDanh}
            autoFocus
            placeholder={t("tiepDon.quetMaQR")}
            value={maDinhDanh}
            onChange={handleOnchange}
            onKeyDown={(e) => onSearchMaThe(e)}
            // onBlur={}
            disabled={disableTiepDon}
            // id="scan-ma-dinh-danh"
          />
          <SVG.IcQrCode className="qr-icon" />
        </div>
      </div>
      <ModalCanhBaoTraThuocBhyt ref={refModalCanhBaoTraThuocBhyt} />
    </Main>
  );
};

export default memo(forwardRef(QuetMaDinhDanh));
