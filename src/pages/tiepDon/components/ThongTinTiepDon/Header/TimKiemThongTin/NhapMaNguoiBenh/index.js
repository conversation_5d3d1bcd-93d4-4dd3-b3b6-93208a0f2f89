import React, {
  useRef,
  memo,
  useContext,
  forwardRef,
  useState,
  useEffect,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { useTranslation } from "react-i18next";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { Radio, Popover, InputTimeout } from "components";
import { Main } from "./styled";
import { useCache, useConfirm, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import {
  CACHE_KEY,
  DOI_TUONG_KCB,
  LOAI_DICH_VU,
  PHAN_LOAI_DOI_TUONG,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
} from "constants/index";
import { Space } from "antd";
import { DEFAULT_THONG_TIN_CHUNG } from "pages/application/TuyChinhGiaoDienPhamMem/TiepDon/config";
import { useKeDichVuKham } from "pages/tiepDon/KeDichVuKham";
import { isArray, renderNbDiaChi } from "utils/index";
import { checkRole } from "lib-utils/role-utils";
import ModalCanhBaoTraThuocBhyt from "pages/tiepDon/components/ThongTinTiepDon/ModalCanhBaoTraThuocBhyt";

const NhapMaNguoiBenh = (
  { fromSetting, renderMessTheoMaLoi, tiemChung = false, ...props },
  ref
) => {
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();

  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { nbDotDieuTriId, onCheckCardInsurance, disableTiepDon } =
    useContext(TiepDonContext);

  const {
    tiepDon: { updateData, searchMaNbTiepDon, updateDetail, updateThongTinNb },
    nbDotDieuTri: { getChiSoSong },
  } = useDispatch();

  const dataMacDinh = useStore("tiepDon.dataMacDinh");
  const refMaNb = useRef();
  const refModalCanhBaoTraThuocBhyt = useRef(null);
  const [dataMA_THE_BH_BO_QUA_CHECK_CONG] = useThietLap(
    THIET_LAP_CHUNG.MA_THE_BH_BO_QUA_CHECK_CONG
  );
  const [dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_TICH_THONG_TUYEN_NB_BHYT
  );

  const [KIEM_TRA_THE_BAO_HIEM_ON_OFF] = useThietLap(
    THIET_LAP_CHUNG.KIEM_TRA_THE_BAO_HIEM_ON_OFF
  );
  const [dataMAC_DINH_KY_TU_TRUOC_MA_NB] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_KY_TU_TRUOC_MA_NB
  );
  const [dataLAY_THONG_TIN_MA_HS_CU_TIEP_DON_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.LAY_THONG_TIN_MA_HS_CU_TIEP_DON_NOI_TRU
  );
  const [dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM
  );
  const [MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE
  );
  const [dataMAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON
    );
  const updateDataMaHsCu =
    dataLAY_THONG_TIN_MA_HS_CU_TIEP_DON_NOI_TRU?.eval() &&
    window.location.pathname.includes("/quan-ly-noi-tru/danh-sach-lap-benh-an");

  const auth = useSelector((state) => state.auth.auth);
  const { coSoKcbId, dsCoSoKcb } = auth || {};

  const [state, _setState] = useState({
    popoverVisible: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  //1 : Tìm mã theo mã nb, 2: Tìm theo mã nb ngoại viện
  const [timTheoMaNb, setTimTheoMaNb] = useCache(
    CACHE_KEY.TIM_THEO_MA_NB,
    "",
    "1",
    false
  );

  const {
    doiTuong,
    nbNguonNb,
    maNb,
    capCuu,
    nbNgoaiVien,
    maNbTuKhuonMat,
    loaiDoiTuongId,
  } = useStore(
    "tiepDon",
    {},
    {
      fields:
        "doiTuong,nbNguonNb,maNb,capCuu,nbNgoaiVien,maNbTuKhuonMat,loaiDoiTuongId",
    }
  );

  const dataTemp = useStore("tiepDon.dataTemp", {});
  const thongTinChung = useStore(
    "thietLap.thietLapGiaoDien.tiepDon.thongTinChung",
    DEFAULT_THONG_TIN_CHUNG
  );
  const onSearchInfo = (key, value = "") => {
    if (value.trim()) {
      value = value.toUpperCase();
      searchMaNbTiepDon({
        doiTuong: doiTuong,
        tiemChung,
        capCuu,
        ...(key === "maNb"
          ? {
              maNb: value,
            }
          : {
              maNbNgoaiVien: value,
            }),
        ...(dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM?.eval() && {
          loaiDoiTuongId,
        }),
      }).then(async (s) => {
        const phanLoaiDoiTuong = [
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(s?.data?.doiTuongKcb)
          ? s?.data?.trangThaiNb >= TRANG_THAI_NB.DA_RA_VIEN
            ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
            : PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
          : PHAN_LOAI_DOI_TUONG.TAI_KHAM;

        const nbDiaChi = renderNbDiaChi({
          dataDiachi: s?.data?.nbDiaChi,
          dataMacDinh,
        });

        let nbChiSoSong = {};
        if (updateDataMaHsCu) {
          let params = {
            nbDotDieuTriId: s?.data?.id,
            dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.KHAM],
            updateRedux: false,
          };
          let thongTinSinhHieu = await getChiSoSong(params);
          nbChiSoSong = {
            ...thongTinSinhHieu,
          };
        }

        let dataUpdate = {
          maNb: s?.data?.maNb,
          maNbParam: null,
          tenNb: s?.data?.tenNb,
          soDienThoai: s?.data?.soDienThoai,
          ngaySinh: s?.data?.ngaySinh,
          gioiTinh: s?.data?.gioiTinh,
          nbDiaChi: nbDiaChi,
          quocTichId: s?.data?.quocTichId,
          dsPhanLoaiNbId: s?.data?.dsPhanLoaiNbId,
          email: s?.data?.email,
          nbGiayToTuyThan: s?.data?.nbGiayToTuyThan,
          danTocId: s?.data?.danTocId,
          soBaoHiemXaHoi: s?.data?.soBaoHiemXaHoi,
          ngheNghiepId: s?.data?.ngheNghiepId,
          nbNguoiBaoLanh: s?.data?.nbNguoiBaoLanh,
          chiNamSinh: s?.data?.chiNamSinh,
          anhDaiDien: s?.data?.anhDaiDien,
          nbNguonNb: { ...nbNguonNb, ghiChu: s?.data?.nbNguonNb?.ghiChu },
          doiTuong: doiTuong,
          doiTuongCu: s?.data?.doiTuong,
          maHoSo: s?.data?.maHoSo,
          maHoSoCu: s?.data?.maHoSoCu,
          doiTuongKcbCu: s?.data?.doiTuongKcb,
          bangLaiXeId: s?.data?.bangLaiXeId,
          nbThongTinId: s.data?.nbThongTinId,
          nbNgoaiVien: { maNb: s?.data?.nbNgoaiVien?.maNb },
          ...(doiTuong === s?.data?.doiTuong
            ? {
                nbTheBaoHiem: {
                  maThe: s?.data?.nbTheBaoHiem?.maThe,
                  mucHuong: s?.data?.nbTheBaoHiem?.mucHuong,
                  denNgayGiayChuyen: s?.data?.nbTheBaoHiem?.denNgayGiayChuyen,
                  tuNgayGiayChuyen: s?.data?.nbTheBaoHiem?.tuNgayGiayChuyen,
                  thongTuyen:
                    dataMAC_DINH_TICH_THONG_TUYEN_NB_BHYT?.eval() && !capCuu
                      ? true
                      : false,
                  ...(dataMA_THE_BH_BO_QUA_CHECK_CONG
                    ?.split(",")
                    .includes(s?.data?.nbTheBaoHiem?.maThe?.substring(0, 2)) ||
                  !KIEM_TRA_THE_BAO_HIEM_ON_OFF?.eval()
                    ? {
                        tuNgay: s?.data?.nbTheBaoHiem?.tuNgay,
                        denNgay: s?.data?.nbTheBaoHiem?.denNgay,
                        noiDangKyId: s?.data?.nbTheBaoHiem?.noiDangKyId,
                        diaChi: s?.data?.nbTheBaoHiem?.diaChi,
                      }
                    : {}),
                  ...(updateDataMaHsCu && {
                    noiGioiThieuId: s?.data?.nbTheBaoHiem?.noiGioiThieuId,
                  }),
                  ...(MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE?.eval()
                    ? {
                        noiGioiThieuId: s?.data?.nbTheBaoHiem?.noiGioiThieuId,
                        giayChuyenTuyen: s?.data?.nbTheBaoHiem?.giayChuyenTuyen,
                      }
                    : {}),
                },
              }
            : {}),
          ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
            ? { phanLoaiDoiTuong: phanLoaiDoiTuong }
            : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
          congTyBaoHiemId: s?.data?.congTyBaoHiemId,
          nbTongKetRaVien: {
            cdNoiGioiThieu: s?.data?.nbTongKetRaVien?.cdNoiGioiThieu,
            ...(updateDataMaHsCu && {
              dsCdVaoVienId: s?.data?.nbTongKetRaVien?.dsCdVaoVienId,
            }),
            ...(MAC_DINH_THONG_TIN_BHYT_KHI_NHAP_THE?.eval()
              ? {
                  dsCdNoiGioiThieuId:
                    s?.data?.nbTongKetRaVien?.dsCdNoiGioiThieuId,
                }
              : {}),
          },
          maNbCu: s?.data?.maNbCu,
          ...(dataMAC_DINH_THONG_TIN_DOI_VOI_NGUOI_BENH_DA_TUNG_TIEP_DON?.eval()
            ? {
                doiTuong: doiTuong,
                nbNguonNb: {
                  nguoiGioiThieuId: s?.data?.nbNguonNb?.nguoiGioiThieuId,
                },
                nbNguonNb: { ten: s?.data?.nbNguonNb?.ten },
              }
            : {}),
          ...(updateDataMaHsCu && {
            nbChiSoSongUpdate: {
              chieuCao: nbChiSoSong?.chieuCao,
              canNang: nbChiSoSong?.canNang,
            },
          }),
          dataTemp: {
            /*
            SAKURA-67495 FE (OP) [Tiếp đón] Không tự động fill địa chỉ cũ NB đã inactive trong danh mục
            nam.mn 16/07/2025 - //sinh địa chỉ 2 cấp từ dữ liệu tên xã tỉnh của người bệnh, nếu không thì tự xử lý tách từ địa chỉ cũ.
            không truyền thông tin địa chỉ vào nbDiaChi nữa mà sẽ truyền vào dataTemp đẻ component tự parse
            */
            diaChi:
              [
                s?.data?.nbDiaChi?.tenXaPhuong,
                s?.data?.nbDiaChi?.tenTinhThanhPho,
              ]
                .filter((item) => item)
                .join(", ") || s?.data?.nbDiaChi?.diaChi?.toAddress()?.diaChi,
          },
        };

        if (
          s.code === 7925 ||
          s.code === 7924 ||
          s.code === 7922 ||
          s.code === 7923 ||
          s.code === 7935 ||
          s.code === 7951 ||
          s.code === 7936
        ) {
          const { content, subContent } = renderMessTheoMaLoi(s, {
            ...dataUpdate,
            thoiGianVaoVien: s?.data.thoiGianVaoVien,
            loaiDoiTuong: s?.data?.loaiDoiTuong,
          });
          if (
            s.code === 7951 &&
            checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_PHIEU_THU_BH_0_DONG])
          ) {
            updateDetail({
              ...dataUpdate,
              maHoSo: null,
              id: null,
              doiTuongCu: dataUpdate.doiTuongCu,
              doiTuongKcbCu: dataUpdate?.doiTuongKcbCu,
              maHoSoCu: dataUpdate.maHoSo,
            });
            updateData({
              thongTinThanhToan: {
                daThanhToan: false,
                messageChuaThanhToan: s.message,
              },
            });
          } else if (s.code === 7936) {
            refModalCanhBaoTraThuocBhyt.current &&
              refModalCanhBaoTraThuocBhyt.current.show(
                {
                  data: s?.data,
                },
                (payload) => {
                  const { value } = payload || {};
                  if (value === 1) {
                    updateDetail(dataUpdate);
                    updateData({
                      thongTinThanhToan: {
                        daThanhToan: true,
                        messageChuaThanhToan: null,
                        boQuaCheckTaiKhamSom: true,
                      },
                    });
                  }
                }
              );
          } else {
            showConfirm(
              {
                showBtnOk:
                  s.code === 7925 &&
                  !checkRole([ROLES["TIEP_DON"].TIEP_DON_NB_DV_CHUA_THANH_TOAN])
                    ? false
                    : true,
                title: t("common.thongBao"),
                content: content,
                subContent: subContent,
                showBtnCancel: s.code === 7935 ? false : true,
              },
              () => {
                if (s.code === 7935) {
                  gotoKeDichVuTiepDon(s?.data?.id, false);
                } else {
                  updateDetail({
                    ...dataUpdate,
                    maHoSo: null,
                    id: null,
                    doiTuongCu: dataUpdate.doiTuongCu,
                    maHoSoCu: dataUpdate.maHoSo,
                    doiTuongKcbCu: dataUpdate?.doiTuongKcbCu,
                  });
                  updateData({
                    thongTinThanhToan: {
                      daThanhToan:
                        s?.data?.trangThai ==
                        TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN
                          ? true
                          : false, //nếu NB đã thanh toán ra viện thì đánh dấu là đã thanh toán
                      messageChuaThanhToan: s.message,
                    },
                  });
                }
              },
              () => {
                if (s.code === 7925 || s.code === 7922 || s?.code === 7923) {
                  gotoKeDichVuTiepDon(s?.data?.id, false);
                } else {
                  updateData({
                    maNb: null,
                    thongTinThanhToan: {
                      daThanhToan: true,
                      messageChuaThanhToan: null,
                    },
                  });
                }
              }
            );
          }
        } else {
          if (s.code === 0) {
            updateDetail(dataUpdate);
            if (s?.data?.doiTuong === 2) {
              //update daThanhToan = true bỏ qua bắt điều kiện với nb có đối tượng bảo hiểm
              updateData({
                thongTinThanhToan: {
                  daThanhToan: true,
                  messageChuaThanhToan: s.message,
                },
              });
              let payload = s?.data;
              onCheckCardInsurance(
                {
                  hoTen: payload?.tenNb,
                  maThe: payload?.nbTheBaoHiem?.maThe,
                  ngaySinh: new Date(
                    moment(new Date(payload?.ngaySinh)).format("YYYY-MM-DD")
                  ),
                  nbTheBaoHiem: dataUpdate.nbTheBaoHiem,
                },
                { ten: payload?.tenNb }
              );
            }
          }
        }
      });
    }
  };
  const onKeyDown = (event) => {
    if (event.nativeEvent.key === "Enter") refMaNb.current.blur();
  };

  const onChange = (key) => (value) => {
    const data = value ? String(value) : "";
    if (key === "nbNgoaiVien") {
      updateThongTinNb(
        {
          [key]: data?.trim(),
        },
        "nbNgoaiVien"
      );
    } else {
      updateData({ [key]: data?.trim() });
    }
  };
  useEffect(() => {
    if (maNbTuKhuonMat) {
      onSearchInfo("maNb", maNbTuKhuonMat);
    }
  }, [maNbTuKhuonMat]);

  useEffect(() => {
    if (dataTemp?.maNb) {
      onChange("maNb", dataTemp.maNb);
      onSearchInfo("maNb", dataTemp.maNb);
    }
  }, [dataTemp]);

  const handleFocus = () => {
    const dsMaThietLap =
      dataMAC_DINH_KY_TU_TRUOC_MA_NB?.split(",")?.map((i) => i.trim()) || [];
    const maCoSoKcb = dsCoSoKcb.find((i) => i.id === coSoKcbId)?.ma;

    if (!isArray(dsMaThietLap, true) || !maCoSoKcb) return;

    const maNbThietLap = dsMaThietLap
      .find((thietLap) => thietLap.split("/")[1] === maCoSoKcb)
      ?.split("/")[0];

    if (!maNbThietLap) return;

    if (!maNb && timTheoMaNb === "1") {
      onChange("maNb")(maNbThietLap);
    }
  };

  const contentTimNb = () => {
    return (
      <div>
        <Radio.Group
          value={timTheoMaNb}
          onChange={(e) => {
            setState({
              popoverVisible: false,
            });
            setTimTheoMaNb(e.target.value);
          }}
        >
          <Space direction="vertical">
            <Radio value={"1"}>{t("tiepDon.timTheoMaNguoiBenh")}</Radio>
            <Radio value={"2"}>
              {t("tiepDon.timTheoMaNguoiBenhNgoaiVien")}
            </Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };
  return (
    <Main
      sm={nbDotDieuTriId ? 12 : 24}
      md={nbDotDieuTriId ? 12 : 24}
      xl={nbDotDieuTriId ? 12 : 24}
      xxl={nbDotDieuTriId ? 12 : 24}
      {...props}
      ref={ref}
    >
      {timTheoMaNb === "1" ? (
        <div className="item-input">
          <label
            className="label"
            style={{ display: "flex", alignItems: "center" }}
          >
            {t("common.maNguoiBenh")}{" "}
            <Popover
              placement="rightTop"
              content={contentTimNb()}
              trigger="click"
              open={state.popoverVisible}
              onOpenChange={(e) => {
                setState({ popoverVisible: e });
              }}
            >
              <SVG.IcOption />
            </Popover>
          </label>
          <InputTimeout
            ref={refMaNb}
            onChange={onChange("maNb")}
            placeholder={t("common.nhapMaNguoiBenh")}
            onBlur={(e) => onSearchInfo("maNb", e.target.value)}
            onKeyDown={onKeyDown}
            value={maNb}
            disabled={nbDotDieuTriId || disableTiepDon}
            onFocus={handleFocus}
          />
        </div>
      ) : (
        <div className="item-input">
          <label
            className="label"
            style={{ display: "flex", alignItems: "center" }}
          >
            {t("tiepDon.maNbNgoaiVien")}{" "}
            <Popover
              placement="rightTop"
              content={contentTimNb()}
              trigger="click"
              open={state.popoverVisible}
              onOpenChange={(e) => {
                setState({ popoverVisible: e });
              }}
            >
              <SVG.IcOption className="ic-action" />
            </Popover>
          </label>
          <InputTimeout
            ref={refMaNb}
            onChange={onChange("maNbNgoaiVien")}
            placeholder={t("tiepDon.nhapMaNbNgoaiVien")}
            onBlur={(e) => onSearchInfo("maNbNgoaiVien", e.target.value)}
            onKeyDown={onKeyDown}
            value={nbNgoaiVien?.maNb}
            disabled={nbDotDieuTriId || disableTiepDon}
            type="number"
          />
        </div>
      )}
      <ModalCanhBaoTraThuocBhyt ref={refModalCanhBaoTraThuocBhyt} />
    </Main>
  );
};

export default memo(forwardRef(NhapMaNguoiBenh));
