import React, {
  forwardRef,
  memo,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Select, Tag } from "antd";
import { CloseOutlined } from "@ant-design/icons";

import { containText, isArray, openInNewTab } from "utils/index";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { Main } from "./styled";

const PhanLoaiNguoiBenh = ({ fromSetting, ...props }, ref) => {
  const { t } = useTranslation();
  const { disableTiepDon } = useContext(TiepDonContext);

  const dsPhanLoaiNbId = useSelector((state) => state.tiepDon.dsPhanLoaiNbId);

  const listAllPhanLoaiNB = useSelector(
    (state) => state.phanLoaiNB.listAllPhanLoaiNB
  );

  const {
    tiepDon: { updateData },
    phanLoaiNB: { getListAllPhanLoaiNB },
  } = useDispatch();

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListAllPhanLoaiNB(param);
  }, []);

  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  return (
    <Main md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-select">
        <label
          onClick={() => openInNewTab("/danh-muc/phan-loai-nb")}
          className="label pointer"
        >
          {t("tiepDon.phanLoai")}
        </label>
        <Select
          className="select"
          placeholder={t("tiepDon.chonPhanLoai")}
          onChange={(e) => {
            updateData({ dsPhanLoaiNbId: e });
          }}
          value={dsPhanLoaiNbId || []}
          disabled={disableTiepDon}
          mode="multiple"
          allowClear
          showSearch
          filterOption={filterOption}
          tagRender={(props) => {
            const { label, value, closable, onClose } = props;
            const item = listAllPhanLoaiNB.find((i) => i.id === value);

            return (
              <Tag
                closable={closable}
                onClose={onClose}
                closeIcon={
                  <CloseOutlined style={{ color: item?.mauChu || undefined }} />
                }
                style={{
                  color: item?.mauChu || undefined,
                  backgroundColor: item?.mauNen || undefined,
                  borderColor: item?.mauNen ? "transparent" : undefined,
                  borderRadius: "4px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                {label}
              </Tag>
            );
          }}
        >
          {isArray(listAllPhanLoaiNB, true) &&
            listAllPhanLoaiNB.map((item) => (
              <Select.Option key={item.id} value={item.id}>
                {item.ten}
              </Select.Option>
            ))}
        </Select>
      </div>
    </Main>
  );
};

export default memo(forwardRef(PhanLoaiNguoiBenh));
