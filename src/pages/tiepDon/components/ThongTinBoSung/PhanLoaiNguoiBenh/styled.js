import { Col } from "antd";
import styled from "styled-components";

export const Main = styled(Col)`
  .ant-select {
    width: 100%;
    &.ant-select-multiple {
      .ant-select-selector {
        overflow-y: scroll;
        height: 32px;
      }
    }
  }
  .ant-select-multiple.ant-select-allow-clear .ant-select-selector {
    padding-right: 8px !important;
  }
  .ant-select-selection-overflow {
    gap: 4px;
    .ant-select-selection-overflow-item {
      span {
        .ant-tag {
          margin-right: 0px !important;
        }
      }
    }
  }
`;
