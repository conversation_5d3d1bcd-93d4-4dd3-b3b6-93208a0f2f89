import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useDispatch } from "react-redux";
import { cloneDeep, groupBy, orderBy } from "lodash";
import { Collapse, Menu, message } from "antd";
import { useTranslation } from "react-i18next";
import { CaretRightOutlined, CaretDownOutlined } from "@ant-design/icons";
import stringUtils from "mainam-react-native-string-utils";

import { useConfirm, useEnum, useListAll, useStore, useThietLap } from "hooks";
import { checkRoleOr } from "lib-utils/role-utils";
import {
  Button,
  TableWrapper,
  ModalTemplate,
  Select,
  InputTimeout,
  Pagination,
  Checkbox,
  Dropdown,
  Tooltip,
} from "components";
import {
  ENUM,
  LOAI_DICH_VU,
  LIST_THOI_DIEM_DUNG,
  THIET_LAP_CHUNG,
  DOI_TUONG_KCB,
  ROL<PERSON>,
  CO_CHE_DUYET_PHAT,
} from "constants/index";
import { SVG } from "assets";
import chiDinhThuocUtils, { evalString } from "utils/chi-dinh-thuoc-utils";
import { roundNumberPoint, isArray } from "utils/index";
import ModalBoSungThongTinThuoc from "../ModalBoSungThongTinThuoc";
import ModalBoSungThongTinDichVu from "../ModalBoSungThongTinDichVu";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import nbDvThuocNhaThuocProvider from "data-access/thuoc/nb-dv-thuoc-nha-thuoc-provider";
import { Main, WrapperInput } from "./styled";

const { Column } = TableWrapper;
const { Panel } = Collapse;

export const getCachDung = (
  t,
  tenDuongDung = "",
  soLan1Ngay,
  soLuong1Lan,
  tenDvtSuDung,
  thoiDiem
) => {
  return `${t("khamBenh.{{tenDuongDung}}ngay{{soLan1Ngay}}lan", {
    tenDuongDung: tenDuongDung || "",
    soLan1Ngay: soLan1Ngay,
  })}, ${t("khamBenh.moiLan{{soLuong1Lan}}{{tenDvtSuDung}}{{thoiDiem}}", {
    soLuong1Lan,
    tenDvtSuDung,
    thoiDiem: thoiDiem ? `, ${thoiDiem}` : "",
  }).toLowerCase()} `;
};

export const ModalChiDinhDichVu = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const refLayerHotKey = useRef(stringUtils.guid());
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const refKey = useRef(null);
  const refBoSungThongTinThuoc = useRef(null);
  const refBoSungThongTinDichVu = useRef(null);
  const { showConfirm } = useConfirm();

  const [listLoaiThoiGianPhacDoDieuTri] = useEnum(
    ENUM.LOAI_THOI_GIAN_PHAC_DO_DIEU_TRI
  );
  const [TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC
  );

  const [TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
  );
  const [dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM] =
    useThietLap(
      THIET_LAP_CHUNG.CHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM
    );
  const { page, size, totalElements, listDanhSachDichVu } = useStore(
    "dmPhacDoDieuTriDichVu",
    {},
    { fields: "page, size, totalElements, listDanhSachDichVu" }
  );
  const listThietLapChonKho = useStore(
    "thietLapChonKho.listThietLapChonKho",
    []
  );
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const [listAllLieuDung] = useListAll("lieuDung", {}, true);
  const isKhamBenh = window.location.pathname.indexOf("/kham-benh") >= 0;
  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
    selectedRowKeys: [],
    dataSelected: [],
    dataSource: {},
    isLoading: false,
    listDvThuoc: [],
    listDvThuocNhaThuoc: [],
    expandedKeys: [],
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    dmPhacDoDieuTriDichVu: {
      onChangeInputSearch,
      updateData,
      onSizeChange,
      onSearch,
      getDsMucDich,
    },
    nbPhacDoDieuTri: { create },
    phongThucHien: { getListPhongTheoDichVu },
    chiDinhDichVuKho: {
      getListDichVuThuocKeNgoai,
      getListThuocNhaThuoc,
      getListDichVuThuoc,
    },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    mucDichSuDung: { getMucDichByDVId },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: (
      {
        thongTinPhacDo,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        khoaChiDinhId,
        dsLoaiDichVu,
        doiTuongKcb,
        doiTuong,
        ngaySinh,
        loaiDoiTuongId,
        thoiGianThucHien,
        thoiGianVaoVien,
      },
      callback
    ) => {
      setState({
        show: true,
        thongTinPhacDo,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId,
        khoaChiDinhId,
        dsLoaiDichVu,
        doiTuongKcb,
      });
      onChangeInputSearch({
        phacDoDieuTriId: thongTinPhacDo.id,
        dsLoaiDichVu,
        isTongHop: true,
        doiTuongKcb,
        doiTuong,
        ngaySinh,
        loaiDoiTuongId,
        thoiGianThucHien,
        thoiGianVaoVien,
      });
      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
      getListThietLapChonKhoTheoTaiKhoan({
        loaiDoiTuongId: configData.thongTinNguoiBenh?.loaiDoiTuongId,
        dsLoaiDichVu: [
          LOAI_DICH_VU.THUOC,
          LOAI_DICH_VU.VAT_TU,
          LOAI_DICH_VU.HOA_CHAT,
        ],
        khoaNbId: configData.thongTinNguoiBenh?.khoaNbId,
        khoaChiDinhId: configData.khoaChiDinhId,
        doiTuong: configData.thongTinNguoiBenh?.doiTuong,
        noiTru: true,
        capCuu: false,
        phongId: configData.phongThucHienId,
        canLamSang: false,
      });
      nbDvThuocProvider
        .searchTongHop({
          nbDotDieuTriId: configData.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        })
        .then((s) => {
          if (s?.code === 0) {
            setState({ listDvThuoc: s.data || [] });
          }
        });
      nbDvThuocNhaThuocProvider
        .searchTongHop({
          nbDotDieuTriId: configData.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        })
        .then((s) => {
          if (s?.code === 0) {
            setState({ listDvThuocNhaThuoc: s.data || [] });
          }
        });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const dsThietLapChonKhoThuoc = useMemo(() => {
    return (listThietLapChonKho || []).filter((x) =>
      x.dsLoaiDichVu.includes(LOAI_DICH_VU.THUOC)
    );
  }, [listThietLapChonKho]);

  const onCancel = () => {
    setState({
      show: false,
      listSelectedDv: [],
      selectedRowKeys: [],
      dataSelected: [],
      dataSource: {},
      isLoading: false,
      maDichVu: "",
      tenDichVu: "",
    });
    updateData({ listDanhSachDichVu: [], dataSearch: {} });
  };

  const sharedOnCell = (row, index) => {
    if (row.isGroup) {
      return { colSpan: 0 };
    }
  };

  const genarateSoLuong = (record, soLuong) => {
    if (record.heSoDinhMuc != 1) {
      if (record.dvtSuDungId == record.donViTinhId) {
        const soLuongSoCap = Math.ceil(soLuong / record.heSoDinhMuc);
        record.soLuong = soLuongSoCap * record.heSoDinhMuc;
        record.soLuongSoCap = soLuongSoCap;
        if (isKhamBenh) {
          record.soLuong = soLuongSoCap * record.heSoDinhMuc;
        }
      } else {
        if (isKhamBenh) {
          record.soLuong = record.soLuong ? record.soLuong : 1;
          record.soLuongSoCap = record.soLuongSoCap ? record.soLuongSoCap : 1;
        }
      }
    } else {
      if (record.dvtSuDungId == record.donViTinhId) {
        const tuDongLamTron =
          isKhamBenh &&
          TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true";
        record.soLuong = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
        record.soLuongSoCap = tuDongLamTron ? Math.ceil(soLuong) : soLuong;
      } else {
        if (isKhamBenh) {
          record.soLuong = record.soLuong ? record.soLuong : 1;
          record.soLuongSoCap = record.soLuongSoCap ? record.soLuongSoCap : 1;
        }
      }
    }
    record.soLuong = record.soLuong
      ? roundNumberPoint(record.soLuong, 6)
      : null;
    record.soLuongSoCap = record.soLuong
      ? roundNumberPoint(record.soLuongSoCap, 6)
      : null;
  };

  const onChangeInput = (key, record) => (e, lists) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;
    const dataSource = cloneDeep(state.dataSource);

    const updateItem = (x) => {
      x[key] = value;
      if (key === "loaiHinhThanhToanId") {
        //thêm biến đánh dấu đã chọn loại hình thanh toán
        x.isSelectedLoaiHinhThanhToan = true;
      }
      if (key === "soLan1Ngay") {
        genarateSoLuong(
          x,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: x.soNgay,
            soLan1Ngay: e,
            soLuong1Lan: x.soLuong1Lan,
          })
        );
        x.cachDung = getCachDung(
          t,
          x.tenDuongDung,
          x.soLan1Ngay,
          e,
          x.tenDvtSuDung
        );
      }
      if (key === "soLuong1Lan") {
        x.soLuong1Lan = e ? roundNumberPoint(evalString(e), 3) : "";
        genarateSoLuong(
          x,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: x.soNgay,
            soLan1Ngay: x.soLan1Ngay,
            soLuong1Lan: x.soLuong1Lan,
          })
        );
        x.cachDung = getCachDung(
          t,
          x.tenDuongDung,
          x.soLuong1Lan,
          x.soLuong1Lan,
          x.tenDvtSuDung
        );
      }

      if (key === "soNgay") {
        genarateSoLuong(
          x,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: e,
            soLan1Ngay: x.soLan1Ngay,
            soLuong1Lan: x.soLuong1Lan,
          })
        );
      }

      if (key === "duongDungId") {
        x.cachDung = getCachDung(
          t,
          lists?.ten,
          x.soLan1Ngay,
          x.soLuong1Lan,
          x.tenDvtSuDung
        );
      }

      if (key === "soLuong" && e) {
        const soLuongSoCap = (Number(evalString(e)) || 0) / x.heSoDinhMuc;
        x.soLuongSoCap =
          x.heSoDinhMuc != 1 && isKhamBenh
            ? Math.ceil(soLuongSoCap)
            : roundNumberPoint(soLuongSoCap, 6);
        x.soLuong = roundNumberPoint(Number(evalString(e), 6));
      }

      if (key === "soLuongSoCap" && e) {
        x.soLuong = roundNumberPoint(
          (Number(evalString(e)) || 0) * x.heSoDinhMuc,
          6
        );
        x.soLuongSoCap = roundNumberPoint(Number(evalString(e)), 6);
      }
      if (
        (key === "soLuong" || key === "soLuongSoCap") &&
        x.heSoDinhMuc == 1 &&
        isKhamBenh &&
        x.dvtSuDungId == x.donViTinhId &&
        TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true" &&
        e
      ) {
        x.soLuong = Math.ceil(e);
        x.soLuongSoCap = Math.ceil(e);
      }
      if (key === "lieuDungId" && lists) {
        const soLuong1Lan = lists?.soLuong1Lan || x.soLuong1Lan;
        const soLan1Ngay = lists?.soLan1Ngay || x.soLan1Ngay;

        if (soLuong1Lan && soLan1Ngay) {
          const tenDuongDung = `${
            lists?.tenDuongDung ? lists?.tenDuongDung : ""
          }`;
          x.cachDung = getCachDung(
            t,
            tenDuongDung,
            soLan1Ngay,
            soLuong1Lan,
            x.tenDvtSuDung,
            x.thoiDiem
          );
        }
        x.soLuong1Lan = soLuong1Lan;
        x.soLan1Ngay = soLan1Ngay;
        x.tenDuongDung = lists?.tenDuongDung || x?.tenDuongDung;
        if (x.lieuDungId && soLuong1Lan && soLan1Ngay) {
          if (x.soNgay) {
            genarateSoLuong(
              x,
              chiDinhThuocUtils.tinhSoLuong({
                soNgay: x.soNgay,
                soLuong1Lan: soLuong1Lan,
                soLan1Ngay: soLan1Ngay,
              })
            );
          } else if (x.soLuong) {
            x.soNgay = chiDinhThuocUtils.tinhSoNgay({
              soLuong: x.soLuong,
              soLuong1Lan: soLuong1Lan,
              soLan1Ngay: soLan1Ngay,
            });
          }
        }
      }
      if (key === "soLuong" && e && x.soLan1Ngay && x.soLuong1Lan) {
        if (
          !isKhamBenh ||
          (x.heSoDinhMuc == 1 && x.dvtSuDungId == x.donViTinhId) ||
          (x.heSoDinhMuc != 1 &&
            TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true")
        ) {
          x.soNgay = chiDinhThuocUtils.tinhSoNgay({
            soLuong: e,
            soLan1Ngay: x.soLan1Ngay,
            soLuong1Lan: x.soLuong1Lan,
            soLuongHuy: x.soLuongHuy,
          });
        }
      }
      if (key === "nhaThuoc") {
        x.khoId = null;
      }
    };

    // Update chỉ target item
    Object.values(dataSource).forEach((item) => {
      item.dsDichVu.forEach((x) => {
        if (x.key === record.key) {
          updateItem(x);
        }
        // Kiểm tra children
        if (x.children && x.children.length > 0) {
          x.children.forEach((child) => {
            if (child.key === record.key) {
              updateItem(child);
            }
          });
        }
      });
    });

    setState({ dataSource: dataSource });
  };

  const renderListThoiDiemDung = (listThoiDiemDung, record) =>
    listThoiDiemDung.map((item) => ({
      key: item.key,
      label: (
        <a
          href={() => false}
          onClick={() => onChangeInput("thoiDiem", record)(item.label)}
        >
          {item.label}
        </a>
      ),
    }));

  const blockInvalidChar = (e) =>
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

  const columns = [
    Column({
      title: t("phacDoDieuTri.maDichVu"),
      width: 100,
      dataIndex: "maDichVu",
      key: "maDichVu",
      onCell: (row, index) => ({
        colSpan: row.isGroup ? 20 : 1,
      }),
      render: (item, record) =>
        record.isGroup ? (
          <b>{record?.ten}</b>
        ) : (
          item || record?.thuocChiDinhNgoai?.ma
        ),
    }),
    Column({
      title: t("phacDoDieuTri.tenDichVu"),
      width: 280,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      render: (item, record) => item || record?.thuocChiDinhNgoai?.ten,
      onCell: sharedOnCell,
    }),
    Column({
      title: t("phacDoDieuTri.soLuongPhacDo"),
      width: 80,
      dataIndex: "soLuongKhaiBao",
      key: "soLuongKhaiBao",
      align: "center",
      onCell: sharedOnCell,
      render: (item, record) => (
        <WrapperInput>
          <div>{item}</div>
          <Tooltip title={record.tenDonViTinh}>
            <span className="span-ellipsis" style={{ paddingLeft: "5px" }}>
              {record.tenDonViTinh}
            </span>
          </Tooltip>
        </WrapperInput>
      ),
    }),
    Column({
      title: t("phacDoDieuTri.soLuongChiDinh"),
      width: 120,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      onCell: sharedOnCell,
      render: (item, record) => (
        <>
          <WrapperInput>
            <InputTimeout
              value={item}
              onChange={onChangeInput("soLuong", record)}
              onKeyDown={blockInvalidChar}
            ></InputTimeout>
            <Tooltip title={record.tenDonViTinh}>
              <span className="span-ellipsis">{record.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
          {state.checkValidate &&
            !item &&
            state.selectedRowKeys.includes(record.key) && (
              <span className="error">
                {t("common.vuiLongNhapSoLuongLon0")}
              </span>
            )}
        </>
      ),
    }),
    Column({
      title: t("common.lan/ngay"),
      width: 120,
      align: "center",
      dataIndex: "soLan1Ngay",
      onCell: sharedOnCell,
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <WrapperInput>
              <InputTimeout
                onChange={onChangeInput("soLan1Ngay", record)}
                value={item}
                type="number"
                min={0}
                timeDelay={100}
                onKeyDown={blockInvalidChar}
              ></InputTimeout>
            </WrapperInput>
          );
      },
    }),
    Column({
      title: t("common.sl/lan"),
      width: 120,
      align: "center",
      dataIndex: "soLuong1Lan",
      onCell: sharedOnCell,
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <WrapperInput>
              <InputTimeout
                onChange={onChangeInput("soLuong1Lan", record)}
                value={item}
                timeDelay={100}
                onKeyDown={blockInvalidChar}
              ></InputTimeout>
            </WrapperInput>
          );
      },
    }),
    Column({
      title: t("common.slSoCap"),
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      width: 100,
      align: "right",
      onCell: sharedOnCell,
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <WrapperInput>
              <InputTimeout
                value={item}
                onChange={onChangeInput("soLuongSoCap", record)}
                style={{
                  marginRight: 2,
                }}
                onKeyDown={blockInvalidChar}
              ></InputTimeout>
              <Tooltip title={record.tenDvtSoCap}>
                <span className="span-ellipsis">{record.tenDvtSoCap}</span>
              </Tooltip>
            </WrapperInput>
          );
      },
    }),
    Column({
      title: t("phacDoDieuTri.phongThucHien"),
      width: 230,
      dataIndex: "phongThucHienId",
      key: "phongThucHienId",
      onCell: sharedOnCell,
      render: (item, record, index) => {
        if (
          [
            LOAI_DICH_VU.THUOC,
            LOAI_DICH_VU.VAT_TU,
            LOAI_DICH_VU.HOA_CHAT,
          ].includes(record?.loaiDichVu)
        ) {
          return record?.phongThucHien?.ten;
        } else {
          const dsPhongThucHien = (record.dsPhongThucHien || []).map(
            (item) => ({
              id: item.phongId,
              ten: item.ten,
            })
          );
          return (
            <Select
              value={item}
              data={dsPhongThucHien || []}
              onChange={onChangeInput("phongThucHienId", record)}
              disabled={!!record.thuocChiDinhNgoaiId}
            />
          );
        }
      },
    }),
    Column({
      title: t("phacDoDieuTri.nhaThuoc"),
      width: 80,
      dataIndex: "nhaThuoc",
      key: "nhaThuoc",
      align: "center",
      onCell: sharedOnCell,
      render: (item, record) => (
        <Checkbox
          checked={item}
          onChange={onChangeInput("nhaThuoc", record)}
          disabled={
            record.loaiDichVu !== LOAI_DICH_VU.THUOC ||
            !record.tenNhomDichVuCap1
          }
        ></Checkbox>
      ),
    }),
    Column({
      title: t("phacDoDieuTri.kho"),
      width: 200,
      dataIndex: "khoId",
      key: "khoId",
      onCell: sharedOnCell,
      render: (item, record) => {
        const listData = listThietLapChonKho.filter((x) =>
          x.dsLoaiDichVu.includes(record?.loaiDichVu)
        );
        const value = record.nhaThuoc ? null : item || listData?.[0]?.id;
        return (
          !!listData?.length && (
            <Select
              data={listData}
              value={value}
              onChange={onChangeInput("khoId", record)}
              disabled={record.nhaThuoc}
            />
          )
        );
      },
    }),
    Column({
      title: t("khamBenh.chiDinh.loaiHinhThanhToan"),
      width: 200,
      dataIndex: "loaiHinhThanhToanId",
      key: "loaiHinhThanhToanId",
      onCell: sharedOnCell,
      render: (item, record) => {
        const getData = () => {
          if (item || record.isSelectedLoaiHinhThanhToan) return item;
          if (record.dsLoaiHinhThanhToan?.length) {
            let loaiHinhThanhToanId = (record.dsLoaiHinhThanhToan || []).find(
              (x) => x.doiTuong === state.doiTuong
            )?.loaiHinhThanhToanId;
            if (loaiHinhThanhToanId) {
              onChangeInput("loaiHinhThanhToanId", record)(loaiHinhThanhToanId);
              return loaiHinhThanhToanId;
            }
            loaiHinhThanhToanId = record.dsLoaiHinhThanhToan.sort(
              (a, b) => b.uuTien - a.uuTien
            )[0]?.loaiHinhThanhToanId;
            onChangeInput("loaiHinhThanhToanId", record)(loaiHinhThanhToanId);
            return loaiHinhThanhToanId;
          }
        };
        const value = getData();
        return (
          <Select
            data={(record?.dsLoaiHinhThanhToan || []).map((el) => ({
              id: el.loaiHinhThanhToanId,
              ten: el.tenLoaiHinhThanhToan,
            }))}
            value={value}
            onChange={onChangeInput("loaiHinhThanhToanId", record)}
            disabled={record.nhaThuoc}
          />
        );
      },
    }),
    Column({
      title: t("common.cachDung"),
      width: 250,
      align: "center",
      dataIndex: "cachDung",
      onCell: sharedOnCell,
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <InputTimeout
              onChange={onChangeInput("cachDung", record)}
              value={item}
            ></InputTimeout>
          );
      },
    }),
    Column({
      title: t("phacDoDieuTri.thoiDiemDung"),
      width: 120,
      align: "center",
      dataIndex: "thoiDiem",
      onCell: sharedOnCell,
      render: (item, record) => {
        const _listThoiDiemDung = item
          ? LIST_THOI_DIEM_DUNG.filter(
              (x) => x.label.toLowerCase().indexOf(item.toLowerCase()) > -1
            )
          : LIST_THOI_DIEM_DUNG;
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <Dropdown
              overlayStyle={
                _listThoiDiemDung.length == 0 ? { display: "none" } : {}
              }
              overlay={
                <Menu
                  items={renderListThoiDiemDung(_listThoiDiemDung, record)}
                />
              }
              trigger={["click"]}
            >
              <InputTimeout
                onChange={onChangeInput("thoiDiem", record)}
                value={item}
              />
            </Dropdown>
          );
      },
    }),
    Column({
      title: t("common.duongDung"),
      width: 150,
      align: "center",
      dataIndex: "duongDungId",
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <Select
              defaultValue={item}
              data={listAllDuongDung}
              onChange={onChangeInput("duongDungId", record)}
            />
          );
      },
    }),
    Column({
      title: t("phacDoDieuTri.lieuDungCachDung"),
      width: 225,
      dataIndex: "lieuDungId",
      key: "lieuDungId",
      onCell: sharedOnCell,
      render: (item, record, index) => {
        if (
          LOAI_DICH_VU.THUOC === record?.loaiDichVu ||
          record.thuocChiDinhNgoaiId
        )
          return (
            <Select
              data={listAllLieuDung}
              onChange={onChangeInput("lieuDungId", record)}
              value={item}
            />
          );
      },
    }),
    Column({
      title: t("phacDoDieuTri.soNgay"),
      width: 80,
      dataIndex: "soNgay",
      key: "soNgay",
      onCell: sharedOnCell,
      render: (item, record) => {
        if (LOAI_DICH_VU.THUOC === record?.loaiDichVu)
          return (
            <InputTimeout
              onChange={onChangeInput("soNgay", record)}
              value={item}
              type="number"
              min={0}
            ></InputTimeout>
          );
      },
    }),
    Column({
      title: t("phacDoDieuTri.donGiaBh"),
      width: 100,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      onCell: sharedOnCell,
    }),
    Column({
      title: t("phacDoDieuTri.donGiaKhongBh"),
      width: 120,
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      onCell: sharedOnCell,
    }),
    Column({
      title: t("phacDoDieuTri.dvsd"),
      width: 80,
      dataIndex: "tenDvtSuDung",
      key: "tenDvtSuDung",
    }),
    Column({
      title: t("phacDoDieuTri.khongTinhTien"),
      width: 100,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      align: "center",
      onCell: sharedOnCell,
      render: (item, record) => {
        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("khongTinhTien", record)}
            disabled={
              [20, 30, 40, 45, 60].includes(record.loaiDichVu)
                ? !checkRoleOr([
                    ROLES["QUAN_LY_NOI_TRU"]
                      .CHINH_SUA_KHONG_TINH_TIEN_POPUP_CHI_DINH_DVKT,
                  ])
                : false
            }
          ></Checkbox>
        );
      },
    }),
    Column({
      title: t("phacDoDieuTri.tuTuc"),
      width: 80,
      dataIndex: "tuTra",
      key: "tuTra",
      align: "center",
      onCell: sharedOnCell,
      render: (item, record) => (
        <Checkbox
          checked={item}
          onChange={onChangeInput("tuTra", record)}
        ></Checkbox>
      ),
    }),
  ];

  useEffect(() => {
    if (listDanhSachDichVu.length && state.khoaChiDinhId) {
      let listPhong = [];
      async function fetchData() {
        try {
          const _dsDichVuId = listDanhSachDichVu
            .map((item) => item.dichVuId)
            .filter((item) => !!item);

          if (_dsDichVuId.length > 0) {
            listPhong = await getListPhongTheoDichVu({
              page: "",
              size: "",
              dsDichVuId: _dsDichVuId,
              khoaChiDinhId: state.khoaChiDinhId,
              doiTuongKcb: configData?.thongTinNguoiBenh?.doiTuongKcb,
            });
          }
        } catch (error) {
          listPhong = [];
        }
        const phongByDichVuId = groupBy(listPhong, "dichVuId");
        listDanhSachDichVu.forEach((dichVu) => {
          dichVu.dsPhongThucHien = phongByDichVuId[dichVu?.dichVuId];
          dichVu.soLuongKhaiBao = dichVu.soLuong;
        });
        const listThuocCha = listDanhSachDichVu.filter((x) => !x.dungKemId);
        const listThuocDungKem = listDanhSachDichVu.filter((x) => x.dungKemId);

        let autoExpandKeys = [];

        const data = orderBy(listThuocCha, "loaiThoiGian", "asc").reduce(
          (a, item) => {
            const { loaiThoiGian, stt, ...rest } = item;
            const key = `${loaiThoiGian} ${stt}`;
            a[key] = a[key] || {
              loaiThoiGian,
              stt,
              dsDichVu: [],
            };
            let thuocDungKem = listThuocDungKem.filter(
              (x) => x.dungKemId === item.id
            );

            a[key]["dsDichVu"].push({
              ...rest,
              children: thuocDungKem.map((x) => ({
                ...x,
                parentKey: item.key,
                parentKeyId: item.keyId,
              })),
            });

            if (thuocDungKem.length > 0) {
              autoExpandKeys.push(item.key);
            }

            return a;
          },
          {}
        );

        const finalExpandedKeys = [
          ...new Set([...state.expandedKeys, ...autoExpandKeys]),
        ];
        setState({ dataSource: data, expandedKeys: finalExpandedKeys });
      }
      fetchData();
    } else {
      setState({ dataSource: [], expandedKeys: [] });
    }
  }, [listDanhSachDichVu, state.khoaChiDinhId]);

  const onShowCanhBaoKeTrung = (
    objDupplicate,
    onChangeSelectLeft,
    objDupplicateThuocKho,
    dsThuocTrung
  ) => {
    let messThuoc = null;
    let data = [];
    messThuoc = t("khamBenh.chiDinh.tenThuoc").replace(
      "{ten_thuoc}",
      objDupplicate[0].tenDichVu
    );
    data = objDupplicate;
    const messCanhBao = _.uniq(
      data.map((x) =>
        t("khamBenh.chiDinh.canhBaoKeTrungThuoc")
          .replace("{ten_bac_si}", x.tenBacSiChiDinh)
          .replace("{so_luong}", x.soLuong)
          .replace("{dvt}", x.tenDonViTinh)
      )
    ).join(", ");
    const msg = `${messThuoc} ${messCanhBao}`;
    const duocChiDinhCungThuoc = () => {
      return (
        dataCHAN_KHONG_CHO_CHI_DINH_CUNG_THUOC_GIUA_CAC_DICH_VU_KHAM?.eval() &&
        [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
        ]?.includes(configData?.thongTinNguoiBenh?.doiTuongKcb) &&
        isKhamBenh &&
        isArray(objDupplicateThuocKho, true)
      );
    };

    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${msg}. ${
          !duocChiDinhCungThuoc() ? t("khamBenh.chiDinh.tiepTuc") : ""
        }`,
        cancelText: t("common.huy"),
        okText: t("common.xacNhan"),
        showImg: false,
        showBtnOk: duocChiDinhCungThuoc() ? false : true,
        typeModal: "warning",
      },
      () => {
        onChangeSelectLeft(dsThuocTrung);
      },
      () => {
        if (isArray(dsThuocTrung, true) && duocChiDinhCungThuoc()) {
          onChangeSelectLeft(dsThuocTrung);
        }
      }
    );
  };

  // Lấy data từ listDanhSachDichVu data API
  const getParentChildRelations = useCallback(() => {
    const listThuocCha = listDanhSachDichVu.filter((x) => !x.dungKemId);
    const listThuocDungKem = listDanhSachDichVu.filter((x) => x.dungKemId);

    return { listThuocCha, listThuocDungKem };
  }, [listDanhSachDichVu]);

  // Lấy data từ state.dataSource data trên UI
  const getUpdatedParentChildRelations = useCallback(() => {
    const listThuocCha = [];
    const listThuocDungKem = [];

    Object.values(state.dataSource).forEach((item) => {
      item.dsDichVu.forEach((x) => {
        listThuocCha.push(x);
        if (x.children && x.children.length > 0) {
          listThuocDungKem.push(...x.children);
        }
      });
    });

    return { listThuocCha, listThuocDungKem };
  }, [state.dataSource]);

  // Lấy tất cả children của một parent
  const getChildrenOfParent = useCallback(
    (parentKey, listThuocDungKem, listThuocCha) => {
      // Tìm parent record để lấy id
      const parent = listThuocCha.find((x) => x.key === parentKey);
      if (!parent) return [];

      return listThuocDungKem.filter((x) => x.dungKemId === parent.id);
    },
    []
  );

  // Lấy tất cả items trong một group
  const getItemsInGroup = useCallback(
    (keyGroup, listThuocCha, listThuocDungKem) => {
      // Tìm tất cả items có cùng loaiThoiGian với groupName
      const listDataCha = listThuocCha.filter((x) => x.keyId === keyGroup);
      const listDataCon = listThuocDungKem.filter((x) =>
        listDataCha.map((item) => item.id).includes(x.dungKemId)
      );
      return [...listDataCha, ...listDataCon];
    },
    []
  );

  // Kiểm tra tất cả children của parent đã được chọn chưa
  const areAllChildrenSelected = useCallback(
    (parentKey, selectedKeys, listThuocDungKem, listThuocCha) => {
      const children = getChildrenOfParent(
        parentKey,
        listThuocDungKem,
        listThuocCha
      );
      return (
        children.length > 0 &&
        children.every((child) => selectedKeys.includes(child.key))
      );
    },
    [getChildrenOfParent]
  );

  // Kiểm tra tất cả items của group đã được chọn chưa
  const areAllGroupItemsSelected = useCallback(
    (keyGroup, selectedKeys, listThuocCha, listThuocDungKem) => {
      const itemsInGroup = getItemsInGroup(
        keyGroup,
        listThuocCha,
        listThuocDungKem
      );
      return (
        itemsInGroup.length > 0 &&
        itemsInGroup.every((item) => selectedKeys.includes(item.key))
      );
    },
    [getItemsInGroup]
  );

  // Kiểm tra có bất kỳ item nào của group được chọn không
  const isAnyGroupItemSelected = useCallback(
    (keyGroup, selectedKeys, listThuocCha, listThuocDungKem) => {
      const itemsInGroup = getItemsInGroup(
        keyGroup,
        listThuocCha,
        listThuocDungKem
      );
      return (
        itemsInGroup.length > 0 &&
        itemsInGroup.some((item) => selectedKeys.includes(item.key))
      );
    },
    [getItemsInGroup]
  );

  // Auto expand parent khi có children được chọn
  const autoExpandParents = useCallback(
    (selectedKeys, listThuocCha, listThuocDungKem) => {
      const parentsToExpand = [];

      listThuocCha.forEach((parent) => {
        const children = getChildrenOfParent(
          parent.key,
          listThuocDungKem,
          listThuocCha
        );
        const hasSelectedChildren = children.some((child) =>
          selectedKeys.includes(child.key)
        );

        if (hasSelectedChildren && children.length > 0) {
          parentsToExpand.push(parent.key);
        }
      });

      return [...new Set([...state.expandedKeys, ...parentsToExpand])];
    },
    [state.expandedKeys, getChildrenOfParent]
  );

  const onSelectLeft = (record, isSelect) => {
    const dsDichVuThuoc = [
      ...state?.listDvThuoc,
      ...state?.listDvThuocNhaThuoc,
    ];

    const { listThuocCha, listThuocDungKem } = getParentChildRelations();

    function onChangeSelectLeft(dsThuocTrung) {
      refKey.current = true;
      let selectedRowKeys = [...state.selectedRowKeys];
      let newExpandedKeys = [...state.expandedKeys];

      if (record.isGroup) {
        // === CASE: Chọn/bỏ chọn GROUP ===
        const itemsInGroup = getItemsInGroup(
          record.key,
          listThuocCha,
          listThuocDungKem
        );
        const itemKeys = itemsInGroup.map((item) => item.key);

        if (isSelect) {
          // Thêm tất cả items trong group
          selectedRowKeys = [...selectedRowKeys, record.key, ...itemKeys];

          // Auto expand các parents có children trong group
          newExpandedKeys = autoExpandParents(
            selectedRowKeys,
            listThuocCha,
            listThuocDungKem
          );
        } else {
          // Bỏ tất cả items trong group
          selectedRowKeys = selectedRowKeys.filter(
            (key) => !itemKeys.includes(key) && key !== record.key
          );
        }
      } else {
        // === CASE: Chọn/bỏ chọn INDIVIDUAL RECORD ===
        const isParent = listThuocCha.some((x) => x.key === record.key);
        const isChild = listThuocDungKem.some((x) => x.key === record.key);

        if (isSelect) {
          // Thêm record hiện tại
          selectedRowKeys.push(record.key);

          if (isParent) {
            // Nếu là parent: tự động chọn tất cả children
            const children = getChildrenOfParent(
              record.key,
              listThuocDungKem,
              listThuocCha
            );
            const childKeys = children.map((child) => child.key);
            selectedRowKeys = [...selectedRowKeys, ...childKeys];

            // Auto expand parent này
            if (children.length > 0 && !newExpandedKeys.includes(record.key)) {
              newExpandedKeys.push(record.key);
            }

            // Kiểm tra có cần auto-select group không
            if (
              record.keyId &&
              areAllGroupItemsSelected(
                record.keyId,
                selectedRowKeys,
                listThuocCha,
                listThuocDungKem
              )
            ) {
              // Tất cả items trong group đã được chọn → auto select group
              if (!selectedRowKeys.includes(record.keyId)) {
                selectedRowKeys.push(record.keyId);
              }
            }
          } else if (isChild) {
            // Nếu là child: kiểm tra có cần auto-select parent không
            const parent = listThuocCha.find((x) => x.key === record.parentKey);
            if (
              parent &&
              areAllChildrenSelected(
                parent.key,
                [...selectedRowKeys, record.key], // Thêm record vào để check
                listThuocDungKem,
                listThuocCha
              )
            ) {
              // Tất cả children đã được chọn → auto select parent
              selectedRowKeys.push(parent.key);

              // Kiểm tra có cần auto-select group không (sau khi select parent)
              const groupKey = parent.keyId;
              if (
                groupKey &&
                areAllGroupItemsSelected(
                  groupKey,
                  selectedRowKeys,
                  listThuocCha,
                  listThuocDungKem
                )
              ) {
                // Tất cả items trong group đã được chọn → auto select group
                if (!selectedRowKeys.includes(groupKey)) {
                  selectedRowKeys.push(groupKey);
                }
              }
            }

            // Auto expand parent của child này
            if (parent && !newExpandedKeys.includes(parent.key)) {
              newExpandedKeys.push(parent.key);
            }
          } else {
            // Nếu là individual item (không phải parent/child): kiểm tra group
            const groupKey = record.parentKeyId || record.keyId;
            if (
              groupKey &&
              areAllGroupItemsSelected(
                groupKey,
                selectedRowKeys,
                listThuocCha,
                listThuocDungKem
              )
            ) {
              // Tất cả items trong group đã được chọn → auto select group
              if (!selectedRowKeys.includes(groupKey)) {
                selectedRowKeys.push(groupKey);
              }
            }
          }
        } else {
          // Bỏ chọn record hiện tại
          selectedRowKeys = selectedRowKeys.filter((key) => key !== record.key);

          if (isParent) {
            // Nếu là parent: tự động bỏ chọn tất cả children
            const children = getChildrenOfParent(
              record.key,
              listThuocDungKem,
              listThuocCha
            );
            const childKeys = children.map((child) => child.key);
            selectedRowKeys = selectedRowKeys.filter(
              (key) => !childKeys.includes(key)
            );

            // Chỉ bỏ chọn group nếu KHÔNG còn item nào trong group được chọn
            if (record.keyId) {
              const groupKey = record.keyId;
              const hasAnyGroupItemSelected = isAnyGroupItemSelected(
                groupKey,
                selectedRowKeys, // selectedRowKeys đã loại bỏ parent và children
                listThuocCha,
                listThuocDungKem
              );

              if (!hasAnyGroupItemSelected) {
                selectedRowKeys = selectedRowKeys.filter(
                  (key) => key !== groupKey
                );
              }
            }
          } else if (isChild) {
            // Nếu là child: kiểm tra có cần bỏ chọn parent không
            const parent = listThuocCha.find((x) => x.key === record.parentKey);
            if (parent) {
              // Chỉ bỏ chọn parent nếu KHÔNG còn children nào được chọn
              const remainingChildren = getChildrenOfParent(
                parent.key,
                listThuocDungKem,
                listThuocCha
              );
              const remainingChildKeys = remainingChildren.map(
                (child) => child.key
              );
              const hasSelectedChildren = remainingChildKeys.some((childKey) =>
                selectedRowKeys.includes(childKey)
              );

              if (!hasSelectedChildren) {
                selectedRowKeys = selectedRowKeys.filter(
                  (key) => key !== parent.key
                );
              }

              // Chỉ bỏ chọn group nếu KHÔNG còn item nào trong group được chọn
              if (parent.keyId) {
                const groupKey = parent.keyId;
                const hasAnyGroupItemSelected = isAnyGroupItemSelected(
                  groupKey,
                  selectedRowKeys, // selectedRowKeys có thể đã loại bỏ parent
                  listThuocCha,
                  listThuocDungKem
                );

                if (!hasAnyGroupItemSelected) {
                  selectedRowKeys = selectedRowKeys.filter(
                    (key) => key !== groupKey
                  );
                }
              }
            }
          } else {
            // Nếu là individual item: chỉ bỏ chọn group nếu KHÔNG còn item nào trong group được chọn
            const groupKey = record.parentKeyId || record.keyId;
            if (groupKey) {
              const hasAnyGroupItemSelected = isAnyGroupItemSelected(
                groupKey,
                selectedRowKeys, // selectedRowKeys đã loại bỏ current record
                listThuocCha,
                listThuocDungKem
              );

              if (!hasAnyGroupItemSelected) {
                selectedRowKeys = selectedRowKeys.filter(
                  (key) => key !== groupKey
                );
              }
            }
          }
        }
      }

      // Loại bỏ thuốc trùng nếu có
      if (isArray(dsThuocTrung, true)) {
        const trungKeys = dsThuocTrung.map((x) => x.key);
        selectedRowKeys = selectedRowKeys.filter(
          (key) => !trungKeys.includes(key)
        );
      }

      // Remove duplicates
      selectedRowKeys = [...new Set(selectedRowKeys)];

      // Update state với cả selectedRowKeys và expandedKeys
      setState({
        selectedRowKeys,
        expandedKeys: newExpandedKeys,
      });
    }

    if (dsDichVuThuoc?.length && isSelect) {
      // Kiểm tra mở popup khi dịch vụ trùng - sử dụng logic cha-con
      let objDupplicate, objDupplicateThuocKho, dsThuocTrung;

      if (record.isGroup) {
        // === CASE: Chọn GROUP - lấy tất cả thuốc cha + con trong group ===
        const {
          listThuocCha: updatedListThuocCha,
          listThuocDungKem: updatedListThuocDungKem,
        } = getUpdatedParentChildRelations();
        const itemsInGroup = getItemsInGroup(
          record.key,
          updatedListThuocCha,
          updatedListThuocDungKem
        );

        // Check duplicate với tất cả items trong group
        objDupplicate = dsDichVuThuoc.filter((item1) =>
          itemsInGroup.map((x) => x.dichVuId)?.includes(item1.dichVuId)
        );

        // Lọc thuốc kho (không phải nhà thuốc) trong group
        let dataThuocKho = itemsInGroup.filter((i) => !i.nhaThuoc);
        let currentKho =
          (dsThietLapChonKhoThuoc || []).find((i) =>
            dataThuocKho.some((j) => j.khoId === i.id)
          ) || dsThietLapChonKhoThuoc?.[0];

        // Check duplicate thuốc kho với cơ chế duyệt phát chung
        objDupplicateThuocKho = dsDichVuThuoc.filter((item1) => {
          return (
            (currentKho?.dsCoCheDuyetPhat || []).includes(
              CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
            ) && dataThuocKho.map((x) => x.dichVuId)?.includes(item1.dichVuId)
          );
        });

        // Danh sách thuốc trùng trong kho
        dsThuocTrung = dataThuocKho.filter((i) =>
          objDupplicateThuocKho.some((j) => j.dichVuId === i.dichVuId)
        );
      } else {
        // === CASE: Chọn INDIVIDUAL RECORD - bao gồm cả parent và children ===
        const {
          listThuocCha: updatedListThuocCha,
          listThuocDungKem: updatedListThuocDungKem,
        } = getUpdatedParentChildRelations();
        const isParent = updatedListThuocCha.some((x) => x.key === record.key);
        let itemsToCheck = [record]; // Bắt đầu với record hiện tại

        if (isParent) {
          // Nếu là parent: thêm tất cả children vào check
          const children = getChildrenOfParent(
            record.key,
            updatedListThuocDungKem,
            updatedListThuocCha
          );
          itemsToCheck = [record, ...children];
        }

        // Check duplicate với record + children (nếu có)
        objDupplicate = dsDichVuThuoc.filter((item1) =>
          itemsToCheck.some(
            (item) => item1.dichVuId === item.dichVuId || item1.key === item.key
          )
        );

        // Xử lý thuốc kho cho record hiện tại
        let currentKho =
          (dsThietLapChonKhoThuoc || []).find((i) => i.id == record.khoId) ||
          dsThietLapChonKhoThuoc?.[0];

        objDupplicateThuocKho = dsDichVuThuoc.filter((item1) => {
          return (
            !record.nhaThuoc &&
            (currentKho?.dsCoCheDuyetPhat || []).includes(
              CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
            ) &&
            itemsToCheck.some(
              (item) =>
                item1.dichVuId === item.dichVuId || item1.key === item.key
            )
          );
        });

        // Danh sách thuốc trùng (chỉ record hiện tại nếu là thuốc kho)
        dsThuocTrung =
          !record.nhaThuoc && objDupplicateThuocKho.length > 0 ? [record] : [];
      }

      if (objDupplicate.length) {
        refKey.current = true;
        onShowCanhBaoKeTrung(
          objDupplicate,
          onChangeSelectLeft,
          objDupplicateThuocKho,
          dsThuocTrung
        );
      } else {
        onChangeSelectLeft();
      }
    } else {
      onChangeSelectLeft();
    }
  };

  const onChangeLeft = (record, listData) => {
    if (refKey.current) {
      refKey.current = null;
      return;
    }

    // Sử dụng data đã được update từ UI thay vì data gốc
    const { listThuocCha, listThuocDungKem } = getUpdatedParentChildRelations();

    // Lọc data dựa trên record keys - bao gồm cả cha và con
    const data = [...listThuocCha, ...listThuocDungKem].filter((x) =>
      record?.includes(x.key)
    );

    const dsDichVuThuoc = [
      ...state?.listDvThuoc,
      ...state?.listDvThuocNhaThuoc,
    ];

    if (data?.length) {
      // Check duplicate với tất cả items được chọn (cha + con)
      let objDupplicate = dsDichVuThuoc.filter((item1) =>
        data.map((x) => x.dichVuId)?.includes(item1.dichVuId)
      );

      // Lọc thuốc kho (không phải nhà thuốc)
      let dataThuocKho = data.filter((i) => !i.nhaThuoc);
      let currentKho =
        (dsThietLapChonKhoThuoc || []).find((i) =>
          dataThuocKho.some((j) => j.khoId === i.id)
        ) || dsThietLapChonKhoThuoc?.[0];
      // Check duplicate thuốc kho với cơ chế duyệt phát chung
      let objDupplicateThuocKho = dsDichVuThuoc.filter((item1) => {
        return (
          (currentKho?.dsCoCheDuyetPhat || []).includes(
            CO_CHE_DUYET_PHAT.DUYET_PHAT_CHUNG
          ) && dataThuocKho.map((x) => x.dichVuId)?.includes(item1.dichVuId)
        );
      });

      // Danh sách thuốc trùng trong kho
      let dsThuocTrung = dataThuocKho.filter((i) =>
        objDupplicateThuocKho.some((j) => j.dichVuId === i.dichVuId)
      );
      if (objDupplicate.length) {
        onShowCanhBaoKeTrung(
          objDupplicate,
          () => {
            setState({
              selectedRowKeys: isArray(dsThuocTrung, true)
                ? record.filter((i) => !dsThuocTrung.some((j) => j.key === i))
                : record,
            });
          },
          objDupplicateThuocKho,
          dsThuocTrung
        );
      } else {
        setState({
          selectedRowKeys: record,
        });
      }
    } else {
      setState({
        selectedRowKeys: record,
      });
    }
  };

  const rowSelection = {
    columnWidth: 40,
    selectedRowKeys: state.selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: onChangeLeft,
    onSelect: onSelectLeft,
  };

  const refreshGetListDichVu = () => {
    const isKsk =
      configData.thongTinNguoiBenh?.khamSucKhoe ||
      configData.thongTinNguoiBenh?.loaiDoiTuongKsk;

    getListDichVuThuoc({
      nbDotDieuTriId: configData?.nbDotDieuTriId,
      chiDinhTuDichVuId: isKsk ? null : configData.chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu: isKsk ? null : configData.chiDinhTuLoaiDichVu,
      dsTrangThaiHoan: [0, 10, 20],
    });
    getListThuocNhaThuoc({
      nbDotDieuTriId: configData.nbDotDieuTriId,
      chiDinhTuDichVuId: isKsk ? null : configData.chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu: isKsk ? null : configData.chiDinhTuLoaiDichVu,
      dsTrangThaiHoan: [0, 10, 20],
    });
    getListDichVuThuocKeNgoai({
      nbDotDieuTriId: configData.nbDotDieuTriId,
      chiDinhTuDichVuId: isKsk ? null : configData.chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu: isKsk ? null : configData.chiDinhTuLoaiDichVu,
    });
  };

  const onOk = () => {
    setState({ isLoading: true });
    let dataSelected = [];
    Object.values(state.dataSource).forEach((item) => {
      item.dsDichVu.forEach((x) => {
        if (state.selectedRowKeys.includes(x.key)) dataSelected.push(x);
      });
    });

    // Kiểm tra children có parent không
    const childrenWithoutParent = [];
    Object.values(state.dataSource).forEach((item) => {
      item.dsDichVu.forEach((x) => {
        if (x.children && x.children.length > 0) {
          x.children.forEach((child) => {
            if (state.selectedRowKeys.includes(child.key)) {
              // Kiểm tra parent có trong dataSelected không
              const parentSelected = dataSelected.some(
                (parent) => parent.key === child.parentKey
              );
              if (!parentSelected) {
                childrenWithoutParent.push(child.tenDichVu);
              }
            }
          });
        }
      });
    });

    if (childrenWithoutParent.length > 0) {
      message.error(
        t("phacDoDieuTri.chonThuocChinhTruocKhiChonThuocDiKem", {
          dsThuoc: childrenWithoutParent.join(", "),
        })
      );
      setState({ isLoading: false });
      return;
    }

    const dsKhoThuoc = listThietLapChonKho.filter((x) =>
      x.dsLoaiDichVu.includes(LOAI_DICH_VU.THUOC)
    );
    const dsKhoVatTu = listThietLapChonKho.filter((x) =>
      x.dsLoaiDichVu.includes(LOAI_DICH_VU.VAT_TU)
    );
    const dsKhoHoaChat = listThietLapChonKho.filter((x) =>
      x.dsLoaiDichVu.includes(LOAI_DICH_VU.HOA_CHAT)
    );

    if (dataSelected.every((x) => !x.soLuong)) {
      setState({ checkValidate: true, isLoading: false });
      return;
    }

    const dsXetNghiem = dataSelected
      .filter((x) => !x.isGroup && x.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM)
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKyThuat: { phongThucHienId: item.phongThucHienId },
        };
      });
    const dsCdhaTdcnPtTt = dataSelected
      .filter(
        (x) =>
          !x.isGroup &&
          [LOAI_DICH_VU.CDHA, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT].includes(
            x.loaiDichVu
          )
      )
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKyThuat: { phongThucHienId: item.phongThucHienId },
        };
      });
    const dsKham = dataSelected
      .filter((x) => !x.isGroup && x.loaiDichVu === LOAI_DICH_VU.KHAM)
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKyThuat: { phongThucHienId: item.phongThucHienId },
        };
      });
    const dsThuoc = dataSelected
      .filter(
        (x) =>
          !x.isGroup &&
          x.loaiDichVu === LOAI_DICH_VU.THUOC &&
          !x.nhaThuoc &&
          !x.thuocChiDinhNgoaiId
      )
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKho: {
            khoId: item.khoId || dsKhoThuoc?.[0]?.id,
            moi: true,
            dsDungKem: (item.children || [])
              .filter((child) => state.selectedRowKeys.includes(child.key))
              .map((x) => {
                return {
                  nbDotDieuTriId: state.nbDotDieuTriId,
                  lieuDungId: x.lieuDungId,
                  duongDungId: x.duongDungId,
                  nbDichVu: {
                    phacDoDieuTriDichVuId: x.id,
                    dichVuId: x?.dichVuId,
                    soLuong: x.soLuong,
                    chiDinhTuDichVuId: state.chiDinhTuDichVuId,
                    chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
                    khoaChiDinhId: state.khoaChiDinhId,
                    tuTra: x?.tuTra,
                    khongTinhTien: x?.khongTinhTien,
                    ghiChu: x?.ghiChu,
                    nguonKhacId: x.nguonKhacId,
                  },
                  nbDvKho: {
                    khoId: x.khoId || item.khoId || dsKhoThuoc?.[0]?.id,
                    lyDoHuy: x?.lyDoHuy,
                    moi: true,
                  },
                  dsMucDich: x?.dsMucDich,
                  cachDung: x?.cachDung,
                  soNgay: x?.soNgay,
                  soLan1Ngay: x.soLan1Ngay,
                  soLuong1Lan: chiDinhThuocUtils?.evalString(x.soLuong1Lan),
                  thoiDiem: x?.thoiDiem,
                };
              }),
          },
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: chiDinhThuocUtils.evalString(item.soLuong1Lan),
          duongDungId: item.duongDungId,
          cachDung: item?.cachDung,
          lieuDungId: item?.lieuDungId,
        };
      });

    const dsThuocNhaThuoc = dataSelected
      .filter(
        (x) =>
          !x.isGroup &&
          x.loaiDichVu === LOAI_DICH_VU.THUOC &&
          x.nhaThuoc &&
          !x.thuocChiDinhNgoaiId
      )
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          phacDoDieuTriDichVuId: item.id,
          dichVuId: item.dichVuId,
          soLuong: item.soLuong,
          chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
          khoaChiDinhId: state.khoaChiDinhId,
          khongTinhTien: item?.khongTinhTien,
          tuTra: item.tuTra,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: chiDinhThuocUtils.evalString(item.soLuong1Lan),
          duongDungId: item.duongDungId,
          cachDung: item?.cachDung,
          lieuDungId: item?.lieuDungId,
        };
      });

    const dsThuocChiDinhNgoai = dataSelected
      .filter((x) => !x.isGroup && x.thuocChiDinhNgoaiId)
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          phacDoDieuTriDichVuId: item.id,
          dichVuId: item.dichVuId,
          soLuong: item.soLuong,
          chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
          khoaChiDinhId: state.khoaChiDinhId,
          khongTinhTien: item?.khongTinhTien,
          tuTra: item.tuTra,
          soNgay: item?.soNgay,
          soLan1Ngay: item.soLan1Ngay,
          soLuong1Lan: chiDinhThuocUtils.evalString(item.soLuong1Lan),
          duongDungId: item.duongDungId,
          cachDung: item?.cachDung,
          lieuDungId: item?.lieuDungId,
          thuocChiDinhNgoaiId: item?.thuocChiDinhNgoaiId,
        };
      });

    const dsVatTu = dataSelected
      .filter((x) => !x.isGroup && x.loaiDichVu === LOAI_DICH_VU.VAT_TU)
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKho: { khoId: item.khoId || dsKhoVatTu?.[0]?.id, moi: true },
        };
      });
    const dsHoaChat = dataSelected
      .filter((x) => !x.isGroup && x.loaiDichVu === LOAI_DICH_VU.HOA_CHAT)
      .map((item) => {
        return {
          nbDotDieuTriId: state?.nbDotDieuTriId,
          nbDichVu: {
            phacDoDieuTriDichVuId: item.id,
            dichVuId: item.dichVuId,
            soLuong: item.soLuong,
            chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
            chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
            khoaChiDinhId: state.khoaChiDinhId,
            khongTinhTien: item?.khongTinhTien,
            tuTra: item.tuTra,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
          nbDvKho: { khoId: item.khoId || dsKhoHoaChat?.[0]?.id, moi: true },
        };
      });
    const body = [
      {
        nbDotDieuTriId: state?.nbDotDieuTriId,
        phacDoDieuTriId: state.thongTinPhacDo?.id,
        dsKham: dsKham,
        dsCdhaTdcnPtTt: dsCdhaTdcnPtTt,
        dsXetNghiem: dsXetNghiem,
        dsHoaChat: dsHoaChat,
        dsThuoc: dsThuoc,
        dsVatTu: dsVatTu,
        dsThuocNhaThuoc: dsThuocNhaThuoc,
        dsThuocChiDinhNgoai: dsThuocChiDinhNgoai,
        chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
      },
    ];
    create(body)
      .then(async (s) => {
        refCallback.current();

        const dsThuocBoSung = await Promise.all(
          (s[0].dsThuoc || [])
            .filter((x) => [7624, 8501].includes(x.code))
            .map(async (item) => {
              const _dsMucDich = await getMucDichByDVId({
                page: "",
                size: "",
                dichVuId: item?.nbDichVu?.dichVuId,
              });

              return {
                ...item,
                dsMucDich: _dsMucDich,
              };
            })
        );

        const data = [
          ...(s[0].dsCdhaTdcnPtTt || []),
          ...(s[0].dsChePhamMau || []),
          ...(s[0].dsHoaChat || []),
          ...(s[0].dsKham || []),
          ...(s[0].dsNgoaiDieuTri || []),
          ...(s[0].dsThuoc || []).filter((x) => ![7624, 8501].includes(x.code)),
          ...(s[0].dsThuocChiDinhNgoai || []),
          ...(s[0].dsThuocNhaThuoc || []),
          ...(s[0].dsVatTu || []),
          ...(s[0].dsXetNghiem || []),
        ];
        const dataError = data.filter((item) => item.code && item.code !== 0);
        if (dataError.length) {
          const messsageError = dataError.map((x) => x.message).join(", ");
          message.error(messsageError);
        } else {
          message.success(t("common.themMoiThanhCongDuLieu"));
        }

        const dataDVKTError = [
          ...(s[0].dsKham || []),
          ...(s[0].dsCdhaTdcnPtTt || []),
          ...(s[0].dsXetNghiem || []),
        ]
          .filter((item) => item.code && item.code !== 0)
          .map((item) => {
            //lấy thông tin dsPhong và loại hình thanh toán từ dịch vụ, do response ko trả về thông tin này
            const _selectedDichVu = dataSelected.find(
              (x) => x?.id == item?.nbDichVu?.phacDoDieuTriDichVuId
            );

            return {
              ...item,
              nbDichVu: {
                ...item.nbDichVu,
                loaiDichVu:
                  item.nbDichVu?.loaiDichVu ||
                  item.nbDichVu?.dichVu?.loaiDichVu,
              },
              dsPhongThucHien: _selectedDichVu?.dsPhongThucHien,
              dsLoaiHinhThanhToan: _selectedDichVu?.dsLoaiHinhThanhToan,
              dsMucDich: _selectedDichVu?.dsMucDich,
            };
          });

        if (dataDVKTError.length) {
          refBoSungThongTinDichVu.current &&
            refBoSungThongTinDichVu.current.show(
              {
                dataSource: dataDVKTError,
                chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
                chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
                phacDoDieuTriId: state.thongTinPhacDo?.id,
                nbDotDieuTriId: state?.nbDotDieuTriId,
                khoaChiDinhId: state.khoaChiDinhId,
              },
              () => {
                refreshGetListDichVu();
              }
            );
        }

        if (dsThuocBoSung.length) {
          refBoSungThongTinThuoc.current &&
            refBoSungThongTinThuoc.current.show(
              {
                dsThuoc: dsThuocBoSung,
                chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
                chiDinhTuDichVuId: state?.chiDinhTuDichVuId,
                phacDoDieuTriId: state.thongTinPhacDo?.id,
                nbDotDieuTriId: state?.nbDotDieuTriId,
                khoaChiDinhId: state.khoaChiDinhId,
              },
              () => {
                refreshGetListDichVu();
              }
            );
        }
        onCancel();
        refreshGetListDichVu();
      })
      .catch(() => {
        setState({ isLoading: false });
      });
  };

  const onSearchInput = (key) => (e) => {
    onChangeInputSearch({ [key]: e, isTongHop: true });
    setState({ [key]: e });
  };

  const onChangePage = (page) => {
    console.log(state);
    onSearch({ page: page - 1, isTongHop: true });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size: size, isTongHop: true });
  };

  const setRowClassName = (record) => {
    if (record?.isGroup) return "row-white";
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={1442}
      layerId={refLayerHotKey.current}
      title={`${t("phacDoDieuTri.chiDinhDvTrongPhacDo")}: ${
        state?.thongTinPhacDo?.ten
      }`}
      onCancel={onCancel}
      actionRight={
        <>
          <Button
            minWidth={100}
            type="default"
            onClick={onCancel}
            iconHeight={15}
          >
            {t("common.huy")}
          </Button>
          <Button
            minWidth={100}
            type="primary"
            onClick={onOk}
            loading={state.isLoading}
          >
            {t("common.dongY")}
          </Button>
        </>
      }
      maskClosable={false}
    >
      <Main>
        <div className="info-header">
          <div className="item" style={{ paddingRight: "20px" }}>
            <span>{t("common.maDv")}</span>
            <InputTimeout
              placeholder={t("phacDoDieuTri.nhapTimMaDvTrongPhacDo")}
              onChange={onSearchInput("maDichVu")}
              suffix={<SVG.IcSearch />}
              value={state.maDichVu}
            ></InputTimeout>
          </div>
          <div className="item">
            <span>{t("common.tenDichVu")}</span>
            <InputTimeout
              placeholder={t("phacDoDieuTri.nhapTimTenDvTrongPhacDo")}
              onChange={onSearchInput("tenDichVu")}
              suffix={<SVG.IcSearch />}
              value={state.tenDichVu}
            ></InputTimeout>
          </div>
        </div>
        <Collapse>
          {Object.values(state.dataSource).map((item) => {
            const tenGroup = `${
              listLoaiThoiGianPhacDoDieuTri.find(
                (x) => x.id === item.loaiThoiGian
              )?.ten
            }  ${item.stt}`;

            const groupKey = `${item.loaiThoiGian} - ${item.stt}`;
            const grouped = groupBy(
              item.dsDichVu,
              (x) => x?.tenNhomDichVuCap1 || t("phacDoDieuTri.thuocKeNgoai")
            );
            let data = [];
            Object.keys(grouped).forEach((key) => {
              data.push({
                ten: key,
                key: `${groupKey} - ${key}`,
                isGroup: true,
              });
              (grouped[key] || []).forEach((element, index) => {
                data.push({
                  ...element,
                  index: index + 1,
                  key: `${groupKey} - ${element.id}`,
                });
              });
            });

            return (
              <Panel header={tenGroup}>
                <TableWrapper
                  columns={columns}
                  dataSource={data}
                  rowSelection={rowSelection}
                  rowClassName={setRowClassName}
                  expandedRowKeys={state?.expandedKeys}
                  rowKey={(record) => record.key}
                  expandable={{
                    expandIcon: ({ expanded, onExpand, record }) =>
                      record?.children?.length ? (
                        expanded ? (
                          <CaretDownOutlined
                            onClick={(e) => {
                              onExpand(record, e);
                              setState({
                                expandedKeys: state?.expandedKeys.filter(
                                  (x) => x !== record.key
                                ),
                              });
                              e.stopPropagation();
                            }}
                          />
                        ) : (
                          <CaretRightOutlined
                            onClick={(e) => {
                              onExpand(record, e);
                              setState({
                                expandedKeys: [
                                  ...state?.expandedKeys,
                                  record.key,
                                ],
                              });
                              e.stopPropagation();
                            }}
                          />
                        )
                      ) : null,
                  }}
                />
              </Panel>
            );
          })}
        </Collapse>
        {!!listDanhSachDichVu.length && (
          <Pagination
            listData={listDanhSachDichVu}
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            onShowSizeChange={handleSizeChange}
            stylePagination={{ justifyContent: "flex-start" }}
          />
        )}
      </Main>
      <ModalBoSungThongTinThuoc ref={refBoSungThongTinThuoc} />
      <ModalBoSungThongTinDichVu ref={refBoSungThongTinDichVu} />
    </ModalTemplate>
  );
});

export default ModalChiDinhDichVu;
