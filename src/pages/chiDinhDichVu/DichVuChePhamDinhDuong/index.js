import { message, Row } from "antd";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import imgSearch from "assets/images/template/icSearch.png";
import { Main } from "./styled";
import { HOTKEY, LOAI_CHI_DINH, LOAI_DICH_VU } from "constants/index.js";
import { InputTimeout, Button, ModalTemplate, Select } from "components";
import TableChePhamDinhDuong from "./TableChePhamDinhDuong";
import { useTranslation } from "react-i18next";
import { useStore } from "hooks";
import cacheUtils from "lib-utils/cache-utils";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import stringUtils from "mainam-react-native-string-utils";
import ThemChiDinhSpan from "../components/ThemChiDinhTxtSpan";
import { debounce } from "lodash";
import chiDinhThuocUtils from "utils/chi-dinh-thuoc-utils";

const DichVuChePhamDinhDuong = (props, ref) => {
  const { t } = useTranslation();
  const nbInfoTitle = useNbInfoTitle();
  const { dataCHI_DINH_LOAI_CHE_DO_AN } = props;

  const refModal = useRef(null);
  const refIsSubmit = useRef(null);
  const refInput = useRef(null);
  const refLayerHotKey = useRef(stringUtils.guid());
  const refSubmit = useRef(null);
  const refCallback = useRef(null);
  const refListSelectedDv = useRef([]);

  const {
    chiDinhChePhamDinhDuong: {
      tamTinhTien,
      chiDinhDichVu,
      getListDichVuChePhamDD,
    },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
    toDieuTri: { getToDieuTriById },
  } = useDispatch();

  const configData = useStore("chiDinhKhamBenh.configData", {});
  const nhanVienId = useStore("auth.auth.nhanVienId", null);

  const [state, _setState] = useState({
    show: false,
    thanhTien: 0,
    splitCacheCustomize: {},
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (state.show) {
      onAddLayer({ layerId: refLayerHotKey.current });
      onRegisterHotkey({
        layerId: refLayerHotKey.current,
        hotKeys: [
          {
            keyCode: HOTKEY.F2, //F2
            onEvent: (e) => {
              onChange("");
              refInput.current && refInput.current.focus();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refSubmit.current && refSubmit.current();
            },
          },
        ],
      });
      async function fetchData() {
        let data = await cacheUtils.read(
          "",
          "DATA_THEO_SO_LUONG_TON_KHO_DINH_DUONG",
          null,
          false
        );
        if (data) {
          setState({ theoSoLuongTonKho: data?.theoSoLuongTonKho });
        } else {
          setState({ theoSoLuongTonKho: 15 });
        }
      }
      fetchData();
      return () => {
        onRemoveLayer({ layerId: refLayerHotKey.current });
      };
    }
  }, [state.show]);

  useImperativeHandle(ref, () => ({
    show: (option = {}, callback) => {
      setState({
        show: true,
        khoId: option.khoId,
        keyword: "",
        dataSource: option?.dataSource || {},
        dataKho: option?.dataKho || [],
      });

      refIsSubmit.current = false;
      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (!state.show) {
      setTimeout(() => refModal.current.hide(), 50);
    } else {
      refModal.current.show({});
      setTimeout(() => {
        refInput.current.focus();
      }, 1000);
    }
  }, [state.show]);

  const onTamTinhTien = debounce((listSelected, notCallApiTinhTien = false) => {
    if (notCallApiTinhTien) {
      refListSelectedDv.current = listSelected;
      return;
    }

    const payload = listSelected.map((item) => ({
      nbDotDieuTriId: configData.nbDotDieuTriId,
      soLan1Ngay: item.soLan1Ngay,
      soLuong1Lan: chiDinhThuocUtils.evalString(item.soLuong1Lan),
      lieuDungId: item.lieuDungId,
      cachDung: item?.cachDung,
      thoiDiem: item?.thoiDiem,
      nbDichVu: {
        dichVuId: item?.dichVuId,
        soLuong: item.soLuong,
        dichVu: {
          id: item?.id,
          ma: item?.ma,
          ten: item?.ten,
        },
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        khoaChiDinhId: configData.khoaChiDinhId,
        ghiChu: item?.ghiChu,
      },
      nbDvKho: {
        khoId: state.khoId,
      },
    }));

    tamTinhTien(payload).then((s) => {
      const thanhTien = (s || []).reduce(
        (accumulator, currentValue) =>
          accumulator + currentValue.nbDichVu.thanhTien,
        0
      );
      const messageWarning = ((s || []).filter((x) => x.message) || []).reduce(
        (value, currentValue) => value.concat(currentValue.message + ", "),
        ""
      );
      if (messageWarning?.length) message.warning(messageWarning);

      refListSelectedDv.current = listSelected;
      setState({
        thanhTien: thanhTien,
      });
    });
  }, 500);

  const onSetData = (listSelected) => {
    refListSelectedDv.current = listSelected;
  };

  const onChange = (value) => {
    setState({ keyword: value });
  };

  const onSubmit = async () => {
    if (refIsSubmit.current) return;

    const listSelectedDv = refListSelectedDv.current;

    if (!listSelectedDv.length) {
      message.error(t("khamBenh.chiDinh.yeuCauNhapChiDinhDichVu"));
      return;
    }
    let checkSoLuong = listSelectedDv.some(
      (item) => !item.soLuong || item.soLuong === 0
    );
    if (checkSoLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }

    const payload = listSelectedDv.map((item) => {
      return {
        nbDotDieuTriId: configData.nbDotDieuTriId,
        soLan1Ngay: item.soLan1Ngay,
        soLuong1Lan: chiDinhThuocUtils.evalString(item.soLuong1Lan),
        lieuDungId: item.lieuDungId,
        cachDung: item?.cachDung,
        thoiDiem: item?.thoiDiem,
        nbDichVu: {
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong,
          chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          khoaChiDinhId: configData.khoaChiDinhId,
          loaiDichVu: item?.loaiDichVu,
          tuTra: item?.tuTra,
          khongTinhTien: item?.khongTinhTien,
          ghiChu: item?.ghiChu,
        },
        nbDvKho: {
          khoId: state.khoId,
          loaiChiDinh: item.dotXuat
            ? LOAI_CHI_DINH.DOT_XUAT
            : item.boSung
            ? LOAI_CHI_DINH.BO_SUNG
            : LOAI_CHI_DINH.THUONG,
        },
      };
    });
    refIsSubmit.current = true;

    try {
      const res = await chiDinhDichVu(payload);

      const messageError = (res || []).filter(
        (item) => item.message && item.code !== 0
      );
      if (messageError.length) {
        message.error(messageError.map((x) => x.message).join(","));
      }

      if (dataCHI_DINH_LOAI_CHE_DO_AN?.eval() && configData.chiDinhTuDichVuId) {
        await getToDieuTriById(configData.chiDinhTuDichVuId);
      }
      await getListDichVuChePhamDD({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        dsChiDinhTuDichVuId: configData.dsChiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20],
      });

      refCallback.current && refCallback.current();
      onCancel();
    } catch (error) {
      refIsSubmit.current = false;
    }
  };

  refSubmit.current = onSubmit;

  const onSelectKho = (value) => {
    setState({
      khoId: value,
    });
  };

  const onCancel = () => {
    setState({
      show: false,
      thanhTien: 0,
    });
  };

  const onResizeSplit = async (key, keyCache, value) => {
    const splitCacheCustomize = Object.assign(state.splitCacheCustomize, {
      [key]: value,
    });
    setState({ splitCacheCustomize });
    await cacheUtils.save(nhanVienId, keyCache, value, false);
  };

  return (
    <ModalTemplate
      width={"95%"}
      ref={refModal}
      title={t("common.chiDinhChePhamDinhDuong")}
      onCancel={onCancel}
      rightTitle={nbInfoTitle}
      visible={state.show}
      maskClosable={false}
      actionRight={
        <>
          <Button type={"default"} onClick={onCancel} minWidth={100}>
            {t("common.huy")}
          </Button>
          <Button type="primary" onClick={onSubmit} minWidth={100}>
            {t("common.dongY")}
          </Button>
        </>
      }
    >
      <Main>
        <Row className="content">
          <Row className="content-title">
            <ThemChiDinhSpan />
            <Select
              placeholder={t("quanLyNoiTru.chonKho")}
              data={state?.dataKho}
              onChange={onSelectKho}
              value={state?.khoId}
            ></Select>
            <div>&nbsp;&nbsp;&nbsp;</div>
            <div className="input-box">
              <img src={imgSearch} alt="imgSearch" />
              <InputTimeout
                style={{ width: 450, height: 32, marginLeft: 0 }}
                className="input-header"
                onChange={onChange}
                value={state.keyword}
                ref={refInput}
              />
            </div>
          </Row>
          <TableChePhamDinhDuong
            thanhTien={state.thanhTien}
            onSelected={onTamTinhTien}
            loaiDichVu={LOAI_DICH_VU.CHE_PHAM_DINH_DUONG}
            visible={state.show}
            layerId={refLayerHotKey.current}
            khoId={state.khoId}
            keyword={state.keyword}
            onResizeSplit={onResizeSplit}
            onSetData={onSetData}
            khoaChiDinhId={configData.khoaChiDinhId}
          />
        </Row>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(DichVuChePhamDinhDuong);
