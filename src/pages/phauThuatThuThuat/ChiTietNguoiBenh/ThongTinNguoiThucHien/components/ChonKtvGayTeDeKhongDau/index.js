import React, { forwardRef, memo } from "react";
import { useTranslation } from "react-i18next";
import { Col } from "antd";
import { Select } from "components";
import { useStore, useQueryAll } from "hooks";
import { useDispatch } from "react-redux";
import TenViTriChamCong, { VI_TRI_CHAM_CONG } from "../TenViTriChamCong";
import useViTriChamCong from "../useViTriChamCong";
import { query } from "redux-store/stores";

const ChonKtvGayTeDeKhongDau = (
  { fromSetting, doiTenNguoiThucHien, disabledAll, ...props },
  ref
) => {
  const { t } = useTranslation();
  const gayTe1Id = useStore("pttt.thongTinNguoiThucHien.gayTe1Id", null);

  const { placeHolder, dsNhomHuongPhuCapPtTtId } = useViTriChamCong({
    viTri: VI_TRI_CHAM_CONG.KTV_GAY_TE_DE_KHONG_DAU,
    defaultTitle: t("pttt.chonKtvGayTeDeKhongDau"),
  });

  const { data: listDataDonViHuong } = useQueryAll(
    query.huongPhuCapPTTT.queryAllHuongPhuCapPTTT({
      params: {
        dsNhomHuongPhuCapPtTtId,
      },
      enabled: dsNhomHuongPhuCapPtTtId?.length > 0,
    })
  );

  const {
    pttt: { updateThongTinNguoiThucHien },
  } = useDispatch();

  const onChange = (value) => {
    updateThongTinNguoiThucHien({ gayTe1Id: value });
  };

  return (
    <Col md={8} xl={8} xxl={8} {...props} ref={ref}>
      <div className="item-select">
        <TenViTriChamCong
          viTri={VI_TRI_CHAM_CONG.KTV_GAY_TE_DE_KHONG_DAU}
          defaultTitle={
            doiTenNguoiThucHien
              ? t("pttt.bacSiKtvGayTeDeKhongDauChinh")
              : t("pttt.ktvGayTeDeKhongDau")
          }
        />
        <Select
          onChange={onChange}
          value={gayTe1Id}
          className="input-filter"
          placeholder={placeHolder}
          data={listDataDonViHuong}
          disabled={disabledAll}
          dropdownMatchSelectWidth={450}
        />
      </div>
    </Col>
  );
};
export default memo(forwardRef(ChonKtvGayTeDeKhongDau));
