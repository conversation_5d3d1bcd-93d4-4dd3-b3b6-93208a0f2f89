import React, { useEffect, useRef, useState } from "react";
import {
  TableWrapper,
  Pagination,
  Checkbox,
  Tooltip,
  EllipsisText,
} from "components";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore, useThietLap } from "hooks";
import {
  ENUM,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
} from "constants/index";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { cloneDeep } from "lodash";
import { SVG } from "assets";
import { transformQueryString, safeConvertToArray } from "utils";
import { select } from "redux-store/stores";
import { useTrangThaiDv } from "../../hooks/useTrangThaiDv";
import ModalPhanPhongMo from "../ModalPhanPhongMo";
import { checkRole } from "lib-utils/role-utils";
import IconDVDaChiDinh from "./IconDVDaChiDinh";
import ModalDoiKhoaChiDinh from "../ModalDoiKhoaChiDinh";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

const { Column, Setting } = TableWrapper;

const DanhSach = ({ khoaLamViec, loadingFetchData }) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const refModalPhanPhongMo = useRef(null);
  const refModalDoiKhoaChiDinh = useRef(null);

  const [listKhoaTheoTaiKhoan, getKhoaTheoTaiKhoanById] = useStore(
    select.khoa.listKhoaTheoTaiKhoan
  );

  const { dataSortColumn, listDsPttt, totalElements, page, size, dataSearch } =
    useSelector((state) => state.pttt);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);
  const [listLoaiPtTt] = useEnum(ENUM.LOAI_PTTT);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const [dataHIEN_THI_TINH_NANG_CHUYEN_MO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TINH_NANG_CHUYEN_MO
  );
  const [dataHIEN_THI_GOI_MO_TRONG_PTTT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_GOI_MO_TRONG_PTTT
  );
  const [dataHIEN_THI_KET_NOI_MEMO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_NOI_MEMO
  );
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const userName = useStore("auth.auth.username", "");
  const { getTrangThaiDv } = useTrangThaiDv();

  const {
    pttt: { onSizeChange, onSortChange, onSearch, getDashboardTheoTrangThai },
  } = useDispatch();

  useEffect(() => {
    if (loadingFetchData || !khoaLamViec?.id) return;
    loadData();
  }, [loadingFetchData, khoaLamViec?.id]);

  const loadData = async () => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = transformQueryString({
      tuThoiGianThucHien: {
        type: "dateOptions",
        format: (date) => date.format("YYYY-MM-DD 00:00:00"),
        defaultValue: moment().format("YYYY-MM-DD 00:00:00"),
      },
      denThoiGianThucHien: {
        type: "dateOptions",
        format: (date) => date.format("YYYY-MM-DD 23:59:59"),
        defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
      },
      dsTrangThai: {
        format: (value) =>
          value
            .split(",")
            .flatMap(
              (i) =>
                getTrangThaiDv(Number(i))?.listId ||
                getTrangThaiDv(Number(i))?.id
            )
            .filter(Boolean),
        defaultValue: [
          TRANG_THAI_DICH_VU.CHO_TIEP_NHAN,
          TRANG_THAI_DICH_VU.CHUAN_BI_THUC_HIEN,
          TRANG_THAI_DICH_VU.DA_TIEP_NHAN,
        ],
      },
      phongThucHienId: {
        format: (value) => parseInt(value),
      },
      khoaThucHienId: {
        format: (value) => (value ? parseInt(value) : value),
        defaultValue: khoaLamViec?.id,
      },
      dsNhomDichVuCap1Id: {
        format: (value) => value.split(",").map(Number),
        defaultValue: listAllNhomDichVuCap1.map((item) => item.id),
      },
      dsTrangThaiHoan: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [0],
      },
    });

    const sort = JSON.parse(dataSortColumn);

    const dsKhoaThucHienId =
      khoaLamViec?.id !== ""
        ? [khoaLamViec?.id]
        : listKhoaTheoTaiKhoan.map((khoa) => khoa.id);

    if (queries.dsTrangThaiHoan?.length === listTrangThaiHoan.length) {
      delete queries.dsTrangThaiHoan;
    }

    onSizeChange({
      dataSearch: queries,
      dataSortColumn: sort,
      size: parseInt(size || 10),
      page: parseInt(page || 0),
    });
    getDashboardTheoTrangThai({
      tuThoiGianThucHien: queries.tuThoiGianThucHien,
      denThoiGianThucHien: queries.denThoiGianThucHien,
      dsKhoaThucHienId,
      dsPhongThucHienId: queries.phongThucHienId
        ? safeConvertToArray(queries.phongThucHienId)
        : null,
    });
  };

  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1 });
  };

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value });
  };

  const history = useHistory();

  const onRow = (record) => {
    return {
      onClick: onViewDetail(record),
    };
  };

  const onViewDetail = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    history.push({
      pathname: "/phau-thuat-thu-thuat/chi-tiet-phau-thuat/" + record.id,
      state: getAllQueryString(undefined, {
        filterEmpty: false,
      }),
    });
  };

  const openMemoPopup = (pid) => (e) => {
    e.stopPropagation();
    e.preventDefault();
    const url = `https://note.hpimc.info/Memo?pid=${pid}&username=${userName}`;
    window.open(
      url,
      "_blank",
      "width=800,height=600,resizable=yes,scrollbars=yes"
    );
  };

  const onPhanPhongMo = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    refModalPhanPhongMo.current &&
      refModalPhanPhongMo.current.show({
        item: record,
        khoaChiDinhId: dataSearch?.khoaThucHienId
          ? dataSearch?.khoaThucHienId
          : khoaLamViec?.id,
      });
  };

  const onDoiKhoaChiDinh = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    refModalDoiKhoaChiDinh.current &&
      refModalDoiKhoaChiDinh.current.show({
        item: record,
      });
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("pttt.hoTenNguoiBenh"),
      sort_key: "tenNb",
      dataSort: dataSortColumn["tenNb"] || "",
      onClickSort: onClickSort,
      width: "250px",
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "pttt.hoTenNguoiBenh",
      align: "left",
    }),
    Column({
      title: t("common.gioiTinh"),
      sort_key: "gioiTinh",
      dataSort: dataSortColumn["gioiTinh"] || "",
      onClickSort: onClickSort,
      width: "80px",
      dataIndex: "gioiTinh",
      key: "gioiTinh",
      i18Name: "common.gioiTinh",
      render: (field) => {
        return listGioiTinh?.find((item) => item.id === field)?.ten || "";
      },
    }),
    Column({
      title: t("pttt.buongPt"),
      sort_key: "tenBuongPtTt",
      dataSort: dataSortColumn["tenBuongPtTt"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "tenBuongPtTt",
      key: "tenBuongPtTt",
      align: "left",
      i18Name: "pttt.buongPt",
    }),
    Column({
      title: t("common.ngaySinh"),
      sort_key: "ngaySinh",
      dataSort: dataSortColumn["ngaySinh"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      align: "left",
      i18Name: "common.ngaySinh",
      render: (field, item) => {
        return (
          <div>
            {field
              ? moment(field).format(item.chiNamSinh ? "YYYY" : "DD/MM/YYYY")
              : ""}
          </div>
        );
      },
    }),
    Column({
      title: t("common.phanLoaiNb"),
      sort_key: "dsPhanLoaiNbId",
      dataSort: dataSortColumn["dsPhanLoaiNbId"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "dsPhanLoaiNbId",
      key: "dsPhanLoaiNbId",
      align: "center",
      i18Name: "common.phanLoaiNb",
      render: (item) => {
        return <ListPhanLoaiNguoiBenh value={item} />;
      },
    }),
    Column({
      title: t("common.maHs"),
      sort_key: "maHoSo",
      dataSort: dataSortColumn["maHoSo"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      align: "left",
      i18Name: "common.maHs",
    }),
    Column({
      title: t("common.maBa"),
      sort_key: "maBenhAn",
      dataSort: dataSortColumn["maBenhAn"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      align: "left",
      i18Name: "common.maBa",
    }),
    Column({
      title: t("common.maNb"),
      sort_key: "maNb",
      dataSort: dataSortColumn["maNb"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      // align: "center",
      render: (field, item, index) => {
        return <div>{item?.maNb}</div>;
      },
    }),
    Column({
      title: t("common.maDv"),
      sort_key: "maDichVu",
      dataSort: dataSortColumn["maDichVu"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "maDichVu",
      key: "maDichVu",
      i18Name: "common.maDv",
      // align: "center",
    }),
    Column({
      title: t("xetNghiem.tenDV"),
      sort_key: "tenDichVu",
      dataSort: dataSortColumn["tenDichVu"] || "",
      onClickSort: onClickSort,
      width: "300px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "xetNghiem.tenDV",
      // align: "center",
      render: (item) => (
        <Tooltip title={item}>
          <div
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {item}
          </div>
        </Tooltip>
      ),
    }),
    Column({
      title: t("pttt.tenGoiMo"),
      sort_key: "tenGoiPtTt",
      dataSort: dataSortColumn["tenGoiPtTt"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "tenGoiPtTt",
      key: "tenGoiPtTt",
      i18Name: "pttt.tenGoiMo",
      hidden: !(
        dataHIEN_THI_GOI_MO_TRONG_PTTT?.toLowerCase() === "true" &&
        khoaLamViec?.id === 54
      ),
      ellipsis: true,
    }),
    Column({
      title: t("common.thoiGianThucHien"),
      sort_key: "thoiGianThucHien",
      dataSort: dataSortColumn["thoiGianThucHien"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      align: "left",
      i18Name: "common.thoiGianThucHien",
      render: (field) => {
        return (
          <div>{field ? moment(field).format("DD/MM/YYYY HH:mm:ss") : ""}</div>
        );
      },
    }),
    Column({
      // thiếu
      title: t("common.trangThai"),
      sort_key: "trangThai",
      dataSort: dataSortColumn["trangThai"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "trangThai",
      key: "trangThai",
      align: "left",
      i18Name: "common.trangThai",
      render: (value) => {
        return listTrangThaiDichVu?.find((item) => item.id == value)?.ten || "";
      },
    }),
    Column({
      title: t("pttt.phanLoaiPTTT"),
      sort_key: "phanLoai",
      dataSort: dataSortColumn["phanLoai"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "phanLoai",
      key: "phanLoai",
      i18Name: "pttt.phanLoaiPTTT",
      // align: "center",
      render: (value) => {
        return listPhanLoaiPTTT?.find((item) => item.id == value)?.ten || "";
      },
    }),
    Column({
      title: t("pttt.tyLeThanhToan"),
      sort_key: "tyLeTtDv",
      dataSort: dataSortColumn["tyLeTtDv"] || "",
      onClickSort: onClickSort,
      width: "135px",
      dataIndex: "tyLeTtDv",
      key: "tyLeTtDv",
      i18Name: "pttt.tyLeThanhToan",
    }),
    Column({
      title: t("pttt.loaiPttt"),
      sort_key: "loaiPtTt",
      dataSort: dataSortColumn["loaiPtTt"] || "",
      onClickSort: onClickSort,
      width: "125px",
      dataIndex: "loaiPtTt",
      key: "loaiPtTt",
      i18Name: "pttt.loaiPttt",
      render: (field) => {
        return listLoaiPtTt.find((x) => x.id === field)?.ten;
      },
    }),
    Column({
      title: t("pttt.khoaChiDinh"),
      sort_key: "khoaChiDinhId",
      dataSort: dataSortColumn["khoaChiDinhId"] || "",
      onClickSort: onClickSort,
      width: "200px",
      dataIndex: "khoaChiDinhId",
      key: "khoaChiDinhId",
      i18Name: "pttt.khoaChiDinh",
      render: (field) => {
        return getKhoaTheoTaiKhoanById(field)?.ten;
      },
    }),
    Column({
      title: t("pttt.dsDvNgoaiDieuTri.thoiGianChiDinh"),
      sort_key: "thoiGianChiDinh",
      dataSort: dataSortColumn["thoiGianChiDinh"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: "pttt.dsDvNgoaiDieuTri.thoiGianChiDinh",
      render: (field) => {
        return (
          <div>{field ? moment(field).format("DD/MM/YYYY HH:mm:ss") : ""}</div>
        );
      },
    }),
    Column({
      title: t("pttt.khongPt"),
      sort_key: "khongThucHien",
      dataSort: dataSortColumn["khongThucHien"] || "",
      onClickSort: onClickSort,
      width: "100px",
      dataIndex: "khongThucHien",
      key: "khongThucHien",
      i18Name: "pttt.khongPt",
      align: "center",
      render: (field) => {
        return <Checkbox checked={field}></Checkbox>;
      },
    }),
    Column({
      title: t("pttt.thongTinNguoiThucHien"),
      sort_key: "tenNguoiThucHien",
      dataSort: dataSortColumn["tenNguoiThucHien"] || "",
      onClickSort: onClickSort,
      width: "180px",
      dataIndex: "tenNguoiThucHien",
      key: "tenNguoiThucHien",
      i18Name: "pttt.thongTinNguoiThucHien",
    }),
    Column({
      title: t("common.soDienThoai"),
      sort_key: "soDienThoai",
      dataSort: dataSortColumn["soDienThoai"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "soDienThoai",
      key: "soDienThoai",
      i18Name: "common.soDienThoai",
      render: (field) => {
        if (!field || checkRole([ROLES["TIEP_DON"].HIEN_THI_DAY_DU_SDT])) {
          return field;
        }
        return field.replace(/(\d{4})\d{3}(\d{3})/, "$1xxx$2");
      },
    }),
    Column({
      title: t("pttt.cachThucPttt"),
      sort_key: "cachThuc",
      dataSort: dataSortColumn["cachThuc"] || "",
      onClickSort: onClickSort,
      width: "150px",
      dataIndex: "cachThuc",
      key: "cachThuc",
      i18Name: "pttt.cachThucPttt",
      show: false,
      render: (field) => <EllipsisText.Tooltip content={field} limitLine={1} />,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: "150px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (record) => {
        return (
          <>
            <IconDVDaChiDinh data={record} />

            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye className="ic-action" onClick={onViewDetail(record)} />
            </Tooltip>

            {dataHIEN_THI_TINH_NANG_CHUYEN_MO?.eval() && (
              <Tooltip title={t("pttt.phanPhongMo")}>
                <SVG.IcReload
                  className="ic-action"
                  onClick={onPhanPhongMo(record)}
                />
              </Tooltip>
            )}
            {dataHIEN_THI_KET_NOI_MEMO?.eval() && (
              <Tooltip title="Tool Memo" placement="bottomLeft">
                <SVG.IcMemo
                  onClick={openMemoPopup(record?.maNb)}
                  className="ic-action"
                />
              </Tooltip>
            )}

            {checkRole([
              ROLES["PHAU_THUAT_THU_THUAT"].DOI_KHOA_CHI_DINH_CUA_DV,
            ]) && (
              <Tooltip title={t("pttt.doiKhoaChiDinhDichVu")}>
                <SVG.IcChuyenDichVu
                  onClick={onDoiKhoaChiDinh(record)}
                  className="ic-action"
                />
              </Tooltip>
            )}
          </>
        );
      },
    }),
  ];

  const onShowSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size });
  };
  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listDsPttt}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        scroll={{ x: 1700 }}
        tableName="table_PTTT_DSNGUOIBENH"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listDsPttt}
          total={totalElements}
          onShowSizeChange={onShowSizeChange}
        />
      )}

      <ModalPhanPhongMo ref={refModalPhanPhongMo} />
      <ModalDoiKhoaChiDinh ref={refModalDoiKhoaChiDinh} />
    </Main>
  );
};

export default DanhSach;
