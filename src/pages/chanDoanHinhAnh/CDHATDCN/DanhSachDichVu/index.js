import React, { useMemo } from "react";
import {
  HeaderSearch,
  Pagination,
  TableWrapper,
  Checkbox,
  Tooltip,
} from "components";
import { useRef, useState, useContext } from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { Main } from "./styled";
import ModalDoiDichVu from "pages/chanDoanHinhAnh/CDHATDCN/ModalDoiDichVu";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { cloneDeep, groupBy } from "lodash";
import {
  ENUM,
  LOAI_IN,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_DICH_VU_CDHA,
} from "constants/index";
import { useHistory } from "react-router-dom";
import { useEnum, useLoading, useThietLap, useStore } from "hooks";
import printProvider from "data-access/print-provider";
import CDHAContext from "../CDHAContext";
import { SVG } from "assets";
import {
  isArray,
  locPhieuLisPacs,
  openInNewTab,
  transformQueryString,
} from "utils";
import IconDVDaChiDinh from "pages/phauThuatThuThuat/DanhSachNguoiBenh/DanhSach/IconDVDaChiDinh";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

const { Setting } = TableWrapper;

const DanhSachDichVu = ({ isChoTiepNhanPage, onChonDichVu, ...props }) => {
  const { showLoading, hideLoading } = useLoading();
  const refModalDoiDichVu = useRef(null);
  const history = useHistory();
  const cdhaContext = useContext(CDHAContext);
  const {
    listData = [],
    page,
    size,
    totalElements,
    dataSortColumn,
    dataSearch,
  } = useStore(
    "tiepNhanCDHA",
    {},
    {
      fields: "listData,page,size,totalElements,dataSortColumn,dataSearch",
    }
  );

  const listPhongChanDoan = useStore("chanDoanHinhAnh.listPhongChanDoan", []);

  const {
    tiepNhanCDHA: { onSearch, onSizeChange, onSortChange, getById },
    choTiepDonDV: { getPhieuKetQua },
    pacs: { getUrl },
  } = useDispatch();
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);

  useEffect(() => {
    if (!isFinish || !listPhongChanDoan.length) return;

    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = transformQueryString({
      tuThoiGianThucHien: {
        type: "dateOptions",
        defaultValue: moment()
          .subtract(14, "days")
          .format("YYYY-MM-DD 00:00:00"),
        format: (value) => value.format("YYYY-MM-DD 00:00:00"),
      },
      denThoiGianThucHien: {
        type: "dateOptions",
        defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
        format: (value) => value.format("YYYY-MM-DD 23:59:59"),
      },
      dsTrangThai: {
        defaultValue: isChoTiepNhanPage
          ? TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_DON
          : TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_NHAN,
      },
      dsTrangThaiHoan: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [0],
      },
      dsTrangThaiNbKskBoQua: {
        defaultValue: [10],
      },
      nguoiThucHienIdFilter: {
        format: (value) => parseInt(value),
      },
    });
    const sort = JSON.parse(dataSortColumn);
    queries.nguoiThucHienId = queries.nguoiThucHienIdFilter;
    delete queries.nguoiThucHienIdFilter;
    if (queries.dsTrangThaiHoan?.length === listTrangThaiHoan.length) {
      delete queries.dsTrangThaiHoan;
    }
    if (!queries.dsPhongThucHienId) {
      queries.dsPhongThucHienId = listPhongChanDoan.map((item) => item.id);
    }
    onSizeChange({
      dataSearch: queries,
      dataSortColumn: sort,
      size: parseInt(dataPageSize),
      page: parseInt(page || 0),
    });
  }, [isFinish, listPhongChanDoan]);

  const [state, _setState] = useState({
    selectedRowKeys: [],
  });

  const refSettings = useRef(null);
  const { t } = useTranslation();

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const onSelectRecord = (record) => () => {
    let selectedRowKeys = [...state.selectedRowKeys];
    if (selectedRowKeys.findIndex((item) => item.id === record.id) >= 0)
      selectedRowKeys = selectedRowKeys.filter((item) => item.id != record.id);
    else selectedRowKeys.push(record);
    setState({ selectedRowKeys });
    onChonDichVu(selectedRowKeys);
  };

  const onRow = (record) => {
    return {
      onClick: onSelectRecord(record),
      onDoubleClick: onViewDetail(record),
    };
  };

  const onShowSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size: size });
  };

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value });
  };

  useEffect(() => {
    setState({ selectedRowKeys: [] });
  }, [listData]);

  const onViewDetail = (data) => () => {
    history.push({
      pathname: `/chan-doan-hinh-anh/chi-tiet-dich-vu/${data.nbDotDieuTriId}/${data.id}`,
      state: {
        queryString: getAllQueryString(undefined, {
          filterEmpty: false,
        }),
        dataSearch,
      },
    });
  };
  const onDoiDichVu = (data) => async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (
      [
        ...TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_DON,
        ...TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_NHAN,
        ...[50],
      ].includes(data?.trangThai) &&
      data?.trangThaiHoan === 0 &&
      data?.thanhToan === 50
    ) {
      const { capCuu, tuTra, khongTinhTien, ghiChu } =
        (await getById(data.id)) || {};

      refModalDoiDichVu?.current &&
        refModalDoiDichVu.current.show(
          {
            data: { ...data, capCuu, tuTra, khongTinhTien, ghiChu },
            khoaChiDinhId: data.khoaId,
          },
          () => {
            loadData();
          }
        );
    }
  };
  const onChiDinhDichVu = (data) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (
      [
        TRANG_THAI_DICH_VU.DA_TIEP_NHAN,
        TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
      ].includes(data?.trangThai)
    )
      history.push(
        `/chan-doan-hinh-anh/chi-tiet-dich-vu/${data.nbDotDieuTriId}/${data.id}?tab=1`
      );
  };

  const onViewPdf = (data) => async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if ([TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(data?.trangThai)) {
      try {
        showLoading();
        const s = await getPhieuKetQua({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          dsSoKetNoi: [data?.soKetNoi],
        });
        onPrintPdf(s);
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const onPrintPdf = async (s) => {
    const dsPhieu = locPhieuLisPacs(s, {
      allData: false,
      isLocPhieu: true,
      isCdha: true,
    });
    const dsPhieuFull = locPhieuLisPacs(s, {
      allData: true,
      isLocPhieu: true,
      isCdha: true,
    });
    if (
      isArray(dsPhieuFull, true) &&
      dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
    ) {
      // [].every luôn luôn true
      const finalFile = await printProvider.getMergePdf(dsPhieu);
      openInNewTab(finalFile);
    } else {
      printProvider.printPdf(dsPhieuFull);
    }
  };

  const onViewPacs = (data) => async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (data?.guiPacs) {
      try {
        showLoading();
        const s = await getUrl({ id: data?.id });
        if (s) window.open(s, "_blank").focus();
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const getIconStatus = (type, data) => {
    switch (type) {
      case "doiDichVu":
        if (
          [
            ...TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_DON,
            ...TRANG_THAI_DICH_VU_CDHA.CHO_TIEP_NHAN,
            ...[50],
          ].includes(data?.trangThai) &&
          data?.trangThaiHoan === 0 &&
          data?.thanhToan === 50
        )
          return "";
        return "disabled";
      case "chiDinhDichVu":
        if (
          [
            TRANG_THAI_DICH_VU.DA_TIEP_NHAN,
            TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
          ].includes(data?.trangThai)
        )
          return "";
        return "disabled";
      case "viewPdf":
        if ([TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(data?.trangThai))
          return "";
        return "disabled";
      case "viewPacs":
        if (data.guiPacs) return "";
        return "disabled";
    }
  };

  //column
  const columns = [
    {
      title: <HeaderSearch title="STT" />,
      dataIndex: "index",
      width: "80px",
      align: "center",
      ignore: true,
      render: (_, item) => {
        return item.index;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maHoSo || 0}
        />
      ),
      i18Name: "common.maHoSo",
      show: true,
      align: "center",
      dataIndex: "maHoSo",
      width: "120px",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maNguoiBenh")}
          sort_key="maNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maNb || 0}
        />
      ),
      align: "center",
      dataIndex: "maNb",
      width: "120px",
      i18Name: "common.maNguoiBenh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenNb")}
          sort_key="tenNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNb || 0}
        />
      ),
      width: "300px",
      align: "left",
      dataIndex: "tenNb",
      i18Name: "common.tenNb",
      show: true,
      render: (item, data) => {
        return (
          <div>
            <div style={{ display: "flex", width: "100%" }}>
              <label style={{ flex: 1 }}>{item}</label>
              <IconDVDaChiDinh data={data} />
            </div>
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.ngaySinh")} />,
      width: "120px",
      dataIndex: "ngaySinh",
      i18Name: "common.ngaySinh",
      show: true,
      render: (value) => value && moment(value).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.tuoi")}
          sort_key="ngaySinh"
          dataSort={dataSortColumn["ngaySinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tuoi2",
      i18Name: "common.tuoi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phanLoaiNb")}
          sort_key="dsPhanLoaiNbId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dsPhanLoaiNbId || 0}
        />
      ),
      width: "150px",
      dataIndex: "dsPhanLoaiNbId",
      i18Name: "common.phanLoaiNb",
      show: true,
      render: (item) => {
        return <ListPhanLoaiNguoiBenh value={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maBenhAn")}
          sort_key="maBenhAn"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maBenhAn || 0}
        />
      ),
      dataIndex: "maBenhAn",
      width: "120px",
      i18Name: "common.maBenhAn",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maDv")}
          sort_key="maDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maDichVu || 0}
        />
      ),
      // align: "center",
      dataIndex: "maDichVu",
      width: "120px",
      i18Name: "common.maDv",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          sort_key="tenDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenDichVu || 0}
        />
      ),
      // align: "center",
      dataIndex: "tenDichVu",
      width: "200px",
      i18Name: "common.tenDichVu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soLuong")}
          sort_key="soLuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuong || 0}
        />
      ),
      // align: "center",
      dataIndex: "soLuong",
      width: "090px",
      i18Name: "common.soLuong",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soPhieu")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soPhieu || 0}
        />
      ),
      align: "center",
      width: "120px",
      dataIndex: "soPhieu",
      i18Name: "common.soPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phongChiDinh")}
          sort_key="tenPhongChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenPhongChiDinh || 0}
        />
      ),
      align: "center",
      width: "150px",
      dataIndex: "tenPhongChiDinh",
      i18Name: "common.phongChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.soKetNoi")}
          sort_key="soKetNoi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soKetNoi || 0}
        />
      ),
      align: "center",
      width: "120px",
      dataIndex: "soKetNoi",
      i18Name: "cdha.soKetNoi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThai")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThai || 0}
        />
      ),
      width: "120px",
      align: "center",
      dataIndex: "trangThai",
      i18Name: "common.trangThai",
      show: true,
      render: (value, list) => {
        if (list?.trangThaiHoan > 0) {
          return (
            listTrangThaiHoan.find((item) => item.id == list?.trangThaiHoan)
              ?.ten || ""
          );
        }
        return listTrangThaiDichVu.find((item) => item.id == value)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.daGuiPacs")}
          sort_key="guiPacs"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.guiPacs || 0}
        />
      ),

      width: "120px",
      align: "center",
      dataIndex: "guiPacs",
      i18Name: "cdha.daGuiPacs",
      show: true,
      render: (value) => {
        return <Checkbox checked={value ? true : false} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.phongThucHien")}
          sort_key="tenPhongThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenPhongThucHien || 0}
        />
      ),
      width: "200px",
      align: "center",
      dataIndex: "tenPhongThucHien",
      i18Name: "cdha.phongThucHien",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.nguoiThucHien")}
          sort_key="tenNguoiThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNguoiThucHien || 0}
        />
      ),
      width: "200px",
      align: "center",
      dataIndex: "tenNguoiThucHien",
      i18Name: "cdha.nguoiThucHien",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.sttTheoPhong")}
          sort_key="stt2"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.stt2 || 0}
        />
      ),
      width: "120px",
      align: "center",
      dataIndex: "stt2",
      i18Name: "cdha.sttTheoPhong",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("hsba.bsChiDinh")}
          sort_key="tenBacSiChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenBacSiChiDinh || 0}
        />
      ),
      width: "180px",
      align: "center",
      dataIndex: "tenBacSiChiDinh",
      i18Name: "hsba.bsChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.ngayChiDinh")}
          sort_key="thoiGianChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianChiDinh || 0}
        />
      ),
      align: "center",
      dataIndex: "thoiGianChiDinh",
      width: "200px",
      i18Name: "cdha.ngayChiDinh",
      show: true,
      render: (item) => {
        return item && new Date(item).format("dd/MM/YYYY - HH:mm:ss");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.daTT")}
          sort_key="thanhToan"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thanhToan || 0}
        />
      ),
      align: "center",
      width: "120px",
      i18Name: "common.daTT",
      show: true,
      dataIndex: "thanhToan",
      render: (item) => {
        return <Checkbox checked={item === 50 ? true : false} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("cdha.khoaDangDieuTri")}
          sort_key="tenKhoa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenKhoa || 0}
        />
      ),
      width: 200,
      dataIndex: "tenKhoa",
      i18Name: "cdha.khoaDangDieuTri",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.moTaLuuy")}
          sort_key="ghiChu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ghiChu || 0}
        />
      ),
      width: 300,
      dataIndex: "ghiChu",
      i18Name: "common.moTaLuuy",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.thoiGianThucHien")}
          sort_key="thoiGianThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianThucHien || 0}
        />
      ),
      dataIndex: "thoiGianThucHien",
      width: "160px",
      i18Name: "common.thoiGianThucHien",
      show: true,
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "180px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (_, item) => {
        return (
          <div className="tool-btn">
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye
                className={`ic-action`}
                onClick={onViewDetail(item)}
              ></SVG.IcEye>
            </Tooltip>
            {!isChoTiepNhanPage && (
              <>
                <Tooltip title={t("pttt.chuyenDichVu")}>
                  <SVG.IcChuyenDichVu
                    className={`ic-action ${getIconStatus("doiDichVu", item)}`}
                    onClick={onDoiDichVu(item)}
                  ></SVG.IcChuyenDichVu>
                </Tooltip>
                <Tooltip title={t("common.chiDinhDichVu")}>
                  <SVG.IcAdd
                    className={`ic-action ${getIconStatus(
                      "chiDinhDichVu",
                      item
                    )}`}
                    onClick={onChiDinhDichVu(item)}
                  ></SVG.IcAdd>
                </Tooltip>
                <Tooltip title={t("cdha.xemKetQuaPdf")}>
                  <SVG.IcPdf
                    className={`ic-action ${getIconStatus("viewPdf", item)}`}
                    onClick={onViewPdf(item)}
                  ></SVG.IcPdf>
                </Tooltip>
                <Tooltip title={t("cdha.xemKetQuaPacs")}>
                  <SVG.IcViewImagePacs
                    className={`ic-action ${getIconStatus("viewPacs", item)}`}
                    onClick={onViewPacs(item)}
                  ></SVG.IcViewImagePacs>
                </Tooltip>
              </>
            )}
          </div>
        );
      },
    },
  ];
  const isCheckedAll = useMemo(() => {
    return (
      listData?.length &&
      listData.every(
        (item) => state.selectedRowKeys.findIndex((i) => i.id === item.id) >= 0
      )
    );
  }, [listData, state.selectedRowKeys]);

  const onCheckAll = (e) => {
    let selectedRowKeys = e.target?.checked ? listData : [];
    setState({
      selectedRowKeys,
    });
    onChonDichVu(selectedRowKeys);
  };
  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1 });
  };

  const onSelectChange = (value, selectedRowKeys) => {
    setState({
      selectedRowKeys,
    });
    onChonDichVu(selectedRowKeys);
  };

  useEffect(() => {
    cdhaContext.onSelectItem(state.selectedRowKeys);
  }, [state.selectedRowKeys, listData]);

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox onChange={onCheckAll} checked={isCheckedAll}></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys.map((item) => item.id),
  };

  return (
    <Main noPadding={true} bottom={0}>
      <TableWrapper
        scroll={{ y: 400, x: 700 }}
        bordered
        ref={refSettings}
        columns={columns}
        tableName="table_CDHATDCN"
        dataSource={listData || []}
        rowSelection={rowSelection}
        rowKey={(record) => record.id}
        onRow={onRow}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={onShowSizeChange}
        />
      )}
      <ModalDoiDichVu ref={refModalDoiDichVu} />
    </Main>
  );
};
export default React.memo(DanhSachDichVu);
