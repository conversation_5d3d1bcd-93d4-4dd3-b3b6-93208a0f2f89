import React, { useMemo } from "react";
import SlideText from "pages/qms/components/SlideText";
import { TRANG_THAI_HIEN_THI } from "pages/qms/qmsDoc/config";
import { useThietLap } from "hooks";
import { LIST_LOAI_QMS, THIET_LAP_CHUNG } from "constants/index";
import { addPrefixNumberZero } from "utils";
import { SVG } from "assets";
import { LABEL_NB_TIEP_THEO } from "pages/qms/constants";
import moment from "moment";
import { useTranslation } from "react-i18next";

function BottomContent(props) {
  const { t } = useTranslation();
  const { dsTiepTheo, dsGoiNho, currentKiosk, hideDsGoiNho = false } = props;
  const [dataTHIET_LAP_HIEN_THI_QMS_CHO_NB_UU_TIEN] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_QMS_CHO_NB_UU_TIEN
  );
  const count = useMemo(() => {
    if (!currentKiosk?.dsVideo[0]) {
      if (currentKiosk?.dsQuay.length === 1) {
        return 5;
      } else if (currentKiosk?.dsQuay.length === 2) {
        return 10;
      } else {
        return 1;
      }
    } else {
      return 2;
    }
  }, [currentKiosk]);

  const render = (data, key) => {
    return (data || []).slice(0, count).map((nb, index) => {
      const { id, stt, tenNb, tuoi2, uuTien, ngaySinh } = nb || {};
      return (
        <div className="box-item" key={id}>
          <div className="box-item__left">
            <div className="box-item__number">
              <span>{addPrefixNumberZero(stt, 4)}</span>
            </div>
            <SlideText className="box-item__name" type={1}>
              <span>
                {tenNb}{" "}
                {uuTien && dataTHIET_LAP_HIEN_THI_QMS_CHO_NB_UU_TIEN
                  ? ` (${dataTHIET_LAP_HIEN_THI_QMS_CHO_NB_UU_TIEN})`
                  : ""}
              </span>
            </SlideText>
          </div>
          <div className="box-item__old">
            <span>
              {currentKiosk.loaiQms == LIST_LOAI_QMS.QMS_THU_NGAN
                ? ngaySinh
                  ? moment(ngaySinh).format("DD/MM/YYYY")
                  : ""
                : tuoi2 && `${tuoi2}`}
            </span>
          </div>
        </div>
      );
    });
  };
  return (
    <div className="bottom-content">
      {currentKiosk?.dsTrangThai?.includes(TRANG_THAI_HIEN_THI.TIEP_THEO) && (
        <div className="bottom-box">
          <div className="bottom-box__header">
            <div className="bottom-box__title">
              <SVG.IcExtend />
              {LABEL_NB_TIEP_THEO[currentKiosk.loaiQms].title}
            </div>
            <div className="bottom-box__length">
              ({dsTiepTheo?.length || 0} {t("kiosk.nb")})
            </div>
          </div>
          <div className="bottom-box__body">{render(dsTiepTheo)}</div>
          {currentKiosk.loaiQms === LIST_LOAI_QMS.QMS_KHAM_BENH &&
            currentKiosk?.dsQuay?.length <= 2 && (
              <div className="bottom-box__footer">
                <span>{`${t("qms.dSNBCoKQCLS")}`}: </span>
              </div>
            )}
        </div>
      )}
      {!hideDsGoiNho &&
        currentKiosk?.dsTrangThai?.includes(TRANG_THAI_HIEN_THI.GOI_NHO) && (
          <div className="bottom-box-ignore bottom-box-ignore--bg">
            <div className="bottom-box-ignore__header">
              <div className="bottom-box-ignore__title">
                <SVG.IcExtend /> NGƯỜI BỆNH GỌI NHỠ
              </div>
              <div className="bottom-box-ignore__length">
                ({dsGoiNho?.length || 0} {t("kiosk.nb")})
              </div>
            </div>
            <div className="bottom-box-ignore__body">
              {render(dsGoiNho, "GoiNho")}
            </div>
          </div>
        )}
    </div>
  );
}
export default BottomContent;
