import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Empty } from "antd";
import { groupBy } from "lodash";

import { useEnum, useThietLap, useListAll } from "hooks";

import {
  TableWrapper,
  Checkbox,
  Pagination,
  InputTimeout,
  Select,
  DatePicker,
} from "components";
import { Main } from "./styled";
import {
  ENUM,
  HIEU_LUC,
  LOAI_DICH_VU,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  LIST_TRANG_THAI_THANH_TOAN,
  THIET_LAP_CHUNG,
} from "constants/index";

const { Column } = TableWrapper;

const DsDichVu = forwardRef(
  (
    {
      tableName = "TABLE_HSBA_DSDV_NGOAI_DIEU_TRI",
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    },
    ref
  ) => {
    const refSettings = useRef(null);

    const { t } = useTranslation();
    const { listData, dataSortColumn, totalElements, page, size } = useSelector(
      (state) => state.dsDvNgoaiDieuTri
    );

    const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
    const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
    const [listAllKhoa] = useListAll("khoa", {
      page: "",
      size: "",
      active: true,
    });
    const [listAllPhong] = useListAll("phong", {
      page: "",
      size: "",
      active: true,
    });
    const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE);

    const {
      dsDvNgoaiDieuTri: {
        onSortChange,
        onSizeChange,
        onSearch,
        onChangeInputSearch,
      },
    } = useDispatch();

    useEffect(() => {
      if (isFinish && nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20, 40],
            loaiDichVu: LOAI_DICH_VU.NGOAI_DIEU_TRI,
          },
          page: 0,
          size: dataPageSize || 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
      }
    }, [
      isFinish,
      dataPageSize,
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);

    useImperativeHandle(ref, () => ({
      onSettings,
    }));

    const listTrangThaiDvMemo = useMemo(() => {
      const groupTrangThai = groupBy(listTrangThaiDichVu, "ten");
      return Object.keys(groupTrangThai).map((key, index) => ({
        ten: key,
        id: index,
        value: groupTrangThai[key].map((x) => x.id),
      }));
    }, [listTrangThaiDichVu]);

    const dataSource = useMemo(() => {
      const _group = groupBy(listData, "tenNhomDichVuCap1");
      let data = [];

      Object.keys(_group).forEach((key) => {
        data.push({
          id: key,
          key: key,
          isGroup: true,
          tenDichVu: key,
        });

        (_group[key] || []).forEach((element, index) => {
          data.push({ ...element, index: index + 1 });
        });
      });

      return data;
    }, [listData]);

    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };

    const onChangeSearch = (key, value) => {
      onChangeInputSearch({ [key]: value });
    };
    const onSearchDate = (key, value) => {
      const newSearch = {
        [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
        [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
      };

      onChangeInputSearch(newSearch);
    };

    const onClickSort = (key, value) => {
      onSortChange({ [key]: value });
    };

    const sharedOnCell = (row, index) => {
      if (row.isGroup) {
        return { colSpan: 0 };
      }
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: "80px",
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("common.maDv"),
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        width: "120px",
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "common.maDv",
        className: "maDichVu",
        onCell: (row, index) => ({
          colSpan: row.isGroup ? 16 : 1,
        }),
        render: (item, list) => (
          <span className={list?.isGroup ? "nhom-dv-cap-1" : ""}>
            {list.isGroup ? list.tenDichVu : item}
          </span>
        ),
        renderSearch: (
          <InputTimeout
            placeholder={t("common.timKiem")}
            onChange={(e) => {
              onChangeSearch("maDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("common.tenDichVu"),
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "common.tenDichVu",
        className: "tenDichVu",
        onCell: sharedOnCell,
        renderSearch: (
          <InputTimeout
            placeholder={t("common.nhapTenDichVu")}
            onChange={(e) => {
              onChangeSearch("tenDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("common.soLuong"),
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "soLuong",
        key: "soLuong",
        i18Name: "common.soLuong",
        className: "soLuong",
        onCell: sharedOnCell,
        renderSearch: (
          <InputTimeout
            placeholder={t("common.timKiem")}
            onChange={(e) => {
              onChangeSearch("soLuong", e);
            }}
          />
        ),
      }),
      Column({
        title: t("common.trangThai"),
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "trangThai",
        key: "trangThai",
        align: "center",
        i18Name: "common.trangThai",
        onCell: sharedOnCell,
        render: (value) =>
          (listTrangThaiDichVu || []).find((e) => e.id === value)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiDvMemo}
            mode="multiple"
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              const selectedTrangThai = listTrangThaiDvMemo.filter((item) =>
                (e || []).includes(item.id)
              );
              const dsTrangThai = (selectedTrangThai || []).reduce(
                (a, b) => [...a, ...b.value],
                []
              );

              onChangeSearch("dsTrangThai", dsTrangThai);
            }}
          />
        ),
      }),
      Column({
        title: t("common.thanhTien"),
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        onCell: sharedOnCell,
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: t("hsba.ttThanhToan"),
        sort_key: "thanhToan",
        dataSort: dataSortColumn["thanhToan"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "thanhToan",
        key: "thanhToan",
        i18Name: "hsba.ttThanhToan",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => (
          <Checkbox
            checked={value === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
          />
        ),
        selectSearch: true,
        renderSearch: (
          <Select
            data={LIST_TRANG_THAI_THANH_TOAN}
            defaultValue={""}
            onChange={(e) => {
              onChangeSearch("thanhToan", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.ttHoan"),
        sort_key: "trangThaiHoan",
        dataSort: dataSortColumn["trangThaiHoan"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "trangThaiHoan",
        key: "trangThaiHoan",
        align: "center",
        i18Name: "hsba.ttHoan",
        onCell: sharedOnCell,
        render: (value) =>
          (listTrangThaiHoan || []).find((element) => element.id === value)
            ?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiHoan}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("dsTrangThaiHoan", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.khoaChiDinh"),
        sort_key: "tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        align: "center",
        i18Name: "hsba.khoaChiDinh",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            mode="multiple"
            placeholder={t("baoCao.chonKhoaChiDinh")}
            onChange={(e) => {
              onChangeSearch("dsKhoaChiDinhId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        align: "center",
        i18Name: "hsba.thoiGianChiDinh",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianChiDinh", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        align: "center",
        i18Name: "hsba.thoiGianThucHien",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianThucHien", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianKetLuan"),
        sort_key: "thoiGianKetLuan",
        dataSort: dataSortColumn["thoiGianKetLuan"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianKetLuan",
        key: "thoiGianKetLuan",
        align: "center",
        i18Name: "hsba.thoiGianKetLuan",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianKetLuan", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianCoKetQua"),
        sort_key: "thoiGianCoKetQua",
        dataSort: dataSortColumn["thoiGianCoKetQua"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianCoKetQua",
        key: "thoiGianCoKetQua",
        align: "center",
        i18Name: "hsba.thoiGianCoKetQua",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianCoKetQua", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.phongThucHien"),
        sort_key: "tenPhongThucHien",
        dataSort: dataSortColumn["tenPhongThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenPhongThucHien",
        key: "tenPhongThucHien",
        align: "center",
        i18Name: "hsba.phongThucHien",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllPhong}
            mode="multiple"
            placeholder={t("baoCao.chonPhongThucHien")}
            onChange={(e) => {
              onChangeSearch("dsPhongThucHienId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.tuTra"),
        sort_key: "tuTra",
        dataSort: dataSortColumn["tuTra"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "tuTra",
        key: "tuTra",
        i18Name: "hsba.tuTra",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => <Checkbox checked={value} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("tuTra", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.khongTinhTien"),
        sort_key: "khongTinhTien",
        dataSort: dataSortColumn["khongTinhTien"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        i18Name: "hsba.khongTinhTien",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => <Checkbox checked={value} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("khongTinhTien", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
    ];

    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };

    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    const setRowClassName = (record) => {
      if (record.khongTinhTien) {
        return "green-color";
      }
      if (record.tuTra) {
        return "orange-color";
      }
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={dataSource}
          rowKey={(record) => record.id}
          tableName={tableName}
          ref={refSettings}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
          rowClassName={setRowClassName}
        />
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listData}
            onShowSizeChange={onHandleSizeChange}
          />
        ) : null}
      </Main>
    );
  }
);

export default DsDichVu;
