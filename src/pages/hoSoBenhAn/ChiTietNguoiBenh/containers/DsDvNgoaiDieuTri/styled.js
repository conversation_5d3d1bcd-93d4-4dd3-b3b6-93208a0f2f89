import styled from "styled-components";
export const Main = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  & .main-table-wrapper {
    height: 100%;
  }
  & svg.icon {
    width: 22px;
    height: 22px;
    flex-shrink: 0;
  }
  & svg.icon-pdf {
    width: 20px;
    height: 20px;
  }

  .row-action {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .nhom-dv-cap-1 {
    font-weight: 700;
    font-size: 18px;
    color: #172b4d;
  }
  .main__container {
    .green-color > .tenDichVu {
      background-color: #81c8a9 !important;
    }
    .orange-color > .tenDichVu {
      background-color: #ffd6c2 !important;
    }
    .blur-ket-qua {
      filter: blur(5px);
      pointer-events: auto;
      user-select: none;
    }
  }
`;
