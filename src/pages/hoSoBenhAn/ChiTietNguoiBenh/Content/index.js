import React, { useState, useRef, useCallback } from "react";
import { LazyLoad, Tabs } from "components";
import ThuocHoSoBenhAn from "../containers/ThuocHoSoBenhAn";
import VatTuHoSoBenhAn from "../containers/VatTuHoSoBenhAn";
import HoSoKhamBenh from "../containers/HoSoKhamBenh";
import DanhSachSuatAn from "../containers/DanhSachSuatAn";
import DichVuGiuong from "../containers/DichVuGiuong";
import DsPhieuThuHoaDon from "../containers/DsPhieuThuHoaDon";
import HoaChat from "../containers/HoaChat";
import Mau from "../containers/Mau";
import DsDichVuNgoaiDieuTri from "../containers/DsDvNgoaiDieuTri";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import VacxinHsba from "../containers/VacxinHsba";
import DsChePhamDD from "../containers/DsChePhamDD";
import { ROLES } from "constants/index";
import { checkRole } from "lib-utils/role-utils";

const Content = ({
  fromPopup,
  onChangeLichSuKham,
  nbDotDieuTriId,
  nbThongTinId,
  isShow = true,
  isDieuTriDaiHan,
  themeKey,
  ...props
}) => {
  const { t } = useTranslation();
  const refThuocHoSoBenhAn = useRef(null);
  const refVatTuHoSoBenhAn = useRef(null);
  const refDanhSachDichVu = useRef(null);
  const refHoaChatHoSoBenhAn = useRef(null);
  const refDanhSachSuatAn = useRef(null);
  const refMauHoSoBenhAn = useRef(null);
  const refDichVuGiuong = useRef(null);
  const refVacxinHsba = useRef(null);
  const refChePhamDD = useRef(null);
  const refPhieuThuHoaDon = useRef(null);
  const refDanhSachDichVuNgoaiDieuTri = useRef(null);

  const [activeTab, setActiveTab] = useState(0);

  const onChangeTab = useCallback((tab) => {
    setActiveTab(tab);
  }, []);

  if (!nbDotDieuTriId || !isShow) return null;

  return (
    <Main>
      <div className="left-body">
        <LazyLoad
          component={() => import("../containers/LichSuKham")}
          nbDotDieuTriId={nbDotDieuTriId}
          onChangeLichSuKham={onChangeLichSuKham}
          fromPopup={fromPopup}
          nbThongTinId={nbThongTinId}
          isDieuTriDaiHan={isDieuTriDaiHan}
        />
      </div>
      <div className="right-body">
        <Tabs defaultActiveKey={activeTab} onChange={onChangeTab}>
          <Tabs.TabPane
            tab={<div className="tab-title">{t("hsba.hoSoKhamChuaBenh")}</div>}
            key={"ho-so-kham-benh"}
          >
            <HoSoKhamBenh
              nbDotDieuTriId={nbDotDieuTriId}
              nbThongTinId={nbThongTinId}
              fromPopup={fromPopup}
              themeKey={themeKey}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <div className="tab-title">{t("quanLyNoiTru.dvNoiTru.dvkt")}</div>
            }
            key={"dich-vu-ky-thuat"}
          >
            {activeTab == "dich-vu-ky-thuat" && (
              <LazyLoad
                component={() => import("../containers/DsDichVu")}
                ref={refDanhSachDichVu}
                tableName={"TABLE_HSBA_DSDV"}
                nbDotDieuTriId={nbDotDieuTriId}
              />
            )}
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={<div className="tab-title">{t("common.thuoc")}</div>}
            key={"thuoc"}
          >
            <ThuocHoSoBenhAn
              ref={refThuocHoSoBenhAn}
              tableName={"TABLE_HSBA_THUOC"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={<div className="tab-title">{t("common.vatTu")}</div>}
            key={"vat-tu"}
          >
            <VatTuHoSoBenhAn
              ref={refVatTuHoSoBenhAn}
              tableName={"VATTU_HSBA_VAT_TU"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={<div className="tab-title">{t("common.suatAn")}</div>}
            key={"suat-an"}
          >
            <DanhSachSuatAn
              ref={refDanhSachSuatAn}
              tableName={"TABLE_HSBA_SUAT_AN"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={<div className="tab-title">{t("danhMuc.hoaChat")}</div>}
            key={"hoa-chat"}
          >
            <HoaChat
              ref={refHoaChatHoSoBenhAn}
              tableName={"TABLE_HSBA_HOA_CHAT"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <div className="tab-title">{t("quanLyNoiTru.dvNoiTru.mau")}</div>
            }
            key={"mau"}
          >
            <Mau
              ref={refMauHoSoBenhAn}
              tableName={"TABLE_HSBA_MAU"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <div className="tab-title">
                {t("quanLyNoiTru.dvNoiTru.dvGiuong")}
              </div>
            }
            key={"dv-giuong"}
          >
            <DichVuGiuong
              ref={refDichVuGiuong}
              nbDotDieuTriId={nbDotDieuTriId}
              tableName={"TABLE_HSBA_DV_GIUONG"}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <div className="tab-title">
                {t("quanLyNoiTru.dvNoiTru.vacxin")}
              </div>
            }
            key={"vac-xin"}
          >
            <VacxinHsba
              ref={refVacxinHsba}
              tableName={"TABLE_HSBA_DV_VACXIN"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <div className="tab-title">
                {t("quanLyNoiTru.dvNoiTru.chePhamDD")}
              </div>
            }
            key={"che-pham-dinh-duong"}
          >
            <DsChePhamDD
              tableName={"TABLE_HSBA_DV_CHE_PHAM_DD"}
              nbDotDieuTriId={nbDotDieuTriId}
              ref={refChePhamDD}
            />
          </Tabs.TabPane>

          {checkRole([ROLES["HO_SO_BENH_AN"].XEM_DS_PHIEU_THU_HOA_DON]) && (
            <Tabs.TabPane
              tab={<div className="tab-title">{t("hsba.phieuThuHoaDon")}</div>}
              key={"phieu-thu-hoa-don"}
            >
              <DsPhieuThuHoaDon
                tableName={"TABLE_HSBA_PHIEU_THU_HOA_DON"}
                nbDotDieuTriId={nbDotDieuTriId}
                ref={refPhieuThuHoaDon}
              />
            </Tabs.TabPane>
          )}
          <Tabs.TabPane
            tab={
              <div className="tab-title">
                {t("quanLyNoiTru.dvNgoaiDieuTri")}
              </div>
            }
            key={"dich-vu-ngoai-dieu-tri"}
          >
            <DsDichVuNgoaiDieuTri
              ref={refDanhSachDichVuNgoaiDieuTri}
              tableName={"TABLE_HSBA_DSDV_NGOAI_DIEU_TRU"}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Tabs.TabPane>
        </Tabs>
      </div>
    </Main>
  );
};

export default Content;
