import React, { useEffect, useMemo, useRef, useState, useImperativeHandle, forwardRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useLazyKVMap, useThietLap } from "hooks";
import { TableWrapper, Pagination } from "components";
import { HeaderSearch } from "components";
import { SVG } from "assets";
import { isArray, transformQueryString } from "utils/index";
import {
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import {
  ENUM,
  DOI_TUONG_KCB,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_NHAP_XUAT,
} from "constants/index";
import { Main } from "./styled";

const DanhSachHangHoa = (props, ref) => {
  const { listDsKhoa, loadFinish, isAllKhoa } = props?.parentState || {};
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [dataLOAI_CHI_DINH] = useEnum(ENUM.LOAI_CHI_DINH);
  const [dataTRANG_THAI_NB] = useEnum(ENUM.TRANG_THAI_NB);
  const [MA_NHOM_DICH_VU_CAP1_CDHA] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_CDHA
  );
  const [MA_NHOM_DICH_VU_CAP1_TT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_TT
  );
  const [MA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT
  );

  const {
    listData,
    totalElements,
    page,
    size,
    isLoading,
    dataSortColumn: _dataSort,
    dataSortDefault,
  } = useSelector((state) => state.danhSachHangHoaChoLinh);

  const dataSortColumn = dataSortDefault || _dataSort;

  const {
    danhSachHangHoaChoLinh: {
      onSearch,
      onSizeChange,
      updateData,
      clearData,
      onSortChange,
    },
  } = useDispatch();

  const doiTuongKcbEnum = useMemo(() => {
    const noiTru = t("danhMuc.noiTru");
    const ngoaiTru = t("common.ngoaiTru");
    return {
      [DOI_TUONG_KCB.NGOAI_TRU]: ngoaiTru,
      [DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU]: ngoaiTru,
      [DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC]: ngoaiTru,
      [DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC]: ngoaiTru,
      [DOI_TUONG_KCB.DIEU_TRI_NOI_TRU]: noiTru,
      [DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY]: noiTru,
      [DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA]: noiTru,
      [DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO]: noiTru,
    };
  }, []);

  const getNhomChiDinh = useMemo(() => {
    const cdha_tt = t("kho.cdhaThuThuat");
    const pttt = t("kho.pttt");
    const vtyt = t("kho.vtyt");
    const nhomKhac = t("kho.nhomKhac");
    const map = new Map([
      [MA_NHOM_DICH_VU_CAP1_CDHA, cdha_tt],
      [MA_NHOM_DICH_VU_CAP1_TT, cdha_tt],
      [true, vtyt],
      [MA_NHOM_DICH_VU_CAP1_PT, pttt],
    ]);

    return (item) => map.get(item) || nhomKhac;
  }, [
    MA_NHOM_DICH_VU_CAP1_CDHA,
    MA_NHOM_DICH_VU_CAP1_TT,
    MA_NHOM_DICH_VU_CAP1_PT,
  ]);

  const [getLoaiChiDinh] = useLazyKVMap(dataLOAI_CHI_DINH);
  const [getTrangThaiNb] = useLazyKVMap(dataTRANG_THAI_NB);

  useImperativeHandle(ref, () => ({
    getListHuyGiuTon: () => {
      const validKeys = selectedRowKeys?.filter((id) => {
        const donThuoc = listData.find((x) => x.id === id);
        return (
          donThuoc?.trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO
        );
      });
      return validKeys;
    },
    setSelectedRowKeys: (keys) => setSelectedRowKeys(keys),
  }));

  const donThuocMap = useMemo(() => {
    return new Map((listData || []).map((x) => [x.id, x]));
  }, [listData]);

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  useEffect(() => {
    return () => {
      updateData({ dataSearch: {}, showButtonHuyGiuTon: false });
    };
  }, []);

  useEffect(() => {
    if (loadFinish) {
      const { page, size, ...queries } = transformQueryString({
        page: {
          format: (value) => parseInt(value),
          defaultValue: 0,
        },
        size: {
          format: (value) => parseInt(value),
          defaultValue: 50,
        },
        tuThoiGianRaVien: {
          format: (date) => date.format("YYYY-MM-DD 00:00:00"),
          type: "dateOptions",
          defaultValue: moment()
            .subtract(7, "days")
            .format("YYYY-MM-DD 00:00:00"),
        },
        denThoiGianRaVien: {
          format: (date) => date.format("YYYY-MM-DD 23:59:59"),
          defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
          type: "dateOptions",
        },
      });

      updateData({
        size,
        dataSearch: {
          ...queries,
          // dsTrangThai: [10, 15, 20],
          dsTrangThaiHoan: [0, 10, 20],
          dsLoaiDichVu: queries.dsLoaiDichVu
            ? queries.dsLoaiDichVu
            : [
              LOAI_DICH_VU.THUOC,
              LOAI_DICH_VU.VAT_TU,
              LOAI_DICH_VU.HOA_CHAT,
              LOAI_DICH_VU.CHE_PHAM_DINH_DUONG,
            ],
          dsKhoaChiDinhId: queries.dsKhoaChiDinhId
            ? Number(queries.dsKhoaChiDinhId)
            : isArray(listDsKhoa, true) && !isAllKhoa
              ? listDsKhoa.map((i) => i.id)
              : null,
        },
      });

      onSearch({ page });
    }
  }, [listDsKhoa, loadFinish, isAllKhoa]);

  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({
      page: 0,
      size,
    });

    onSizeChange(size);
  };

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: <HeaderSearch title={t("hsba.ngayRaVien")} />,
      width: 180,
      dataIndex: "thoiGianRaVien",
      key: "thoiGianRaVien",
      i18Name: "hsba.ngayRaVien",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key={"maHoSo"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maHoSo"] || 0}
        />
      ),
      width: 100,
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHoSo",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.maBenhAn")} />,
      width: 100,
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.maNb")} />,
      width: 100,
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.hoTenNb")}
          sort_key={"tenNb"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenNb"] || 0}
        />
      ),
      width: 250,
      dataIndex: "tenNb",
      key: "tenNb",
      align: "left",
      i18Name: "thuNgan.hoTenNb",
      show: true,
    },
    {
      title: <HeaderSearch title={t("danhMuc.maDichVu")} />,
      width: 150,
      dataIndex: "maDichVu",
      key: "maDichVu",
      align: "left",
      i18Name: "danhMuc.maDichVu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          sort_key={"tenDichVu"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenDichVu"] || 0}
        />
      ),
      width: 250,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      align: "left",
      i18Name: "common.tenDichVu",
      show: true,
    },
    {
      title: <HeaderSearch title={t("baoCao.nguoiChiDinh")} />,
      width: 250,
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      align: "left",
      i18Name: "baoCao.nguoiChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.khoaChiDinh")}
          sort_key={"tenKhoaChiDinh"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenKhoaChiDinh"] || 0}
        />
      ),
      width: 300,
      dataIndex: "tenKhoaChiDinh",
      key: "tenKhoaChiDinh",
      i18Name: "baoCao.khoaChiDinh",
      show: true,
    },
    {
      title: <HeaderSearch title={t("cdha.thoiGianChiDinh")} />,
      width: 180,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: "cdha.thoiGianChiDinh",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ngayThucHien")}
          sort_key={"thoiGianThucHien"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thoiGianThucHien"] || 0}
        />
      ),
      width: 180,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "common.ngayThucHien",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("common.hamLuong")} />,
      width: 150,
      dataIndex: "hamLuong",
      key: "hamLuong",
      i18Name: "common.hamLuong",
      show: true,
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.soLuongKe")} />,
      width: 100,
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      i18Name: "quanLyNoiTru.dvNoiTru.soLuongKe",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.soLuongDuyet")} />,
      width: 100,
      dataIndex: "soLuongDuyet",
      key: "soLuongDuyet",
      i18Name: "kho.soLuongDuyet",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.nhomChiDinh")} />,
      width: 140,
      dataIndex: "maChiDinhTuNhomDichVuCap1",
      key: "nhomChiDinh",
      i18Name: "kho.nhomChiDinh",
      show: true,
      render: (item, data) => getNhomChiDinh(data.vatTuXetNghiem || item),
    },
    {
      title: <HeaderSearch title={t("common.loaiChiDinh")} />,
      width: 120,
      dataIndex: "loaiChiDinh",
      key: "loaiChiDinh",
      i18Name: "common.loaiChiDinh",
      show: true,
      render: (item) => getLoaiChiDinh(item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.soPhieuLinh")}
          sort_key={"soPhieuLinh"}
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soPhieuLinh"] || 0}
        />
      ),
      width: 100,
      dataIndex: "soPhieuLinh",
      key: "soPhieuLinh",
      i18Name: "baoCao.soPhieuLinh",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.trangThaiNb")} />,
      width: 150,
      dataIndex: "trangThaiNb",
      key: "trangThaiNb",
      i18Name: "common.trangThaiNb",
      show: true,
      render: (item) => getTrangThaiNb(item)?.ten,
    },
    {
      title: <HeaderSearch title={t("kho.kho")} />,
      width: 250,
      dataIndex: "tenKho",
      key: "tenKho",
      i18Name: "kho.kho",
      show: true,
    },

    {
      title: <HeaderSearch title={t("common.loai")} />,
      width: 150,
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      i18Name: "common.loai",
      show: true,
      render: (item) => doiTuongKcbEnum[item],
    },
    {
      title: <HeaderSearch title={t("danhMuc.donVi")} />,
      width: 120,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "danhMuc.donVi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={<SVG.IcSetting onClick={onSettings} className="icon" />}
        />
      ),
      width: 40,
      dataIndex: "",
      key: "",
      align: "center",
      fixed: "right",
    },
  ];

  const onRow = (record) => {
    return {
      onClick: () => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onOpenDetail(record)();
          updateData({
            selectedRowId: record.id,
          });
        }
      },
    };
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData}
        scroll={{ x: 2000 }}
        tableName="table_Kho-danhSachHangHoaChoTra"
        ref={refSettings}
        loading={isLoading}
        onRow={onRow}
        rowSelection={{
          type: "checkbox",
          selectedRowKeys,
          onChange: (selectedRowKeys) => {
            const showButtonHuyGiuTon = selectedRowKeys.some(
              (id) =>
                donThuocMap.get(id)?.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO
            );

            updateData({ showButtonHuyGiuTon });
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        rowKey="id"
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listData}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      )}
    </Main>
  );
};

export default forwardRef(DanhSachHangHoa);
