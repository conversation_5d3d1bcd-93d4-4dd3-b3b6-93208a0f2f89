import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { message } from "antd";

import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils";
import { toSafePromise } from "lib-utils";

import { Page, Button, AuthWrapper } from "components";
import { useLoading, useStore } from "hooks";
import DanhSach from "./DanhSach";
import TimKiemNb from "./TimKiemNb";
import { Main } from "./styled";
import { ROLES } from "constants/index";

const DanhSachNbChuaTaoDuyetTraHangHoa = () => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refDanhSach = useRef(null);
  const showButtonHuyGiuTon = useStore(
    "danhSachHangHoaChoLinh.showButtonHuyGiuTon",
    false
  );

  const [state, _setState] = useState({
    listDsKhoa: [],
    loadFinish: false,
    isAllKhoa: false,
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    khoa: { getKhoaTheoTaiKhoan, getListAllKhoa },
    danhSachHangHoaChoLinh: { updateData, onSizeChange },
    phatThuocNgoaiTru: {
      huyGiuTon
    }
  } = useDispatch();

  useEffect(() => {
    const loadData = async () => {
      const isAllKhoa = checkRole([
        ROLES["KHO"].XEM_DS_NB_CHUA_TAO_DUYET_LINH_TRA_HANG_HOA_TAT_CA_KHOA,
      ]);
      try {
        setState({ listDsKhoa: [], loadFinish: false, isAllKhoa });
        let res = [];
        if (isAllKhoa) {
          res = await getListAllKhoa({ page: "", size: "", active: true });
        } else {
          res = await getKhoaTheoTaiKhoan({ page: "", size: "", active: true });
        }
        setState({ listDsKhoa: res, loadFinish: true, isAllKhoa });
      } catch (error) {
        setState({ listDsKhoa: [], loadFinish: true, isAllKhoa });
        console.error(error?.message || error);
      }
    };

    loadData();
  }, []);

  const onHuyGuiTon = async () => {
    const selectedRowKeys = refDanhSach.current?.getListHuyGiuTon();
    console.log('selectedRowKeys', selectedRowKeys);
    const isRowSelected = isArray(selectedRowKeys, 1);
    console.log('isRowSelected', isRowSelected);
    if (!isRowSelected) {
      message.error(t("kho.vuiLongChonDonThuoc"));
      return;
    }
    showLoading();
    const [err] = await toSafePromise(huyGiuTon({ body: selectedRowKeys }));
    if (err) {
      hideLoading();
      return message.error(err.message || t("kho.huyGiuTonKhongThanhCong"));
    }
    refDanhSach.current?.setSelectedRowKeys([]);
    updateData({ showButtonHuyGiuTon: false });
    onSizeChange({ page: 0 });
    hideLoading();
  };

  return (
    <Page
      breadcrumb={[
        { link: "/kho", title: t("kho.kho") },
        {
          link: "/kho/danh-sach-hang-hoa-cho-linh",
          title: t("kho.danhSachHangHoaChoLinh"),
        },
      ]}
      title={t("kho.danhSachHangHoaChoLinh")}
      topHeader={
        showButtonHuyGiuTon && (
          <div style={{ display: "flex", justifyContent: "flex-end", padding: "0 16px" }}>
            <AuthWrapper
              accessRoles={[ROLES["PHAT_THUOC_NGOAI_TRU"].HUY_GIU_TON]}
            >
              <Button type="primary" onClick={onHuyGuiTon} height={28}>
                {t("kho.huyGiuTon")}
              </Button>
            </AuthWrapper>
          </div>
        )
      }
    >
      <Main>
        <TimKiemNb parentState={state} />
        <DanhSach ref={refDanhSach} parentState={state} />
      </Main>
    </Page>
  );
};

export default DanhSachNbChuaTaoDuyetTraHangHoa;
