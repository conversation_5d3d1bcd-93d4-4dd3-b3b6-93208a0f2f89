import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useMemo,
  useState,
} from "react";
import { useHistory } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import classNames from "classnames";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { message, InputNumber, Switch, Input } from "antd";
import { cloneDeep, orderBy } from "lodash";

import { checkRole } from "lib-utils/role-utils";
import { useStore, useThietLap } from "hooks";

import ModalChietKhau from "pages/kho/components/ModalChietKhau";
import {
  InputTimeout,
  HeaderSearch,
  Select,
  Pagination,
  TableWrapper,
  DateTimePicker,
  Popover,
  Tooltip,
  Checkbox,
} from "components";
import {
  parserNumber,
  formatNumber,
  roundToDigits,
  isNumber,
} from "utils/index";
import PopoverHangHoa from "../PopoverHangHoa";
import TableEmpty from "pages/kho/components/TableEmpty";
import ModalDanhSachHangHoa from "../ModalDanhSachHangHoa";
import {
  LOAI_DICH_VU,
  LOAI_KHO,
  THIET_LAP_CHUNG,
  ROLES,
  LOAI_NHAP_XUAT,
  HOTKEY,
} from "constants/index";
import { SVG } from "assets";
import {
  getTenHangHoa,
  getThangSoBanLe,
  tinhGiaTruocVAT,
  tinhGiaTruocVATChuaChietKhau,
  tinhTienChietKhau,
  tinhThanhTienVat,
} from "utils/kho-utils";
import { Main } from "./styled";

const formatterFunc = (value) => {
  return value
    ? value.replace(".", ",").replace(/\B(?=(\d{3})+(?!\d))/g, ".")
    : 0;
};

const DanhSachHangHoa = (
  {
    id,
    onFocusSearchHangHoa,
    setIsFormChange = () => {},
    isReadOnlyHangHoa = false,
    layerId,
    isSuaSoLuong = false,
    ...props
  },
  ref
) => {
  const { t } = useTranslation();
  const styleInput = {
    border: "none",
    textAlign: "right",
    width: "100%",
  };
  const refModalChietKhau = useRef(null);
  const refModalDanhSachHangHoa = useRef(null);

  const history = useHistory();

  const [dataNHAP_KHO_HIEN_THI_VAT_BAN, finishNHAP_KHO_HIEN_THI_VAT_BAN] =
    useThietLap(THIET_LAP_CHUNG.NHAP_KHO_HIEN_THI_VAT_BAN);
  const [
    dataKHONG_SUA_VAT_BAN_KHI_NHAP_KHO,
    finishKHONG_SUA_VAT_BAN_KHI_NHAP_KHO,
  ] = useThietLap(THIET_LAP_CHUNG.KHONG_SUA_VAT_BAN_KHI_NHAP_KHO);
  const [dataKHONG_SUA_GIA_BH_KBH] = useThietLap(
    THIET_LAP_CHUNG.KHONG_SUA_GIA_BH_KBH
  );
  const [dataHIEN_THI_CHECKBOX_BO_BAT_BUOC_SOLO_HSD] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_BO_BAT_BUOC_SOLO_HSD
  );
  const [dataNHAP_GIA_SAU_VAT_NHAP_KHO_NCC] = useThietLap(
    THIET_LAP_CHUNG.NHAP_GIA_SAU_VAT_NHAP_KHO_NCC
  );
  const [dataNHAP_KHO_SO_LUONG_THAP_PHAN] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KHO_SO_LUONG_THAP_PHAN
  );
  const [dataNHAP_KICH_CO_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KICH_CO_VAT_TU,
    "0"
  );
  const [dataNHAP_GIA_BAO_HIEM_VAT_TU_KHONG_CO_TY_LE_BAO_HIEM] = useThietLap(
    THIET_LAP_CHUNG.NHAP_GIA_BAO_HIEM_VAT_TU_KHONG_CO_TY_LE_BAO_HIEM
  );
  const [dataTU_DONG_IN_HOA_SO_LO] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_IN_HOA_SO_LO
  );

  const listAllXuatXu = useSelector((state) => state.xuatXu.listAllXuatXu);
  const listAllKichCo = useStore("kichCo.listAllKichCo", []);
  const [openPopover, setOpenPopover] = useState({ itemId: null, type: null });

  const {
    thongTinPhieu,
    dsNhapXuatChiTiet,
    dataSortColumn,
    page = 0,
    size = 10,
    totalElements = 0,
    nhapKhongTheoThau,
  } = useSelector((state) => state.phieuNhapXuat);

  const listAllNhaSanXuat = useSelector(
    (state) => state.doiTac.listAllNhaSanXuat
  );
  const listAllThangSoBanLe = useSelector(
    (state) => state.thangSoBanLe.listAllThangSoBanLe
  );
  const listKhoUser = useSelector((state) => state.kho.listKhoUser);

  const {
    xuatXu: { getListAllXuatXu },
    phieuNhapXuat: { updateData, onSortChange, onSearch: onSearchChiTiet },
    thangSoBanLe: { getListAllThangSoBanLe },
    phimTat: { onRegisterHotkey },
  } = useDispatch();

  const soLuongSoCap =
    dsNhapXuatChiTiet?.reduce(
      (total, x) => (total = total + (x?.soLuongSoCap || 0)),
      0
    ) || 0;

  const tongTien =
    dsNhapXuatChiTiet?.reduce(
      (total, x) =>
        (total =
          total + (x?.loNhap?.giaNhapSauVat || 0) * (x?.soLuongSoCap || 0)),
      0
    ) || 0;

  //field tiền chiết khấu lưu giá trị tổng = chiết khấu theo % + chiết khấu theo tiền
  //=> không cần tính lại
  const chietKhau =
    parseFloat(thongTinPhieu?.chietKhauTheoTien || 0) +
    (tongTien * (thongTinPhieu.phanTramChietKhau || 0)) / 100;

  const thanhTien = (tongTien || 0) - (chietKhau || 0);
  const thanhTienSuaDoi =
    (dsNhapXuatChiTiet?.reduce(
      (total, x) => (total = total + x?.thanhTienSuaDoi),
      0
    ) || 0) - (chietKhau || 0);

  const thanhTienTruocVat =
    dsNhapXuatChiTiet?.reduce(
      (total, x) => (total = total + x?.thanhTienTruocVat),
      0
    ) || 0;

  const thanhTienSuaDoiTruocVat =
    dsNhapXuatChiTiet?.reduce(
      (total, x) => (total = total + x?.thanhTienSuaDoiTruocVat),
      0
    ) || 0;

  const thanhTienVat =
    dsNhapXuatChiTiet?.reduce(
      (total, x) =>
        (total =
          total +
          tinhThanhTienVat(x?.thanhTienSuaDoi, x?.thanhTienSuaDoiTruocVat)),
      0
    ) || 0;

  useImperativeHandle(ref, () => ({
    getTong: () => ({
      thanhTien: roundToDigits(thanhTien, 3),
      thanhTienSuaDoi: roundToDigits(thanhTienSuaDoi, 3),
      phanTramChietKhau: thongTinPhieu.phanTramChietKhau,
      tienChietKhau: roundToDigits(chietKhau, 3),
    }),
  }));

  // Logic focus cho các phím tắt
  // - Lần 1: Focus line đầu tiên
  // - Lần 2: Focus line thứ 2 (cycle qua các lines)
  // - Nếu chỉ có 1 line: Mặc định focus line đó
  const handleSmartFocus = (popoverType) => {
    if (!dsNhapXuatChiTiet || dsNhapXuatChiTiet.length === 0) return;

    const currentFocusedItem = dsNhapXuatChiTiet.find((x) => x.isFocus);
    let targetItem = null;

    if (dsNhapXuatChiTiet.length === 1) {
      // Nếu chỉ có 1 line thì mặc định focus line đó
      targetItem = dsNhapXuatChiTiet[0];
    } else if (!currentFocusedItem) {
      // Nếu chưa có item nào được focus thì focus line đầu tiên
      targetItem = dsNhapXuatChiTiet[0];
    } else {
      // Tìm index của item hiện tại
      const currentIndex = dsNhapXuatChiTiet.findIndex((x) => x.isFocus);

      // Kiểm tra xem popover hiện tại có đang mở cho item này không
      const currentItemId =
        currentFocusedItem.id || currentFocusedItem.detachId;
      const isCurrentPopoverOpen =
        openPopover.itemId === currentItemId &&
        openPopover.type === popoverType;

      if (isCurrentPopoverOpen) {
        // Nếu popover đang mở cho item hiện tại, chuyển sang item tiếp theo
        const nextIndex = (currentIndex + 1) % dsNhapXuatChiTiet.length;
        targetItem = dsNhapXuatChiTiet[nextIndex];
      } else {
        // Nếu popover chưa mở, giữ nguyên item hiện tại
        targetItem = currentFocusedItem;
      }
    }

    if (targetItem) {
      // Set focus cho target item
      const newDsNhapXuatChiTiet = dsNhapXuatChiTiet.map((item) => ({
        ...item,
        isFocus:
          (item.id || item.detachId) === (targetItem.id || targetItem.detachId),
      }));

      updateData({ dsNhapXuatChiTiet: newDsNhapXuatChiTiet });

      // Mở popover cho target item
      const itemId = targetItem.id || targetItem.detachId;
      setOpenPopover({ itemId, type: popoverType });
    }
  };

  useEffect(() => {
    if (!layerId) return;
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F6,
          onEvent: () => {
            handleSmartFocus("p1");
          },
        },
        {
          keyCode: HOTKEY.F7,
          onEvent: () => {
            handleSmartFocus("p2");
          },
        },
        {
          keyCode: HOTKEY.F8,
          onEvent: () => {
            handleSmartFocus("p3");
          },
        },
        {
          keyCode: HOTKEY.F9,
          onEvent: () => {
            handleSmartFocus("p4");
          },
        },
      ],
    });
  }, [layerId, dsNhapXuatChiTiet, openPopover]);

  const { isNhapThapPhan, loaiDVNhapThapPhan } = useMemo(() => {
    if (dataNHAP_KHO_SO_LUONG_THAP_PHAN) {
      let _arr = dataNHAP_KHO_SO_LUONG_THAP_PHAN.split("/");
      if (_arr.length == 2) {
        return {
          isNhapThapPhan: _arr[0]?.eval(),
          loaiDVNhapThapPhan: _arr[1],
        };
      }
    }

    return { isNhapThapPhan: false, loaiDVNhapThapPhan: [] };
  }, [dataNHAP_KHO_SO_LUONG_THAP_PHAN]);

  const isShowVatBan = useMemo(() => {
    if (!finishNHAP_KHO_HIEN_THI_VAT_BAN) return false;

    const maKho = (listKhoUser || []).find(
      (x) => x.id === thongTinPhieu.khoId
    )?.ma;
    return (dataNHAP_KHO_HIEN_THI_VAT_BAN || "").split(",").includes(maKho);
  }, [
    dataNHAP_KHO_HIEN_THI_VAT_BAN,
    thongTinPhieu,
    listKhoUser,
    finishNHAP_KHO_HIEN_THI_VAT_BAN,
  ]);

  const nhaThuoc = useMemo(() => {
    const _dsLoaiKho =
      (listKhoUser || []).find((x) => x.id === thongTinPhieu.khoId)
        ?.dsLoaiKho || [];

    return _dsLoaiKho.includes(LOAI_KHO.NHA_THUOC);
  }, [thongTinPhieu]);

  const layGiaTuDanhMuc = useMemo(() => {
    return (
      checkRole([ROLES["KHO"].FILL_GIA_BH_GIA_KBH_THEO_DM]) &&
      [LOAI_NHAP_XUAT.NHAP_KHAC, LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(
        thongTinPhieu?.loaiNhapXuat
      )
    );
  }, [thongTinPhieu]);

  const isReadOnly = useMemo(() => {
    if (history.location.pathname.includes("chi-tiet")) {
      return true;
    } else {
      return false;
    }
  }, [history.location.pathname]);

  const nhapNhanhGiaSauVat = useMemo(() => {
    return (
      dataNHAP_GIA_SAU_VAT_NHAP_KHO_NCC?.eval() &&
      [LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(thongTinPhieu?.loaiNhapXuat)
    );
  }, [thongTinPhieu, dataNHAP_GIA_SAU_VAT_NHAP_KHO_NCC]);

  const isNhapNcc = useMemo(() => {
    return thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.NHAP_TU_NCC;
  }, [thongTinPhieu]);

  const tinhDonGiaBaoHiem = (data, giaNhapSauVat) => {
    if (
      dataNHAP_GIA_BAO_HIEM_VAT_TU_KHONG_CO_TY_LE_BAO_HIEM?.eval() &&
      [LOAI_NHAP_XUAT.NHAP_KHAC, LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(
        thongTinPhieu?.loaiNhapXuat
      ) &&
      data?.loaiDichVu === LOAI_DICH_VU.VAT_TU &&
      checkRole([ROLES["KHO"].FILL_GIA_BH_GIA_KBH_THEO_DM]) &&
      data.tyLeBhTt == 0
    ) {
      return (giaNhapSauVat * 1).toFixed(3);
    } else {
      return data?.tyLeBhTt === 0 ? 0 : (giaNhapSauVat * 1).toFixed(3);
    }
  };

  function capNhatTheoGiaNhapTruocVat(giaNhapTruocVat) {
    let res = listAllThangSoBanLe.filter(
      (x) => x.khoId === thongTinPhieu?.khoId
    );

    res = res.filter(
      (x) =>
        (!x.phanLoaiDvKhoId || x.phanLoaiDvKhoId === data.phanLoaiDvKhoId) &&
        (!x.nhomDichVuCap2Id || x.nhomDichVuCap2Id === data.nhomDichVuCap2Id) &&
        (!x.nhomDichVuCap1Id || x.nhomDichVuCap1Id === data.nhomDichVuCap1Id)
    );

    const giaNhapSauVat = (
      (giaNhapTruocVat || 0) *
      (1 + (data?.loNhap?.vat || 0) / 100)
    ).toFixed(3);
    if (res.length > 0) {
      res = res.filter((x) => {
        const min = x?.giaNhapNhoNhat || 0;
        const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;

        return min <= giaNhapSauVat && giaNhapSauVat <= max;
      });
    }

    let thangSoBanLe = getThangSoBanLe(res);

    let giaKhongBaoHiem = giaNhapSauVat * (1 + (thangSoBanLe || 0) / 100);

    let thanhTienTruocVat = roundToDigits(giaNhapTruocVat * value || 0, 3);
    let thanhTienSuaDoi = roundToDigits(giaNhapSauVat * value || 0, 3);

    return {
      giaNhapTruocVat: giaNhapTruocVat,
      giaNhapSauVat: giaNhapSauVat,
      giaBaoHiem: tinhDonGiaBaoHiem(data, giaNhapSauVat),
      ...(!(layGiaTuDanhMuc && data.loaiDichVu === LOAI_DICH_VU.VAT_TU) && {
        giaKhongBaoHiem: nhaThuoc
          ? Math.floor(giaKhongBaoHiem)
          : roundToDigits(giaKhongBaoHiem, 3),
      }),
      thangSoBanLe,
      thanhTienSuaDoi: thanhTienSuaDoi,
      thanhTienTruocVat: thanhTienTruocVat,
      thanhTienSuaDoiTruocVat: thanhTienTruocVat,
      thanhTienVat: tinhThanhTienVat(thanhTienSuaDoi, thanhTienTruocVat),
    };
  }

  const changeSoLuong = (data) => (value) => {
    if (data?.vatTuBoId > 0) {
      let dataBo = dsNhapXuatChiTiet.find((item) =>
        thongTinPhieu?.quyetDinhThauId
          ? item?.quyetDinhThauChiTietId === data?.vatTuBoId
          : item.dichVuId === data?.vatTuBoId
      );
      const index = dataBo.dsNhapXuatChiTiet.findIndex((item) =>
        data.id ? item.id === data.id : item.detachId === data.detachId
      );
      if (index !== -1) {
        const item = dataBo.dsNhapXuatChiTiet[index];
        item.soLuongSoCap = value;

        if (thongTinPhieu.loaiChietKhau == 20) {
          const giaNhapTruocVat = tinhGiaTruocVAT(
            {
              giaTruocVatChuaChietKhau: item?.loNhap?.giaTruocVatChuaChietKhau,
              tienChietKhau: item?.loNhap?.tienChietKhau,
              soLuongSoCap: value,
              phanTramChietKhau: item?.loNhap?.phanTramChietKhau,
            },
            3
          );

          item.loNhap = {
            ...item.loNhap,
            ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
          };
        } else {
          const giaKhongBaoHiem =
            (item?.loNhap.giaNhapSauVat *
              (100 + (item?.loNhap.thangSoBanLe || 0))) /
            100;

          item.thanhTienSuaDoi = item?.loNhap?.giaNhapSauVat * value || 0;
          let thanhTienTruocVat = roundToDigits(
            item?.loNhap?.giaNhapTruocVat * value || 0,
            3
          );
          item.thanhTienSuaDoiTruocVat = thanhTienTruocVat;
          item.thanhTienVat = tinhThanhTienVat(
            item.thanhTienSuaDoi,
            thanhTienTruocVat
          );
          item.thanhTienSuaDoiTruocVat = thanhTienTruocVat;
          item.loNhap = {
            ...item.loNhap,
            ...(!(
              layGiaTuDanhMuc && item.loaiDichVu === LOAI_DICH_VU.VAT_TU
            ) && {
              giaKhongBaoHiem: nhaThuoc
                ? Math.floor(giaKhongBaoHiem)
                : roundToDigits(giaKhongBaoHiem, 3),
            }),
          };
        }
        if (value > 0) {
          item.isFocus = false;
        }

        setIsFormChange(true);
        updateData({
          dsNhapXuatChiTiet: [
            ...dsNhapXuatChiTiet.map((item) => {
              return { ...item, isFocus: false };
            }),
          ],
        });
      }
    } else {
      const index = dsNhapXuatChiTiet.findIndex((item) =>
        data.id ? item.id === data.id : item.detachId === data.detachId
      );
      if (index !== -1) {
        const item = dsNhapXuatChiTiet[index];
        item.soLuongSoCap = value;

        if (thongTinPhieu.loaiChietKhau == 20) {
          if (value > 0) {
            const giaNhapTruocVat = tinhGiaTruocVAT(
              {
                giaTruocVatChuaChietKhau:
                  item?.loNhap?.giaTruocVatChuaChietKhau,
                tienChietKhau: item?.loNhap?.tienChietKhau,
                soLuongSoCap: value,
                phanTramChietKhau: item?.loNhap?.phanTramChietKhau,
              },
              3
            );

            item.loNhap = {
              ...item.loNhap,
              ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
            };
            item.thanhTienSuaDoi = item?.loNhap?.thanhTienSuaDoi;
          }
        } else {
          const giaKhongBaoHiem =
            (item?.loNhap.giaNhapSauVat *
              (100 + (item?.loNhap.thangSoBanLe || 0))) /
            100;

          item.thanhTienSuaDoi = roundToDigits(
            item?.loNhap?.giaNhapSauVat * value || 0,
            3
          );

          let thanhTienTruocVat = roundToDigits(
            item?.loNhap?.giaNhapTruocVat * value || 0,
            3
          );
          item.thanhTienSuaDoiTruocVat = thanhTienTruocVat;
          item.thanhTienVat = tinhThanhTienVat(
            item.thanhTienSuaDoi,
            thanhTienTruocVat
          );
          item.thanhTienTruocVat = thanhTienTruocVat;
          item.loNhap = {
            ...item.loNhap,
            ...(!(
              layGiaTuDanhMuc && item.loaiDichVu === LOAI_DICH_VU.VAT_TU
            ) && {
              giaKhongBaoHiem: nhaThuoc
                ? Math.floor(giaKhongBaoHiem)
                : roundToDigits(giaKhongBaoHiem, 3),
            }),
          };
        }
        if (value > 0) {
          item.isFocus = false;
        }
        setIsFormChange(true);
        updateData({
          dsNhapXuatChiTiet: [...dsNhapXuatChiTiet],
        });
      }
    }
  };

  const changeDSLo = (key, data, index) => async (value, item) => {
    let newData = { [key]: value };

    if (key == "kichCoVtId") newData["tenKichCoVt"] = item?.ten;
    let res = listAllThangSoBanLe.filter(
      (x) => x.khoId === thongTinPhieu?.khoId
    );

    res = res.filter(
      (x) =>
        (!x.phanLoaiDvKhoId || x.phanLoaiDvKhoId === data.phanLoaiDvKhoId) &&
        (!x.nhomDichVuCap2Id || x.nhomDichVuCap2Id === data.nhomDichVuCap2Id) &&
        (!x.nhomDichVuCap1Id || x.nhomDichVuCap1Id === data.nhomDichVuCap1Id)
    );

    function capNhatTheoGiaNhapTruocVat(giaNhapTruocVat) {
      const giaNhapSauVat = (
        (giaNhapTruocVat || 0) *
        (1 + (data?.loNhap?.vat || 0) / 100)
      ).toFixed(3);
      if (res.length > 0) {
        res = res.filter((x) => {
          const min = x?.giaNhapNhoNhat || 0;
          const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;

          return min <= giaNhapSauVat && giaNhapSauVat <= max;
        });
      }

      let thangSoBanLe = getThangSoBanLe(res);
      let giaKhongBaoHiem = giaNhapSauVat * (1 + (thangSoBanLe || 0) / 100);

      let thanhTienTruocVat = roundToDigits(
        giaNhapTruocVat * data?.soLuongSoCap || 0,
        3
      );
      let thanhTienSuaDoi = giaNhapSauVat * data?.soLuongSoCap || 0;

      return {
        giaNhapTruocVat: giaNhapTruocVat,
        giaNhapSauVat: giaNhapSauVat,
        giaBaoHiem: tinhDonGiaBaoHiem(data, giaNhapSauVat),
        giaKhongBaoHiem: nhaThuoc
          ? Math.floor(giaKhongBaoHiem)
          : roundToDigits(giaKhongBaoHiem, 3),
        thangSoBanLe,
        thanhTienSuaDoi: giaNhapSauVat * data?.soLuongSoCap || 0,
        thanhTienTruocVat: thanhTienTruocVat,
        thanhTienSuaDoiTruocVat: thanhTienTruocVat,
        thanhTienVat: tinhThanhTienVat(thanhTienSuaDoi, thanhTienTruocVat),
      };
    }

    if (key === "vat") {
      const giaNhapTruocVat = (
        (data?.loNhap?.giaNhapSauVat || 0) /
        (1 + value / 100)
      ).toFixed(3);

      if (res.length > 0) {
        res = res.filter((x) => {
          const min = x?.giaNhapNhoNhat || 0;
          const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;
          const giaNhapSauVat = data?.loNhap?.giaNhapSauVat || 0;

          return min <= giaNhapSauVat && giaNhapSauVat <= max;
        });
      }
      let thangSoBanLe = getThangSoBanLe(res);

      let giaKhongBaoHiem = data?.loNhap?.giaKhongBaoHiem;
      let thanhTienTruocVat = roundToDigits(
        giaNhapTruocVat * data?.soLuongSoCap,
        3
      );

      newData = {
        ...newData,
        vatBan: isShowVatBan ? value : null,
        giaNhapTruocVat: giaNhapTruocVat,
        thanhTienTruocVat: thanhTienTruocVat,
        thanhTienSuaDoiTruocVat: thanhTienTruocVat,
        thanhTienVat: tinhThanhTienVat(
          newData?.loNhap?.thanhTienSuaDoi ?? newData.thanhTienSuaDoi,
          thanhTienTruocVat
        ),
        thangSoBanLe,
        giaKhongBaoHiem: nhaThuoc
          ? Math.floor(giaKhongBaoHiem)
          : roundToDigits(giaKhongBaoHiem, 3),
        ...(thongTinPhieu.loaiChietKhau == 20
          ? {
              giaTruocVatChuaChietKhau: tinhGiaTruocVATChuaChietKhau(
                {
                  tienChietKhau: data.loNhap?.tienChietKhau,
                  giaNhapTruocVat: giaNhapTruocVat,
                  soLuongSoCap: data?.soLuongSoCap,
                  phanTramChietKhau: data.loNhap?.phanTramChietKhau,
                },
                3
              ),
            }
          : {}),
      };
    } else if (key === "vatBan") {
      let giaKhongBaoHiem = (
        (data?.loNhap?.giaNhapSauVat || 0) *
        (1 + data?.loNhap?.thangSoBanLe / 100)
      ).toFixed(3);

      newData = {
        ...newData,
        giaKhongBaoHiem: nhaThuoc
          ? Math.floor(giaKhongBaoHiem)
          : giaKhongBaoHiem,
      };
    } else if (key === "giaNhapSauVat") {
      const giaNhapTruocVat = (
        (value || 0) /
        (1 + (data?.loNhap?.vat || 0) / 100)
      ).toFixed(3);

      if (res.length > 0) {
        res = res.filter((x) => {
          const min = x?.giaNhapNhoNhat || 0;
          const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;

          return min <= value && value <= max;
        });
      }

      let thangSoBanLe = getThangSoBanLe(res);

      let giaKhongBaoHiem = value * (1 + (thangSoBanLe || 0) / 100);
      let thanhTienTruocVat = roundToDigits(
        giaNhapTruocVat * data?.soLuongSoCap,
        3
      );
      let thanhTienSuaDoi = roundToDigits(value * data?.soLuongSoCap || 0, 3);

      newData = {
        ...newData,
        ...(dataKHONG_SUA_GIA_BH_KBH?.eval()
          ? {}
          : {
              giaBaoHiem: tinhDonGiaBaoHiem(data, value),
              giaKhongBaoHiem: nhaThuoc
                ? Math.floor(giaKhongBaoHiem)
                : roundToDigits(giaKhongBaoHiem, 3),
            }),
        giaNhapTruocVat,
        thangSoBanLe,
        thanhTienSuaDoi: thanhTienSuaDoi,
        thanhTienTruocVat: thanhTienTruocVat,
        thanhTienSuaDoiTruocVat: thanhTienTruocVat,
        thanhTienVat: tinhThanhTienVat(thanhTienSuaDoi, thanhTienTruocVat),
        ...(thongTinPhieu.loaiChietKhau == 20
          ? {
              giaTruocVatChuaChietKhau: tinhGiaTruocVATChuaChietKhau(
                {
                  tienChietKhau: data.loNhap?.tienChietKhau,
                  giaNhapTruocVat: giaNhapTruocVat,
                  soLuongSoCap: data?.soLuongSoCap,
                  phanTramChietKhau: data.loNhap?.phanTramChietKhau,
                },
                3
              ),
            }
          : {}),
      };
    } else if (key === "giaKhongBaoHiem") {
      newData = {
        ...newData,
        giaKhongBaoHiem: nhaThuoc ? Math.floor(value) : roundToDigits(value, 3),
      };
    } else if (key === "giaNhapTruocVat") {
      const giaNhapSauVat = (
        (value || 0) *
        (1 + (data?.loNhap?.vat || 0) / 100)
      ).toFixed(3);
      if (res.length > 0) {
        res = res.filter((x) => {
          const min = x?.giaNhapNhoNhat || 0;
          const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;

          return min <= giaNhapSauVat && giaNhapSauVat <= max;
        });
      }

      let thangSoBanLe = getThangSoBanLe(res);
      let giaKhongBaoHiem = giaNhapSauVat * (1 + (thangSoBanLe || 0) / 100);
      let thanhTienTruocVat = roundToDigits(value * data?.soLuongSoCap || 0, 3);
      let thanhTienSuaDoi = roundToDigits(
        giaNhapSauVat * data?.soLuongSoCap || 0,
        3
      );

      newData = {
        ...newData,
        giaNhapSauVat: giaNhapSauVat,
        ...(dataKHONG_SUA_GIA_BH_KBH?.eval()
          ? {}
          : {
              giaBaoHiem: tinhDonGiaBaoHiem(data, giaNhapSauVat),
              giaKhongBaoHiem: nhaThuoc
                ? Math.floor(giaKhongBaoHiem)
                : roundToDigits(giaKhongBaoHiem, 3),
            }),

        thangSoBanLe,
        thanhTienSuaDoi: roundToDigits(
          giaNhapSauVat * data?.soLuongSoCap || 0,
          3
        ),
        thanhTienTruocVat: thanhTienTruocVat,
        thanhTienSuaDoiTruocVat: thanhTienTruocVat,
        thanhTienVat: tinhThanhTienVat(thanhTienSuaDoi, thanhTienTruocVat),
        ...(thongTinPhieu.loaiChietKhau == 20
          ? {
              giaTruocVatChuaChietKhau: tinhGiaTruocVATChuaChietKhau(
                {
                  tienChietKhau: data.loNhap?.tienChietKhau,
                  giaNhapTruocVat: value,
                  soLuongSoCap: data?.soLuongSoCap,
                  phanTramChietKhau: data.loNhap?.phanTramChietKhau,
                },
                3
              ),
            }
          : {}),
      };
    } else if (key === "phanTramChietKhau") {
      const giaNhapTruocVat = tinhGiaTruocVAT(
        {
          giaTruocVatChuaChietKhau: data?.loNhap?.giaTruocVatChuaChietKhau,
          tienChietKhau: null,
          soLuongSoCap: data?.soLuongSoCap,
          phanTramChietKhau: value,
        },
        3
      );

      newData = {
        ...newData,
        tienChietKhau: null,
        ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
      };
    } else if (key === "tienChietKhau") {
      const giaNhapTruocVat = tinhGiaTruocVAT(
        {
          giaTruocVatChuaChietKhau: data?.loNhap?.giaTruocVatChuaChietKhau,
          tienChietKhau: value,
          soLuongSoCap: data?.soLuongSoCap,
          phanTramChietKhau: null,
        },
        3
      );

      newData = {
        ...newData,
        phanTramChietKhau: null,
        ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
      };
    } else if (key === "soLuongSoCap") {
      if (
        data?.quyetDinhThauId &&
        value > (data?.chiTietThau?.soLuongConLai || 0)
      ) {
        message.error(t("kho.slNhapVaoLonHonSLConLaiCuaThau"));
      }
    } else if (key === "ngayHanSuDung") {
      newData.ngayHanSuDung = newData.ngayHanSuDung
        ? newData.ngayHanSuDung.format("YYYY-MM-DD")
        : null;
    } else if (key === "giaTruocVatChuaChietKhau") {
      const giaNhapTruocVat = tinhGiaTruocVAT(
        {
          giaTruocVatChuaChietKhau: value,
          tienChietKhau: data?.loNhap?.tienChietKhau,
          soLuongSoCap: data?.soLuongSoCap,
          phanTramChietKhau: data?.loNhap?.phanTramChietKhau,
        },
        3
      );

      newData = {
        ...newData,
        ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
      };
    } else if (key === "thanhTienSuaDoiTruocVat") {
      newData.thanhTienSuaDoiTruocVat = value;
      newData = {
        ...newData,
        thanhTienSuaDoiTruocVat: value,
        thanhTienVat: tinhThanhTienVat(data.thanhTienSuaDoi, value),
      };
    }
    if (data?.vatTuBoId > 0) {
      let dataBo = dsNhapXuatChiTiet.find((item) =>
        thongTinPhieu?.quyetDinhThauId
          ? item?.quyetDinhThauChiTietId === data?.vatTuBoId
          : item.dichVuId === data?.vatTuBoId
      );
      const index = dataBo?.dsNhapXuatChiTiet.findIndex((item) =>
        data.id ? item.id === data.id : item.detachId === data.detachId
      );
      if (index !== -1) {
        const item = dataBo?.dsNhapXuatChiTiet[index];
        if (key === "thanhTienSuaDoi") {
          item.thanhTienSuaDoi = value;
          item.thanhTienVat = tinhThanhTienVat(
            value,
            item.thanhTienSuaDoiTruocVat
          );
        }
        if (["boBatBuocHsd", "boBatBuocSoLo"].includes(key)) {
          item[key] = value;
        }
        item.loNhap = { ...item.loNhap, ...newData };
        [
          "thanhTienSuaDoi",
          "thanhTienVat",
          "thanhTienSuaDoiTruocVat",
          "thanhTienTruocVat",
        ].forEach((key) => {
          item[key] = newData[key] ?? item[key] ?? data[key] ?? 0;
        });
        item.isFocus = false;
        setIsFormChange(true);
        updateData({
          dsNhapXuatChiTiet: [
            ...dsNhapXuatChiTiet.map((item) => {
              return { ...item, isFocus: false };
            }),
          ],
        });
      }
    } else {
      const index = dsNhapXuatChiTiet.findIndex((item) =>
        data.id ? item.id === data.id : item.detachId === data.detachId
      );
      if (index !== -1) {
        const item = dsNhapXuatChiTiet[index];
        if (key === "thanhTienSuaDoi") {
          item.thanhTienSuaDoi = value;
          item.thanhTienVat = tinhThanhTienVat(
            value,
            item.thanhTienSuaDoiTruocVat
          );
        }
        if (["boBatBuocHsd", "boBatBuocSoLo"].includes(key)) {
          item[key] = value;
        }
        item.loNhap = { ...item.loNhap, ...newData };
        item.isFocus = false;
        [
          "thanhTienSuaDoi",
          "thanhTienVat",
          "thanhTienSuaDoiTruocVat",
          "thanhTienTruocVat",
        ].forEach((key) => {
          item[key] = newData[key] ?? item[key] ?? data[key] ?? 0;
        });
        setIsFormChange(true);
        updateData({
          dsNhapXuatChiTiet: [...dsNhapXuatChiTiet],
        });
      }
    }
  };

  const changeSlQuyDoi = (key, data) => (value) => {
    const index = dsNhapXuatChiTiet.findIndex((item) =>
      data.id ? item.id === data.id : item.detachId === data.detachId
    );

    if (index !== -1) {
      let soLuongSoCap;
      let newData = { [key]: value };
      let _dsNhapXuatChiTiet = cloneDeep(dsNhapXuatChiTiet);
      let soLuongQuyDoi =
        key === "soLuongQuyDoi"
          ? value
          : _dsNhapXuatChiTiet[index]["soLuongQuyDoi"];

      let heSoQuyDoi =
        key === "heSoQuyDoi" ? value : _dsNhapXuatChiTiet[index]["heSoQuyDoi"];

      if (soLuongQuyDoi && heSoQuyDoi) {
        soLuongSoCap = roundToDigits(soLuongQuyDoi * heSoQuyDoi, 3);
      }

      _dsNhapXuatChiTiet[index] = {
        ..._dsNhapXuatChiTiet[index],
        ...newData,
        isFocus: false,
      };
      if (isNumber(soLuongSoCap)) {
        if (data?.vatTuBoId > 0) {
          let dataBo = _dsNhapXuatChiTiet.find((item) =>
            thongTinPhieu?.quyetDinhThauId
              ? item?.quyetDinhThauChiTietId === data?.vatTuBoId
              : item.dichVuId === data?.vatTuBoId
          );
          const index = dataBo.dsNhapXuatChiTiet.findIndex((item) =>
            data.id ? item.id === data.id : item.detachId === data.detachId
          );
          if (index !== -1) {
            const item = dataBo.dsNhapXuatChiTiet[index];
            item.soLuongSoCap = soLuongSoCap;

            if (thongTinPhieu.loaiChietKhau == 20) {
              const giaNhapTruocVat = tinhGiaTruocVAT(
                {
                  giaTruocVatChuaChietKhau:
                    item?.loNhap?.giaTruocVatChuaChietKhau,
                  tienChietKhau: item?.loNhap?.tienChietKhau,
                  soLuongSoCap: soLuongSoCap,
                  phanTramChietKhau: item?.loNhap?.phanTramChietKhau,
                },
                3
              );

              item.loNhap = {
                ...item.loNhap,
                ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
              };
            } else {
              const giaKhongBaoHiem =
                (item?.loNhap.giaNhapSauVat *
                  (100 + (item?.loNhap.thangSoBanLe || 0))) /
                100;

              item.thanhTienSuaDoi =
                item?.loNhap?.giaNhapSauVat * soLuongSoCap || 0;
              let thanhTienTruocVat = roundToDigits(
                item?.loNhap?.giaNhapTruocVat * soLuongSoCap || 0,
                3
              );
              item.thanhTienSuaDoiTruocVat = thanhTienTruocVat;
              item.thanhTienVat = tinhThanhTienVat(
                item.thanhTienSuaDoi,
                thanhTienTruocVat
              );
              item.thanhTienTruocVat = thanhTienTruocVat;
              item.loNhap = {
                ...item.loNhap,
                ...(!(
                  layGiaTuDanhMuc && item.loaiDichVu === LOAI_DICH_VU.VAT_TU
                ) && {
                  giaKhongBaoHiem: nhaThuoc
                    ? Math.floor(giaKhongBaoHiem)
                    : roundToDigits(giaKhongBaoHiem, 3),
                }),
              };
            }
            if (soLuongSoCap > 0) {
              item.isFocus = false;
            }
          }
        } else {
          const index = _dsNhapXuatChiTiet.findIndex((item) =>
            data.id ? item.id === data.id : item.detachId === data.detachId
          );
          if (index !== -1) {
            const item = _dsNhapXuatChiTiet[index];
            item.soLuongSoCap = soLuongSoCap;

            if (thongTinPhieu.loaiChietKhau == 20) {
              if (soLuongSoCap > 0) {
                const giaNhapTruocVat = tinhGiaTruocVAT(
                  {
                    giaTruocVatChuaChietKhau:
                      item?.loNhap?.giaTruocVatChuaChietKhau,
                    tienChietKhau: item?.loNhap?.tienChietKhau,
                    soLuongSoCap: soLuongSoCap,
                    phanTramChietKhau: item?.loNhap?.phanTramChietKhau,
                  },
                  3
                );

                item.loNhap = {
                  ...item.loNhap,
                  ...capNhatTheoGiaNhapTruocVat(giaNhapTruocVat),
                };
                item.thanhTienSuaDoi = item?.loNhap?.thanhTienSuaDoi;
              }
            } else {
              const giaKhongBaoHiem =
                (item?.loNhap.giaNhapSauVat *
                  (100 + (item?.loNhap.thangSoBanLe || 0))) /
                100;

              item.thanhTienSuaDoi = roundToDigits(
                item?.loNhap?.giaNhapSauVat * soLuongSoCap || 0,
                3
              );

              let thanhTienTruocVat = roundToDigits(
                item?.loNhap?.giaNhapTruocVat * soLuongSoCap || 0,
                3
              );
              item.thanhTienTruocVat = thanhTienTruocVat;
              item.thanhTienSuaDoiTruocVat = thanhTienTruocVat;
              item.thanhTienVat = tinhThanhTienVat(
                item.thanhTienSuaDoi,
                thanhTienTruocVat
              );

              item.loNhap = {
                ...item.loNhap,
                ...(!(
                  layGiaTuDanhMuc && item.loaiDichVu === LOAI_DICH_VU.VAT_TU
                ) && {
                  giaKhongBaoHiem: nhaThuoc
                    ? Math.floor(giaKhongBaoHiem)
                    : roundToDigits(giaKhongBaoHiem, 3),
                }),
              };
            }
            if (soLuongSoCap > 0) {
              item.isFocus = false;
            }
          }
        }
      }
      setIsFormChange(true);
      updateData({
        dsNhapXuatChiTiet: [..._dsNhapXuatChiTiet],
      });
    }
  };

  useEffect(() => {
    let ds = dsNhapXuatChiTiet?.filter((x) => x.isFocus);
    if (ds?.length) {
      let item = ds?.[ds.length - 1]?.id
        ? ds?.[ds.length - 1]?.id
        : ds?.[ds.length - 1]?.detachId;
      document.getElementById(item).focus();
    }
  }, [dsNhapXuatChiTiet]);

  const onClickSort = (key, value) => {
    if (!dsNhapXuatChiTiet || dsNhapXuatChiTiet?.length < 1) return;
    onSortChange({
      [key]: value,
    });
  };

  // Helper function để set focus cho hàng hóa khi click vào input
  const setFocusItem = (data) => {
    const newDsNhapXuatChiTiet = dsNhapXuatChiTiet.map((item) => ({
      ...item,
      isFocus: (item.id || item.detachId) === (data.id || data.detachId),
    }));

    updateData({
      dsNhapXuatChiTiet: newDsNhapXuatChiTiet,
    });
  };

  const renderProductInfo = (data) => {
    const _disabledDonGia = dataKHONG_SUA_GIA_BH_KBH?.eval();

    const renderSoLo = () => {
      return (
        <div className="product_info-line-item flex-1">
          {dataHIEN_THI_CHECKBOX_BO_BAT_BUOC_SOLO_HSD?.eval() ? (
            <Popover
              placement="top"
              content={
                <>
                  <Checkbox
                    checked={data?.boBatBuocSoLo}
                    onChange={(e) =>
                      changeDSLo("boBatBuocSoLo", data)(e?.target?.checked)
                    }
                  >
                    {t("kho.boBatBuocSoLo")}
                  </Checkbox>
                </>
              }
            >
              <span
                className="product_info-line-item-label"
                style={{
                  color: `${
                    (data.loNhap?.soLo &&
                      data?.loaiDichVu !== LOAI_DICH_VU.THUOC) ||
                    data?.boBatBuocSoLo
                      ? ""
                      : "red"
                  }`,
                }}
              >
                {t("kho.soLo")}:
              </span>
            </Popover>
          ) : (
            <span
              className="product_info-line-item-label"
              style={{
                color: `${
                  data.loNhap?.soLo && data?.loaiDichVu !== LOAI_DICH_VU.THUOC
                    ? ""
                    : "red"
                }`,
              }}
            >
              {t("kho.soLo")}:
            </span>
          )}

          <Input
            onChange={(e) => {
              let value = e.target.value;
              if (dataTU_DONG_IN_HOA_SO_LO?.eval()) {
                value = value?.toUpperCase();
              }

              changeDSLo("soLo", data)(value);
            }}
            placeholder={t("kho.nhapSoLo")}
            value={data?.loNhap?.soLo}
            style={{ width: "none", flex: 1 }}
            disabled={isReadOnlyHangHoa}
          />
        </div>
      );
    };

    const renderNgayHSD = () => {
      return (
        <div className="product_info-line-item flex-1">
          {dataHIEN_THI_CHECKBOX_BO_BAT_BUOC_SOLO_HSD?.eval() ? (
            <Popover
              placement="top"
              content={
                <>
                  <Checkbox
                    checked={data?.boBatBuocHsd}
                    onChange={(e) =>
                      changeDSLo("boBatBuocHsd", data)(e?.target?.checked)
                    }
                  >
                    {t("kho.boBatBuocHSD")}
                  </Checkbox>
                </>
              }
            >
              <span
                className="product_info-line-item-label"
                style={{
                  color: `${
                    (data.loNhap?.ngayHanSuDung &&
                      data?.loaiDichVu !== LOAI_DICH_VU.THUOC) ||
                    data?.boBatBuocHsd
                      ? ""
                      : "red"
                  }`,
                }}
              >
                {t("kho.hsd")}:
              </span>
            </Popover>
          ) : (
            <span
              className="product_info-line-item-label"
              style={{
                color: `${
                  data.loNhap?.ngayHanSuDung &&
                  data?.loaiDichVu !== LOAI_DICH_VU.THUOC
                    ? ""
                    : "red"
                }`,
              }}
            >
              {t("kho.hsd")}:
            </span>
          )}

          <DateTimePicker
            format={"DD/MM/YYYY"}
            value={
              data?.loNhap?.ngayHanSuDung && moment(data?.loNhap?.ngayHanSuDung)
            }
            onChange={changeDSLo("ngayHanSuDung", data)}
            style={{ width: "none", flex: 1 }}
            showTime={false}
            disabled={isReadOnlyHangHoa}
            allowClear
          />
        </div>
      );
    };

    return (
      <div className="product_info">
        <div className="product_info-line">
          {thongTinPhieu.loaiChietKhau === 20 ? (
            <div className="product_info-line-item flex-15">
              <Switch
                checkedChildren="đ"
                unCheckedChildren="%"
                checked={data?.loNhap?.tienChietKhau !== null}
                onChange={(value) => {
                  if (value) {
                    changeDSLo("tienChietKhau", data)(0);
                  } else {
                    changeDSLo("phanTramChietKhau", data)(0);
                  }
                }}
                disabled={isReadOnlyHangHoa}
              />
              <span
                style={{ marginLeft: 5 }}
                className="product_info-line-item-label"
              >
                {t("kho.chietKhau")}:
              </span>
              <InputNumber
                min={0}
                formatter={(value) => formatterFunc(value)}
                parser={(value) => parserNumber(value, 3)}
                onChange={(e) => {
                  const value =
                    !isNaN(parseFloat(e)) && parseFloat(e) > 0
                      ? parseFloat(e) % 1.0 === 0.0
                        ? parseInt(e)
                        : parseFloat(e)
                      : 0;

                  if (data?.loNhap?.tienChietKhau !== null) {
                    changeDSLo("tienChietKhau", data)(value);
                  } else {
                    changeDSLo("phanTramChietKhau", data)(value);
                  }
                }}
                placeholder={t("kho.nhapChietKhau")}
                value={
                  data?.loNhap?.tienChietKhau !== null
                    ? data?.loNhap?.tienChietKhau
                    : data?.loNhap?.phanTramChietKhau
                }
                style={{ width: "none", flex: 1 }}
                readOnly={isReadOnlyHangHoa}
              />
            </div>
          ) : (
            <div className="product_info-line-item flex-1">
              <span className="product_info-line-item-label">
                {t("kho.quyetDinhThau.maHieu")}:
              </span>
              <InputTimeout
                onChange={changeDSLo("maKyHieu", data)}
                placeholder={t("kho.nhapMaHieu")}
                value={data?.loNhap?.maKyHieu}
                style={{ width: "none", flex: 1 }}
                disabled={isReadOnlyHangHoa}
              />
            </div>
          )}
          <div className="product_info-line-item flex-15">
            <span className="product_info-line-item-label">
              {t("kho.quyetDinhThau.soVisa")}:
            </span>
            <InputTimeout
              placeholder={t("danhMuc.nhapSoVisa")}
              value={
                data.chiTietThau?.soVisa ||
                data.loNhap?.quyetDinhThauChiTiet?.soVisa ||
                data.loNhap?.soVisa ||
                data.dichVu?.soVisa
              }
              style={{ width: "none", flex: 1 }}
              disabled={true}
            />
          </div>
          {!nhapKhongTheoThau && (
            <div className="product_info-line-item flex-15">
              <span className="product_info-line-item-label">
                {t("kho.quyetDinhThau.tenTrungThau")}:
              </span>
              <Tooltip title={data?.chiTietThau?.tenTrungThau}>
                <span style={{ display: "inline-block", flex: 1 }}>
                  <InputTimeout
                    onChange={changeDSLo("tenTrungThau", data)}
                    placeholder={t("kho.quyetDinhThau.nhapTenTrungThau")}
                    value={data?.chiTietThau?.tenTrungThau}
                    style={{ width: "none", flex: 1 }}
                    disabled={true}
                  />
                </span>
              </Tooltip>
            </div>
          )}
          {renderSoLo()}
          {renderNgayHSD()}
          <div className="product_info-line-item flex-basic-150">
            <span className="product_info-line-item-label">
              {t("danhMuc.thangSoBanLe")}:
            </span>
            <InputNumber
              style={{ width: "none", flex: 1 }}
              min={0}
              formatter={(value) => formatterFunc(value)}
              parser={(value) => parserNumber(value, 3)}
              value={data.loNhap?.thangSoBanLe}
              readOnly
            />
          </div>
          {(isShowVatBan || true) && (
            <div className="product_info-line-item flex-basic-150">
              <span className="product_info-line-item-label">
                {t("kho.vatBan")}:
              </span>
              <InputNumber
                min={0}
                parser={(value) => parserNumber(value, 3)}
                formatter={(value) => formatterFunc(value, "")}
                onChange={(e) => {
                  const value =
                    !isNaN(parseFloat(e)) && parseFloat(e) > 0
                      ? parseFloat(e) % 1.0 === 0.0
                        ? parseInt(e)
                        : parseFloat(e)
                      : 0;
                  changeDSLo("vatBan", data)(value);
                }}
                placeholder={t("kho.nhapVAT")}
                value={data?.loNhap?.vatBan}
                style={styleInput}
                readOnly={
                  isReadOnlyHangHoa ||
                  (dataKHONG_SUA_VAT_BAN_KHI_NHAP_KHO?.eval() &&
                    finishKHONG_SUA_VAT_BAN_KHI_NHAP_KHO)
                }
              />
            </div>
          )}
        </div>

        <div className="product_info-line" style={{ marginTop: "10px" }}>
          <div className="product_info-line-item flex-1">
            <span className="product_info-line-item-label">
              {t("danhMuc.donGiaBh")}:
            </span>
            <InputNumber
              min={0}
              onChange={changeDSLo("giaBaoHiem", data)}
              placeholder={t("khoMau.nhapDonGiaBaoHiem")}
              value={data?.loNhap?.giaBaoHiem}
              formatter={(value) => formatterFunc(value)}
              parser={(value) => parserNumber(value, 3)}
              style={{ width: "none", flex: 1 }}
              readOnly={_disabledDonGia || isReadOnlyHangHoa}
            />
            {checkRole([ROLES["KHO"].HIEN_THI_TY_LE_THANH_TOAN_DANH_MUC]) && (
              <Popover
                placement="bottomRight"
                content={
                  <>
                    <div>
                      {t("kho.quyetDinhThau.tiLeThanhToanBHDanhMuc")}:{" "}
                      {data?.tyLeBhTt}
                    </div>
                    <div>
                      {t("kho.quyetDinhThau.tiLeThanhToanDichVuDanhMuc")}:{" "}
                      {data?.tyLeTtDv}
                    </div>
                  </>
                }
              >
                <div style={{ marginLeft: "5px" }}>
                  <SVG.IcInfo style={{ width: 20, height: 20 }} />
                </div>
              </Popover>
            )}
          </div>
          <div
            className={`product_info-line-item ${
              !nhapKhongTheoThau ? "flex-15" : "flex-1"
            }`}
          >
            <span className="product_info-line-item-label">
              {t("danhMuc.donGiaKhongBh")}:
            </span>
            <InputNumber
              min={0}
              onChange={changeDSLo("giaKhongBaoHiem", data)}
              formatter={(value) => formatterFunc(value)}
              parser={(value) => parserNumber(value, 3)}
              placeholder={t("khoMau.nhapDonGiaKhongBaoHiem")}
              value={data?.loNhap?.giaKhongBaoHiem}
              style={{ width: "none", flex: 1 }}
              readOnly={_disabledDonGia || isReadOnlyHangHoa}
            />
          </div>
          <div className="product_info-line-item flex-1">
            <span className="product_info-line-item-label">
              {t("danhMuc.phuThu")}:
            </span>
            <InputNumber
              className={"per100"}
              min={0}
              onChange={(e) => {
                const value =
                  !isNaN(parseFloat(e)) && parseFloat(e) > 0
                    ? parseFloat(e) % 1.0 === 0.0
                      ? parseInt(e)
                      : parseFloat(e)
                    : 0;
                changeDSLo("giaPhuThu", data)(value);
              }}
              formatter={(value) => formatterFunc(value)}
              parser={(value) => parserNumber(value, 3)}
              placeholder={t("khoMau.nhapPhuThu")}
              value={data.loNhap?.giaPhuThu}
              readOnly={isReadOnlyHangHoa}
            />
          </div>
          <div
            className={`product_info-line-item ${
              !nhapKhongTheoThau ? "flex-15" : "flex-1"
            }`}
          >
            <span
              className="product_info-line-item-label"
              style={{
                color: `${data.loNhap?.xuatXuId ? "" : "red"}`,
              }}
            >
              {t("danhMuc.xuatXu")}:
            </span>

            <Select
              className="per100"
              placeholder={t("kho.chonXuatXu")}
              data={listAllXuatXu}
              onChange={changeDSLo("xuatXuId", data)}
              disabled={!!thongTinPhieu.quyetDinhThauId || isReadOnlyHangHoa}
              value={data.loNhap?.xuatXuId}
            />
          </div>
        </div>
        <div className="product_info-line" style={{ marginTop: "10px" }}>
          <div className="product_info-line-item flex-03">
            <span className="product_info-line-item-label">
              {t("common.nhaSanXuat")}:
            </span>
            <Select
              className="nha-san-xuat"
              placeholder={t("danhMuc.chonNhaSanXuat")}
              data={listAllNhaSanXuat}
              onChange={changeDSLo("nhaSanXuatId", data)}
              disabled={!!thongTinPhieu.quyetDinhThauId || isReadOnlyHangHoa}
              value={data.loNhap?.nhaSanXuatId}
              style={{ width: "none", flex: 1 }}
            />
          </div>
        </div>
      </div>
    );
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      key: "stt",
      width: 50,
      align: "center",
      ellipsis: {
        showTitle: false,
      },
      render: (_, data, index) => {
        return index % 2 === 1 ? (
          {
            children: renderProductInfo(data),
            props: {
              colSpan: isNhapNcc ? 10 : 9,
            },
          }
        ) : (
          <div> {data?.index}</div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.maHangHoa")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["dichVu.ma"] || 0}
        />
      ),
      dataIndex: "ma",
      key: "ma",
      width: 120,
      render: (item, data, index) => {
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          // <PopoverHangHoa data={data}>
          <div className="item-ma">{item}</div>
          // </PopoverHangHoa>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.tenHangHoa")}
          sort_key="dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["dichVu.ten"] || 0}
        />
      ),
      dataIndex: "ten",
      key: "ten",
      width: 450,
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div>
            <PopoverHangHoa data={data}>
              <div className="item-ten">
                {getTenHangHoa(data, dataNHAP_KICH_CO_VAT_TU)}
              </div>
            </PopoverHangHoa>
            <InputTimeout
              onChange={changeDSLo("ghiChu", data)}
              value={data?.loNhap?.ghiChu}
              placeholder={t("common.ghiChu")}
              disabled={isReadOnlyHangHoa}
            />
            <div>
              {!!listAllKichCo?.length && dataNHAP_KICH_CO_VAT_TU != "0" && (
                <Select
                  className="mt-10"
                  data={listAllKichCo}
                  onChange={(value, item) => {
                    changeDSLo("kichCoVtId", data)(value, item);
                  }}
                  placeholder={t("kho.chonVatTuKichCo")}
                />
              )}
            </div>
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sl")}
          sort_key="soLuongSoCap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.soLuongSoCap || 0}
        />
      ),
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      width: 150,
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1 + (data?.tenDvtSoCap || data?.dvtSoCap?.ten),
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="flex gap-5 input-so-luong">
            <div>
              <InputNumber
                id={data.id ? data.id : data.detachId}
                min={0}
                placeholder={t("common.nhapSoLuong")}
                className="mr-5 flex1"
                value={data?.soLuongSoCap}
                onFocus={() => setFocusItem(data)} // Set focus khi click vào input
                {...(isNhapThapPhan && data.loaiDichVu == loaiDVNhapThapPhan
                  ? {}
                  : {
                      parser: (value) => parserNumber(value, 3),
                      formatter: (value) => formatterFunc(value),
                    })}
                onChange={(e) => {
                  const value =
                    !isNaN(parseFloat(e)) && parseFloat(e) > 0
                      ? parseFloat(e) % 1.0 === 0.0
                        ? parseInt(e)
                        : parseFloat(e)
                      : 0;
                  changeSoLuong(data)(value);
                }}
                readOnly={isReadOnlyHangHoa && !isSuaSoLuong}
              />
              <div>{data?.tenDvtSoCap || data?.dvtSoCap?.ten}</div>
            </div>
            {isNhapNcc && (
              <Popover
                trigger="click"
                open={
                  openPopover.itemId === (data.id || data.detachId) &&
                  openPopover.type === "p1"
                }
                onOpenChange={(visible) => {
                  const itemId = data.id || data.detachId;
                  setOpenPopover(
                    visible
                      ? { itemId, type: "p1" }
                      : { itemId: null, type: null }
                  );
                }}
                placement="bottomRight"
                overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
                content={
                  <div>
                    <label>{t("editor.heSoQuyDoi")}</label>
                    <InputNumber
                      min={0}
                      value={data?.heSoQuyDoi}
                      style={styleInput}
                      onChange={(e) => {
                        changeSlQuyDoi("heSoQuyDoi", data, index)(e);
                      }}
                      readOnly={isReadOnlyHangHoa}
                    />
                    <hr
                      style={{
                        borderTop: "1px solid #c5cad3",
                        marginLeft: "-12px",
                        marginRight: "-12px",
                      }}
                    />
                    <label>{t("kho.slQuyDoi")}</label>
                    <InputNumber
                      min={0}
                      formatter={(value) => formatterFunc(value)}
                      parser={(value) => parserNumber(value, 3)}
                      value={data?.soLuongQuyDoi}
                      style={styleInput}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;
                        changeSlQuyDoi("soLuongQuyDoi", data, index)(value);
                      }}
                      readOnly={isReadOnlyHangHoa}
                    />
                  </div>
                }
              >
                <SVG.IcMayTinh />
              </Popover>
            )}
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={
            thongTinPhieu.loaiChietKhau == 20
              ? t("kho.giaTruocVATChuaChietKhau")
              : t("khoMau.giaSauVAT")
          }
          sort_key="loNhap.giaNhapSauVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["loNhap.giaNhapSauVat"] || 0}
        />
      ),
      dataIndex: "giaNhapSauVat",
      key: "giaNhapSauVat",
      width: 150,
      render: (item, data, index) => {
        const giaNhapSauVatThau =
          data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat ??
          data?.chiTietThau?.giaNhapSauVat;
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <>
            <div className={classNames({ flex: nhapNhanhGiaSauVat })}>
              {nhapNhanhGiaSauVat && (
                <>
                  {thongTinPhieu.loaiChietKhau == 20 ? (
                    <InputNumber
                      formatter={(value) => formatterFunc(value)}
                      parser={(value) => parserNumber(value, 3)}
                      style={{ width: "100%", textAlign: "right" }}
                      value={data?.loNhap?.giaTruocVatChuaChietKhau}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;

                        changeDSLo("giaTruocVatChuaChietKhau", data)(value);
                      }}
                    />
                  ) : (
                    <InputNumber
                      formatter={(value) => formatterFunc(value)}
                      parser={(value) => parserNumber(value, 3)}
                      style={{ width: "100%", textAlign: "right" }}
                      value={data?.loNhap?.giaNhapSauVat || 0}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;
                        changeDSLo("giaNhapSauVat", data)(value);
                      }}
                    />
                  )}
                </>
              )}
              <Popover
                trigger="click"
                placement="bottomRight"
                open={
                  openPopover.itemId === (data.id || data.detachId) &&
                  openPopover.type === "p2"
                }
                onOpenChange={(visible) => {
                  const itemId = data.id || data.detachId;
                  setOpenPopover(
                    visible
                      ? { itemId, type: "p2" }
                      : { itemId: null, type: null }
                  );
                }}
                overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
                content={
                  <div>
                    {/* loại chiết khấu 20 = trước VAT thì hiển giá trước VAT chưa chiết khấu */}
                    {thongTinPhieu.loaiChietKhau == 20 && (
                      <>
                        <label>{t("kho.giaTruocVATChuaChietKhau")}</label>
                        <InputNumber
                          min={0}
                          formatter={(value) => formatterFunc(value)}
                          parser={(value) => parserNumber(value, 3)}
                          onChange={(e) => {
                            const value =
                              !isNaN(parseFloat(e)) && parseFloat(e) > 0
                                ? parseFloat(e) % 1.0 === 0.0
                                  ? parseInt(e)
                                  : parseFloat(e)
                                : 0;

                            changeDSLo("giaTruocVatChuaChietKhau", data)(value);
                          }}
                          placeholder={t("kho.nhapGiaTruocVATChuaChietKhau")}
                          value={data?.loNhap?.giaTruocVatChuaChietKhau}
                          style={styleInput}
                          readOnly={isReadOnlyHangHoa}
                        />
                        <hr
                          style={{
                            borderTop: "1px solid #c5cad3",
                            marginLeft: "-12px",
                            marginRight: "-12px",
                          }}
                        />
                      </>
                    )}
                    <label>{t("kho.giaTruocVAT")}</label>
                    <InputNumber
                      min={0}
                      formatter={(value) => formatterFunc(value)}
                      parser={(value) => parserNumber(value, 3)}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;
                        changeDSLo("giaNhapTruocVat", data)(value);
                      }}
                      placeholder={t("kho.nhapGiaTruocVAT")}
                      value={data?.loNhap?.giaNhapTruocVat}
                      style={styleInput}
                      readOnly={isReadOnlyHangHoa}
                    />
                    {/* loại chiết khấu 20 = trước VAT thì hiển tiền chiết khấu */}
                    {thongTinPhieu.loaiChietKhau == 20 && (
                      <>
                        <hr
                          style={{
                            borderTop: "1px solid #c5cad3",
                            marginLeft: "-12px",
                            marginRight: "-12px",
                          }}
                        />
                        <label>{t("kho.tienChietKhau")}</label>
                        <InputNumber
                          min={0}
                          readOnly
                          formatter={(value) => formatterFunc(value)}
                          parser={(value) => parserNumber(value, 3)}
                          onChange={(e) => {
                            const value =
                              !isNaN(parseFloat(e)) && parseFloat(e) > 0
                                ? parseFloat(e) % 1.0 === 0.0
                                  ? parseInt(e)
                                  : parseFloat(e)
                                : 0;

                            if (data?.loNhap?.tienChietKhau !== null) {
                              changeDSLo("tienChietKhau", data)(value);
                            } else {
                              changeDSLo("phanTramChietKhau", data)(value);
                            }
                          }}
                          placeholder={t("kho.nhapChietKhau")}
                          value={tinhTienChietKhau(
                            {
                              tienChietKhau: data?.loNhap?.tienChietKhau,
                              phanTramChietKhau:
                                data?.loNhap?.phanTramChietKhau,
                              giaTruocVatChuaChietKhau:
                                data?.loNhap?.giaTruocVatChuaChietKhau,
                              soLuongSoCap: data?.soLuongSoCap,
                            },
                            3
                          )}
                          style={styleInput}
                        />
                      </>
                    )}
                    <hr
                      style={{
                        borderTop: "1px solid #c5cad3",
                        marginLeft: "-12px",
                        marginRight: "-12px",
                      }}
                    />
                    <label>VAT</label>
                    <InputNumber
                      min={0}
                      parser={(value) => parserNumber(value, 3)}
                      formatter={(value) => formatterFunc(value)}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;
                        changeDSLo("vat", data)(value);
                      }}
                      placeholder={t("kho.nhapVAT")}
                      value={data?.loNhap?.vat}
                      style={styleInput}
                      readOnly={isReadOnlyHangHoa}
                    />
                    <hr
                      style={{
                        borderTop: "1px solid #c5cad3",
                        marginLeft: "-12px",
                        marginRight: "-12px",
                      }}
                    />
                    <label>{t("khoMau.giaSauVAT")}</label>
                    <InputNumber
                      min={0}
                      formatter={(value) => formatterFunc(value)}
                      parser={(value) => parserNumber(value, 3)}
                      onChange={(e) => {
                        const value =
                          !isNaN(parseFloat(e)) && parseFloat(e) > 0
                            ? parseFloat(e) % 1.0 === 0.0
                              ? parseInt(e)
                              : parseFloat(e)
                            : 0;
                        changeDSLo("giaNhapSauVat", data)(value);
                      }}
                      placeholder={t("kho.nhapGiaSauVAT")}
                      value={data?.loNhap?.giaNhapSauVat}
                      style={styleInput}
                      readOnly={isReadOnlyHangHoa}
                    />
                    <hr
                      style={{
                        borderTop: "1px solid #c5cad3",
                        marginLeft: "-12px",
                        marginRight: "-12px",
                      }}
                    />
                  </div>
                }
              >
                <div style={{ display: "flex" }}>
                  {!nhapNhanhGiaSauVat && (
                    <>
                      {thongTinPhieu.loaiChietKhau == 20 ? (
                        <InputNumber
                          formatter={(value) => formatterFunc(value)}
                          parser={(value) => parserNumber(value, 3)}
                          style={{ width: "100%", textAlign: "right" }}
                          value={data?.loNhap?.giaTruocVatChuaChietKhau}
                          readOnly
                        />
                      ) : (
                        <Tooltip
                          title={formatterFunc(
                            `${data?.loNhap?.giaNhapSauVat || 0}`
                          )}
                        >
                          <InputNumber
                            formatter={(value) => formatterFunc(value)}
                            parser={(value) => parserNumber(value, 3)}
                            style={{ width: "100%", textAlign: "right" }}
                            value={data?.loNhap?.giaNhapSauVat || 0}
                            readOnly
                          />
                        </Tooltip>
                      )}
                    </>
                  )}
                  <SVG.IcMayTinh />
                </div>
              </Popover>
            </div>
            {!nhapKhongTheoThau &&
              isNhapNcc &&
              giaNhapSauVatThau &&
              giaNhapSauVatThau !== data?.loNhap?.giaNhapSauVat && (
                <div className="error label-error ">
                  {t("kho.giaThauGia", {
                    gia: giaNhapSauVatThau?.formatPrice(),
                  })}
                </div>
              )}
          </>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khoMau.thanhTien")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.thanhTien || 0}
        />
      ),
      dataIndex: "thanhTien",
      key: "thanhTien",
      width: 140,
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1 + data?.ten,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div>
            <Popover
              trigger="click"
              placement="bottomRight"
              overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
              open={
                openPopover.itemId === (data.id || data.detachId) &&
                openPopover.type === "p3"
              }
              onOpenChange={(visible) => {
                const itemId = data.id || data.detachId;
                setOpenPopover(
                  visible
                    ? { itemId, type: "p3" }
                    : { itemId: null, type: null }
                );
              }}
              content={
                <div>
                  <label>{t("khoMau.thanhTienSuaDoi")}</label>
                  <InputNumber
                    min={0}
                    formatter={(value) => formatterFunc(value)}
                    parser={(value) => parserNumber(value, 3)}
                    value={data?.thanhTienSuaDoi}
                    style={styleInput}
                    onChange={(e) => {
                      const value =
                        !isNaN(parseFloat(e)) && parseFloat(e) > 0
                          ? parseFloat(e) % 1.0 === 0.0
                            ? parseInt(e)
                            : parseFloat(e)
                          : 0;
                      changeDSLo("thanhTienSuaDoi", data, index)(value);
                    }}
                    readOnly={isReadOnlyHangHoa}
                  />
                </div>
              }
            >
              <div style={{ display: "flex" }}>
                <InputNumber
                  formatter={(value) => formatterFunc(value)}
                  parser={(value) => parserNumber(value, 3)}
                  style={{ width: "100%", textAlign: "right" }}
                  value={data?.thanhTienSuaDoi?.toFixed(3)}
                  readOnly
                />
                <SVG.IcMayTinh />
              </div>
            </Popover>
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={t("khoMau.thanhTienTruocVat")}
          onClickSort={onClickSort}
        />
      ),
      dataIndex: "thanhTienTruocVat",
      key: "thanhTienTruocVat",
      width: 150,
      hidden: !isNhapNcc,
      render: (item, data, index) =>
        index % 2 === 1 ? (
          {
            children: index + 1 + data?.ten,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div>
            <Popover
              trigger="click"
              placement="bottomRight"
              overlayInnerStyle={{ borderRadius: "5px", borderColor: "" }}
              open={
                openPopover.itemId === (data.id || data.detachId) &&
                openPopover.type === "p4"
              }
              onOpenChange={(visible) => {
                const itemId = data.id || data.detachId;
                setOpenPopover(
                  visible
                    ? { itemId, type: "p4" }
                    : { itemId: null, type: null }
                );
              }}
              content={
                <div>
                  <label>{t("kho.thanhTienTruocVatSuaDoi")}</label>
                  <InputNumber
                    min={0}
                    formatter={(value) => formatterFunc(value)}
                    parser={(value) => parserNumber(value, 3)}
                    value={data?.thanhTienSuaDoiTruocVat}
                    style={styleInput}
                    onChange={(e) => {
                      const value =
                        !isNaN(parseFloat(e)) && parseFloat(e) > 0
                          ? parseFloat(e) % 1.0 === 0.0
                            ? parseInt(e)
                            : parseFloat(e)
                          : 0;
                      changeDSLo("thanhTienSuaDoiTruocVat", data, index)(value);
                    }}
                    readOnly={isReadOnlyHangHoa}
                  />
                  <hr
                    style={{
                      borderTop: "1px solid #c5cad3",
                      marginLeft: "-12px",
                      marginRight: "-12px",
                    }}
                  />
                  <label>{t("kho.thanhTienVat")}</label>
                  <InputNumber
                    min={0}
                    formatter={(value) => formatterFunc(value)}
                    parser={(value) => parserNumber(value, 3)}
                    value={data?.thanhTienVat}
                    style={styleInput}
                    readOnly
                  />
                </div>
              }
            >
              <div style={{ display: "flex" }}>
                <InputNumber
                  formatter={(value) => formatterFunc(value)}
                  parser={(value) => parserNumber(value, 3)}
                  style={{ width: "100%", textAlign: "right" }}
                  value={data?.thanhTienTruocVat?.toFixed(3)}
                  readOnly
                />
                <SVG.IcMayTinh />
              </div>
            </Popover>
          </div>
        ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.slDuocPhepMua")}
          sort_key="soLuongDuocPhepMua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["soLuongDuocPhepMua"] || 0}
        />
      ),
      width: 80,
      dataIndex: "soLuongDuocPhepMua",
      key: "soLuongDuocPhepMua",
      show: true,
      i18Name: "kho.quyetDinhThau.slDuocPhepMua",
      render: (_, record, index) => {
        const soLuongDuocPhepMua =
          record?.loNhap?.quyetDinhThauChiTiet?.soLuongDuocPhepMua ??
          record?.chiTietThau?.soLuongDuocPhepMua;
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">{soLuongDuocPhepMua?.formatPrice()}</div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soLuongConLai")}
          sort_key="soLuongConLai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn?.["soLuongConLai"] || 0}
        />
      ),
      dataIndex: "soLuongConLai",
      key: "soLuongConLai",
      width: 80,
      align: "right",
      render: (item, data, index) => {
        const soLuongConLai =
          data?.loNhap?.quyetDinhThauChiTiet?.soLuongConLai ??
          data?.chiTietThau?.soLuongCon;
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">{soLuongConLai?.formatPrice()}</div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("kho.quyetDinhThau.giaSauVatThau")} />,
      dataIndex: "giaSauVatThau",
      key: "giaSauVatThau",
      width: 100,
      align: "right",
      render: (_, data, index) => {
        const giaNhapSauVat =
          data?.loNhap?.quyetDinhThauChiTiet?.giaNhatSauVat ??
          data?.chiTietThau?.giaNhapSauVat;
        return index % 2 === 1 ? (
          {
            children: index + 1,
            props: {
              colSpan: 0,
            },
          }
        ) : (
          <div className="bold">{giaNhapSauVat?.formatPrice()}</div>
        );
      },
    },
    {
      title: <HeaderSearch title="" />,
      key: "delete",
      width: 50,
      align: "center",
      fixed: "right",
      render: (_, data, index) =>
        index % 2 === 0 &&
        !isReadOnlyHangHoa && (
          <div className="icon">
            <SVG.IcDelete
              onClick={() => {
                setIsFormChange(true);
                updateData({
                  dsNhapXuatChiTiet: dsNhapXuatChiTiet.filter((item) =>
                    data.id
                      ? item.id !== data.id
                      : item.detachId !== data.detachId
                  ),
                });
              }}
              className="ic-action"
            />
            {data?.vatTuBo && (
              <SVG.IcAdd
                onClick={() => onShowDsHangHoa(data?.dichVuId, data)}
                className="ic-action"
              />
            )}
          </div>
        ),
    },
  ];

  const dataSource = useMemo(() => {
    let data = [];

    let _dsNhapXuatChiTiet = dsNhapXuatChiTiet;

    //nếu ko có dữ liệu sort thì mặc định sort theo thứ tự nhập
    if (
      !dataSortColumn ||
      Object.values(dataSortColumn).every((x) => x === 0)
    ) {
      _dsNhapXuatChiTiet = orderBy(dsNhapXuatChiTiet || [], "id", "asc");
    }

    _dsNhapXuatChiTiet.map((item, index) => {
      if (
        thongTinPhieu.loaiChietKhau == 20 &&
        item?.loNhap &&
        !item.loNhap.giaTruocVatChuaChietKhau
      ) {
        item.loNhap.giaTruocVatChuaChietKhau = tinhGiaTruocVATChuaChietKhau(
          {
            tienChietKhau: item.loNhap?.tienChietKhau,
            giaNhapTruocVat: item.loNhap?.giaNhapTruocVat,
            soLuongSoCap: item?.soLuongSoCap,
            phanTramChietKhau: item.loNhap?.phanTramChietKhau,
          },
          3
        );
      }

      data.push({ ...item, index: index + 1 });

      (item.dsNhapXuatChiTiet || []).map((item2, index2) => {
        if (
          thongTinPhieu.loaiChietKhau == 20 &&
          item2?.loNhap &&
          !item.loNhap.giaTruocVatChuaChietKhau
        ) {
          item2.loNhap.giaTruocVatChuaChietKhau = tinhGiaTruocVATChuaChietKhau(
            {
              tienChietKhau: item2.loNhap?.tienChietKhau,
              giaNhapTruocVat: item2.loNhap?.giaNhapTruocVat,
              soLuongSoCap: item2?.soLuongSoCap,
              phanTramChietKhau: item2.loNhap?.phanTramChietKhau,
            },
            3
          );
        }

        data.push({
          ...item2,
          index: index + 1 + "." + (index2 + 1),
          ma: item2?.dichVu?.ma || item2?.ma,
          ten: item2?.dichVu?.ten || item2?.ten,
        });
      });
    });

    return (data || []).reduce((a, item, index) => {
      return [
        ...a,
        {
          ...item,
          rowId: index,
        },
        {
          ...item,
          rowId: index + "_",
        },
      ];
    }, []);
  }, [dsNhapXuatChiTiet, dataSortColumn]);

  const onShowChietKhau = (e) => {
    if (refModalChietKhau.current) {
      refModalChietKhau.current.show({ isReadOnly });
    }
  };

  useEffect(() => {
    getListAllXuatXu({ active: true, page: "", size: "" });
    getListAllThangSoBanLe({ active: true, page: "", size: "" });
  }, []);

  const onChangePage = (page) => {
    onSearchChiTiet({ page: page - 1 });
  };

  const onChangeSize = (size) => {
    props.onSizeChange({ size: size });
  };

  const onShowDsHangHoa = (e, data) => {
    refModalDanhSachHangHoa.current.show(
      { vatTuBoId: e, data, hiddenButon: true },
      (data) => {}
    );
  };

  return (
    <Main>
      <TableWrapper
        className="danh-sach-hang-hoa"
        locale={{
          emptyText: (
            <TableEmpty
              onClickButton={onFocusSearchHangHoa}
              showButton={true}
            />
          ),
        }}
        showSorterTooltip={false}
        rowClassName={(record, index) =>
          index % 2 === 0 ? "level-1" : "level-2"
        }
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={(record) => record.rowId}
      />
      <div className="">
        {id && id != "them-moi" && totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            onShowSizeChange={onChangeSize}
            style={{ flex: 1, justifyContent: "flex-end" }}
          />
        ) : null}
        <div
          className="d-flex flex-end pt-2 border-top gap-16"
          style={{ justifyContent: "end", marginRight: "20px" }}
        >
          {isNhapNcc && (
            <div className="text-right pr-4">
              <div className="item-line">
                <div className="line-label nhap-ncc">{""}</div>
                <div className="line-value">{""}</div>
              </div>
              <div className="item-line">
                <div className="line-label nhap-ncc">{""}</div>
                <div className="line-value">{""}</div>
              </div>
              <div className="item-line">
                <div className="line-label nhap-ncc">
                  {t("khoMau.thanhTienTruocVat")}:
                </div>
                <div className="line-value">
                  {formatNumber(thanhTienTruocVat || 0, 3)}
                </div>
              </div>
              <div className="item-line">
                <div className="line-label nhap-ncc">
                  {t("kho.thanhTienVat")}:
                </div>
                <div className="line-value">
                  {formatNumber(thanhTienVat || 0, 3)}
                </div>
              </div>
              <div className="item-line">
                <div className="line-label nhap-ncc">
                  {t("kho.thanhTienTruocVatSuaDoi")}:
                </div>
                <div className="line-value">
                  {formatNumber(thanhTienSuaDoiTruocVat || 0, 3)}
                </div>
              </div>
            </div>
          )}
          <div className="text-right pr-4">
            <div className="item-line">
              <div className="line-label">{t("khoMau.tongSoLuong")}:</div>
              <div className="line-value">
                {formatNumber(soLuongSoCap || 0, 3)}
              </div>
            </div>
            <div className="item-line">
              <div className="line-label">{t("nhaThuoc.tongTien")}:</div>
              <div className="line-value">{formatNumber(tongTien || 0, 3)}</div>
            </div>
            <div className="item-line">
              <div
                className="flex flex-a-center line-label pointer chiet-khau"
                onClick={onShowChietKhau}
              >
                <SVG.IcChietKhau className="mr-5" /> {t("nhaThuoc.chietKhau")}:
              </div>
              <div className="line-value">
                {formatNumber(chietKhau || 0, 3)}
              </div>
            </div>
            <div className="item-line">
              <div className="line-label">{t("kho.thanhTien")}:</div>
              <div className="line-value">
                {formatNumber(thanhTien || 0, 3)}
              </div>
            </div>
            <div className="item-line">
              <div className="line-label">{t("khoMau.thanhTienSuaDoi")}:</div>
              <div className="line-value">
                {formatNumber(thanhTienSuaDoi || 0, 3)}
              </div>
            </div>
          </div>
        </div>
      </div>
      <ModalChietKhau ref={refModalChietKhau} />
      <ModalDanhSachHangHoa ref={refModalDanhSachHangHoa} />
    </Main>
  );
};

export default forwardRef(DanhSachHangHoa);
