import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Input, Row } from "antd";
import { Main, Header, SelectStyled } from "./styled";
import {
  DatePicker,
  Checkbox,
  HeaderSearch,
  InputTimeout,
  Pagination,
  Select,
  TableWrapper,
  Tooltip,
} from "components";
import moment from "moment";
import { useParams } from "react-router-dom";
import { useEnum, useThietLap, useListAll, useLoading } from "hooks";
import {
  ENUM,
  LOAI_NHAP_XUAT,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import { isObject } from "utils/index";

const { Setting } = TableWrapper;

function parseSttRange(input) {
  if (!input || typeof input !== "string") return null;

  const trimmed = input.trim();

  if (/^\d+$/.test(trimmed)) {
    const num = parseInt(trimmed, 10);
    return { tuStt: num, denStt: num };
  }

  const match = trimmed.match(/^(\d+)\s*-\s*(\d+)$/);
  if (match) {
    const tuStt = parseInt(match[1], 10);
    const denStt = parseInt(match[2], 10);
    if (tuStt <= denStt) {
      return { tuStt, denStt };
    }
  }

  return null;
}

const DanhSach = (props) => {
  const { t } = useTranslation();
  const {
    totalElements,
    size,
    page,
    dataSortColumn,
    listPhieuNhapChiTiet,
    selectedItem,
    dataSearch,
  } = useSelector((state) => state.danhSachDichVuKhoChiTiet);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const refSettings = useRef(null);
  const refSettings1 = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);

  const [dataCANH_BAO_MAU_THUOC_SAP_HET_HSD, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_MAU_THUOC_SAP_HET_HSD
  );

  const {
    danhSachDichVuKhoChiTiet: {
      onSortChange,
      getListPhieuNhapXuatChiTiet,
      updateData,
      onChangeInputSearch,
      exportExcel,
    },
  } = useDispatch();

  const [state, _setState] = useState({ loaiDichVu: 1 });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { khoId, dichVuId } = useParams();
  const { khoTaiKhoaId, quyetDinhThauId, loNhapId } = getAllQueryString();

  const listPhieuNhapChiTietMemo = useMemo(() => {
    return listPhieuNhapChiTiet.map((item, idx) => ({
      ...item,
      key: `${item.phieuNhapXuatId}-${idx}`,
    }));
  }, [listPhieuNhapChiTiet]);

  useEffect(() => {
    onChangeInputSearch({
      khoId: khoId,
      dichVuId: dichVuId,
      nhapKho: true,
      dsTrangThai: [30],
      khoTaiKhoaId,
      quyetDinhThauId,
      loNhapId,
    });
  }, []);
  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        // onShowAndHandleUpdate(record);
      },
    };
  };
  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onSearchInput = (name) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e._d.format("dd/MM/yyyy");
    else value = e;
    if (name === "stt") {
      let sttRange = parseSttRange(value);
      if (isObject(sttRange, true)) {
        let { tuStt, denStt } = sttRange;
        onChangeInputSearch({ tuStt, denStt });
        return;
      }
      onChangeInputSearch({ tuStt: null, denStt: null });
    } else {
      onChangeInputSearch({ [name]: value });
    }
  };

  const columnsNhap = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "40px",
      dataIndex: "index",
      key: "index",
      hideSearch: true,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soLuong")}
          sort_key="soLuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "70px",
      dataIndex: "soLuong",
      key: "soLuong",
      type: true,
      hideSearch: true,
      align: "center",
      i18Name: "common.soLuong",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dvt")}
          sort_key="tenDonViTinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenDonViTinh"] || ""}
        />
      ),
      width: "70px",
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      hideSearch: true,
      i18Name: "kho.dvt",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hanSuDung")}
          sort_key="ngayHanSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ngayHanSuDung"] || ""}
        />
      ),
      render: (item) => {
        return item?.toDateObject()?.format("dd/MM/YYYY");
      },
      width: "100px",
      dataIndex: "ngayHanSuDung",
      key: "ngayHanSuDung",
      hideSearch: true,
      className: "ngayHanSuDung",
      i18Name: "kho.hanSuDung",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soLo")}
          sort_key="soLo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soLo"] || ""}
        />
      ),
      width: "70px",
      dataIndex: "soLo",
      hideSearch: true,
      key: "soLo",
      i18Name: "kho.soLo",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soPhieu")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soPhieu"] || ""}
          search={
            <InputTimeout
              placeholder={t("thuNgan.nhapSoPhieu")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "soPhieu",
      hideSearch: true,
      key: "soPhieu",
      i18Name: "kho.soPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soHoaDon")}
          sort_key="soHoaDon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soHoaDon"] || ""}
          search={
            <InputTimeout
              placeholder={t("kho.nhapSoHoaDon")}
              onChange={onSearchInput("soHoaDon")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "soHoaDon",
      hideSearch: true,
      key: "soHoaDon",
      i18Name: "kho.soHoaDon",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaBh")}
          sort_key="giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaBaoHiem"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaBaoHiem",
      hideSearch: true,
      key: "giaBaoHiem",
      i18Name: "kho.quyetDinhThau.donGiaBh",
      show: true,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaKhongBh")}
          sort_key="giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaKhongBaoHiem"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaKhongBaoHiem",
      hideSearch: true,
      key: "giaKhongBaoHiem",
      i18Name: "kho.quyetDinhThau.donGiaKhongBh",
      show: true,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.phuThu")}
          sort_key="giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaPhuThu"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaPhuThu",
      hideSearch: true,
      key: "giaPhuThu",
      i18Name: "kho.quyetDinhThau.phuThu",
      show: true,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.loaiNhapXuat")}
          sort_key="loaiNhapXuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiNhapXuat"] || ""}
        />
      ),
      width: "120px",
      dataIndex: "loaiNhapXuat",
      hideSearch: true,
      key: "loaiNhapXuat",
      i18Name: "kho.loaiNhapXuat",
      show: true,
      render: (item) => listLoaiNhapXuat.find((x) => x.id == item)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soPhieuNhapNCC")}
          sort_key="soPhieuNhap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soPhieuNhap"] || ""}
        />
      ),
      width: "150px",
      dataIndex: "soPhieuNhap",
      hideSearch: true,
      key: "soPhieuNhap",
      i18Name: "kho.soPhieuNhapNCC",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hinhThucNhap")}
          sort_key="tenHinhThucNhapXuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenHinhThucNhapXuat"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "tenHinhThucNhapXuat",
      hideSearch: true,
      key: "tenHinhThucNhapXuat",
      i18Name: "kho.hinhThucNhap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.title")}
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "quyetDinhThau",
      hideSearch: true,
      key: "quyetDinhThau",
      i18Name: "kho.quyetDinhThau.title",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoXuat")}
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "khoXuat",
      hideSearch: true,
      key: "khoXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.xuatXu")}
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "xuatXu",
      hideSearch: true,
      key: "xuatXu",
      i18Name: "kho.khoXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soVisa")}
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || ""}
        />
      ),
      width: "70px",
      dataIndex: "soVisa",
      hideSearch: true,
      key: "soVisa",
      i18Name: "kho.quyetDinhThau.soVisa",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoTaiKhoa")}
          sort_key="tenKhoaKhoTaiKhoa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenKhoaKhoTaiKhoa"] || ""}
          searchSelect={
            <Select
              placeholder={t("common.chonKhoa")}
              data={listAllKhoa || []}
              onChange={onSearchInput("khoTaiKhoaId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "tenKhoaKhoTaiKhoa",
      key: "tenKhoaKhoTaiKhoa",
      i18Name: "kho.khoTaiKhoa",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.ngayDuyet")}
          sort_key="thoiGianDuyet"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thoiGianDuyet"] || ""}
          searchSelect={
            <DatePicker
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("thoiGianDuyet")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "thoiGianDuyet",
      hideSearch: true,
      key: "thoiGianDuyet",
      i18Name: "kho.ngayDuyet",
      show: true,
      render: (item) => {
        return (
          <>
            <div>{item && moment(item).format("DD/MM/YYYY")}</div>
          </>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 100,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      colSpan: 1,
    },
  ];

  const columnsXuat = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "40px",
      dataIndex: "index",
      key: "index",
      hideSearch: true,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hoTenNb")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          search={
            <InputTimeout
              placeholder={t("kho.nhapHoTenNb")}
              onChange={onSearchInput("tenNb")}
            />
          }
        />
      ),
      width: "250px",
      dataIndex: "tenNb",
      key: "tenNb",
      type: true,
      i18Name: "kho.hoTenNb",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          search={
            <InputTimeout
              placeholder={t("common.nhapMaHoSo")}
              onChange={onSearchInput("maHoSo")}
            />
          }
        />
      ),
      width: "110px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      type: true,
      i18Name: "common.maHoSo",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.doiTuongKcb")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          searchSelect={
            <Select
              placeholder={t("common.chonDoiTuongKCB")}
              data={listDoiTuongKcb || []}
              onChange={onSearchInput("doiTuongKcb")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      type: true,
      hideSearch: true,
      i18Name: "baoCao.doiTuongKcb",
      show: true,
      render: (item) => listDoiTuongKcb.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoaChiDinh")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "130px",
      dataIndex: "tenKhoaChiDinh",
      key: "tenKhoaChiDinh",
      type: true,
      hideSearch: true,
      i18Name: "kho.khoaChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soLuongXuat")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "120px",
      dataIndex: "soLuong",
      key: "soLuong",
      type: true,
      hideSearch: true,
      i18Name: "kho.soLuongXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.heSoDinhMuc")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "80px",
      dataIndex: "",
      key: "",
      type: true,
      hideSearch: true,
      i18Name: "danhMuc.heSoDinhMuc",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dvt")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "70px",
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      type: true,
      hideSearch: true,
      i18Name: "kho.dvt",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("nhaThuoc.trangThaiThanhToan")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "90px",
      dataIndex: "thanhToan",
      key: "thanhToan",
      type: true,
      hideSearch: true,
      align: "center",
      i18Name: "nhaThuoc.trangThaiThanhToan",
      show: true,
      render: (item) => (
        <Checkbox
          checked={item === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
        />
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.loaiNhapXuat")}
          sort_key="loaiNhapXuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiNhapXuat"] || ""}
        />
      ),
      width: "120px",
      dataIndex: "loaiNhapXuat",
      hideSearch: true,
      key: "loaiNhapXuat",
      i18Name: "kho.loaiNhapXuat",
      show: true,
      render: (item) => listLoaiNhapXuat.find((x) => x.id == item)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soPhieu")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          search={
            <InputTimeout
              placeholder={t("thuNgan.nhapSoPhieu")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      type: true,
      hideSearch: true,
      i18Name: "kho.soPhieu",
      show: true,
      render: (item, data) => {
        if (
          data.loaiNhapXuat === LOAI_NHAP_XUAT.NB_NOI_TRU &&
          [2, 3].includes(state.loaiDichVu)
        ) {
          return "";
        }
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sttXuatThuoc")}
          sort_key="stt"
          dataSort={dataSortColumn["stt"] || ""}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("stt")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "stt",
      key: "stt",
      align: "center",
      hidden: state.loaiDichVu !== 2,
      i18Name: "kho.sttXuatThuoc",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaBh")}
          sort_key="giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaBaoHiem"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaBaoHiem",
      hideSearch: true,
      key: "giaBaoHiem",
      i18Name: "kho.quyetDinhThau.donGiaBh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.donGiaKhongBh")}
          sort_key="giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaKhongBaoHiem"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaKhongBaoHiem",
      hideSearch: true,
      key: "giaKhongBaoHiem",
      i18Name: "kho.quyetDinhThau.donGiaKhongBh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.phuThu")}
          sort_key="giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["giaPhuThu"] || ""}
        />
      ),
      width: "100px",
      dataIndex: "giaPhuThu",
      hideSearch: true,
      key: "giaPhuThu",
      i18Name: "kho.quyetDinhThau.phuThu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.trangThaiPhieu")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          searchSelect={
            <Select
              placeholder={t("kho.chonTrangThaiPhieu")}
              data={listTrangThaiPhieuNhapXuat || []}
              onChange={onSearchInput("trangThai")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "trangThai",
      key: "trangThai",
      type: true,
      hideSearch: true,
      i18Name: "kho.trangThaiPhieu",
      show: true,
      render: (item) =>
        (listTrangThaiPhieuNhapXuat || []).find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("nhaThuoc.ngayPhat")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
          searchSelect={
            <DatePicker
              placeholder={t("kho.chonNgayPhat")}
              onChange={onSearchInput("thoiGianDuyet")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      type: true,
      hideSearch: true,
      i18Name: "nhaThuoc.ngayPhat",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("kho.soPhieuNhap")} />,
      width: "120px",
      dataIndex: "soPhieuNhap",
      key: "soPhieuNhap",
      type: true,
      hideSearch: true,
      hidden: ![2, 3].includes(state.loaiDichVu),
      i18Name: "kho.soPhieuNhap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.soVisa")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "120px",
      dataIndex: "soVisa",
      key: "soVisa",
      type: true,
      hideSearch: true,
      i18Name: "kho.quyetDinhThau.soVisa",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soLo")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "90px",
      dataIndex: "soLo",
      key: "soLo",
      type: true,
      hideSearch: true,
      i18Name: "kho.soLo",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.nhaCungCap")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "200px",
      dataIndex: "",
      key: "",
      type: true,
      hideSearch: true,
      i18Name: "kho.quyetDinhThau.nhaCungCap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.title")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "200px",
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      type: true,
      hideSearch: true,
      i18Name: "kho.quyetDinhThau.title",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.nhaSanXuat")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "200px",
      dataIndex: "tenNhaSanXuat",
      key: "tenNhaSanXuat",
      type: true,
      hideSearch: true,
      i18Name: "common.nhaSanXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.xuatXu")}
          sort_key=""
          onClickSort={onClickSort}
          // dataSort={dataSortColumn["soLuong"] || ""}
        />
      ),
      width: "200px",
      dataIndex: "tenXuatXu",
      key: "tenXuatXu",
      type: true,
      hideSearch: true,
      i18Name: "kho.quyetDinhThau.xuatXu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoTaiKhoa")}
          sort_key="tenKhoaKhoTaiKhoa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenKhoaKhoTaiKhoa"] || ""}
          searchSelect={
            <Select
              placeholder={t("common.chonKhoa")}
              data={listAllKhoa || []}
              onChange={onSearchInput("khoTaiKhoaId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "tenKhoaKhoTaiKhoa",
      key: "tenKhoaKhoTaiKhoa",
      i18Name: "kho.khoTaiKhoa",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.hanSuDung")} />,
      width: "150px",
      dataIndex: "ngayHanSuDung",
      key: "ngayHanSuDung",
      type: true,
      hideSearch: true,
      i18Name: "kho.hanSuDung",
      show: true,
      hidden: state.loaiDichVu !== 2,
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.ngayThucHien")} />,
      width: "120px",
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      type: true,
      hideSearch: true,
      i18Name: "quanLyNoiTru.ngayThucHien",
      show: true,
      hidden: state.loaiDichVu !== 3,
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings1} />
            </>
          }
        />
      ),
      width: 100,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      colSpan: 1,
    },
  ];
  const dataloaiDichVu = [
    {
      id: 1,
      ten: t("kho.dsNhap"),
    },
    {
      id: 2,
      ten: t("kho.dsXuat"),
    },
    {
      id: 3,
      ten: t("kho.dsGiuCho"),
    },
    {
      id: 4,
      ten: t("kho.dsNbHuyGiuTon"),
    },
    {
      id: 5,
      ten: t("kho.danhSachChoXnNhap"),
    },
  ];

  const onChangeGroupService = (e) => {
    const config = {
      2: { nhapKho: false },
      3: { nhapKho: false, dsTrangThai: [15, 20] },
      4: { nhapKho: false, dsTrangThai: [10], loaiNhapXuat: 100 },
      5: { dsTrangThai: [28], dsLoaiNhapXuat: [20, 80, 85] },
    };

    let {
      nhapKho = true,
      dsTrangThai = [30],
      loaiNhapXuat,
      dsLoaiNhapXuat,
    } = config[e] || {};

    updateData({ dataSearch: {} });
    onChangeInputSearch({
      khoId,
      dichVuId,
      nhapKho,
      dsTrangThai,
      loaiNhapXuat,
      dsLoaiNhapXuat,
      khoTaiKhoaId,
      quyetDinhThauId,
      loNhapId,
    });

    setState({ loaiDichVu: e, nhapKho });

    if (e !== 3) {
      updateData({ selectedItem: null });
    }
  };

  const changePage = (page) => {
    getListPhieuNhapXuatChiTiet({
      page: page - 1,
    });

    updateData({
      selectedItem: null,
    });
  };

  const changeSize = (size) => {
    getListPhieuNhapXuatChiTiet({
      page: 0,
      size,
    });
  };

  const onSelectChange = (selectedRowKeys, data) => {
    updateData({
      selectedItem: data && data[data.length - 1],
    });
  };

  const onExportDuLieu = async () => {
    try {
      showLoading();
      await exportExcel({
        ...dataSearch,
        ...(state.loaiDichVu === 1 && { loai: 10 }),
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const rowSelection = {
    columnTitle: <HeaderSearch title="" />,
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: selectedItem
      ? listPhieuNhapChiTietMemo
          .filter((x) => x.phieuNhapXuatId == selectedItem.phieuNhapXuatId)
          .map((x) => x.key)
      : [],
    type: "checkbox",
  };

  return (
    <Main className="main" noPadding={true}>
      <Header>
        <div className="header">
          <Row className="header-row">
            <div className="w-full flex justify-between align-items-center">
              <Row>
                <div className="content">{t("kho.lichSuNhapXuat")}</div>
                <div className="content-note">
                  <SelectStyled>
                    <Select
                      onChange={onChangeGroupService}
                      value={state.loaiDichVu}
                      placeholder={t("kho.dsNhap")}
                      data={dataloaiDichVu}
                    />
                  </SelectStyled>
                </div>
              </Row>
              <Tooltip title={t("common.xuatDuLieu")}>
                <SVG.IcDownload
                  style={{ cursor: "pointer" }}
                  width={30}
                  height={30}
                  onClick={onExportDuLieu}
                />
              </Tooltip>
            </div>
          </Row>
        </div>
      </Header>
      <TableWrapper
        className="table"
        scroll={{ y: 2000 }}
        rowKey={(record) => record.key}
        onRow={onRow}
        rowClassName={(record, index) => {
          if (isLoadFinish)
            if (dataCANH_BAO_MAU_THUOC_SAP_HET_HSD) {
              return moment(record?.ngayHanSuDung).diff(moment(), "days") <=
                dataCANH_BAO_MAU_THUOC_SAP_HET_HSD &&
                moment(record?.ngayHanSuDung).diff(moment(), "days") >= 0
                ? "row-green"
                : moment(record.ngayHanSuDung).diff(
                    moment(new Date()),
                    "days"
                  ) < 0
                ? "row-yellow"
                : "";
            } else {
              return moment(record.ngayHanSuDung).diff(
                moment(new Date()).add(record.soNgayCanhBaoHsd || 0, "days"),
                "days"
              ) < 0
                ? "row-yellow"
                : "";
            }
        }}
        columns={state.loaiDichVu === 1 ? columnsNhap : columnsXuat}
        dataSource={listPhieuNhapChiTietMemo}
        rowSelection={[3, 4].includes(state.loaiDichVu) && rowSelection}
        tableName={`table_Chi_tiet_ton_kho_${state.loaiDichVu}`}
        ref={state.loaiDichVu === 1 ? refSettings : refSettings1}
        alwayGetFromCache
      />
      {totalElements ? (
        <Pagination
          listData={listPhieuNhapChiTietMemo}
          onChange={changePage}
          onShowSizeChange={changeSize}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          style={{ flex: 1, justifyContent: "flex-end", width: "100%" }}
        />
      ) : null}
    </Main>
  );
};

export default memo(DanhSach);
