import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage, Main, HeaderTitle, SearchPartient } from "./styled";
import { useTranslation } from "react-i18next";
import { Row, Col, message, Menu } from "antd";
import {
  Button,
  Card,
  Select,
  Tabs,
  Dropdown,
  ModalSignPrint,
  ThongTinBenhNhan,
} from "components";
import { useDispatch } from "react-redux";
import { useHistory, useLocation, useParams } from "react-router-dom";
import {
  useConfirm,
  useEnum,
  useGuid,
  useLoading,
  useQueryString,
  useStore,
} from "hooks";
import {
  ENUM,
  HOTKEY,
  LOAI_DICH_VU,
  LOAI_KHAM_TIEM_CHUNG,
  TRANG_THAI_DICH_VU,
  LOAI_PHONG,
  MA_BIEU_MAU_EDITOR,
  MAN_HINH_PHIEU_IN,
  VI_TRI_PHIEU_IN,
  LIST_PHIEU_IN_BANG_KE,
} from "constants/index";
import Thu<PERSON><PERSON><PERSON><PERSON><PERSON> from "./ThucHienKham";
import ChiDinhDichVuKyThuat from "./ChiDinhDichVuKyThuat";
import ChiDinhThuoc from "./ChiDinhThuoc";
import ChiDinhVacxin from "./ChiDinhVacxin";
import HenTiem from "./HenTiem";
import {
  setQueryStringValue,
  transformObjToQueryString,
} from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import ChiDinhVatTu from "./ChiDinhVatTu";
import LichSuTiemChung from "./LichSuTiemChung";
import printProvider from "data-access/print-provider";
import ModalThemLichHen from "./HenTiem/ModalThemLichHen";
import KetLuanKham from "../KetLuanKham";
import ButtonKetThucKham from "../KetLuanKham/ButtonKetThucKham";
import moment from "moment";
import ModalKetThucKham from "pages/khamBenh/ThongTin/ModalKetThucKham";
import { checkRole } from "lib-utils/role-utils";
import SoLuongBN from "../components/SoLuongBN";
import LayDSBN from "../components/LayDsBN";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { openInNewTab } from "utils";

const ChiTietKhamSL = () => {
  const { showConfirm } = useConfirm();
  const [tab] = useQueryString("tab", "0");
  const [tabKham] = useQueryString("tabKham", LOAI_KHAM_TIEM_CHUNG.SANG_LOC);
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();
  const history = useHistory();
  const { id: dvKhamId, nbDotDieuTriId, maTiemChung } = useParams();
  const { state: locationState } = useLocation();
  const layerId = useGuid();
  const refFunction = useRef({});
  const refModalThemLichHen = useRef(null);
  const refThucHienKham = useRef(null);
  const refKetLuanKham = useRef(null);
  const refModalKetThucKham = useRef(null);
  const refChiDinhVacxin = useRef(null);
  const refChiDinhThuoc = useRef(null);
  const refModalSignPrint = useRef(null);

  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);

  const {
    khoa: { getKhoaTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
    dsKhamSL: {
      inPhieu,
      getThongTinNbTheoNbDotDieuTriId,
      updateData,
      dangKhamTiemChung,
      boQuaKhamTiemChung,
      clearData,
      getListKhamSL,
      huyKham,
    },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    danhSachLichSuTiem: { dongBoLichSuTiem },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    khamBenh: {
      getNbDvKham,
      ketLuanKham,
      huyKetLuanGetMethod,
      huyKetLuanKham,
      getPhongTheoTaiKhoan,
      getStatisticsRoom,
    },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
  } = useDispatch();

  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const trangThaiKham = useStore(
    "khamBenh.thongTinChiTiet.nbDvKyThuat.trangThai",
    null
  );
  const dichVuKham = useStore("khamBenh.thongTinChiTiet.nbDichVu.dichVu", null);
  const phongThucHien = useStore(
    "khamBenh.thongTinChiTiet.nbDvKyThuat.phongThucHien",
    null
  );
  const statisticsRoom = useStore("khamBenh.statisticsRoom", []);
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});

  const [state, _setState] = useState({
    activeKey: tab,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (dvKhamId && tabKham != LOAI_KHAM_TIEM_CHUNG.KHAM_CHUNG)
      getNbDvKham({
        dichVuId: dvKhamId,
        chuyenTrangThai: false,
      });
  }, [dvKhamId, tabKham]);

  useEffect(() => {
    getKhoaTheoTaiKhoan({
      dsTinhChatKhoa: 70,
      page: "",
      size: "",
      ative: true,
    });
    getPhongTheoTaiKhoan({
      loaiPhong: LOAI_PHONG.PHONG_KHAM,
      page: "",
      size: "",
      active: true,
    });
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.S,
          ctrlKey: true,
          onEvent: (e) => {
            refFunction.current["onSave"] && refFunction.current["onSave"]();
          },
        },
      ],
    });
    return () => {
      clearData(); //clear dữ liệu khám
      onRemoveLayer({ layerId: layerId });
    };
  }, []);
  useEffect(() => {
    if (nbDotDieuTriId) {
      getThongTinNbTheoNbDotDieuTriId(nbDotDieuTriId).then((s) => {});
    }
  }, [nbDotDieuTriId]);

  const onGetDsPhieu = () => {
    getListPhieu({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC,
      maViTri: VI_TRI_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC.IN_GIAY_TO,
      chiDinhTuDichVuId: dvKhamId,
    }).then((res) => {
      setState({
        listPhieu: res || [],
      });
    });
  };

  useEffect(() => {
    if (nbDotDieuTriId && dvKhamId) {
      onGetDsPhieu();
    }
  }, [nbDotDieuTriId, dvKhamId]);

  useEffect(() => {
    if (!phongThucHien?.id) return;
    getStatisticsRoom({ phongThucHienId: phongThucHien.id });
  }, [phongThucHien]);

  useEffect(() => {
    if (thongTinBenhNhan.id && dvKhamId) {
      updateConfigData({
        configData: {
          chiDinhTuDichVuId: dvKhamId,
          nbDotDieuTriId: nbDotDieuTriId,
          nbThongTinId: thongTinBenhNhan.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
          khoaChiDinhId: phongThucHien?.khoaId,
          thongTinNguoiBenh: thongTinBenhNhan,
          isNgoaiTru: true,
          khoaChiDinhVacXinId: phongThucHien?.khoaId,
          doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
          canLamSang: false,
          phongThucHienId: thongTinBenhNhan?.phongId,
        },
      });
      let activeTab = tab;
      if (tab === "2" && !thongTinChiTiet?.nbChanDoan?.cdChinh) {
        message.error(t("tiemChung.nhapChanDoanBenhTaiKhamChungDeKeDonThuoc"));
        activeTab = "0";
      }
      onChange(activeTab);
    }
  }, [thongTinBenhNhan, dvKhamId, phongThucHien, thongTinChiTiet]);

  const listTabs = [
    {
      name: t("tiemChung.thucHienKham"), //0
      component: (
        <ThucHienKham
          dvKhamId={dvKhamId}
          nbDotDieuTriId={nbDotDieuTriId}
          ref={refThucHienKham}
        />
      ),
      iconTab: <SVG.IcSangLoc />,
      isShow: true,
    },
    {
      name: t("common.chiDinhVacxin"), //1
      component: <ChiDinhVacxin ref={refChiDinhVacxin} />,
      iconTab: <SVG.IcVacxin />,
      isShow: true,
    },
    {
      name: t("common.chiDinhThuoc"), //2
      component: <ChiDinhThuoc ref={refChiDinhThuoc} />,
      iconTab: <SVG.IcDonThuoc color={"var(--color-gray-primary)"} />,
      isShow: true,
    },
    {
      name: t("common.chiDinhDichVu"), //3
      component: <ChiDinhDichVuKyThuat />,
      iconTab: <SVG.IcChiDinhDichVu />,
      isShow: true,
    },
    {
      name: t("common.chiDinhVatTu"), //4
      component: <ChiDinhVatTu />,
      iconTab: <SVG.IcVatTu />,
      isShow: true,
    },
    {
      name: t("tiemChung.lichSuTiemChung"), //5
      component: <LichSuTiemChung />,
      iconTab: <SVG.IcLichSuTiemChung />,
      isShow: true,
    },
    {
      name: t("tiemChung.ketLuanKham"), //6
      component: <KetLuanKham ref={refKetLuanKham} />,
      iconTab: <SVG.IcKetLuan />,
      isShow: true,
      fixHeight: true,
    },
    {
      name: t("tiemChung.henTiem"), //7
      component: <HenTiem nbThongTinId={thongTinBenhNhan.nbThongTinId} />,
      iconTab: <SVG.IcCalendar />,
      isShow: true,
      fixHeight: true,
    },
    {
      name: t("danhMuc.goiDichVu"), //8
      component: <></>,
      iconTab: <SVG.IcLichSuTiemChung />,
      isShow: true,
    },
  ];

  const onSave = async () => {
    switch (Number(state.activeKey)) {
      case 0:
        refThucHienKham.current && (await refThucHienKham.current.onSave());
        break;
      case 1:
        refChiDinhVacxin.current && refChiDinhVacxin.current.onSave();
        break;
      case 2:
        refChiDinhThuoc.current && refChiDinhThuoc.current.onSave();
        break;
      case 6:
        refKetLuanKham.current && refKetLuanKham.current.onSave();
        break;
    }
    if (state.activeKey == 0) {
      onGetDsPhieu();
    }
  };
  refFunction.current["onSave"] = onSave;

  const onChange = (tab) => {
    if (tab === "2" && !thongTinChiTiet?.nbChanDoan?.cdChinh) {
      message.error(t("tiemChung.nhapChanDoanBenhTaiKhamChungDeKeDonThuoc"));
      return;
    }
    setState({ activeKey: tab });
    if (tab === "2" || tab === "1" || tab === "4") {
      let loaiDichVu = LOAI_DICH_VU.THUOC;
      switch (tab) {
        case "2":
          loaiDichVu = LOAI_DICH_VU.THUOC;
          break;
        case "1":
          loaiDichVu = LOAI_DICH_VU.VAC_XIN;
          break;
        case "4":
          loaiDichVu = LOAI_DICH_VU.VAT_TU;
          break;
      }
      let payload = {
        khoaNbId: thongTinBenhNhan?.khoaNbId,
        khoaChiDinhId: phongThucHien?.khoaId,
        doiTuong: thongTinBenhNhan?.doiTuong,
        loaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
        capCuu: thongTinBenhNhan?.capCuu,
        phongId: thongTinBenhNhan?.phongId,
        noiTru: false,
        canLamSang: false,
        loaiDichVu: loaiDichVu,
      };
      getListThietLapChonKhoTheoTaiKhoan({ ...payload });
    }
    setQueryStringValue("tab", tab);
  };
  const onChangeSelect = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    setState({
      [key]: value,
    });
  };

  const onDongBo = () => {
    showLoading();
    dongBoLichSuTiem(maTiemChung).finally(() => {
      hideLoading();
    });
  };

  const onInPhieu = () => {
    inPhieu({ id: nbDotDieuTriId, phongId: phongThucHien?.id }).then((s) => {
      printProvider.printPdf(s);
    });
  };
  const onThemMoiLichHen = () => {
    refModalThemLichHen.current &&
      refModalThemLichHen.current.show(
        { nbDotDieuTriId, nbThongTinId: thongTinBenhNhan.nbThongTinId },
        () => {}
      );
  };

  const onGoiKham = async () => {
    try {
      showLoading();
      await dangKhamTiemChung({ id: dvKhamId, trangThai: trangThaiKham });
    } catch (error) {
    } finally {
      getNbDvKham({
        dichVuId: dvKhamId,
        chuyenTrangThai: false,
        isForce: true,
      });
      hideLoading();
    }
  };

  const onBoQua = async () => {
    try {
      showLoading();
      await boQuaKhamTiemChung({ id: dvKhamId, trangThai: trangThaiKham });
    } catch (error) {
    } finally {
      getNbDvKham({
        dichVuId: dvKhamId,
        chuyenTrangThai: false,
        isForce: true,
      });
      hideLoading();
    }
  };

  const onHuyKham = async () => {
    try {
      showLoading();
      await huyKham([
        {
          id: dvKhamId,
          nbDvKyThuat: { trangThai: TRANG_THAI_DICH_VU.CHO_KHAM },
        },
      ]);
    } catch (error) {
    } finally {
      getNbDvKham({
        dichVuId: dvKhamId,
        chuyenTrangThai: false,
        isForce: true,
      });
      hideLoading();
    }
  };

  const onKetThucKham = () => {
    refModalKetThucKham.current &&
      refModalKetThucKham.current.show({}, (values) => {
        const { bacSiKetLuanId, thoiGianKetLuan, nguoiPhienDichId } =
          values || {};
        ketLuanKham(
          {
            bacSiKetLuanId,
            thoiGianKetLuan,
            id: dvKhamId,
            nguoiPhienDichId,
          },
          true
        ).then(() => {
          getNbDvKham({
            dichVuId: dvKhamId,
            chuyenTrangThai: false,
            isForce: true,
          });
        });
      });
  };

  const onHuyKetLuan = () => {
    huyKetLuanGetMethod({ nbDotDieuTriId }).then((res) => {
      let { data } = res;
      let content = "";
      let okText = "";
      let showBtnOk = true;
      let functionSubmit = huyKetLuanKham;
      if (!data) {
        // true : không có quyền MO_HO_SO
        content = t(
          "khamBenh.nguoiBenhDaThanhToanBaoHiemHuyThanhToanDeMoLaiHoSo"
        );
        showBtnOk = false;
        if (checkRole([ROLES["KHAM_BENH"].MO_HO_SO])) {
          // true : có quyền MO_HO_SO
          showBtnOk = true;
          content = t("khamBenh.nguoiBenhDaThanhToanBaoHiemMoHoSoAnhHuongQTBH");
          okText = t("khamBenh.moHoSo");
        }
      } else {
        // false
        content = t("khamBenh.xacNhanThayDoiKetQuaKhiDaKetThucKham");
        okText = t("common.dongY");
        showBtnOk = true;
      }
      showConfirm(
        {
          content,
          cancelText: t("common.quayLai"),
          okText,
          showBtnOk,
          typeModal: "warning",
          title: t("common.canhBao"),
        },
        async () => {
          await functionSubmit({
            id: dvKhamId,
          });

          getNbDvKham({
            dichVuId: dvKhamId,
            chuyenTrangThai: false,
            isForce: true,
          });
        }
      );
    });
  };

  const renderRightAction = (tab) => {
    const actions = () => {
      switch (tab) {
        case 0: //khám sàng lọc
          const tenDichVu = dichVuKham?.ten || "";
          const tenTrangThai =
            listTrangThaiDichVu.find((x) => x.id == trangThaiKham)?.ten || "";

          // Điều kiện hiển thị button Gọi khám: Dịch vụ khám có trạng thái: Chờ khám(20) Đang thực hiện dịch vụ(70) Chờ kết luận(100) Bỏ qua(50) Bỏ qua kết luận(130)
          const isShowGoiKham = [20, 40, 70, 100, 50, 130].includes(
            trangThaiKham
          );
          // Điều kiện hiển thị button Bỏ qua Chờ khám(20) Chuẩn bị khám(40) Đang khám(60) Chờ kết luận(100) Đã checkin chờ kết luận(110) Chuẩn bị kết luận(120) Đang kết luận(140)
          const isShowBoQua = [20, 40, 60, 100, 110, 120, 140].includes(
            trangThaiKham
          );

          return (
            <>
              <span
                style={{ fontSize: 13, marginRight: 20 }}
              >{`${tenDichVu}: ${tenTrangThai}`}</span>

              {isShowGoiKham && (
                <Button type="primary" minWidth={80} onClick={onGoiKham}>
                  {t("common.goiKham")}
                </Button>
              )}
              {isShowBoQua && (
                <Button type="default" onClick={onBoQua}>
                  {t("tiemChung.boQua")}
                </Button>
              )}
              {isShowBoQua && (
                <Button type="default" minWidth={80} onClick={onHuyKham}>
                  {t("tiemChung.huyKham")}
                </Button>
              )}
              <Button type="default" onClick={onInPhieu}>
                {t("common.inPhieu")}
              </Button>
            </>
          );
        case 1: //chỉ định vắc xin
          return (
            <Button type="default" onClick={onInPhieu}>
              {t("common.inPhieu")}
            </Button>
          );
        case 7: //hẹn tiêm
          return (
            <>
              <Button type="success" minWidth={100} onClick={onThemMoiLichHen}>
                {t("common.themMoi")}
              </Button>
            </>
          );
        case 5: //lịch sử tiêm chủng
          return (
            <>
              <Button type="success" minWidth={100} onClick={onDongBo}>
                {t("tiemChung.capNhatLichSuTCQG")}
              </Button>
            </>
          );
      }
    };
    const renderDongHS = () => {
      //tab thực hiện khám và tab Kết luận khám + trạng thái Chờ kết luận
      if (tab == 0 || tab == 6) {
        return (
          <>
            {trangThaiKham == TRANG_THAI_DICH_VU.DANG_KET_LUAN && (
              <Button type="primary" onClick={onKetThucKham}>
                {t("khamBenh.dongHoSo")}
              </Button>
            )}
            {trangThaiKham == TRANG_THAI_DICH_VU.DA_KET_LUAN && (
              <Button type="default" onClick={onHuyKetLuan}>
                {t("khamBenh.huyKetLuan")}
              </Button>
            )}
          </>
        );
      }
    };
    return (
      <>
        {actions()}
        {trangThaiKham >= TRANG_THAI_DICH_VU.DANG_KHAM &&
          trangThaiKham < TRANG_THAI_DICH_VU.DANG_KET_LUAN && (
            <ButtonKetThucKham
              onFinish={() => {
                onChange("6");
              }}
            />
          )}
        {renderDongHS()}
      </>
    );
  };
  const onChangeTitleTab = (index) => (tab) => {
    if (index == 0) {
      const tabActive =
        tab == 0
          ? LOAI_KHAM_TIEM_CHUNG.SANG_LOC
          : LOAI_KHAM_TIEM_CHUNG.KHAM_CHUNG;
      setQueryStringValue("tabKham", tabActive);
      updateData({ loaiKhamTiemChung: tabActive });
    }
  };
  const renderTitleTab = (tab, index) => {
    if (index == 0) {
      //thuc hien kham
      let activeTab = LOAI_KHAM_TIEM_CHUNG.SANG_LOC;
      switch (Number(tabKham)) {
        case LOAI_KHAM_TIEM_CHUNG.SANG_LOC:
          activeTab = "0";
          break;
        case LOAI_KHAM_TIEM_CHUNG.KHAM_CHUNG:
          activeTab = "1";
          break;
      }
      return (
        <ThucHienKham.TabTitle
          defaultActiveKey={activeTab}
          onChange={onChangeTitleTab(index)}
          tabs={[t("tiemChung.khamSangLoc"), t("tiemChung.khamChung")]}
        />
      );
    }
    return tab?.name;
  };

  const onKeyDown = (e) => {
    if (e.keyCode == 13) {
      //enter
      const stt = e?.target?.value;
      if (stt)
        getListKhamSL({
          dataSearch: {
            stt2: `${phongThucHien.ma}.${stt}`,
            phongThucHienId: phongThucHien?.id,
            tuThoiGianThucHien: moment().format("YYYY-MM-DD 00:00:00"),
            denThoiGianThucHien: moment().format("YYYY-MM-DD 23:59:59"),
          },
        }).then(async (res) => {
          debugger;
          if (res && res.length > 0) {
            if (res.length == 1) {
              try {
                showLoading();
                const { nbDotDieuTriId, id, maTiemChung, trangThai } = res[0];
                await dangKhamTiemChung({ id, trangThai });

                if (maTiemChung) {
                  history.push(
                    `/quan-ly-tiem-chung/kham-sang-loc/${nbDotDieuTriId}/${id}/${maTiemChung}`
                  );
                } else {
                  history.push(
                    `/quan-ly-tiem-chung/kham-sang-loc/${nbDotDieuTriId}/${id}`
                  );
                }
              } catch (error) {
                console.error(error);
              } finally {
                hideLoading();
              }
            }
          } else {
            message.error("Không tìm thấy STT khám!");
          }
        });
    }
  };

  const onPrintPhieu = (item) => async () => {
    let mhParams = {};
    if (checkIsPhieuKySo(item)) {
      mhParams = {
        maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC,
        maViTri: VI_TRI_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC.IN_GIAY_TO,
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId: dvKhamId,
        kySo: true,
        phongId: phongThucHien?.id,
      };
    }

    if (item?.type == "editor") {
      showFileEditor({
        phieu: item,
        ...(LIST_PHIEU_IN_BANG_KE.includes(item.ma)
          ? { id: nbDotDieuTriId }
          : {}),
        ma: item.ma,
        maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
          ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
          : "",
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId: dvKhamId,
        mhParams: {
          ...mhParams,
          baoCaoId: item.baoCaoId,
        },
      });
    } else {
      if (checkIsPhieuKySo(item)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              maManHinh: MAN_HINH_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC,
              maViTri: VI_TRI_PHIEU_IN.CHI_TIET_KHAM_SANG_LOC.IN_GIAY_TO,
              nbDotDieuTriId: nbDotDieuTriId,
              chiDinhTuDichVuId: dvKhamId,
              id: nbDotDieuTriId,
              phongId: phongThucHien?.id,
            },
          });
      } else {
        try {
          showLoading();
          const { finalFile, dsPhieu } = await getFilePhieuIn({
            listPhieus: [item],
            id: nbDotDieuTriId,
            showError: true,
            nbDotDieuTriId: nbDotDieuTriId,
            phongId: phongThucHien?.id,
            chiDinhTuDichVuId: dvKhamId,
          });
          if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
            openInNewTab(finalFile);
          } else {
            printProvider.printPdf(dsPhieu);
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      }
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state?.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  return (
    <MainPage
      breadcrumb={[
        {
          link: "/quan-ly-tiem-chung",
          title: t("tiemChung.quanLyTiemChung"),
        },
        {
          link:
            "/quan-ly-tiem-chung/danh-sach-kham-sang-loc" +
            transformObjToQueryString(locationState),
          title: t("tiemChung.dsKhamSL"),
        },
        {
          link: window.location.pathname,
          title: t("tiemChung.chiTietKhamSL"),
        },
      ]}
    >
      <Main>
        <HeaderTitle>
          <h1>{t("tiemChung.chiTietKhamSL")}</h1>
        </HeaderTitle>

        <div className={"transition-4s"}>
          <Row>
            <Col span={24}>
              <Row>
                <Col span={4}>
                  <SearchPartient>
                    <Select
                      placeholder={t("common.chonPhongKham")}
                      data={[statisticsRoom]?.map((o) => ({
                        ten: o.tenPhong,
                        ma: o.maPhong,
                        id: o.id,
                      }))}
                      value={phongThucHien?.id}
                    />
                  </SearchPartient>
                </Col>
                <Col span={9}>
                  <LayDSBN layerId={layerId} />
                </Col>
                <Col span={10}>
                  <SoLuongBN layerId={layerId} />
                </Col>
              </Row>
            </Col>
          </Row>
        </div>
        <Row>
          <Col className="header-left" span={24}>
            <ThongTinBenhNhan
              isShowMaTiemChung={true}
              nbDotDieuTriId={nbDotDieuTriId}
            />
          </Col>
        </Row>

        <Card className="content" bottom={50}>
          <Tabs.Left
            activeKey={state.activeKey}
            tabPosition={"left"}
            tabWidth={220}
            type="card"
            className={`tab-main ${
              state.collapse ? "collapse-tab" : "show-more"
            }`}
            onChange={onChange}
          >
            {listTabs.map((obj, i) => {
              return (
                <Tabs.TabPane
                  key={i}
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                  disabled={!obj.isShow}
                >
                  <Tabs.TabBox
                    fixHeight={false}
                    title={renderTitleTab(obj, i)}
                    rightAction={renderRightAction(i)}
                  >
                    {obj?.component}
                  </Tabs.TabBox>
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>

        <Row className="action-bottom">
          <div className="button-right">
            <Dropdown overlay={menu} trigger="click">
              <Button minWidth={100} iconHeight={15} onClick={onGetDsPhieu}>
                {t("common.inGiayTo")}
              </Button>
            </Dropdown>

            <Button
              minWidth={100}
              type="primary"
              iconHeight={15}
              onClick={onSave}
              rightIcon={<SVG.IcSave />}
            >
              {t("common.luu")}
            </Button>
          </div>
        </Row>
      </Main>

      <ModalThemLichHen ref={refModalThemLichHen} />
      <ModalKetThucKham ref={refModalKetThucKham} />
      <ModalSignPrint ref={refModalSignPrint} />
    </MainPage>
  );
};

export default ChiTietKhamSL;
