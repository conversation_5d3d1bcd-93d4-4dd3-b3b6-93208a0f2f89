import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT, LOAI_DICH_VU } from "constants/index";
import dvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import { t } from "i18next";
import { cloneDeep } from "lodash";
import { combineSort } from "utils";

const initialState = {
  listData: [],
  totalElements: null,
  page: PAGE_DEFAULT,
  size: PAGE_SIZE,
  dataSearch: {
    loaiDichVu: LOAI_DICH_VU.NGOAI_DIEU_TRI,
  },
  dataSortColumn: {},
  listChiTietDv: [],
};

export default {
  state: cloneDeep(initialState),
  reducers: {
    clearData(state, payload = {}) {
      return { ...cloneDeep(initialState), ...payload };
    },
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }, state) => {
      dispatch.dsDvNgoaiDieuTri.updateData({
        ...rest,
      });
      dispatch.dsDvNgoaiDieuTri.onSearch({ rest });
    },
    onSearch: ({ page = 0, fromChiTiet = false, ...payload }, state) => {
      let newState = { isLoading: true };
      dispatch.dsDvNgoaiDieuTri.updateData(newState);
      let size = payload.size || state.dsDvNgoaiDieuTri.size || 10;
      const dataSortColumn =
        payload.dataSortColumn || state.dsDvNgoaiDieuTri.dataSortColumn || {};
      const sort = combineSort(dataSortColumn);
      const dataSearch =
        payload.dataSearch || state.dsDvNgoaiDieuTri.dataSearch || {};

      dvKyThuatProvider
        .getDsDichVu({
          page,
          size,
          sort,
          ...dataSearch,
        })
        .then((s) => {
          let listData = (s?.data || []).map((item, index) => {
            item.index = index + 1;
            return item;
          });
          dispatch.dsDvNgoaiDieuTri.updateData({
            ...(fromChiTiet
              ? { listChiTietDv: listData }
              : { listData: listData }),
            isLoading: false,
            totalElements: s?.totalElements,
            page,
            dataSearch,
            dataSortColumn,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.dsDvNgoaiDieuTri.updateData({
            ...(fromChiTiet ? { listChiTietDv: [] } : { listData: [] }),
            isLoading: false,
            dataSearch,
            dataSortColumn,
          });
        });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.dsDvNgoaiDieuTri.dataSortColumn,
        ...payload,
      };
      dispatch.dsDvNgoaiDieuTri.updateData({
        dataSortColumn,
      });
      dispatch.dsDvNgoaiDieuTri.onSearch({
        dataSortColumn,
      });
    },
    onChangeInputSearch: ({ ...payload }, state) => {
      const dataSearch = {
        ...(state.dsDvNgoaiDieuTri.dataSearch || {}),
        ...payload,
      };
      dispatch.dsDvNgoaiDieuTri.updateData({
        dataSearch,
      });
      dispatch.dsDvNgoaiDieuTri.onSearch({
        dataSearch,
      });
    },

    getSlNbTheoPhongThucHien: (payload, state) => {
      return new Promise((resolve, reject) => {
        dvKyThuatProvider
          .getSlNbTheoPhongThucHien(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
  }),
};
