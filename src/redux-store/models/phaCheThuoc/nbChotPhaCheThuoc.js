import { PAGE_SIZE, PAGE_DEFAULT } from "constants/index";
import nbPhaCheThuocProvider from "data-access/phaCheThuoc/nb-pha-che-thuoc-provider";
import { combineSort } from "utils";
import { message } from "antd";
import { t } from "i18next";
import printProvider from "data-access/print-provider";

export default {
  state: {
    isLoading: false,
    listData: [],
    dataTongHopTheoId: {},
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataSearch: {},
    dataSortColumn: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },

  effects: (dispatch) => ({
    onSearchTongHop: ({ page = 0, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        dispatch.nbChotPhaCheThuoc.updateData({ isLoading: true, page });
        let size = payload.size || state.nbChotPhaCheThuoc.size || 10;
        const sort = combineSort(
          payload.dataSortColumn || state.nbChotPhaCheThuoc.dataSortColumn || {}
        );
        const dataSearch =
          payload.dataSearch || state.nbChotPhaCheThuoc.dataSearch || {};
        nbPhaCheThuocProvider
          .searchTongHop({ ...dataSearch, page, size, sort, active: true })
          .then((s) => {
            resolve(s?.data);
            dispatch.nbChotPhaCheThuoc.updateData({
              listData: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              isLoading: false,
              totalElements: s?.totalElements || 0,
              page,
            });
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
            dispatch.nbChotPhaCheThuoc.updateData({
              listData: [],
              isLoading: false,
            });
          });
      });
    },
    onChangeInputSearch: ({ page, size = 10, ...payload }, state) => {
      return new Promise((resolve) => {
        const dataSearch = {
          ...(state.nbChotPhaCheThuoc.dataSearch || {}),
          ...payload,
        };
        dispatch.nbChotPhaCheThuoc.updateData({
          page,
          size,
          dataSearch,
        });
        dispatch.nbChotPhaCheThuoc.onSearchTongHop({
          page,
          size,
          dataSearch,
        });
      });
    },
    onSizeChange: ({ dataSearch, ...rest }, state) => {
      dispatch.nbChotPhaCheThuoc.updateData({
        ...rest,
      });
      dispatch.nbChotPhaCheThuoc.onSearchTongHop({ page: 0, ...rest });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.nbChotPhaCheThuoc.dataSortColumn,
        ...payload,
      };
      dispatch.nbChotPhaCheThuoc.updateData({
        dataSortColumn,
      });
      dispatch.nbChotPhaCheThuoc.onSearchTongHop({
        page: 0,
        dataSortColumn,
      });
    },
    themMoiChotPhaCheThuoc: async (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .taoPhieuLinhThuocPhaChe(payload)
          .then((res) => {
            if (res?.code === 0) {
              message.success(t("common.themMoiThanhCongDuLieu"));
              resolve(res);
            } else {
              resolve(res);
              if (res?.code != 1032) {
                message.error(
                  res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
              }
            }
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },
    getChiTietPhieuPhaCheTheoChot: async (id, state) => {
      return new Promise((resolve, reject) => {
        try {
          nbPhaCheThuocProvider
            .getById(id)
            .then((s) => {
              dispatch.nbChotPhaCheThuoc.updateData({
                chiTietPhieuPhaCheTheoChot: s?.data || {},
              });
              resolve(s?.data);
            })
            .catch((e) => {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              dispatch.nbChotPhaCheThuoc.updateData({
                chiTietPhieuPhaCheTheoChot: {},
              });
              reject(e);
            });
        } catch (err) {
          message.error(err.message.toString());
          reject(err);
        }
      });
    },
    getDSPhieuPhaCheTheoChot: async ({ id, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        try {
          nbPhaCheThuocProvider
            .searchTongHop(payload)
            .then((s) => {
              const _selectPhieu = (s?.data || []).find((x) => x.id == id);

              if (_selectPhieu) {
                dispatch.nbChotPhaCheThuoc.updateData({
                  dsPhieuPhaCheTheoChot:
                    _selectPhieu.dsPhaCheThuocChiTiet || [],
                  phieuNhapXuatId: _selectPhieu.phieuNhapXuatId,
                });
                resolve(s?.data);
              } else {
                dispatch.nbChotPhaCheThuoc.updateData({
                  dsPhieuPhaCheTheoChot: [],
                  phieuNhapXuatId: null,
                });
                resolve([]);
              }
            })
            .catch((e) => {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              dispatch.nbChotPhaCheThuoc.updateData({
                dsPhieuPhaCheTheoChot: [],
                phieuNhapXuatId: null,
              });
              reject(e);
            });
        } catch (err) {
          message.error(err.message.toString());
          reject(err);
        }
      });
    },
    phaCheThuoc: async (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .duyetPhaChe(payload)
          .then((res) => {
            if (res?.code === 0) {
              message.success(t("common.themMoiThanhCongDuLieu"));
              resolve(res.data);
            } else {
              resolve(res.data);
              message.error(
                res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
            }
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },
    inNhanPhaCheChai: async (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .getNhanPhaCheThuoc(payload)
          .then((res) => {
            printProvider.printPdf(res.data);
            resolve(res.data);
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },
    xoaThuocPhieuLinh: ({ id, nbDotDieuTriId }, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .xoaThuocPhieuLinh(id, nbDotDieuTriId)
          .then((s) => {
            if (s?.code == 0) {
              resolve(s);
              message.success(t("common.xoaDuLieuThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    deletePhieuXuatPhaChe: (id, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .delete(id)
          .then((s) => {
            resolve(s);
            message.success(t("common.xoaDuLieuThanhCong"));
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    inBanGiaoDichTruyen: async (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhaCheThuocProvider
          .getPhieuBanGiaoDichTruyen(payload)
          .then((res) => {
            printProvider.printPdf(res.data);
            resolve(res.data);
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },
  }),
};
