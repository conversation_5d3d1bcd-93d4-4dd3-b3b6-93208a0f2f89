import thietLapChonKhoProvider from "data-access/kho/thiet-lap-chon-kho-provider";
import { message } from "antd";
import { LOAI_DICH_VU, PAGE_DEFAULT, PAGE_SIZE } from "constants/index";
import apiBase from "data-access/api-base";
import dmMauDuLieuProvider from "data-access/dm-mau-du-lieu-provider";
import fileUtils from "utils/file-utils";
import { t } from "i18next";
import baseStore from "redux-store/models/base-store";

export default {
  ...baseStore({
    fetchProvider: thietLapChonKhoProvider,
    storeName: "thietLapChonKho",
    title: t("kho.thietLapChonKho"),
    initState: {
      listThietLapChonKho: [],
      listThietLapChonKhoTongHop: [],
      totalElements: null,
      page: PAGE_DEFAULT,
      size: PAGE_SIZE,
      dataEditDefault: {},
      dataSearch: {},
      listTuKho: [],
      dataSortColumn: { active: 2, ["dichVu.ten"]: 1 },
      listThietLapChonKhoVatTu: [],
      listThietLapChonKhoThuoc: [],
      listThietLapChonKhoMau: [],
      listThietLapChonKhoVacxin: [],
      listThietLapChonKhoChePhamDD: [],
    },
    customEffect: ({ dispatch }) => ({
      getListThietLapChonKho: async (payload = {}, state) => {
        const dataSearch = {
          ...(state.thietLapChonKho.dataSearch || {}),
          ...payload,
        };
        const response = await thietLapChonKhoProvider.search({
          sort: "createdAt,asc",
          ...dataSearch,
        });
        let {
          code,
          data: listThietLapChonKho,
          totalElements,
          message: messageInfo,
          pageNumber: page,
          pageSize: size,
          numberOfElements,
        } = response;
        if (code !== 0) throw new Error(messageInfo);

        if (page > 0 && numberOfElements === 0) {
          return dispatch.thietLapChonKho.listThietLapChonKho({
            ...payload,
            page: page - 1,
            size,
          });
        }

        return dispatch.thietLapChonKho.updateData({
          listThietLapChonKho,
          totalElements,
          page,
          size,
          listTuKho: listThietLapChonKho,
        });
      },

      getListThietLapChonKhoTongHop: async (payload = {}, state) => {
        return new Promise((resolve, reject) => {
          thietLapChonKhoProvider
            .searchTongHop({
              ...payload,
            })
            .then((response) => {
              let {
                code,
                data: listThietLapChonKhoTongHop,
                totalElements,
                message: messageInfo,
                pageNumber: page,
                pageSize: size,
                numberOfElements,
              } = response;

              if (code !== 0) throw new Error(messageInfo);

              if (page > 0 && numberOfElements === 0) {
                return dispatch.thietLapChonKho.listThietLapChonKho({
                  ...payload,
                  page: page - 1,
                  size,
                });
              }

              resolve(response.data);

              return dispatch.thietLapChonKho.updateData({
                listThietLapChonKhoTongHop,
                totalElements,
                page,
                size,
              });
            });
        });
      },

      getListThietLapChonKhoTheoTaiKhoan: async (payload = {}, state) => {
        const response = await thietLapChonKhoProvider.searchTheoTaiKhoan({
          page: "",
          size: "",
          sort: "createdAt,asc",
          ...payload,
        });
        let {
          code,
          data: listThietLapChonKho,
          totalElements,
          message: messageInfo,
          pageNumber: page,
          pageSize: size,
          numberOfElements,
        } = response;
        if (code !== 0) throw new Error(messageInfo);

        if (page > 0 && numberOfElements === 0) {
          return dispatch.thietLapChonKho.listThietLapChonKho({
            ...payload,
            page: page - 1,
            size,
          });
        }

        let addition = {};
        if (payload.loaiDichVu === LOAI_DICH_VU.THUOC) {
          addition.listThietLapChonKhoThuoc = listThietLapChonKho;
        } else if (payload.loaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU) {
          addition.listThietLapChonKhoMau = listThietLapChonKho;
        } else if (payload.loaiDichVu === LOAI_DICH_VU.VAT_TU) {
          addition.listThietLapChonKhoVatTu = listThietLapChonKho;
        } else if (payload.loaiDichVu === LOAI_DICH_VU.VAC_XIN) {
          addition.listThietLapChonKhoVacxin = listThietLapChonKho;
        } else if (payload.loaiDichVu === LOAI_DICH_VU.CHE_PHAM_DINH_DUONG) {
          addition.listThietLapChonKhoChePhamDD = listThietLapChonKho;
        } else if (payload.loaiDichVu === LOAI_DICH_VU.HOA_CHAT) {
          addition.listThietLapChonKhoHoaChat = listThietLapChonKho;
        }

        return dispatch.thietLapChonKho.updateData({
          listThietLapChonKho,
          totalElements,
          page,
          size,
          ...addition,
        });
      },
      onExport: () => {
        return new Promise((resolve, reject) => {
          dmMauDuLieuProvider
            .get({ dsBang: "kho_thiet_lap_chon_kho" })
            .then((res) => {
              if (res && res.code === 0) {
                fileUtils.downloadFile(
                  res.data?.data,
                  "kho_thiet_lap_chon_kho.xlsx"
                );
              }
              resolve(res);
            })
            .catch((e) => {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(e);
            });
        });
      },
      onImport: async (payload, state) => {
        apiBase
          .onImport(payload, thietLapChonKhoProvider.import)
          .then((res) => {
            dispatch.thietLapChonKho.getListThietLapChonKho({
              sort: "createdAt,desc",
            });
          });
      },

      onSortChange: ({ ...payload }, state) => {
        const dataSortColumn = {
          ...state.thietLapChonKho.dataSortColumn,
          ...payload,
        };
        dispatch.thietLapChonKho.updateData({
          page: 0,
          dataSortColumn,
        });
        dispatch.thietLapChonKho.getListThietLapChonKho({
          page: 0,
          dataSortColumn,
        });
      },

      onChangeInputSearch: ({ ...payload }, state) => {
        dispatch.thietLapChonKho.updateData({
          page: 0,
          ...payload,
        });
        dispatch.thietLapChonKho.getListThietLapChonKho({
          page: 0,
          ...payload,
        });
      },
    }),
  }),
};
