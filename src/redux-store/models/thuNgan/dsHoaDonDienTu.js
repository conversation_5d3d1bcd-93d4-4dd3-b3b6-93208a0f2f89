import nbHoaDonDienTuProvider from "data-access/nb-hoa-don-dien-tu-provider";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import { message } from "antd";
import {
  DINH_DANG_HOA_DON,
  HUONG_GIAY,
  KHO_GIAY,
  PAGE_DEFAULT,
} from "constants/index";
import { combineSort } from "utils";
import { t } from "i18next";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import pdfUtils from "utils/pdf-utils";
import printProvider, { printJS } from "data-access/print-provider";
import { showError } from "utils/message-utils";
import { isArray } from "lodash";

export default {
  state: {
    listData: [],
    chuaThanhToan: 0,
    daThanhToan: 0,
    tongSo: 0,
    totalElements: 0,
    page: PAGE_DEFAULT,
    size: 10,
    dataSearch: {},
    dataSortColumn: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ size }, state) => {
      dispatch.dsHoaDonDienTu.updateData({
        size,
        page: 0,
      });
      dispatch.dsHoaDonDienTu.onSearch({
        page: 0,
        size,
      });
    },

    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.dsHoaDonDienTu.updateData(newState);
      let size = payload?.size || state.dsHoaDonDienTu?.size;
      const sort = combineSort(
        payload?.dataSortColumn || state.dsHoaDonDienTu?.dataSortColumn || {}
      );
      const dataSearch =
        payload?.dataSearch || state?.dsHoaDonDienTu?.dataSearch || {};
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .search({ page, size, sort, ...dataSearch })
          .then((s) => {
            dispatch.dsHoaDonDienTu.updateData({
              listData: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              totalElements: s?.totalElements || 0,
              page,
              size,
              dataSearch,
            });
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            dispatch.dsHoaDonDienTu.updateData({
              listData: [],
              isLoading: false,
            });
          });
      });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.dsHoaDonDienTu.dataSortColumn,
        ...payload,
      };
      dispatch.dsHoaDonDienTu.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.dsHoaDonDienTu.onSearch({
        page: 0,
        dataSortColumn,
      });
    },

    onChangeInputSearch: ({ ...payload }, state) => {
      const dataSearch = {
        ...(state.dsHoaDonDienTu.dataSearch || {}),
        ...payload,
      };
      dispatch.dsHoaDonDienTu.updateData({
        page: 0,
        dataSearch: dataSearch,
      });
      dispatch.dsHoaDonDienTu.onSearch({
        page: 0,
        dataSearch,
      });
    },
    getDsDichVuDefault: ({ id, phieuThuId, dsPhieuThuId }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getDsDichVuDefault({ nbDotDieuTriId: id, phieuThuId, dsPhieuThuId })
          .then((s) => {
            resolve(s);
          })
          .catch((s) => {
            reject("");
          });
      });
    },
    getDsDichVu: ({ listId = [], nbDotDieuTriId }) => {
      return new Promise((resolve, reject) => {
        const promises = listId.map((id) => {
          return new Promise((resolve, reject) => {
            nbDichVuProvider
              .searchAll({ phieuThuId: id, nbDotDieuTriId })
              .then((s) => {
                if (s.code === 0) {
                  resolve(s.data);
                } else {
                  resolve({});
                }
              })
              .catch((s) => {
                reject({});
              });
          });
        });
        Promise.all(promises).then((s) => {
          const newData = s.filter((el) => el);

          resolve(newData);
        });
      });
    },
    getDsPhieuThu: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchTongHop(payload)
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject("");
          });
      });
    },
    xuatHoaDon: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .xuatHoaDon(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.xuatHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    luuNhapHoaDon: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .luuNhapHoaDon(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.taoMoiHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    xuatHoaDonNhap: ({ id }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .xuatHoaDonNhap({ id })
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.xuatHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getDsDichVuChiTiet: ({ hoaDonId }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getDsDichVuChiTiet({ hoaDonId })
          .then((s) => {
            if (s.code === 0) {
              const newData = s.data.map((item) => ({
                ...item,
                soPhieuThu: item.soPhieu,
              }));
              resolve(newData);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getFileHoaDon: ({
      id,
      lanGoi = 1,
      soLanGoi = 1,
      dinhDang = 10,
      chuyenDoi = false,
    }) => {
      return new Promise(async (resolve, reject) => {
        try {
          const hoaDon = await nbHoaDonDienTuProvider.getFileHoaDon({
            id,
            dinhDang,
            chuyenDoi,
          });
          if (hoaDon?.code == 1017) {
            //{code: 1017, message: 'Lỗi xem hóa đơn: Mã 12: Lỗi không xác định', data: null}
            if (lanGoi < soLanGoi) {
              setTimeout(() => {
                dispatch.dsHoaDonDienTu
                  .getFileHoaDon({
                    id,
                    lanGoi: lanGoi + 1,
                    soLanGoi,
                    dinhDang,
                    chuyenDoi,
                  })
                  .then((s) => {
                    resolve(s);
                  })
                  .catch((e) => {
                    reject(e);
                  });
              }, 2000);
            } else {
              message.error(
                hoaDon?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(hoaDon);
            }
          } else {
            resolve(hoaDon);
          }
        } catch (error) {
          if (typeof error !== "boolean") {
            message.error(
              error?.message || t("common.xayRaLoiVuiLongThuLaiSau")
            );
          }
          reject(error);
        }
      });
    },
    getBlobUrlHoaDon: async ({ hoaDonId, dinhDang, chuyenDoi }, state) => {
      try {
        let s = await dispatch.dsHoaDonDienTu.getFileHoaDon({
          id: hoaDonId,
          dinhDang: dinhDang,
          chuyenDoi,
        });

        let blob = null;
        if (dinhDang != DINH_DANG_HOA_DON.PDF) {
          const baoCao = await dispatch.baoCao.getMauBaoCao("EMR_BA271");
          const khoGiay = KHO_GIAY.A5 == baoCao?.khoGiay ? "A5" : "A4";
          const huongGiay =
            HUONG_GIAY.DOC == baoCao?.huongGiay
              ? HUONG_GIAY.DOC
              : HUONG_GIAY.NGANG;
          blob = await pdfUtils.htmlToPdf(s, {
            format: khoGiay,
            landscape: huongGiay == HUONG_GIAY.NGANG,
          });
        } else {
          blob = new Blob([new Uint8Array(s)], {
            type: "application/pdf",
          });
        }
        return window.URL.createObjectURL(blob);
      } catch (error) {
        showError(error?.message);
        return null;
      }
    },
    /*
    print = true: mặc định in luôn hóa đơn (tự động hoặc show print preview)
    print = false: chỉ trả về blob file hóa đơn    
    */
    inHoaDon: async (
      { hoaDonId, dinhDang, print = true, chuyenDoi = false },
      state
    ) => {
      try {
        if (!hoaDonId) return null;
        let blobUrl = null;
        if (!isArray(hoaDonId)) {
          blobUrl = await dispatch.dsHoaDonDienTu.getBlobUrlHoaDon({
            hoaDonId,
            dinhDang,
            chuyenDoi,
          });
        } else {
          if (!hoaDonId?.length) return null;
          const values = (
            await Promise.allSettled(
              hoaDonId.map((id) => {
                return dispatch.dsHoaDonDienTu.getBlobUrlHoaDon({
                  hoaDonId: id,
                  dinhDang,
                  chuyenDoi,
                });
              })
            )
          )
            .filter((item) => item.status === "fulfilled")
            .map((item) => item.value);
          blobUrl = await pdfUtils.mergePdf(values);
        }
        if (!blobUrl) return;
        // nếu truyền vào là print == false thì chỉ trả về file blob hóa đơn thôi, không thực hiện in.
        if (!print) return blobUrl;
        const baoCao = await dispatch.baoCao.getMauBaoCao("EMR_BA271");
        if (baoCao != null) {
          await printProvider.printPdf(
            [
              {
                loaiIn: baoCao.loaiIn,
                huongGiay: baoCao.huongGiay,
                khoGiay: baoCao.khoGiay,
                hinhThucIn: baoCao.hinhThucIn,
                file: { pdf: blobUrl },
              },
            ],
            {
              isEditor: true,
              mergePdfFile: blobUrl,
            }
          );
        } else {
          printJS({
            printable: blobUrl,
            type: "pdf",
          });
        }
      } catch (error) {
        showError(error?.message);
      }
    },
    deleteHoaDon: ({ id, lyDo }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .deleteHoaDon({ id, lyDo })
          .then((s) => {
            if (s.code === 0) {
              if (!s.data?.phanHoi) {
                message.success(t("thuNgan.xoaHoaDonThanhCong"));
                resolve(s.data);
              } else {
                message.error(
                  s.data?.phanHoi || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                reject({});
              }
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject({});
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getChiTietHoaDon: ({ id }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getChiTietHoaDon({ id })
          .then((s) => {
            if (s.code === 0) {
              resolve(s.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject({});
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getBangKeKemHDDT: ({ id }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getBangKeKemHDDT({ id })
          .then((s) => {
            if (s.code === 0) {
              resolve(s?.data?.file?.pdf);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject({});
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    getBienBanDieuChinh: ({ id }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getBienBanDieuChinh({ id })
          .then((s) => {
            if (!s?.byteLength) {
              reject({
                message: s?.message || t("phieuIn.khongCoThongTinPhieuIn"),
              });
            } else {
              resolve(s);
            }
          })
          .catch((e) => {
            reject({
              message: e?.message || t("common.xayRaLoiVuiLongThuLaiSau"),
            });
          });
      });
    },
    dieuChinhHoaDon: ({ id }) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .dieuChinhHoaDon({ id })
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.dieuChinhHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    capNhatHoaDon: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .capNhatHoaDon(payload?.hoaDonId, payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.capNhatHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    getHoaDonNhap: (id) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getHoaDonNhap(id)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inHoaDonNhap: (id) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .xemHoaDonNhap(id)
          .then((s) => {
            if (!s?.byteLength) {
              message.error(s?.message || t("phieuIn.khongCoThongTinPhieuIn"));
              reject(s);
            } else {
              resolve(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getTrangThaiDaPhatHanhHDDVBH: (nbDotDieuTriId) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .getTrangThaiDaPhatHanhHDDVBH(nbDotDieuTriId)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getDanhSachHoaDon: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .search(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s?.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    phatHanhHddtTaoMoiHangLoat: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .phatHanhHddtTaoMoiHangLoat(payload)
          .then((s) => {
            if (s.code === 0) {
              resolve(s);
              message.success(t("thuNgan.phatHanhHoaDonThanhCong"));
            } else {
              message.error(s?.message || t("thuNgan.phatHanhHoaDonThatBai"));
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message || t("thuNgan.phatHanhHoaDonThatBai"));
            reject(e);
          });
      });
    },
    taoHoaDonThayThe: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .taoHoaDonThayThe(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("thuNgan.taoHoaDonThayTheThanhCong"));
              resolve(s);
            } else {
              message.error(s?.message || t("thuNgan.taoHoaDonThayTheThatBai"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("thuNgan.taoHoaDonThayTheThatBai"));
            reject(e);
          });
      });
    },
    chuyenDoiHoaDon: (id) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .chuyenDoiHoaDon(id)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              message.error(s?.message || t("thuNgan.taoHoaDonThayTheThatBai"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("thuNgan.taoHoaDonThayTheThatBai"));
            reject(e);
          });
      });
    },
  }),
};
